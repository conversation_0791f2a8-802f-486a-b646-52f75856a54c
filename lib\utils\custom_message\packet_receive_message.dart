import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_custom_elem.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_message.dart';
// '{"redEnvelopeID":"red_envelope","isOpen":false,"image":"https://pic1.imgdb.cn/item/6858f1bc58cb8da5c864cbb4/base.png","remark":"${data['remark']}","amount":"${data['amount']}","version":4}'
class PacketReceiveMessage {
  String? remark;
  String? receiveID;
  int? version;
  String? changeUser;
  String? sender;
  String? senderName;

  PacketReceiveMessage.fromJSON(Map json) {
    remark = json["remark"];
    receiveID = json["receiveID"];
    version = json["version"];
    changeUser = json["changeUser"];
    sender = json["sender"];
    senderName = json["senderName"];
  }
}

PacketReceiveMessage? getPacketReceiveMessage(V2TimCustomElem? customElem) {

  try {
    if (customElem?.data != null) {
      final customMessage = jsonDecode(customElem!.data!);
      if (customMessage['receiveID'] != null) {
        debugPrint('领取红包消息8888: ${customMessage.toString()}');
        return PacketReceiveMessage.fromJSON(customMessage);
      }else {
        return null;
      }
    }
    return null;
  } catch (err) {
    return null;
  }
}

bool isPacketReceiveMessage(V2TimMessage? message) {
  try {
    if (message?.customElem?.data != null) {
      final customMessage = jsonDecode(message!.customElem!.data!);
      if (customMessage['receiveID'] != null) {
        return true;
      } else {
        return false;
      }
    }
    return false;
  } catch (err) {
    return false;
  }
}
