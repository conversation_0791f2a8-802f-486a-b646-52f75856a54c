// ignore_for_file: file_names

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_friend_info.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

import 'package:tencent_cloud_chat_demo/src/provider/theme.dart';
import 'package:tencent_cloud_chat_demo/src/user_profile.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/screen_utils.dart';

import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_friend_info.dart'
    if (dart.library.html) 'package:tencent_cloud_chat_sdk/web/compatible_models/v2_tim_friend_info.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/view_models/tui_friendship_view_model.dart';
import 'package:tencent_cloud_chat_uikit/data_services/services_locatar.dart';

import '../../widgets/avatar.dart';
import './widgets/empty.dart';

class BlackList extends StatelessWidget {
  const BlackList({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Provider.of<DefaultThemeData>(context).theme;
    final TUIFriendShipViewModel _friendshipViewModel =
        serviceLocator<TUIFriendShipViewModel>();
    _getShowName(V2TimFriendInfo item) {
      final friendRemark = item.friendRemark ?? "";
      final nickName = item.userProfile?.nickName ?? "";
      final userID = item.userID;
      final showName = nickName != "" ? nickName : userID;
      return friendRemark != "" ? friendRemark : showName;
    }

    Widget _itemBuilder(BuildContext context, V2TimFriendInfo friendInfo) {
      final showName = _getShowName(friendInfo);
      final faceUrl = friendInfo.userProfile?.faceUrl ?? "";
      return Slidable(
          endActionPane: ActionPane(motion: const DrawerMotion(), children: [
            SlidableAction(
              onPressed: (context) async {
                await _friendshipViewModel
                    .deleteFromBlockList([friendInfo.userID]);
              },
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              label: TIM_t("删除"),
              autoClose: true,
            )
          ]),
          child: InkWell(
              onTap: () {
                final isWideScreen = TUIKitScreenUtils.getFormFactor(context) ==
                    DeviceType.Desktop;
                print("----");
                print(isWideScreen);
                print("----");
                if (isWideScreen) {
                  // TODO
                } else {
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) =>
                            UserProfile(userID: friendInfo.userID),
                      ));
                }
              },
              child: Container(
                padding: const EdgeInsets.only(top: 16, left: 16),
                color: Colors.white,
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.only(bottom: 8),
                      margin: const EdgeInsets.only(right: 16),
                      child: Avatar(avatarUrl: faceUrl, size: 46, radius: 23),
                    ),
                    Expanded(
                        child: Container(
                      alignment: Alignment.centerLeft,
                      padding: const EdgeInsets.only(top: 16, bottom: 20),
                      decoration: const BoxDecoration(
                          border: Border(
                              bottom: BorderSide(color: Color(0xFFE9E9E9)))),
                      child: Text(
                        showName,
                        style:
                            const TextStyle(color: Colors.black, fontSize: 14),
                      ),
                    ))
                  ],
                ),
              )));
    }

    Widget blockedUsers() {
      return TIMUIKitBlackList(
        emptyBuilder: (_) {
          return const EmptyStateWidget(type: EmptyStateType.blacklist);
        },
        itemBuilder: _itemBuilder,
        onTapItem: (V2TimFriendInfo friendInfo) {
          final isWideScreen =
              TUIKitScreenUtils.getFormFactor(context) == DeviceType.Desktop;
          print("----");
          print(isWideScreen);
          print("----");
          if (isWideScreen) {
            // TODO
          } else {
            Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => UserProfile(userID: friendInfo.userID),
                ));
          }
        },
      );
    }

    Widget _body() {
      return Container(
        color: const Color(0xFFF9F9F9),
        child: Column(
          children: [const SizedBox(height: 16), blockedUsers()],
        ),
      );
    }

    return TUIKitScreenUtils.getDeviceWidget(
        context: context,
        desktopWidget: _body(),
        defaultWidget: Scaffold(
          appBar: AppBar(
            title: Text(
              TIM_t("黑名单"),
              style: const TextStyle(color: Color(0xFF333333), fontSize: 17),
            ),
            backgroundColor: const Color(0xFFFFFFFF),
          ),
          body: _body(),
        ));
  }
}
