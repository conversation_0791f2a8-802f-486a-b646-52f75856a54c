// 选择可见朋友圈的朋友
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';
import 'package:tencent_cloud_chat_sdk/enum/group_member_role.dart';
import 'package:tencent_cloud_chat_sdk/enum/group_member_role_enum.dart';
import 'package:tencent_cloud_chat_sdk/enum/group_type.dart';
import 'package:tencent_cloud_chat_sdk/manager/v2_tim_manager.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_conversation.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_friend_info.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_group_member.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_msg_create_info_result.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

import 'package:tencent_cloud_chat_demo/src/chat.dart';
import 'package:tencent_cloud_chat_demo/src/provider/theme.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/screen_utils.dart';
import 'package:tencent_cloud_chat_uikit/data_services/message/message_services.dart';
import 'package:tencent_cloud_chat_uikit/data_services/services_locatar.dart';

enum GroupTypeForUIKit { single, work, chat, meeting, public, community }

GlobalKey<_ChooseCan> chooseCanKey = GlobalKey();

class ChooseCan extends StatefulWidget {
  final GroupTypeForUIKit convType;
  final ValueChanged<V2TimConversation>? directToChat;
  final List<String>? preSelectedUserIDs;

  const ChooseCan({Key? key, required this.convType, this.directToChat, this.preSelectedUserIDs})
      : super(key: key);

  @override
  State<StatefulWidget> createState() => _ChooseCan();
}

class _ChooseCan extends State<ChooseCan> {
  final V2TIMManager _sdkInstance = TIMUIKitCore.getSDKInstance();
  final MessageService _messageService = serviceLocator<MessageService>();
  final CoreServicesImpl _coreInstance = TIMUIKitCore.getInstance();
  List<V2TimFriendInfo> friendList = [];
  List<V2TimFriendInfo> selectedFriendList = [];
  Map<String, V2TimFriendInfo> friendMap = {}; // 用于快速查找好友信息

  _getConversationList() async {
    final res = await _sdkInstance.getFriendshipManager().getFriendList();
    if (res.code == 0 && res.data != null) {
      friendList = res.data!;
      
      // 创建好友ID到好友信息的映射，用于快速查找
      friendMap = {for (var friend in friendList) friend.userID: friend};
      
      // 如果有预选的用户ID，将它们标记为已选择
      if (widget.preSelectedUserIDs != null && widget.preSelectedUserIDs!.isNotEmpty) {
        selectedFriendList = [];
        for (var userID in widget.preSelectedUserIDs!) {
          if (friendMap.containsKey(userID)) {
            selectedFriendList.add(friendMap[userID]!);
          }
        }
      }
      
      setState(() {});
    }
  }

  _createSingleConversation() async {
    final userID = selectedFriendList.first.userID;
    final conversationID = "c2c_$userID";
    final res = await _sdkInstance
        .getConversationManager()
        .getConversation(conversationID: conversationID);

    if (res.code == 0) {
      final V2TimConversation conversation = res.data ??
          V2TimConversation(
              conversationID: conversationID, userID: userID, type: 1);
      if (widget.directToChat != null) {
        widget.directToChat!(conversation);
      } else {
        Navigator.pushReplacement(
            context,
            MaterialPageRoute(
                builder: (context) =>
                    Chat(selectedConversation: conversation)));
      }
    }
  }

  _getShowName(V2TimFriendInfo item) {
    final friendRemark = item.friendRemark ?? "";
    final nickName = item.userProfile?.nickName ?? "";
    final userID = item.userID;
    final showName = nickName != "" ? nickName : userID;
    return friendRemark != "" ? friendRemark : showName;
  }

  bool _isValidGroupName(String groupName) {
    final List<int> bytes = utf8.encode(groupName);
    return !(bytes.length > 30);
  }

  String _generateGroupName() {
    String groupName =
        selectedFriendList.map((e) => _getShowName(e)).join(", ");
    if (_isValidGroupName(groupName)) {
      return groupName;
    }

    final option1 = selectedFriendList.length;
    groupName = _getShowName(selectedFriendList[0]) +
        TIM_t_para(" 等{{option1}}人", " 等$option1人")(option1: option1);
    if (_isValidGroupName(groupName)) {
      return groupName;
    }

    final option2 = selectedFriendList.length + 1;
    groupName = _getShowName(selectedFriendList[0]) +
        TIM_t_para("{{option2}}人群", "$option2人群")(option2: option2);
    if (_isValidGroupName(groupName)) {
      return groupName;
    }

    return TIM_t("新群聊");
  }

  _createGroup(String groupType) async {
    String groupName = _generateGroupName();
    final groupMember = selectedFriendList.map((e) {
      final role = e.userProfile!.role!;
      GroupMemberRoleTypeEnum roleEnum =
          GroupMemberRoleTypeEnum.V2TIM_GROUP_MEMBER_UNDEFINED;
      switch (role) {
        case GroupMemberRoleType.V2TIM_GROUP_MEMBER_ROLE_ADMIN:
          roleEnum = GroupMemberRoleTypeEnum.V2TIM_GROUP_MEMBER_ROLE_ADMIN;
          break;
        case GroupMemberRoleType.V2TIM_GROUP_MEMBER_ROLE_MEMBER:
          roleEnum = GroupMemberRoleTypeEnum.V2TIM_GROUP_MEMBER_ROLE_MEMBER;
          break;
        case GroupMemberRoleType.V2TIM_GROUP_MEMBER_ROLE_OWNER:
          roleEnum = GroupMemberRoleTypeEnum.V2TIM_GROUP_MEMBER_ROLE_OWNER;
          break;
        case GroupMemberRoleType.V2TIM_GROUP_MEMBER_UNDEFINED:
          roleEnum = GroupMemberRoleTypeEnum.V2TIM_GROUP_MEMBER_UNDEFINED;
          break;
      }

      return V2TimGroupMember(role: roleEnum, userID: e.userID);
    }).toList();
    final res = await _sdkInstance.getGroupManager().createGroup(
        groupType: groupType,
        groupName: groupName,
        memberList: groupType != GroupType.AVChatRoom ? groupMember : null);
    if (res.code == 0) {
      final groupID = res.data;
      final conversationID = "group_$groupID";
      if (groupType == "AVChatRoom" && groupID != null) {
        await _sdkInstance.joinGroup(groupID: groupID, message: "Hi");
      }
      final convRes = await _sdkInstance
          .getConversationManager()
          .getConversation(conversationID: conversationID);
      if (convRes.code == 0) {
        await _sendMessageToNewlyCreatedGroup(groupType, groupID!);
        final conversation = convRes.data ??
            V2TimConversation(
                conversationID: conversationID,
                type: 2,
                showName: groupName,
                groupType: groupType,
                groupID: groupID);

        if (widget.directToChat != null) {
          widget.directToChat!(conversation);
        } else {
          Navigator.pushAndRemoveUntil(
              context,
              MaterialPageRoute(
                  builder: (context) =>
                    Chat(selectedConversation: conversation)
              ),
              ModalRoute.withName("/homePage")
          );
        }
      }
    }
  }

  _sendMessageToNewlyCreatedGroup(String groupType, String groupID) async {
    final loginUserInfo = _coreInstance.loginUserInfo;
    V2TimMsgCreateInfoResult? res = await _messageService.createCustomMessage(
        data: json.encode(
            {"businessID": "group_create",
              "version": 4,
              "opUser": loginUserInfo?.nickName ?? loginUserInfo!.userID,
              "content": groupType == GroupType.Community ? TIM_t("创建社群") : TIM_t("创建群组"),
              "cmd": groupType == GroupType.Community ? 1 : 0}));
    if (res != null) {
      final sendMsgRes = await _messageService.sendMessage(
          id: res.id!,
          groupID: groupID,
          receiver: '');
    }
  }

  @override
  void initState() {
    super.initState();
    _getConversationList();
    // 在下一帧执行，确保friendList已加载
  WidgetsBinding.instance.addPostFrameCallback((_) {
    _initializeSelectedContacts();
  });
  }

  // 在 _ChooseCan 类中添加一个方法
void _initializeSelectedContacts() {
  // 在获取到好友列表后，手动设置选中状态
  if (widget.preSelectedUserIDs != null && widget.preSelectedUserIDs!.isNotEmpty && friendList.isNotEmpty) {
    // 根据预选的用户ID找到对应的好友信息
    selectedFriendList = friendList.where((friend) => 
      widget.preSelectedUserIDs!.contains(friend.userID)).toList();
    setState(() {});
  }
}

  void onSubmit() {
    if (selectedFriendList.isNotEmpty) {
      // 将选择的好友列表返回给上一个页面
      Navigator.pop(context, selectedFriendList);
    } else {
      // 如果没有选择好友，直接返回空列表
      Navigator.pop(context, []);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Provider.of<DefaultThemeData>(context).theme;
    final isWideScreen =
        TUIKitScreenUtils.getFormFactor(context) == DeviceType.Desktop;

    Widget chooseMembers() {
      // 创建一个自定义的ContactList组件
      return ContactListWithPreselection(
        bgColor: isWideScreen ? theme.wideBackgroundColor : null,
        contactList: friendList,
        isCanSelectMemberItem: true,
        maxSelectNum: widget.convType == GroupTypeForUIKit.single ? null : null,
        onSelectedMemberItemChange: (selectedMember) {
          selectedFriendList = selectedMember;
          setState(() {});
        },
        preSelectedUserIDs: widget.preSelectedUserIDs,
      );
    }

    return TUIKitScreenUtils.getDeviceWidget(
        context: context,
        desktopWidget: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: chooseMembers(),
        ),
        defaultWidget: Scaffold(
          backgroundColor: Colors.white,

          appBar: AppBar(
              automaticallyImplyLeading: false, // 禁用自动生成的返回按钮
              leading: null, // 移除leading
              leadingWidth: 0, // 设置leading宽度为0

              backgroundColor: Colors.white,

              shadowColor: theme.weakDividerColor,
              actions: [
                GestureDetector(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                    margin: const EdgeInsets.only(left: 16),

                    height: 32,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      color: const Color(0xFFE9E9E9),
                    ),
                    child:  Center(
                      child: Text(TIM_t('取消'), style: const TextStyle(color: Color(0XFF666666))),
                    ),                ),
                ),
                const Spacer(),
                Text(
                  TIM_t("选择可见的朋友"),
                  style: const TextStyle(color: Colors.black, fontSize: 14),
                ),
                const Spacer(),
                GestureDetector(
                  onTap: () {
                    onSubmit();
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                    margin: const EdgeInsets.only(right: 16),

                    height: 32,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      color: const Color(0xFF007AFF),
                    ),
                    child:  Center(

                      child: Text(TIM_t('确定'), style: const TextStyle(color: Colors.white)),
                    )
                  ),
                )
              ],
              iconTheme: const IconThemeData(
                color: Colors.black,
              )),
          body: Container(

            child: chooseMembers(),
          ),
        ));
  }
}

// 自定义的ContactListWithPreselection组件
class ContactListWithPreselection extends StatefulWidget {
  final List<V2TimFriendInfo> contactList;
  final bool isCanSelectMemberItem;
  final int? maxSelectNum;
  final ValueChanged<List<V2TimFriendInfo>> onSelectedMemberItemChange;
  final List<String>? preSelectedUserIDs;
  final Color? bgColor;

  const ContactListWithPreselection({
    Key? key,
    required this.contactList,
    required this.isCanSelectMemberItem,
    this.maxSelectNum,
    required this.onSelectedMemberItemChange,
    this.preSelectedUserIDs,
    this.bgColor,
  }) : super(key: key);

  @override
  State<ContactListWithPreselection> createState() => _ContactListWithPreselectionState();
}

class _ContactListWithPreselectionState extends State<ContactListWithPreselection> {
  List<V2TimFriendInfo> _selectedMemberList = [];

  @override
  void initState() {
    super.initState();
    _updateSelectedMembers();
  }
  
  @override
  void didUpdateWidget(ContactListWithPreselection oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 当contactList更新时，重新设置预选状态
    if (oldWidget.contactList != widget.contactList) {
      _updateSelectedMembers();
    }
  }
  
  // 更新选中的成员列表
  void _updateSelectedMembers() {
    if (widget.preSelectedUserIDs != null && 
        widget.preSelectedUserIDs!.isNotEmpty && 
        widget.contactList.isNotEmpty) {
      if (mounted) {  
        setState(() {
          _selectedMemberList = widget.contactList.where((friend) => 
            widget.preSelectedUserIDs!.contains(friend.userID)).toList();
        });
        
        // 通知父组件更新选中状态
        if (_selectedMemberList.isNotEmpty) {
          // 使用Future.microtask确保在当前帧结束后执行
          Future.microtask(() {
            if (mounted) {  
              widget.onSelectedMemberItemChange(_selectedMemberList);
            }
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: widget.bgColor,
      child: ListView.builder(
        itemCount: widget.contactList.length,
        itemBuilder: (context, index) {
          final friend = widget.contactList[index];
          final bool isSelected = _selectedMemberList.contains(friend);
          
          void toggleSelection() {
            if (widget.isCanSelectMemberItem) {
              if (isSelected) {
                _selectedMemberList.remove(friend);
              } else {
                if (widget.maxSelectNum != null && _selectedMemberList.length >= widget.maxSelectNum!) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('最多只能选择 ${widget.maxSelectNum} 个联系人'),
                      duration: const Duration(seconds: 1),
                    ),
                  );
                  return;
                }
                _selectedMemberList.add(friend);
              }
              widget.onSelectedMemberItemChange(_selectedMemberList);
              setState(() {});
            }
          }
          
          return ListTile(
            contentPadding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
            horizontalTitleGap: 0, // 减少title和leading之间的间距
            title: Text(_getShowName(friend)),
            leading: Checkbox(
              value: isSelected,
              onChanged: (bool? value) {
                toggleSelection();
              },
              shape: const CircleBorder(),
              checkColor: Colors.white,
              fillColor: WidgetStateProperty.resolveWith<Color>(
                (Set<WidgetState> states) {
                  if (states.contains(WidgetState.selected)) {
                    return const Color(0xFF007AFF);
                  }
                  return Colors.transparent;
                },
              ),
              side: WidgetStateBorderSide.resolveWith((states) {
                if (states.contains(WidgetState.selected)) {
                  return const BorderSide(
                    width:1.0,
                    color: Color(0xFFFFFFFF),
                  );
                }
                return const BorderSide(
                  width: 1.0,
                  color: Color(0xFFD0D0D0), // 未选中时的灰色边框
                );
              }),
            ),
            onTap: toggleSelection,
          );
        },
      ),
    );
  }

  String _getShowName(V2TimFriendInfo item) {
    final friendRemark = item.friendRemark ?? "";
    final nickName = item.userProfile?.nickName ?? "";
    final userID = item.userID;
    final showName = nickName != "" ? nickName : userID;
    return friendRemark != "" ? friendRemark : showName;
  }
}
