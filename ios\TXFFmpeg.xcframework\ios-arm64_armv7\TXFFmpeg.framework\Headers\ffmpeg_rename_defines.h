// Copyright (c) 2021 Tencent. All rights reserved.

// This file generated by rename_symbols_generater.py.
// Do not modify it manually.

#ifndef THIRD_PARTY_FFMPEG_FFMPEG_RENAME_DEFINES_H
#define THIRD_PARTY_FFMPEG_FFMPEG_RENAME_DEFINES_H

// clang-format off
#define ff_hevc_merge_flag_decode liteav_ff_hevc_merge_flag_decode
#define ff_deblock_h_chroma_10_avx liteav_ff_deblock_h_chroma_10_avx
#define ff_pred16x16_plane_rv40_8_mmx liteav_ff_pred16x16_plane_rv40_8_mmx
#define ff_avg_h264_qpel8_mc03_neon liteav_ff_avg_h264_qpel8_mc03_neon
#define ff_h264_direct_dist_scale_factor liteav_ff_h264_direct_dist_scale_factor
#define ff_deblock_h_chroma_intra_8_mmxext liteav_ff_deblock_h_chroma_intra_8_mmxext
#define av_buffer_is_writable liteav_av_buffer_is_writable
#define ff_pw_96 liteav_ff_pw_96
#define webvtt_packet_parse liteav_webvtt_packet_parse
#define ff_dv_frame_profile liteav_ff_dv_frame_profile
#define av_buffer_unref liteav_av_buffer_unref
#define av_opt_query_ranges_default liteav_av_opt_query_ranges_default
#define av_frame_set_color_range liteav_av_frame_set_color_range
#define av_bprint_init liteav_av_bprint_init
#define av_des_mac liteav_av_des_mac
#define ff_init_desc_chscale liteav_ff_init_desc_chscale
#define ff_fdctdsp_init liteav_ff_fdctdsp_init
#define ff_hevcdsp_init_neon_intrinsics liteav_ff_hevcdsp_init_neon_intrinsics
#define ff_pcm_read_seek liteav_ff_pcm_read_seek
#define av_fifo_generic_write liteav_av_fifo_generic_write
#define avio_close_dir liteav_avio_close_dir
#define av_strlcpy liteav_av_strlcpy
#define av_sha_final liteav_av_sha_final
#define avfilter_link liteav_avfilter_link
#define ff_mpeg4_intra_run liteav_ff_mpeg4_intra_run
#define ff_check_interrupt liteav_ff_check_interrupt
#define ff_ps_hybrid_synthesis_deint_neon liteav_ff_ps_hybrid_synthesis_deint_neon
#define av_strdup liteav_av_strdup
#define av_get_channel_layout_nb_channels liteav_av_get_channel_layout_nb_channels
#define ff_sws_init_output_funcs liteav_ff_sws_init_output_funcs
#define ff_pred4x4_horizontal_down_10_ssse3 liteav_ff_pred4x4_horizontal_down_10_ssse3
#define ff_hevc_pred_angular_16x16_v_zero_neon_8 liteav_ff_hevc_pred_angular_16x16_v_zero_neon_8
#define ff_put_h264_qpel8_mc30_neon liteav_ff_put_h264_qpel8_mc30_neon
#define vlc_css_declaration_New liteav_vlc_css_declaration_New
#define ff_videotoolbox_alloc_frame liteav_ff_videotoolbox_alloc_frame
#define ff_draw_init liteav_ff_draw_init
#define av_find_best_pix_fmt_of_2 liteav_av_find_best_pix_fmt_of_2
#define ff_avg_pixels16_xy2_neon liteav_ff_avg_pixels16_xy2_neon
#define avpriv_slicethread_free liteav_avpriv_slicethread_free
#define ff_blockdsp_init_x86 liteav_ff_blockdsp_init_x86
#define av_tree_node_size liteav_av_tree_node_size
#define ff_pred4x4_down_left_10_sse2 liteav_ff_pred4x4_down_left_10_sse2
#define av_image_fill_max_pixsteps liteav_av_image_fill_max_pixsteps
#define ff_attach_decode_data liteav_ff_attach_decode_data
#define ff_aic_dc_scale_table liteav_ff_aic_dc_scale_table
#define ff_h264_idct_add16_8_mmxext liteav_ff_h264_idct_add16_8_mmxext
#define ff_mp4_read_descr liteav_ff_mp4_read_descr
#define ffurl_closep liteav_ffurl_closep
#define ff_mov_init_hinting liteav_ff_mov_init_hinting
#define ff_hevc_put_pel_uw_pixels_w4_neon_8_asm liteav_ff_hevc_put_pel_uw_pixels_w4_neon_8_asm
#define av_packet_new_side_data liteav_av_packet_new_side_data
#define ff_hevc_put_qpel_uw_v3_neon_8 liteav_ff_hevc_put_qpel_uw_v3_neon_8
#define ff_dct32_float_sse2 liteav_ff_dct32_float_sse2
#define av_append_path_component liteav_av_append_path_component
#define ff_pack_8ch_float_to_float_u_sse2 liteav_ff_pack_8ch_float_to_float_u_sse2
#define av_log_set_level liteav_av_log_set_level
#define ff_h264_chroma422_dc_scan liteav_ff_h264_chroma422_dc_scan
#define ff_af_aformat liteav_ff_af_aformat
#define ff_pw_4 liteav_ff_pw_4
#define ff_fmt_is_in liteav_ff_fmt_is_in
#define ff_pw_2 liteav_ff_pw_2
#define ff_pw_3 liteav_ff_pw_3
#define ff_hyscale_fast_c liteav_ff_hyscale_fast_c
#define ff_pw_1 liteav_ff_pw_1
#define ff_pw_8 liteav_ff_pw_8
#define av_opt_is_set_to_default liteav_av_opt_is_set_to_default
#define ff_dither_2x2_4 liteav_ff_dither_2x2_4
#define ff_flac_parse_picture liteav_ff_flac_parse_picture
#define ff_dct32_fixed liteav_ff_dct32_fixed
#define ff_h264_weight_4_10_sse2 liteav_ff_h264_weight_4_10_sse2
#define ff_put_pixels8_l2_mmxext liteav_ff_put_pixels8_l2_mmxext
#define ff_h263_static_rl_table_store liteav_ff_h263_static_rl_table_store
#define ff_mpv_common_init liteav_ff_mpv_common_init
#define rgb24to32 liteav_rgb24to32
#define ff_aac_num_swb_128 liteav_ff_aac_num_swb_128
#define av_videotoolbox_default_free liteav_av_videotoolbox_default_free
#define ff_amf_match_string liteav_ff_amf_match_string
#define ff_h263_h_loop_filter_mmx liteav_ff_h263_h_loop_filter_mmx
#define av_get_colorspace_name liteav_av_get_colorspace_name
#define ff_h264_execute_ref_pic_marking liteav_ff_h264_execute_ref_pic_marking
#define ff_aac_num_swb_120 liteav_ff_aac_num_swb_120
#define ff_put_h264_chroma_mc8_10_avx liteav_ff_put_h264_chroma_mc8_10_avx
#define av_rescale liteav_av_rescale
#define ffurl_open_whitelist liteav_ffurl_open_whitelist
#define ff_mdct_end liteav_ff_mdct_end
#define av_register_all liteav_av_register_all
#define ff_h264_idct_add8_8_mmxext liteav_ff_h264_idct_add8_8_mmxext
#define ff_sbr_hf_apply_noise_0_neon liteav_ff_sbr_hf_apply_noise_0_neon
#define av_dv_codec_profile liteav_av_dv_codec_profile
#define ff_rtmpts_protocol liteav_ff_rtmpts_protocol
#define ff_j_rev_dct1 liteav_ff_j_rev_dct1
#define ff_j_rev_dct4 liteav_ff_j_rev_dct4
#define ff_h264_chroma_dc_dequant_idct_12_c liteav_ff_h264_chroma_dc_dequant_idct_12_c
#define av_tree_destroy liteav_av_tree_destroy
#define av_bsf_list_append2 liteav_av_bsf_list_append2
#define ff_avg_h264_qpel16_mc22_10_sse2 liteav_ff_avg_h264_qpel16_mc22_10_sse2
#define ff_videotoolbox_h264_start_frame liteav_ff_videotoolbox_h264_start_frame
#define ff_frame_thread_encoder_init liteav_ff_frame_thread_encoder_init
#define ff_cos_4096 liteav_ff_cos_4096
#define ff_pred8x8l_dc_8_ssse3 liteav_ff_pred8x8l_dc_8_ssse3
#define ff_mvtab liteav_ff_mvtab
#define ff_blend_mask liteav_ff_blend_mask
#define ff_hevc_put_qpel_uw_h1v2_neon_8 liteav_ff_hevc_put_qpel_uw_h1v2_neon_8
#define ff_h263_decode_mb liteav_ff_h263_decode_mb
#define ff_simple_idct_neon liteav_ff_simple_idct_neon
#define av_hwframe_get_buffer liteav_av_hwframe_get_buffer
#define rgb32to16 liteav_rgb32to16
#define rgb32to15 liteav_rgb32to15
#define ff_put_pixels8_neon liteav_ff_put_pixels8_neon
#define ff_avg_h264_qpel16_mc10_10_sse2_cache64 liteav_ff_avg_h264_qpel16_mc10_10_sse2_cache64
#define ff_avg_h264_qpel4_mc20_10_mmxext liteav_ff_avg_h264_qpel4_mc20_10_mmxext
#define ff_ebur128_loudness_momentary liteav_ff_ebur128_loudness_momentary
#define ff_deblock_h_chroma422_intra_8_mmxext liteav_ff_deblock_h_chroma422_intra_8_mmxext
#define av_packet_unpack_dictionary liteav_av_packet_unpack_dictionary
#define ff_sprite_trajectory_tab liteav_ff_sprite_trajectory_tab
#define ff_inlink_peek_frame liteav_ff_inlink_peek_frame
#define avio_wb16 liteav_avio_wb16
#define ff_int32_to_int16_u_sse2 liteav_ff_int32_to_int16_u_sse2
#define ff_h263_decode_init liteav_ff_h263_decode_init
#define avcodec_dct_init liteav_avcodec_dct_init
#define ff_hevc_put_qpel_uw_weight_h3v1_neon_8 liteav_ff_hevc_put_qpel_uw_weight_h3v1_neon_8
#define ff_make_formatu64_list liteav_ff_make_formatu64_list
#define ff_h263_update_motion_val liteav_ff_h263_update_motion_val
#define ff_mpeg4video_parser liteav_ff_mpeg4video_parser
#define ff_raw_video_read_header liteav_ff_raw_video_read_header
#define av_dv_codec_profile2 liteav_av_dv_codec_profile2
#define ff_inlink_check_available_samples liteav_ff_inlink_check_available_samples
#define av_get_bits_per_pixel liteav_av_get_bits_per_pixel
#define ff_yuv2rgb_c_init_tables liteav_ff_yuv2rgb_c_init_tables
#define avio_get_str16le liteav_avio_get_str16le
#define ff_simple_idct_int16_10bit liteav_ff_simple_idct_int16_10bit
#define ff_codec_bmp_tags liteav_ff_codec_bmp_tags
#define ff_h264_idct_dc_add_8_sse2 liteav_ff_h264_idct_dc_add_8_sse2
#define av_opt_set_defaults2 liteav_av_opt_set_defaults2
#define ff_avg_h264_qpel4_mc33_10_mmxext liteav_ff_avg_h264_qpel4_mc33_10_mmxext
#define av_audio_fifo_peek liteav_av_audio_fifo_peek
#define ff_mpeg4_default_intra_matrix liteav_ff_mpeg4_default_intra_matrix
#define ff_h264_idct_add16_8_mmx liteav_ff_h264_idct_add16_8_mmx
#define ff_put_h264_qpel8_mc23_10_sse2 liteav_ff_put_h264_qpel8_mc23_10_sse2
#define av_frame_get_side_data liteav_av_frame_get_side_data
#define avcodec_decode_audio4 liteav_avcodec_decode_audio4
#define ff_put_pixels8_mmx liteav_ff_put_pixels8_mmx
#define ff_h264_p_mb_type_info liteav_ff_h264_p_mb_type_info
#define ff_mpv_common_end liteav_ff_mpv_common_end
#define ff_cbrt_tab liteav_ff_cbrt_tab
#define swri_rematrix_init_x86 liteav_swri_rematrix_init_x86
#define ff_avg_h264_qpel4_mc10_10_mmxext liteav_ff_avg_h264_qpel4_mc10_10_mmxext
#define ff_framequeue_take liteav_ff_framequeue_take
#define ff_h263dsp_init_x86 liteav_ff_h263dsp_init_x86
#define av_packet_move_ref liteav_av_packet_move_ref
#define ff_avg_h264_qpel16_mc02_10_sse2 liteav_ff_avg_h264_qpel16_mc02_10_sse2
#define av_cpu_max_align liteav_av_cpu_max_align
#define av_buffer_default_free liteav_av_buffer_default_free
#define av_int2i liteav_av_int2i
#define ff_unpack_6ch_float_to_int32_a_avx liteav_ff_unpack_6ch_float_to_int32_a_avx
#define ff_codec_wav_tags liteav_ff_codec_wav_tags
#define ff_pred16x16_dc_8_sse2 liteav_ff_pred16x16_dc_8_sse2
#define ff_init_ff_sine_windows liteav_ff_init_ff_sine_windows
#define ff_simple_idct10_sse2 liteav_ff_simple_idct10_sse2
#define av_camellia_size liteav_av_camellia_size
#define ff_put_h264_qpel16_mc10_10_sse2_cache64 liteav_ff_put_h264_qpel16_mc10_10_sse2_cache64
#define ff_pred8x8_top_dc_8_mmxext liteav_ff_pred8x8_top_dc_8_mmxext
#define rgb64tobgr48_nobswap liteav_rgb64tobgr48_nobswap
#define ff_parse_time_base liteav_ff_parse_time_base
#define av_chroma_location_from_name liteav_av_chroma_location_from_name
#define ff_yuv422p_to_argb_neon liteav_ff_yuv422p_to_argb_neon
#define ff_hevc_put_qpel_uw_h2v3_neon_8 liteav_ff_hevc_put_qpel_uw_h2v3_neon_8
#define av_get_pix_fmt_loss liteav_av_get_pix_fmt_loss
#define ffio_free_dyn_buf liteav_ffio_free_dyn_buf
#define ff_unpack_2ch_int16_to_int16_a_sse2 liteav_ff_unpack_2ch_int16_to_int16_a_sse2
#define ff_h264_chroma_dc_dequant_idct_10_c liteav_ff_h264_chroma_dc_dequant_idct_10_c
#define ff_cos_tabs_fixed liteav_ff_cos_tabs_fixed
#define av_frame_set_channel_layout liteav_av_frame_set_channel_layout
#define ff_h264_get_profile liteav_ff_h264_get_profile
#define ff_h264_idct8_add4_14_c liteav_ff_h264_idct8_add4_14_c
#define ff_pred4x4_down_right_8_mmxext liteav_ff_pred4x4_down_right_8_mmxext
#define ff_float_to_int32_u_sse2 liteav_ff_float_to_int32_u_sse2
#define ff_pred16x16_plane_h264_8_mmx liteav_ff_pred16x16_plane_h264_8_mmx
#define ff_hevc_put_qpel_h1v3_neon_8 liteav_ff_hevc_put_qpel_h1v3_neon_8
#define ff_hevc_luma_mv_merge_mode liteav_ff_hevc_luma_mv_merge_mode
#define ff_bsf_get_packet_ref liteav_ff_bsf_get_packet_ref
#define ff_hevc_put_qpel_uw_pixels_w24_neon_8 liteav_ff_hevc_put_qpel_uw_pixels_w24_neon_8
#define ff_h264_b_mb_type_info liteav_ff_h264_b_mb_type_info
#define ff_h264_biweight_16_mmxext liteav_ff_h264_biweight_16_mmxext
#define ff_h264qpel_init liteav_ff_h264qpel_init
#define av_opt_get_pixel_fmt liteav_av_opt_get_pixel_fmt
#define ff_int16_to_float_a_sse2 liteav_ff_int16_to_float_a_sse2
#define ff_mpa_synth_filter_fixed liteav_ff_mpa_synth_filter_fixed
#define ff_qpeldsp_init liteav_ff_qpeldsp_init
#define av_mdct_end liteav_av_mdct_end
#define ff_alloc_packet2 liteav_ff_alloc_packet2
#define avfilter_config_links liteav_avfilter_config_links
#define ff_aac_scalefactor_bits liteav_ff_aac_scalefactor_bits
#define ff_avg_pixels16_xy2_no_rnd_neon liteav_ff_avg_pixels16_xy2_no_rnd_neon
#define ff_hevc_put_pel_uw_pixels_w16_neon_8_asm liteav_ff_hevc_put_pel_uw_pixels_w16_neon_8_asm
#define avio_get_str16be liteav_avio_get_str16be
#define ff_pack_2ch_int32_to_int16_u_sse2 liteav_ff_pack_2ch_int32_to_int16_u_sse2
#define av_thread_message_queue_nb_elems liteav_av_thread_message_queue_nb_elems
#define ff_amf_write_string liteav_ff_amf_write_string
#define ff_vf_rotate liteav_ff_vf_rotate
#define ff_codec_wav_guids liteav_ff_codec_wav_guids
#define ff_put_pixels16_sse2 liteav_ff_put_pixels16_sse2
#define ff_blockdsp_init liteav_ff_blockdsp_init
#define avio_read liteav_avio_read
#define av_frame_get_best_effort_timestamp liteav_av_frame_get_best_effort_timestamp
#define avcodec_decode_video2 liteav_avcodec_decode_video2
#define ff_avg_h264_qpel8or16_v_lowpass_op_mmxext liteav_ff_avg_h264_qpel8or16_v_lowpass_op_mmxext
#define ff_swb_offset_1024 liteav_ff_swb_offset_1024
#define ff_mpv_decode_defaults liteav_ff_mpv_decode_defaults
#define ff_h263_chroma_qscale_table liteav_ff_h263_chroma_qscale_table
#define ff_hevc_sao_edge_eo0_w32_neon_8 liteav_ff_hevc_sao_edge_eo0_w32_neon_8
#define ff_rtmp_calc_digest liteav_ff_rtmp_calc_digest
#define swr_alloc_set_opts liteav_swr_alloc_set_opts
#define av_thread_message_queue_alloc liteav_av_thread_message_queue_alloc
#define av_strnstr liteav_av_strnstr
#define av_write_trailer liteav_av_write_trailer
#define ff_inlink_acknowledge_status liteav_ff_inlink_acknowledge_status
#define ff_id3v2_parse_chapters liteav_ff_id3v2_parse_chapters
#define avfilter_init_dict liteav_avfilter_init_dict
#define ff_init_cabac_encoder liteav_ff_init_cabac_encoder
#define ff_pred8x8l_down_right_8_mmxext liteav_ff_pred8x8l_down_right_8_mmxext
#define ff_mpeg_draw_horiz_band liteav_ff_mpeg_draw_horiz_band
#define ff_hevc_diag_scan8x8_x liteav_ff_hevc_diag_scan8x8_x
#define ff_hevc_diag_scan8x8_y liteav_ff_hevc_diag_scan8x8_y
#define ff_amf_write_null liteav_ff_amf_write_null
#define ff_avg_h264_qpel16_mc21_neon liteav_ff_avg_h264_qpel16_mc21_neon
#define rgb32tobgr24 liteav_rgb32tobgr24
#define ff_amf_read_number liteav_ff_amf_read_number
#define ff_h264_idct_add16intra_8_c liteav_ff_h264_idct_add16intra_8_c
#define avio_skip liteav_avio_skip
#define ff_w4_min_w6_lo liteav_ff_w4_min_w6_lo
#define av_probe_input_buffer liteav_av_probe_input_buffer
#define ff_draw_supported_pixel_formats liteav_ff_draw_supported_pixel_formats
#define ff_ac3_muxer liteav_ff_ac3_muxer
#define ff_hevc_reset_sei liteav_ff_hevc_reset_sei
#define ff_h264_idct_add_12_c liteav_ff_h264_idct_add_12_c
#define ff_mp4_muxer liteav_ff_mp4_muxer
#define ff_pack_8ch_float_to_float_a_sse2 liteav_ff_pack_8ch_float_to_float_a_sse2
#define ff_videotoolbox_hvcc_extradata_create liteav_ff_videotoolbox_hvcc_extradata_create
#define ff_hevc_end_of_slice_flag_decode liteav_ff_hevc_end_of_slice_flag_decode
#define ff_frame_pool_video_init liteav_ff_frame_pool_video_init
#define ff_h264_idct_add_14_c liteav_ff_h264_idct_add_14_c
#define avcodec_pix_fmt_to_codec_tag liteav_avcodec_pix_fmt_to_codec_tag
#define av_dovi_alloc liteav_av_dovi_alloc
#define av_copy_packet liteav_av_copy_packet
#define ff_h264_v_loop_filter_chroma_neon liteav_ff_h264_v_loop_filter_chroma_neon
#define av_opt_find liteav_av_opt_find
#define av_write_uncoded_frame liteav_av_write_uncoded_frame
#define ff_get_chomp_line liteav_ff_get_chomp_line
#define swr_set_matrix liteav_swr_set_matrix
#define ff_listen_bind liteav_ff_listen_bind
#define av_thread_message_queue_set_free_func liteav_av_thread_message_queue_set_free_func
#define av_opt_query_ranges liteav_av_opt_query_ranges
#define sws_addVec liteav_sws_addVec
#define av_hwdevice_ctx_init liteav_av_hwdevice_ctx_init
#define ff_pack_8ch_int32_to_float_u_avx liteav_ff_pack_8ch_int32_to_float_u_avx
#define av_parse_cpu_caps liteav_av_parse_cpu_caps
#define av_mod_i liteav_av_mod_i
#define avfilter_get_matrix liteav_avfilter_get_matrix
#define ff_id3v2_tags liteav_ff_id3v2_tags
#define avpriv_mpa_freq_tab liteav_avpriv_mpa_freq_tab
#define av_frame_get_pkt_duration liteav_av_frame_get_pkt_duration
#define ff_emulated_edge_mc_8 liteav_ff_emulated_edge_mc_8
#define ff_mpeg4_y_dc_scale_table liteav_ff_mpeg4_y_dc_scale_table
#define avpriv_pix_fmt_bps_mov liteav_avpriv_pix_fmt_bps_mov
#define ff_outlink_get_status liteav_ff_outlink_get_status
#define ff_sws_alphablendaway liteav_ff_sws_alphablendaway
#define ff_avg_pixels16_sse2 liteav_ff_avg_pixels16_sse2
#define ff_ebur128_loudness_range liteav_ff_ebur128_loudness_range
#define ff_h263_mbtype_b_tab liteav_ff_h263_mbtype_b_tab
#define av_image_get_linesize liteav_av_image_get_linesize
#define ff_cos_16_fixed liteav_ff_cos_16_fixed
#define ff_h264_i_mb_type_info liteav_ff_h264_i_mb_type_info
#define ff_h264_decode_mb_cabac liteav_ff_h264_decode_mb_cabac
#define ff_imdct_half_c liteav_ff_imdct_half_c
#define ff_h264_dequant8_coeff_init liteav_ff_h264_dequant8_coeff_init
#define ff_smil_extract_next_text_chunk liteav_ff_smil_extract_next_text_chunk
#define ff_mpeg4_init_direct_mv liteav_ff_mpeg4_init_direct_mv
#define ff_id3v2_parse_priv_dict liteav_ff_id3v2_parse_priv_dict
#define av_tree_find liteav_av_tree_find
#define av_calloc liteav_av_calloc
#define ff_h264_idct_add8_422_14_c liteav_ff_h264_idct_add8_422_14_c
#define yyset_in liteav_yyset_in
#define av_pix_fmt_get_chroma_sub_sample liteav_av_pix_fmt_get_chroma_sub_sample
#define av_murmur3_final liteav_av_murmur3_final
#define av_frame_get_channel_layout liteav_av_frame_get_channel_layout
#define ff_pack_6ch_float_to_float_a_mmx liteav_ff_pack_6ch_float_to_float_a_mmx
#define av_fft_calc liteav_av_fft_calc
#define ff_init_2d_vlc_rl liteav_ff_init_2d_vlc_rl
#define ff_hevc_put_qpel_uw_h1v1_neon_8 liteav_ff_hevc_put_qpel_uw_h1v1_neon_8
#define ff_reshuffle_raw_rgb liteav_ff_reshuffle_raw_rgb
#define ff_hevc_put_epel_uw_pixels_w12_neon_8 liteav_ff_hevc_put_epel_uw_pixels_w12_neon_8
#define av_get_token liteav_av_get_token
#define ff_videodsp_init_aarch64 liteav_ff_videodsp_init_aarch64
#define ff_vf_hflip liteav_ff_vf_hflip
#define ff_hevc_prev_intra_luma_pred_flag_decode liteav_ff_hevc_prev_intra_luma_pred_flag_decode
#define av_frame_get_pkt_pos liteav_av_frame_get_pkt_pos
#define ff_default_query_formats liteav_ff_default_query_formats
#define ff_h264_idct8_dc_add_8_c liteav_ff_h264_idct8_dc_add_8_c
#define av_packet_free_side_data liteav_av_packet_free_side_data
#define ff_avg_h264_qpel16_mc30_10_sse2_cache64 liteav_ff_avg_h264_qpel16_mc30_10_sse2_cache64
#define ff_interleaved_peek liteav_ff_interleaved_peek
#define ff_hevc_hls_mvd_coding liteav_ff_hevc_hls_mvd_coding
#define ff_avg_h264_qpel8_mc00_neon liteav_ff_avg_h264_qpel8_mc00_neon
#define ff_rtmp_packet_create liteav_ff_rtmp_packet_create
#define av_expr_eval liteav_av_expr_eval
#define ff_pd_65535 liteav_ff_pd_65535
#define ff_pred16x16_128_dc_neon liteav_ff_pred16x16_128_dc_neon
#define ff_mpeg12_find_best_frame_rate liteav_ff_mpeg12_find_best_frame_rate
#define ff_hevc_put_qpel_uw_weight_v3_neon_8 liteav_ff_hevc_put_qpel_uw_weight_v3_neon_8
#define av_bsf_receive_packet liteav_av_bsf_receive_packet
#define ff_simple_idct_int16_8bit liteav_ff_simple_idct_int16_8bit
#define ff_rtmp_packet_dump liteav_ff_rtmp_packet_dump
#define ff_pack_8ch_float_to_int32_a_sse2 liteav_ff_pack_8ch_float_to_int32_a_sse2
#define ff_pack_6ch_float_to_float_u_sse liteav_ff_pack_6ch_float_to_float_u_sse
#define av_frame_side_data_name liteav_av_frame_side_data_name
#define ff_deblock_h_luma_8_avx liteav_ff_deblock_h_luma_8_avx
#define ff_pred8x8_horizontal_8_mmx liteav_ff_pred8x8_horizontal_8_mmx
#define ff_hevc_put_qpel_uw_weight_h2_neon_8 liteav_ff_hevc_put_qpel_uw_weight_h2_neon_8
#define ff_side_data_set_encoder_stats liteav_ff_side_data_set_encoder_stats
#define av_samples_fill_arrays liteav_av_samples_fill_arrays
#define ff_nv12_to_argb_neon liteav_ff_nv12_to_argb_neon
#define ff_put_h264_qpel4_v_lowpass_mmxext liteav_ff_put_h264_qpel4_v_lowpass_mmxext
#define ff_get_line liteav_ff_get_line
#define ff_simple_idct_put_int32_10bit liteav_ff_simple_idct_put_int32_10bit
#define av_audio_fifo_space liteav_av_audio_fifo_space
#define ff_hevc_videotoolbox_hwaccel liteav_ff_hevc_videotoolbox_hwaccel
#define ff_sws_rgb2rgb_init liteav_ff_sws_rgb2rgb_init
#define ff_vsink_buffer liteav_ff_vsink_buffer
#define av_iformat_next liteav_av_iformat_next
#define ff_hevc_pred_mode_decode liteav_ff_hevc_pred_mode_decode
#define av_fast_mallocz liteav_av_fast_mallocz
#define ff_deblock_h_chroma422_10_sse2 liteav_ff_deblock_h_chroma422_10_sse2
#define avio_flush liteav_avio_flush
#define av_frame_ref liteav_av_frame_ref
#define ff_hwframe_map_replace liteav_ff_hwframe_map_replace
#define ff_deblock_h_chroma422_8_avx liteav_ff_deblock_h_chroma422_8_avx
#define ff_hevc_put_pel_uw_pixels_w32_neon_8_asm liteav_ff_hevc_put_pel_uw_pixels_w32_neon_8_asm
#define yuv422ptoyuy2 liteav_yuv422ptoyuy2
#define ff_hevc_idct_32x32_dc_neon_8 liteav_ff_hevc_idct_32x32_dc_neon_8
#define yy_create_buffer liteav_yy_create_buffer
#define ff_hevc_add_residual_8x8_neon_8 liteav_ff_hevc_add_residual_8x8_neon_8
#define av_parser_close liteav_av_parser_close
#define av_buffer_create liteav_av_buffer_create
#define ff_pred4x4_vertical_left_10_avx liteav_ff_pred4x4_vertical_left_10_avx
#define swr_get_delay liteav_swr_get_delay
#define ff_jpeg_fdct_islow_10 liteav_ff_jpeg_fdct_islow_10
#define ff_h264_idct8_add_8_c liteav_ff_h264_idct8_add_8_c
#define av_frame_get_qp_table liteav_av_frame_get_qp_table
#define avpicture_layout liteav_avpicture_layout
#define ff_deblock_h_chroma_8_avx liteav_ff_deblock_h_chroma_8_avx
#define av_packet_merge_side_data liteav_av_packet_merge_side_data
#define ff_get_buffer liteav_ff_get_buffer
#define av_fft_permute liteav_av_fft_permute
#define av_realloc_array liteav_av_realloc_array
#define ff_h264_chroma_dc_dequant_idct_9_c liteav_ff_h264_chroma_dc_dequant_idct_9_c
#define ff_fetch_timestamp liteav_ff_fetch_timestamp
#define av_buffer_pool_uninit liteav_av_buffer_pool_uninit
#define ff_set_common_samplerates liteav_ff_set_common_samplerates
#define avio_get_dyn_buf liteav_avio_get_dyn_buf
#define ff_put_bmp_header liteav_ff_put_bmp_header
#define av_fifo_alloc liteav_av_fifo_alloc
#define ff_aac_pow34sf_tab liteav_ff_aac_pow34sf_tab
#define ff_float_to_int32_a_sse2 liteav_ff_float_to_int32_a_sse2
#define ff_deblock_h_chroma422_10_avx liteav_ff_deblock_h_chroma422_10_avx
#define ff_hevc_put_pixels_w32_w48_w64_neon_8 liteav_ff_hevc_put_pixels_w32_w48_w64_neon_8
#define ff_hevc_frame_nb_refs liteav_ff_hevc_frame_nb_refs
#define yyset_out liteav_yyset_out
#define ff_put_h264_qpel8_mc30_10_ssse3_cache64 liteav_ff_put_h264_qpel8_mc30_10_ssse3_cache64
#define av_aes_ctr_set_random_iv liteav_av_aes_ctr_set_random_iv
#define ff_hevc_put_qpel_h3v2_neon_8 liteav_ff_hevc_put_qpel_h3v2_neon_8
#define av_tree_insert liteav_av_tree_insert
#define ff_avg_pixels4_l2_shift5_mmxext liteav_ff_avg_pixels4_l2_shift5_mmxext
#define ff_put_pixels8_x2_neon liteav_ff_put_pixels8_x2_neon
#define ff_mpegts_muxer liteav_ff_mpegts_muxer
#define ff_put_h264_qpel8_mc02_neon liteav_ff_put_h264_qpel8_mc02_neon
#define shuffle_bytes_3012 liteav_shuffle_bytes_3012
#define ff_h263_parser liteav_ff_h263_parser
#define av_dynarray_add liteav_av_dynarray_add
#define ff_sine_2048_fixed liteav_ff_sine_2048_fixed
#define av_lfg_init_from_data liteav_av_lfg_init_from_data
#define av_hmac_alloc liteav_av_hmac_alloc
#define avpriv_mpeg4audio_get_config liteav_avpriv_mpeg4audio_get_config
#define av_get_pix_fmt_string liteav_av_get_pix_fmt_string
#define ff_hevc_slice_rpl liteav_ff_hevc_slice_rpl
#define ff_h264_idct_dc_add_9_c liteav_ff_h264_idct_dc_add_9_c
#define ff_get_qtpalette liteav_ff_get_qtpalette
#define av_aes_init liteav_av_aes_init
#define ff_avg_h264_qpel16_mc22_neon liteav_ff_avg_h264_qpel16_mc22_neon
#define ff_avg_pixels4_mmxext liteav_ff_avg_pixels4_mmxext
#define ff_put_pixels8_y2_no_rnd_neon liteav_ff_put_pixels8_y2_no_rnd_neon
#define ff_pred4x4_vertical_left_8_mmxext liteav_ff_pred4x4_vertical_left_8_mmxext
#define ff_put_qpel8_mc32_old_c liteav_ff_put_qpel8_mc32_old_c
#define ff_hls_protocol liteav_ff_hls_protocol
#define av_get_pix_fmt liteav_av_get_pix_fmt
#define ff_bsf_get_packet liteav_ff_bsf_get_packet
#define avfilter_get_class liteav_avfilter_get_class
#define ff_h264chroma_init_x86 liteav_ff_h264chroma_init_x86
#define ff_mpv_report_decode_progress liteav_ff_mpv_report_decode_progress
#define yv12touyvy liteav_yv12touyvy
#define ff_put_h264_qpel8_mc33_10_sse2 liteav_ff_put_h264_qpel8_mc33_10_sse2
#define ff_frame_pool_get liteav_ff_frame_pool_get
#define ff_h264_direct_ref_list_init liteav_ff_h264_direct_ref_list_init
#define ff_rl_init liteav_ff_rl_init
#define ff_hevc_add_residual_16x16_neon_8 liteav_ff_hevc_add_residual_16x16_neon_8
#define av_encryption_init_info_alloc liteav_av_encryption_init_info_alloc
#define avfilter_pad_count liteav_avfilter_pad_count
#define ff_idctdsp_init_aarch64 liteav_ff_idctdsp_init_aarch64
#define ff_imdct36_float_avx liteav_ff_imdct36_float_avx
#define av_get_padded_bits_per_pixel liteav_av_get_padded_bits_per_pixel
#define av_ac3_parse_header liteav_av_ac3_parse_header
#define av_fifo_reset liteav_av_fifo_reset
#define ff_w4_min_w6_hi liteav_ff_w4_min_w6_hi
#define av_bitstream_filter_close liteav_av_bitstream_filter_close
#define avfilter_mul_matrix liteav_avfilter_mul_matrix
#define avcodec_descriptor_get_by_name liteav_avcodec_descriptor_get_by_name
#define ff_put_qpel16_mc13_old_c liteav_ff_put_qpel16_mc13_old_c
#define ff_put_h264_qpel8or16_hv1_lowpass_op_mmxext liteav_ff_put_h264_qpel8or16_hv1_lowpass_op_mmxext
#define yv12toyuy2 liteav_yv12toyuy2
#define ff_inter_vlc liteav_ff_inter_vlc
#define vlc_css_declarations_Delete liteav_vlc_css_declarations_Delete
#define ff_flacdsp_init_x86 liteav_ff_flacdsp_init_x86
#define ff_mov_get_channel_layout liteav_ff_mov_get_channel_layout
#define ff_pw_5 liteav_ff_pw_5
#define ff_deblock_h_luma_intra_8_sse2 liteav_ff_deblock_h_luma_intra_8_sse2
#define ff_hflip_init liteav_ff_hflip_init
#define ff_h264_idct_add8_8_c liteav_ff_h264_idct_add8_8_c
#define ff_pred16x16_horizontal_10_mmxext liteav_ff_pred16x16_horizontal_10_mmxext
#define ff_pd_8192 liteav_ff_pd_8192
#define ffio_open_whitelist liteav_ffio_open_whitelist
#define avio_feof liteav_avio_feof
#define ff_flv_demuxer liteav_ff_flv_demuxer
#define avio_rb64 liteav_avio_rb64
#define av_log_default_callback liteav_av_log_default_callback
#define ff_pred16x16_dc_8_ssse3 liteav_ff_pred16x16_dc_8_ssse3
#define ff_pred8x8l_top_dc_10_sse2 liteav_ff_pred8x8l_top_dc_10_sse2
#define av_max_alloc liteav_av_max_alloc
#define ff_put_qpel8_mc11_old_c liteav_ff_put_qpel8_mc11_old_c
#define ff_avg_h264_chroma_mc4_mmxext liteav_ff_avg_h264_chroma_mc4_mmxext
#define ff_mpeg4_resync_prefix liteav_ff_mpeg4_resync_prefix
#define ff_pred16x16_top_dc_10_sse2 liteav_ff_pred16x16_top_dc_10_sse2
#define swri_resample_dsp_init liteav_swri_resample_dsp_init
#define ff_avfilter_graph_update_heap liteav_ff_avfilter_graph_update_heap
#define ff_hevc_sao_offset_abs_decode liteav_ff_hevc_sao_offset_abs_decode
#define av_buffersrc_parameters_alloc liteav_av_buffersrc_parameters_alloc
#define av_gettime_relative_is_monotonic liteav_av_gettime_relative_is_monotonic
#define avpicture_get_size liteav_avpicture_get_size
#define avcodec_register_all liteav_avcodec_register_all
#define swri_audio_convert_alloc liteav_swri_audio_convert_alloc
#define avpriv_request_sample liteav_avpriv_request_sample
#define ff_put_h264_qpel8_mc31_10_sse2 liteav_ff_put_h264_qpel8_mc31_10_sse2
#define ff_hevc_inter_pred_idc_decode liteav_ff_hevc_inter_pred_idc_decode
#define ff_pw_9 liteav_ff_pw_9
#define ff_er_add_slice liteav_ff_er_add_slice
#define ff_pd_16 liteav_ff_pd_16
#define ff_unpack_2ch_float_to_int16_a_sse2 liteav_ff_unpack_2ch_float_to_int16_a_sse2
#define ff_subtitles_read_line liteav_ff_subtitles_read_line
#define av_strerror liteav_av_strerror
#define swr_drop_output liteav_swr_drop_output
#define avio_r8 liteav_avio_r8
#define sws_getIdentityVec liteav_sws_getIdentityVec
#define ff_put_qpel16_mc31_old_c liteav_ff_put_qpel16_mc31_old_c
#define av_audio_fifo_reset liteav_av_audio_fifo_reset
#define ff_pred16x16_plane_rv40_8_mmxext liteav_ff_pred16x16_plane_rv40_8_mmxext
#define sws_freeFilter liteav_sws_freeFilter
#define ff_startcode_find_candidate_c liteav_ff_startcode_find_candidate_c
#define vu9_to_vu12 liteav_vu9_to_vu12
#define ff_tls_deinit liteav_ff_tls_deinit
#define av_hash_get_name liteav_av_hash_get_name
#define ff_unpack_2ch_float_to_int32_a_sse2 liteav_ff_unpack_2ch_float_to_int32_a_sse2
#define avfilter_free liteav_avfilter_free
#define swr_set_compensation liteav_swr_set_compensation
#define planar2x liteav_planar2x
#define ff_aac_spectral_bits liteav_ff_aac_spectral_bits
#define ff_mpv_reconstruct_mb liteav_ff_mpv_reconstruct_mb
#define av_buffersink_get_type liteav_av_buffersink_get_type
#define ff_avg_pixels16_l2_mmxext liteav_ff_avg_pixels16_l2_mmxext
#define av_content_light_metadata_alloc liteav_av_content_light_metadata_alloc
#define av_get_sample_fmt liteav_av_get_sample_fmt
#define ff_hevc_put_qpel_uw_bi_h_neon_8 liteav_ff_hevc_put_qpel_uw_bi_h_neon_8
#define ff_id3v2_parse_priv liteav_ff_id3v2_parse_priv
#define ff_hevc_put_qpel_uw_bi_hv_neon_8 liteav_ff_hevc_put_qpel_uw_bi_hv_neon_8
#define ff_dither_8x8_128 liteav_ff_dither_8x8_128
#define ff_mpeg1_videotoolbox_hwaccel liteav_ff_mpeg1_videotoolbox_hwaccel
#define avio_rl64 liteav_avio_rl64
#define ff_isom_write_av1c liteav_ff_isom_write_av1c
#define sws_scaleVec liteav_sws_scaleVec
#define ff_isom_write_avcc liteav_ff_isom_write_avcc
#define ff_w1_plus_w5 liteav_ff_w1_plus_w5
#define ff_put_h264_qpel8or16_hv2_lowpass_op_mmxext liteav_ff_put_h264_qpel8or16_hv2_lowpass_op_mmxext
#define yyget_column liteav_yyget_column
#define ff_hevc_put_qpel_uw_pixels_w8_neon_8 liteav_ff_hevc_put_qpel_uw_pixels_w8_neon_8
#define ff_mpeg2_videotoolbox_hwaccel liteav_ff_mpeg2_videotoolbox_hwaccel
#define ff_ape_parse_tag liteav_ff_ape_parse_tag
#define ff_http_match_no_proxy liteav_ff_http_match_no_proxy
#define ff_h264_idct_dc_add_neon liteav_ff_h264_idct_dc_add_neon
#define ff_h264_idct_add8_422_10_sse2 liteav_ff_h264_idct_add8_422_10_sse2
#define ff_put_h264_qpel4_mc20_10_mmxext liteav_ff_put_h264_qpel4_mc20_10_mmxext
#define ff_cos_32768 liteav_ff_cos_32768
#define ff_h264_idct_add16_10_c liteav_ff_h264_idct_add16_10_c
#define av_interleaved_write_uncoded_frame liteav_av_interleaved_write_uncoded_frame
#define av_opt_set_dict2 liteav_av_opt_set_dict2
#define ff_h264_idct8_add_10_c liteav_ff_h264_idct8_add_10_c
#define ff_avg_vc1_chroma_mc8_nornd_mmxext liteav_ff_avg_vc1_chroma_mc8_nornd_mmxext
#define av_nearer_q liteav_av_nearer_q
#define ff_mpeg2_frame_rate_tab liteav_ff_mpeg2_frame_rate_tab
#define avio_write_marker liteav_avio_write_marker
#define av_spherical_alloc liteav_av_spherical_alloc
#define av_fft_init liteav_av_fft_init
#define ff_put_no_rnd_qpel8_mc33_old_c liteav_ff_put_no_rnd_qpel8_mc33_old_c
#define ff_http_averror liteav_ff_http_averror
#define ff_h264_idct_add8_neon liteav_ff_h264_idct_add8_neon
#define ff_put_h264_qpel8_h_lowpass_l2_ssse3 liteav_ff_put_h264_qpel8_h_lowpass_l2_ssse3
#define ff_decode_get_packet liteav_ff_decode_get_packet
#define ff_mp3on4float_decoder liteav_ff_mp3on4float_decoder
#define ff_avg_qpel16_mc33_old_c liteav_ff_avg_qpel16_mc33_old_c
#define avfilter_graph_parse_ptr liteav_avfilter_graph_parse_ptr
#define ff_interleave_packet_per_dts liteav_ff_interleave_packet_per_dts
#define ff_hevc_sao_band_w64_neon_8 liteav_ff_hevc_sao_band_w64_neon_8
#define ff_put_qpel16_mc11_old_c liteav_ff_put_qpel16_mc11_old_c
#define ff_frame_thread_init liteav_ff_frame_thread_init
#define ff_webvtt_demuxer liteav_ff_webvtt_demuxer
#define ff_float_to_int16_u_sse2 liteav_ff_float_to_int16_u_sse2
#define ff_avg_h264_qpel16_mc21_10_sse2 liteav_ff_avg_h264_qpel16_mc21_10_sse2
#define ff_avg_pixels8_mmxext liteav_ff_avg_pixels8_mmxext
#define ff_avg_h264_qpel4_mc03_10_mmxext liteav_ff_avg_h264_qpel4_mc03_10_mmxext
#define ff_hevc_pred_planar_8x8_neon_8 liteav_ff_hevc_pred_planar_8x8_neon_8
#define avfilter_sub_matrix liteav_avfilter_sub_matrix
#define rgb15tobgr24 liteav_rgb15tobgr24
#define ff_init_lls_x86 liteav_ff_init_lls_x86
#define av_get_packed_sample_fmt liteav_av_get_packed_sample_fmt
#define av_frame_set_pkt_pos liteav_av_frame_set_pkt_pos
#define ff_put_h264_qpel16_mc13_neon liteav_ff_put_h264_qpel16_mc13_neon
#define av_hash_names liteav_av_hash_names
#define ff_h263_v_loop_filter_mmx liteav_ff_h263_v_loop_filter_mmx
#define ff_qdm2_at_decoder liteav_ff_qdm2_at_decoder
#define ff_put_no_rnd_qpel16_mc12_old_c liteav_ff_put_no_rnd_qpel16_mc12_old_c
#define ff_avg_pixels8_neon liteav_ff_avg_pixels8_neon
#define ff_mp4_read_descr_len liteav_ff_mp4_read_descr_len
#define ff_decode_bsfs_uninit liteav_ff_decode_bsfs_uninit
#define ffio_realloc_buf liteav_ffio_realloc_buf
#define av_bmg_get liteav_av_bmg_get
#define av_dump_format liteav_av_dump_format
#define ff_thread_flush liteav_ff_thread_flush
#define ff_hevc_put_qpel_uw_v2_neon_8 liteav_ff_hevc_put_qpel_uw_v2_neon_8
#define ff_pixblockdsp_init_x86 liteav_ff_pixblockdsp_init_x86
#define rgb48tobgr64_nobswap liteav_rgb48tobgr64_nobswap
#define ff_mjpegenc_huffman_compute_bits liteav_ff_mjpegenc_huffman_compute_bits
#define ff_aac_codebook_vector_idx liteav_ff_aac_codebook_vector_idx
#define text_segment_chain_delete liteav_text_segment_chain_delete
#define yylex_init_extra liteav_yylex_init_extra
#define ff_avg_qpel8_mc11_old_c liteav_ff_avg_qpel8_mc11_old_c
#define ff_volume_init_x86 liteav_ff_volume_init_x86
#define ff_mpeg12_init_vlcs liteav_ff_mpeg12_init_vlcs
#define ff_w7_plus_w3_lo liteav_ff_w7_plus_w3_lo
#define av_md5_sum liteav_av_md5_sum
#define ff_pred4x4_horizontal_up_8_mmxext liteav_ff_pred4x4_horizontal_up_8_mmxext
#define ff_imdct_half_avx liteav_ff_imdct_half_avx
#define ff_h264_idct_add8_10_avx liteav_ff_h264_idct_add8_10_avx
#define av_aes_ctr_set_iv liteav_av_aes_ctr_set_iv
#define ff_print_debug_info liteav_ff_print_debug_info
#define ff_cos_2048 liteav_ff_cos_2048
#define ff_put_h264_qpel16_h_lowpass_l2_ssse3 liteav_ff_put_h264_qpel16_h_lowpass_l2_ssse3
#define ffurl_open liteav_ffurl_open
#define av_grow_packet liteav_av_grow_packet
#define avpriv_mpegts_parse_open liteav_avpriv_mpegts_parse_open
#define ff_list_bsf liteav_ff_list_bsf
#define ff_put_h264_qpel4_mc11_10_mmxext liteav_ff_put_h264_qpel4_mc11_10_mmxext
#define yyrestart liteav_yyrestart
#define ff_pred8x8_dc_neon liteav_ff_pred8x8_dc_neon
#define ff_isom_write_vpcc liteav_ff_isom_write_vpcc
#define ff_hevc_pred_planar_4x4_neon_8 liteav_ff_hevc_pred_planar_4x4_neon_8
#define ff_add_pixels_clamped_c liteav_ff_add_pixels_clamped_c
#define avio_wb32 liteav_avio_wb32
#define av_qsv_alloc_context liteav_av_qsv_alloc_context
#define ff_put_pixels_clamped_c liteav_ff_put_pixels_clamped_c
#define ff_mpeg4_studio_intra liteav_ff_mpeg4_studio_intra
#define av_write_image_line2 liteav_av_write_image_line2
#define av_vorbis_parse_reset liteav_av_vorbis_parse_reset
#define ff_pred4x4_vertical_right_8_mmxext liteav_ff_pred4x4_vertical_right_8_mmxext
#define ff_h264_decode_seq_parameter_set liteav_ff_h264_decode_seq_parameter_set
#define ff_swb_offset_128 liteav_ff_swb_offset_128
#define ff_pack_2ch_float_to_int32_a_sse2 liteav_ff_pack_2ch_float_to_int32_a_sse2
#define ffurl_close liteav_ffurl_close
#define ff_put_v liteav_ff_put_v
#define ff_swb_offset_120 liteav_ff_swb_offset_120
#define ff_avg_pixels16_neon liteav_ff_avg_pixels16_neon
#define ff_resample_common_apply_filter_x8_float_neon liteav_ff_resample_common_apply_filter_x8_float_neon
#define ff_pred8x8_plane_8_mmx liteav_ff_pred8x8_plane_8_mmx
#define av_new_packet liteav_av_new_packet
#define av_reallocp_array liteav_av_reallocp_array
#define yvu9_to_yuy2 liteav_yvu9_to_yuy2
#define sws_getConstVec liteav_sws_getConstVec
#define ff_pack_6ch_float_to_float_u_avx liteav_ff_pack_6ch_float_to_float_u_avx
#define ff_mpeg12_vlc_dc_lum_bits liteav_ff_mpeg12_vlc_dc_lum_bits
#define ff_init_mpadsp_tabs_float liteav_ff_init_mpadsp_tabs_float
#define ff_vf_vflip liteav_ff_vf_vflip
#define ff_avg_h264_qpel4_v_lowpass_mmxext liteav_ff_avg_h264_qpel4_v_lowpass_mmxext
#define av_ripemd_init liteav_av_ripemd_init
#define text_style_copy liteav_text_style_copy
#define ff_rtp_get_payload_type liteav_ff_rtp_get_payload_type
#define av_packet_from_data liteav_av_packet_from_data
#define ff_cos_2048_fixed liteav_ff_cos_2048_fixed
#define ff_sine_4096 liteav_ff_sine_4096
#define ff_aac_num_swb_960 liteav_ff_aac_num_swb_960
#define swri_resample_dsp_x86_init liteav_swri_resample_dsp_x86_init
#define ff_hevc_put_epel_uw_pixels_w48_neon_8 liteav_ff_hevc_put_epel_uw_pixels_w48_neon_8
#define ff_sine_windows liteav_ff_sine_windows
#define ff_put_pixels16_xy2_no_rnd_neon liteav_ff_put_pixels16_xy2_no_rnd_neon
#define ff_mov_close_hinting liteav_ff_mov_close_hinting
#define ff_decode_get_hw_frames_ctx liteav_ff_decode_get_hw_frames_ctx
#define ff_put_h264_qpel16_mc10_10_ssse3_cache64 liteav_ff_put_h264_qpel16_mc10_10_ssse3_cache64
#define av_opt_get_image_size liteav_av_opt_get_image_size
#define av_image_alloc liteav_av_image_alloc
#define ff_parse_close liteav_ff_parse_close
#define ff_h264_dequant8_coeff_init_scan liteav_ff_h264_dequant8_coeff_init_scan
#define ff_put_h264_qpel8_mc03_10_sse2 liteav_ff_put_h264_qpel8_mc03_10_sse2
#define ff_mpeg12_common_init liteav_ff_mpeg12_common_init
#define ff_pred8x8l_horizontal_down_8_ssse3 liteav_ff_pred8x8l_horizontal_down_8_ssse3
#define av_hwframe_map liteav_av_hwframe_map
#define ff_hevc_pred_planar_8x8_neon_8_1 liteav_ff_hevc_pred_planar_8x8_neon_8_1
#define ff_er_frame_end liteav_ff_er_frame_end
#define ff_amf_write_object_start liteav_ff_amf_write_object_start
#define ff_pred16x16_vertical_8_mmx liteav_ff_pred16x16_vertical_8_mmx
#define ff_h264_idct_add_8_avx liteav_ff_h264_idct_add_8_avx
#define av_aes_ctr_get_iv liteav_av_aes_ctr_get_iv
#define av_opt_child_class_next liteav_av_opt_child_class_next
#define ff_codec_movsubtitle_tags liteav_ff_codec_movsubtitle_tags
#define ff_mdct_calcw_c liteav_ff_mdct_calcw_c
#define rgb12to15 liteav_rgb12to15
#define ff_hevc_idct_8x8_dc_neon_8_asm liteav_ff_hevc_idct_8x8_dc_neon_8_asm
#define avcodec_get_hw_frames_parameters liteav_avcodec_get_hw_frames_parameters
#define ff_yuv422p_to_bgra_neon liteav_ff_yuv422p_to_bgra_neon
#define ff_unpack_2ch_int16_to_float_a_ssse3 liteav_ff_unpack_2ch_int16_to_float_a_ssse3
#define ff_deblock_v_luma_intra_10_sse2 liteav_ff_deblock_v_luma_intra_10_sse2
#define ff_avg_h264_qpel16_mc03_neon liteav_ff_avg_h264_qpel16_mc03_neon
#define yyset_extra liteav_yyset_extra
#define av_log_set_callback liteav_av_log_set_callback
#define ff_tlog_link liteav_ff_tlog_link
#define ff_h264_luma_dc_dequant_idct_sse2 liteav_ff_h264_luma_dc_dequant_idct_sse2
#define text_style_delete liteav_text_style_delete
#define ff_pred8x8l_down_left_10_avx liteav_ff_pred8x8l_down_left_10_avx
#define avcodec_dct_alloc liteav_avcodec_dct_alloc
#define ff_ebur128_destroy liteav_ff_ebur128_destroy
#define ff_int16_to_int32_u_mmx liteav_ff_int16_to_int32_u_mmx
#define ff_mpeg_update_thread_context liteav_ff_mpeg_update_thread_context
#define ff_id3v1_genre_str liteav_ff_id3v1_genre_str
#define av_adts_header_parse liteav_av_adts_header_parse
#define ff_h263_inter_MCBPC_code liteav_ff_h263_inter_MCBPC_code
#define ff_pack_6ch_float_to_float_a_sse liteav_ff_pack_6ch_float_to_float_a_sse
#define ff_butterflies_float_neon liteav_ff_butterflies_float_neon
#define ff_h264_biweight_16_sse2 liteav_ff_h264_biweight_16_sse2
#define avcodec_descriptor_get liteav_avcodec_descriptor_get
#define ff_put_h264_qpel8_mc11_neon liteav_ff_put_h264_qpel8_mc11_neon
#define av_cmp_i liteav_av_cmp_i
#define uyvytoyuv420 liteav_uyvytoyuv420
#define ff_pred4x4_vertical_vp8_8_mmxext liteav_ff_pred4x4_vertical_vp8_8_mmxext
#define ff_avg_qpel8_mc13_old_c liteav_ff_avg_qpel8_mc13_old_c
#define av_div_q liteav_av_div_q
#define ff_h263_pred_acdc liteav_ff_h263_pred_acdc
#define av_color_space_name liteav_av_color_space_name
#define ff_h263_videotoolbox_hwaccel liteav_ff_h263_videotoolbox_hwaccel
#define ff_mpa_decode_header liteav_ff_mpa_decode_header
#define ff_isom_write_hvcc liteav_ff_isom_write_hvcc
#define ff_put_pixels8x8_c liteav_ff_put_pixels8x8_c
#define ff_hevc_add_residual_4x4_neon_8 liteav_ff_hevc_add_residual_4x4_neon_8
#define ff_avg_pixels16_y2_neon liteav_ff_avg_pixels16_y2_neon
#define av_div_i liteav_av_div_i
#define ff_default_get_video_buffer liteav_ff_default_get_video_buffer
#define swri_oldapi_conv_fltp_to_s16_nch_neon liteav_swri_oldapi_conv_fltp_to_s16_nch_neon
#define ff_put_h264_qpel16_mc23_neon liteav_ff_put_h264_qpel16_mc23_neon
#define ff_eac3_demuxer liteav_ff_eac3_demuxer
#define ff_mpeg4_get_video_packet_prefix_length liteav_ff_mpeg4_get_video_packet_prefix_length
#define yuv422ptouyvy liteav_yuv422ptouyvy
#define ff_simple_idct12_avx liteav_ff_simple_idct12_avx
#define ff_unpack_2ch_int16_to_int32_u_sse2 liteav_ff_unpack_2ch_int16_to_int32_u_sse2
#define ff_pred8x8_0lt_dc_neon liteav_ff_pred8x8_0lt_dc_neon
#define ff_inlink_check_available_frame liteav_ff_inlink_check_available_frame
#define ff_mpa_quant_steps liteav_ff_mpa_quant_steps
#define ff_thread_can_start_frame liteav_ff_thread_can_start_frame
#define ff_h264_filter_mb_fast liteav_ff_h264_filter_mb_fast
#define av_hash_final_hex liteav_av_hash_final_hex
#define ff_put_h264_qpel16_mc13_10_sse2 liteav_ff_put_h264_qpel16_mc13_10_sse2
#define ffio_set_buf_size liteav_ffio_set_buf_size
#define av_timecode_get_smpte_from_framenum liteav_av_timecode_get_smpte_from_framenum
#define swri_audio_convert_free liteav_swri_audio_convert_free
#define ff_h264_idct_add16intra_12_c liteav_ff_h264_idct_add16intra_12_c
#define ff_metadata_conv liteav_ff_metadata_conv
#define ffurl_get_file_handle liteav_ffurl_get_file_handle
#define ff_put_h264_qpel16_mc11_neon liteav_ff_put_h264_qpel16_mc11_neon
#define ff_h264_golomb_to_inter_cbp liteav_ff_h264_golomb_to_inter_cbp
#define ff_mpeg_unref_picture liteav_ff_mpeg_unref_picture
#define ff_imdct36_blocks_fixed liteav_ff_imdct36_blocks_fixed
#define ff_avg_h264_qpel8_mc11_neon liteav_ff_avg_h264_qpel8_mc11_neon
#define ff_h264_idct_add16intra_14_c liteav_ff_h264_idct_add16intra_14_c
#define ff_cbrt_tableinit liteav_ff_cbrt_tableinit
#define ff_mpeg4_pred_ac liteav_ff_mpeg4_pred_ac
#define ff_h264_weight_16_mmxext liteav_ff_h264_weight_16_mmxext
#define ff_hevc_put_epel_uw_bi_v_neon_8 liteav_ff_hevc_put_epel_uw_bi_v_neon_8
#define ff_h264_idct8_add4_10_sse2 liteav_ff_h264_idct8_add4_10_sse2
#define vlc_css_expression_New liteav_vlc_css_expression_New
#define ff_hevc_ps_uninit liteav_ff_hevc_ps_uninit
#define ff_four_imdct36_float_avx liteav_ff_four_imdct36_float_avx
#define ff_hevc_pred_angular_32x32_v_neon_8 liteav_ff_hevc_pred_angular_32x32_v_neon_8
#define av_mdct_init liteav_av_mdct_init
#define ff_put_h264_qpel8or16_hv2_lowpass_ssse3 liteav_ff_put_h264_qpel8or16_hv2_lowpass_ssse3
#define ff_pd_32 liteav_ff_pd_32
#define ff_mpa_l2_select_table liteav_ff_mpa_l2_select_table
#define ff_frame_pool_get_video_config liteav_ff_frame_pool_get_video_config
#define ff_hevc_cu_chroma_qp_offset_idx liteav_ff_hevc_cu_chroma_qp_offset_idx
#define ff_http_init_auth_state liteav_ff_http_init_auth_state
#define sws_freeContext liteav_sws_freeContext
#define av_probe_input_format liteav_av_probe_input_format
#define vlc_css_parser_ParseBytes liteav_vlc_css_parser_ParseBytes
#define av_strireplace liteav_av_strireplace
#define ff_h264_luma_dc_dequant_idct_14_c liteav_ff_h264_luma_dc_dequant_idct_14_c
#define ff_put_h264_qpel4_mc03_10_mmxext liteav_ff_put_h264_qpel4_mc03_10_mmxext
#define avcodec_get_chroma_sub_sample liteav_avcodec_get_chroma_sub_sample
#define av_vlog liteav_av_vlog
#define ff_avg_h264_qpel16_mc00_10_sse2 liteav_ff_avg_h264_qpel16_mc00_10_sse2
#define swr_get_out_samples liteav_swr_get_out_samples
#define ff_choose_timebase liteav_ff_choose_timebase
#define av_match_name liteav_av_match_name
#define ff_rtmp_packet_read_internal liteav_ff_rtmp_packet_read_internal
#define sws_setColorspaceDetails liteav_sws_setColorspaceDetails
#define ff_pred8x8l_horizontal_down_8_mmxext liteav_ff_pred8x8l_horizontal_down_8_mmxext
#define av_opt_eval_int64 liteav_av_opt_eval_int64
#define ff_w7_min_w5 liteav_ff_w7_min_w5
#define ff_put_h264_qpel4_mc12_10_mmxext liteav_ff_put_h264_qpel4_mc12_10_mmxext
#define av_buffersink_get_h liteav_av_buffersink_get_h
#define av_abuffersink_params_alloc liteav_av_abuffersink_params_alloc
#define avio_put_str liteav_avio_put_str
#define sws_isSupportedOutput liteav_sws_isSupportedOutput
#define ff_ps_hybrid_analysis_sse liteav_ff_ps_hybrid_analysis_sse
#define ff_h264_field_end liteav_ff_h264_field_end
#define ff_hevc_put_pixels_w16_neon_8_asm liteav_ff_hevc_put_pixels_w16_neon_8_asm
#define yyparse liteav_yyparse
#define av_sha512_update liteav_av_sha512_update
#define av_buffersink_get_w liteav_av_buffersink_get_w
#define av_vbprintf liteav_av_vbprintf
#define av_image_fill_linesizes liteav_av_image_fill_linesizes
#define ff_deblock_h_luma_mbaff_8_sse2 liteav_ff_deblock_h_luma_mbaff_8_sse2
#define avcodec_find_encoder liteav_avcodec_find_encoder
#define av_frame_get_pkt_size liteav_av_frame_get_pkt_size
#define yyfree liteav_yyfree
#define ff_hevc_output_frame liteav_ff_hevc_output_frame
#define ff_avg_h264_qpel8or16_hv1_lowpass_op_mmxext liteav_ff_avg_h264_qpel8or16_hv1_lowpass_op_mmxext
#define ff_avg_qpel8_mc12_old_c liteav_ff_avg_qpel8_mc12_old_c
#define ff_ac3_frame_size_tab liteav_ff_ac3_frame_size_tab
#define ff_init_desc_hscale liteav_ff_init_desc_hscale
#define ff_dct_init liteav_ff_dct_init
#define ff_af_loudnorm liteav_ff_af_loudnorm
#define ff_ps_mul_pair_single_sse liteav_ff_ps_mul_pair_single_sse
#define ff_aac_latm_parser liteav_ff_aac_latm_parser
#define ff_h264_luma_dc_dequant_idct_10_c liteav_ff_h264_luma_dc_dequant_idct_10_c
#define avio_open_dyn_buf liteav_avio_open_dyn_buf
#define avcodec_get_pix_fmt_loss liteav_avcodec_get_pix_fmt_loss
#define sws_getCoefficients liteav_sws_getCoefficients
#define ff_merge_samplerates liteav_ff_merge_samplerates
#define avfilter_graph_parse liteav_avfilter_graph_parse
#define sws_cloneVec liteav_sws_cloneVec
#define ff_sbr_hf_apply_noise_3_neon liteav_ff_sbr_hf_apply_noise_3_neon
#define ff_parse_pixel_format liteav_ff_parse_pixel_format
#define ff_h264_alloc_tables liteav_ff_h264_alloc_tables
#define ff_put_h264_qpel8_mc22_10_sse2 liteav_ff_put_h264_qpel8_mc22_10_sse2
#define ff_h264_luma_dc_dequant_idct_12_c liteav_ff_h264_luma_dc_dequant_idct_12_c
#define av_rc4_init liteav_av_rc4_init
#define ff_network_wait_fd_timeout liteav_ff_network_wait_fd_timeout
#define ff_nv12_to_abgr_neon liteav_ff_nv12_to_abgr_neon
#define ff_hevc_put_epel_uw_h_neon_8 liteav_ff_hevc_put_epel_uw_h_neon_8
#define yylex_destroy liteav_yylex_destroy
#define sws_getCachedContext liteav_sws_getCachedContext
#define ff_avg_h264_qpel8_mc22_10_sse2 liteav_ff_avg_h264_qpel8_mc22_10_sse2
#define ffurl_size liteav_ffurl_size
#define swr_free liteav_swr_free
#define ff_simple_idct10_avx liteav_ff_simple_idct10_avx
#define ff_fft_calc_neon liteav_ff_fft_calc_neon
#define ff_rtmp_packet_read liteav_ff_rtmp_packet_read
#define ff_vorbiscomment_metadata_conv liteav_ff_vorbiscomment_metadata_conv
#define ff_asrc_abuffer liteav_ff_asrc_abuffer
#define ff_pw_4096 liteav_ff_pw_4096
#define ff_hevc_put_pel_uw_pixels_w8_neon_8_asm liteav_ff_hevc_put_pel_uw_pixels_w8_neon_8_asm
#define ff_h264_pred_direct_motion liteav_ff_h264_pred_direct_motion
#define ff_pw_4095 liteav_ff_pw_4095
#define av_aes_size liteav_av_aes_size
#define ff_get_audio_buffer liteav_ff_get_audio_buffer
#define ff_hevc_put_qpel_h2v2_neon_8 liteav_ff_hevc_put_qpel_h2v2_neon_8
#define ff_vorbis_stream_comment liteav_ff_vorbis_stream_comment
#define ff_hevc_put_pixels_w12_neon_8 liteav_ff_hevc_put_pixels_w12_neon_8
#define ff_pack_2ch_int16_to_int16_u_sse2 liteav_ff_pack_2ch_int16_to_int16_u_sse2
#define ff_put_no_rnd_qpel8_mc11_old_c liteav_ff_put_no_rnd_qpel8_mc11_old_c
#define ff_hevc_cabac_init liteav_ff_hevc_cabac_init
#define ff_h264_chroma422_dc_dequant_idct_9_c liteav_ff_h264_chroma422_dc_dequant_idct_9_c
#define av_frame_copy liteav_av_frame_copy
#define ff_codec_guid_get_id liteav_ff_codec_guid_get_id
#define ff_h263_decoder liteav_ff_h263_decoder
#define ff_h264_decode_extradata liteav_ff_h264_decode_extradata
#define ff_init_filters liteav_ff_init_filters
#define av_opt_get_double liteav_av_opt_get_double
#define ff_parse_sample_rate liteav_ff_parse_sample_rate
#define ff_ass_split_override_codes liteav_ff_ass_split_override_codes
#define ff_deblock_h_chroma422_8_sse2 liteav_ff_deblock_h_chroma422_8_sse2
#define ff_rtmp_protocol liteav_ff_rtmp_protocol
#define ff_hevc_decode_nal_pps liteav_ff_hevc_decode_nal_pps
#define ffurl_read liteav_ffurl_read
#define av_get_channel_name liteav_av_get_channel_name
#define ff_crop_tab liteav_ff_crop_tab
#define ff_mpeg4_decode_video_packet_header liteav_ff_mpeg4_decode_video_packet_header
#define av_hwdevice_get_hwframe_constraints liteav_av_hwdevice_get_hwframe_constraints
#define ff_hevc_put_pixels_w24_neon_8_asm liteav_ff_hevc_put_pixels_w24_neon_8_asm
#define ff_rdft_calc_neon liteav_ff_rdft_calc_neon
#define ff_filter_graph_remove_filter liteav_ff_filter_graph_remove_filter
#define ff_ac3_at_decoder liteav_ff_ac3_at_decoder
#define ff_put_h264_qpel8_mc20_10_sse2 liteav_ff_put_h264_qpel8_mc20_10_sse2
#define ff_hevc_put_pixels_w12_neon_8_asm liteav_ff_hevc_put_pixels_w12_neon_8_asm
#define ff_init_scantable_permutation liteav_ff_init_scantable_permutation
#define av_get_default_channel_layout liteav_av_get_default_channel_layout
#define ff_put_h264_qpel8_mc10_10_sse2_cache64 liteav_ff_put_h264_qpel8_mc10_10_sse2_cache64
#define avio_wb24 liteav_avio_wb24
#define av_display_rotation_get liteav_av_display_rotation_get
#define ff_make_format_list liteav_ff_make_format_list
#define ff_get_v_length liteav_ff_get_v_length
#define ff_filter_init_hw_frames liteav_ff_filter_init_hw_frames
#define ff_h264_muxer liteav_ff_h264_muxer
#define ff_unpack_6ch_float_to_int32_u_avx liteav_ff_unpack_6ch_float_to_int32_u_avx
#define av_color_transfer_name liteav_av_color_transfer_name
#define av_sha_alloc liteav_av_sha_alloc
#define ff_text_eof liteav_ff_text_eof
#define av_rc4_alloc liteav_av_rc4_alloc
#define text_style_duplicate liteav_text_style_duplicate
#define av_map_videotoolbox_format_from_pixfmt liteav_av_map_videotoolbox_format_from_pixfmt
#define ff_hevc_put_pixels_w64_neon_8_asm liteav_ff_hevc_put_pixels_w64_neon_8_asm
#define swri_resampler liteav_swri_resampler
#define ff_avg_h264_qpel4_mc23_10_mmxext liteav_ff_avg_h264_qpel4_mc23_10_mmxext
#define ff_h264_decode_ref_pic_marking liteav_ff_h264_decode_ref_pic_marking
#define ff_sws_init_swscale_aarch64 liteav_ff_sws_init_swscale_aarch64
#define ff_avg_rv40_chroma_mc8_mmxext liteav_ff_avg_rv40_chroma_mc8_mmxext
#define ff_put_h264_qpel8_mc21_neon liteav_ff_put_h264_qpel8_mc21_neon
#define ff_hevc_pred_init liteav_ff_hevc_pred_init
#define ff_sbrdsp_init_aarch64 liteav_ff_sbrdsp_init_aarch64
#define ff_put_h264_qpel16_mc31_10_sse2 liteav_ff_put_h264_qpel16_mc31_10_sse2
#define ff_avg_h264_qpel4_mc00_10_mmxext liteav_ff_avg_h264_qpel4_mc00_10_mmxext
#define ff_ps_hybrid_synthesis_deint_sse liteav_ff_ps_hybrid_synthesis_deint_sse
#define av_rdft_end liteav_av_rdft_end
#define ff_avg_pixels16_x2_no_rnd_neon liteav_ff_avg_pixels16_x2_no_rnd_neon
#define ff_sbr_hf_gen_neon liteav_ff_sbr_hf_gen_neon
#define av_packet_shrink_side_data liteav_av_packet_shrink_side_data
#define ff_inlink_evaluate_timeline_at_frame liteav_ff_inlink_evaluate_timeline_at_frame
#define ff_cbpc_b_tab liteav_ff_cbpc_b_tab
#define ff_avg_h264_chroma_mc4_10_mmxext liteav_ff_avg_h264_chroma_mc4_10_mmxext
#define ff_h263_decode_init_vlc liteav_ff_h263_decode_init_vlc
#define ff_pred8x8_vertical_10_sse2 liteav_ff_pred8x8_vertical_10_sse2
#define av_opt_eval_double liteav_av_opt_eval_double
#define ff_fdct_ifast liteav_ff_fdct_ifast
#define ff_h264_idct_add_10_sse2 liteav_ff_h264_idct_add_10_sse2
#define ff_vector_fmul_add_neon liteav_ff_vector_fmul_add_neon
#define ff_rtmps_protocol liteav_ff_rtmps_protocol
#define ff_mpeg12_mbPatTable liteav_ff_mpeg12_mbPatTable
#define av_bsf_get_by_name liteav_av_bsf_get_by_name
#define ff_default_get_audio_buffer liteav_ff_default_get_audio_buffer
#define ff_amf_write_field_name liteav_ff_amf_write_field_name
#define ff_yuv422p_to_abgr_neon liteav_ff_yuv422p_to_abgr_neon
#define ff_graph_thread_free liteav_ff_graph_thread_free
#define av_register_codec_parser liteav_av_register_codec_parser
#define ff_avg_h264_qpel8_mc10_10_ssse3_cache64 liteav_ff_avg_h264_qpel8_mc10_10_ssse3_cache64
#define av_image_fill_arrays liteav_av_image_fill_arrays
#define avfilter_all_channel_layouts liteav_avfilter_all_channel_layouts
#define av_log liteav_av_log
#define ff_network_close liteav_ff_network_close
#define av_pix_fmt_desc_next liteav_av_pix_fmt_desc_next
#define ff_vf_pad liteav_ff_vf_pad
#define av_timecode_check_frame_rate liteav_av_timecode_check_frame_rate
#define ff_mpeg4_rl_intra liteav_ff_mpeg4_rl_intra
#define ff_tls_open_underlying liteav_ff_tls_open_underlying
#define av_timecode_init_from_string liteav_av_timecode_init_from_string
#define ffio_read_indirect liteav_ffio_read_indirect
#define ff_h264_mb_sizes liteav_ff_h264_mb_sizes
#define ff_h263_pixel_aspect liteav_ff_h263_pixel_aspect
#define ff_pred8x8_top_dc_neon liteav_ff_pred8x8_top_dc_neon
#define ff_aac_kbd_long_960 liteav_ff_aac_kbd_long_960
#define ff_hevc_put_pixels_w6_neon_8 liteav_ff_hevc_put_pixels_w6_neon_8
#define ff_put_h264_qpel16_mc21_10_sse2 liteav_ff_put_h264_qpel16_mc21_10_sse2
#define ff_deblock_h_luma_8_sse2 liteav_ff_deblock_h_luma_8_sse2
#define ff_mpv_frame_start liteav_ff_mpv_frame_start
#define ff_avg_qpel16_mc32_old_c liteav_ff_avg_qpel16_mc32_old_c
#define av_opt_serialize liteav_av_opt_serialize
#define swr_convert_frame liteav_swr_convert_frame
#define ff_pack_8ch_int32_to_float_a_sse2 liteav_ff_pack_8ch_int32_to_float_a_sse2
#define ff_hevc_epel_filters liteav_ff_hevc_epel_filters
#define rgb12tobgr12 liteav_rgb12tobgr12
#define ff_alternate_horizontal_scan liteav_ff_alternate_horizontal_scan
#define av_image_check_size2 liteav_av_image_check_size2
#define ff_mpv_idct_init liteav_ff_mpv_idct_init
#define av_memdup liteav_av_memdup
#define ff_ac3_enc_channel_map liteav_ff_ac3_enc_channel_map
#define ff_amf_write_object_end liteav_ff_amf_write_object_end
#define av_opt_get_channel_layout liteav_av_opt_get_channel_layout
#define ff_hevc_luma_mv_mvp_mode liteav_ff_hevc_luma_mv_mvp_mode
#define av_opt_eval_flags liteav_av_opt_eval_flags
#define ff_sine_64_fixed liteav_ff_sine_64_fixed
#define av_opt_find2 liteav_av_opt_find2
#define ff_subtitles_queue_seek liteav_ff_subtitles_queue_seek
#define av_tea_crypt liteav_av_tea_crypt
#define ff_simple_idct_add_neon liteav_ff_simple_idct_add_neon
#define ff_put_h264_qpel16_mc22_10_sse2 liteav_ff_put_h264_qpel16_mc22_10_sse2
#define ff_float_to_int32_u_avx2 liteav_ff_float_to_int32_u_avx2
#define ff_hevc_put_qpel_h3v1_neon_8 liteav_ff_hevc_put_qpel_h3v1_neon_8
#define av_frame_set_sample_rate liteav_av_frame_set_sample_rate
#define ffio_open_null_buf liteav_ffio_open_null_buf
#define ff_ac3_bitrate_tab liteav_ff_ac3_bitrate_tab
#define ff_hpeldsp_init_x86 liteav_ff_hpeldsp_init_x86
#define ff_interleaved_ue_golomb_vlc_code liteav_ff_interleaved_ue_golomb_vlc_code
#define ff_ac3_sample_rate_tab liteav_ff_ac3_sample_rate_tab
#define ff_pred8x8_dc_10_sse2 liteav_ff_pred8x8_dc_10_sse2
#define sws_getDefaultFilter liteav_sws_getDefaultFilter
#define ff_shuffle_bytes_2103_mmxext liteav_ff_shuffle_bytes_2103_mmxext
#define ff_h264_biweight_16_ssse3 liteav_ff_h264_biweight_16_ssse3
#define ff_put_pixels16_neon liteav_ff_put_pixels16_neon
#define av_murmur3_update liteav_av_murmur3_update
#define av_get_channel_description liteav_av_get_channel_description
#define av_frame_move_ref liteav_av_frame_move_ref
#define av_opt_set_sample_fmt liteav_av_opt_set_sample_fmt
#define ff_mov_get_channel_layout_tag liteav_ff_mov_get_channel_layout_tag
#define ff_h264_slice_context_init liteav_ff_h264_slice_context_init
#define rgb32tobgr15 liteav_rgb32tobgr15
#define ff_shuffle_bytes_1230_ssse3 liteav_ff_shuffle_bytes_1230_ssse3
#define rgb32tobgr16 liteav_rgb32tobgr16
#define ff_pw_128 liteav_ff_pw_128
#define ff_mpeg4_static_rl_table_store liteav_ff_mpeg4_static_rl_table_store
#define ff_mpadsp_apply_window_float liteav_ff_mpadsp_apply_window_float
#define ffio_open2_wrapper liteav_ffio_open2_wrapper
#define ff_vector_fmul_vfp liteav_ff_vector_fmul_vfp
#define ff_hevc_put_qpel_uw_h2v1_neon_8 liteav_ff_hevc_put_qpel_uw_h2v1_neon_8
#define av_opt_child_next liteav_av_opt_child_next
#define av_encryption_info_add_side_data liteav_av_encryption_info_add_side_data
#define ffurl_handshake liteav_ffurl_handshake
#define av_probe_input_format3 liteav_av_probe_input_format3
#define av_image_fill_pointers liteav_av_image_fill_pointers
#define ff_pred8x8l_dc_8_mmxext liteav_ff_pred8x8l_dc_8_mmxext
#define ff_pred16x16_plane_h264_8_sse2 liteav_ff_pred16x16_plane_h264_8_sse2
#define av_buffersink_get_hw_frames_ctx liteav_av_buffersink_get_hw_frames_ctx
#define ff_put_h264_qpel16_mc31_neon liteav_ff_put_h264_qpel16_mc31_neon
#define ff_avg_qpel8_mc31_old_c liteav_ff_avg_qpel8_mc31_old_c
#define yy_scan_buffer liteav_yy_scan_buffer
#define ff_avg_h264_chroma_mc8_rnd_mmxext liteav_ff_avg_h264_chroma_mc8_rnd_mmxext
#define avpriv_tempfile liteav_avpriv_tempfile
#define av_camellia_alloc liteav_av_camellia_alloc
#define ff_avg_h264_qpel8_mc10_10_sse2 liteav_ff_avg_h264_qpel8_mc10_10_sse2
#define av_file_map liteav_av_file_map
#define av_encryption_info_alloc liteav_av_encryption_info_alloc
#define av_hmac_init liteav_av_hmac_init
#define av_hash_final liteav_av_hash_final
#define av_lfg_init liteav_av_lfg_init
#define avcodec_register liteav_avcodec_register
#define text_segment_delete liteav_text_segment_delete
#define ff_hevc_transform_16x16_neon_8_asm liteav_ff_hevc_transform_16x16_neon_8_asm
#define ff_mpadsp_apply_window_float_neon liteav_ff_mpadsp_apply_window_float_neon
#define ff_interleaved_golomb_vlc_len liteav_ff_interleaved_golomb_vlc_len
#define ff_hevc_decode_extradata liteav_ff_hevc_decode_extradata
#define ff_print_debug_info2 liteav_ff_print_debug_info2
#define av_opt_get_int liteav_av_opt_get_int
#define ff_hevc_put_qpel_h2v3_neon_8 liteav_ff_hevc_put_qpel_h2v3_neon_8
#define ff_hevc_put_pixels_w2_neon_8_asm liteav_ff_hevc_put_pixels_w2_neon_8_asm
#define av_opt_freep_ranges liteav_av_opt_freep_ranges
#define ff_avg_h264_qpel8_mc13_neon liteav_ff_avg_h264_qpel8_mc13_neon
#define avio_open liteav_avio_open
#define ff_h264_weight_8_10_sse2 liteav_ff_h264_weight_8_10_sse2
#define ff_h264_weight_8_10_sse4 liteav_ff_h264_weight_8_10_sse4
#define yyget_debug liteav_yyget_debug
#define av_write_frame liteav_av_write_frame
#define ff_hevc_put_qpel_hv_neon_8_wrapper liteav_ff_hevc_put_qpel_hv_neon_8_wrapper
#define avio_enum_protocols liteav_avio_enum_protocols
#define av_buffer_make_writable liteav_av_buffer_make_writable
#define ff_check_alignment liteav_ff_check_alignment
#define ff_put_pixels16_xy2_neon liteav_ff_put_pixels16_xy2_neon
#define ff_ebur128_loudness_window liteav_ff_ebur128_loudness_window
#define av_fifo_generic_peek_at liteav_av_fifo_generic_peek_at
#define ff_put_rv40_chroma_mc8_mmx liteav_ff_put_rv40_chroma_mc8_mmx
#define ff_h264_idct_add16intra_10_avx liteav_ff_h264_idct_add16intra_10_avx
#define ff_hevc_sao_offset_sign_decode liteav_ff_hevc_sao_offset_sign_decode
#define avio_context_free liteav_avio_context_free
#define ffio_open_dyn_packet_buf liteav_ffio_open_dyn_packet_buf
#define ff_avg_h264_qpel4_mc13_10_mmxext liteav_ff_avg_h264_qpel4_mc13_10_mmxext
#define ff_h264_parse_ref_count liteav_ff_h264_parse_ref_count
#define ff_init_scantable_permutation_x86 liteav_ff_init_scantable_permutation_x86
#define sws_freeVec liteav_sws_freeVec
#define ff_af_amix liteav_ff_af_amix
#define avpriv_ac3_parse_header liteav_avpriv_ac3_parse_header
#define ff_mp3adu_decoder liteav_ff_mp3adu_decoder
#define ff_deblock_h_chroma422_intra_8_sse2 liteav_ff_deblock_h_chroma422_intra_8_sse2
#define ff_interleave_add_packet liteav_ff_interleave_add_packet
#define ff_inlink_set_status liteav_ff_inlink_set_status
#define ff_cos_131072_fixed liteav_ff_cos_131072_fixed
#define av_compare_ts liteav_av_compare_ts
#define sws_getGaussianVec liteav_sws_getGaussianVec
#define ff_mov_read_stsd_entries liteav_ff_mov_read_stsd_entries
#define ff_pred8x8l_down_right_10_ssse3 liteav_ff_pred8x8l_down_right_10_ssse3
#define ff_psdsp_init_x86 liteav_ff_psdsp_init_x86
#define ff_hevc_put_qpel_v_neon_8_wrapper liteav_ff_hevc_put_qpel_v_neon_8_wrapper
#define ff_dct_init_x86 liteav_ff_dct_init_x86
#define ff_hevc_set_new_ref liteav_ff_hevc_set_new_ref
#define ff_fft_lut_init liteav_ff_fft_lut_init
#define av_packet_make_refcounted liteav_av_packet_make_refcounted
#define av_hmac_calc liteav_av_hmac_calc
#define av_dup_packet liteav_av_dup_packet
#define ff_swb_offset_960 liteav_ff_swb_offset_960
#define ff_id3v2_match liteav_ff_id3v2_match
#define ff_put_h264_qpel16_mc20_10_ssse3_cache64 liteav_ff_put_h264_qpel16_mc20_10_ssse3_cache64
#define ff_hevc_sao_edge_eo3_w32_neon_8 liteav_ff_hevc_sao_edge_eo3_w32_neon_8
#define av_dirname liteav_av_dirname
#define ff_cos_16384_fixed liteav_ff_cos_16384_fixed
#define ff_avg_h264_qpel8_mc30_10_ssse3_cache64 liteav_ff_avg_h264_qpel8_mc30_10_ssse3_cache64
#define avfilter_license liteav_avfilter_license
#define ff_pred4x4_tm_vp8_8_mmxext liteav_ff_pred4x4_tm_vp8_8_mmxext
#define av_bprintf liteav_av_bprintf
#define av_audio_fifo_size liteav_av_audio_fifo_size
#define ff_pred16x16_left_dc_neon liteav_ff_pred16x16_left_dc_neon
#define ff_mpadsp_init liteav_ff_mpadsp_init
#define ff_codec_movvideo_tags liteav_ff_codec_movvideo_tags
#define ff_videotoolbox_h264_decode_slice liteav_ff_videotoolbox_h264_decode_slice
#define ff_h264_sei_decode liteav_ff_h264_sei_decode
#define ff_videodsp_init_x86 liteav_ff_videodsp_init_x86
#define ff_h264_decoder liteav_ff_h264_decoder
#define ff_unpack_2ch_int16_to_float_a_sse2 liteav_ff_unpack_2ch_int16_to_float_a_sse2
#define ff_hevc_pred_init_aarch64 liteav_ff_hevc_pred_init_aarch64
#define yy_delete_buffer liteav_yy_delete_buffer
#define ff_avc_parse_nal_units_buf liteav_ff_avc_parse_nal_units_buf
#define ff_vorbis_channel_layout_offsets liteav_ff_vorbis_channel_layout_offsets
#define ff_avg_qpel16_mc31_old_c liteav_ff_avg_qpel16_mc31_old_c
#define avio_find_protocol_name liteav_avio_find_protocol_name
#define ff_mpeg4video_split liteav_ff_mpeg4video_split
#define ff_int32_to_int16_a_sse2 liteav_ff_int32_to_int16_a_sse2
#define av_opt_set_channel_layout liteav_av_opt_set_channel_layout
#define av_xtea_crypt liteav_av_xtea_crypt
#define ff_thread_decode_frame liteav_ff_thread_decode_frame
#define ff_avg_pixels4_l2_mmxext liteav_ff_avg_pixels4_l2_mmxext
#define av_opt_copy liteav_av_opt_copy
#define av_buffersink_get_frame liteav_av_buffersink_get_frame
#define ff_get_unscaled_swscale_aarch64 liteav_ff_get_unscaled_swscale_aarch64
#define ff_fft_offsets_lut liteav_ff_fft_offsets_lut
#define yyget_in liteav_yyget_in
#define ff_hevc_res_scale_sign_flag liteav_ff_hevc_res_scale_sign_flag
#define ff_sine_32_fixed liteav_ff_sine_32_fixed
#define avfilter_graph_create_filter liteav_avfilter_graph_create_filter
#define ff_formats_unref liteav_ff_formats_unref
#define ff_ac3_rematrix_band_tab liteav_ff_ac3_rematrix_band_tab
#define ff_mpeg1_dc_scale_table liteav_ff_mpeg1_dc_scale_table
#define ff_yuv420p_to_argb_neon liteav_ff_yuv420p_to_argb_neon
#define ff_ass_get_dialog liteav_ff_ass_get_dialog
#define ff_deblock_v_luma_intra_8_sse2 liteav_ff_deblock_v_luma_intra_8_sse2
#define ff_pred16x16_horizontal_8_ssse3 liteav_ff_pred16x16_horizontal_8_ssse3
#define avpriv_io_move liteav_avpriv_io_move
#define ff_videodsp_init liteav_ff_videodsp_init
#define ff_framequeue_peek liteav_ff_framequeue_peek
#define ff_sine_window_init liteav_ff_sine_window_init
#define av_sha_init liteav_av_sha_init
#define ff_mpeg4videodec_static_init liteav_ff_mpeg4videodec_static_init
#define av_camellia_crypt liteav_av_camellia_crypt
#define sws_isSupportedEndiannessConversion liteav_sws_isSupportedEndiannessConversion
#define ff_imdct_half_c_fixed liteav_ff_imdct_half_c_fixed
#define ff_mp3float_decoder liteav_ff_mp3float_decoder
#define ff_int32_to_int16_u_mmx liteav_ff_int32_to_int16_u_mmx
#define ff_h264_idct_dc_add_10_mmxext liteav_ff_h264_idct_dc_add_10_mmxext
#define ff_sine_120 liteav_ff_sine_120
#define av_read_image_line liteav_av_read_image_line
#define ff_faanidct_add liteav_ff_faanidct_add
#define ff_sine_128 liteav_ff_sine_128
#define sws_init_context liteav_sws_init_context
#define ff_avg_pixels16_mmx liteav_ff_avg_pixels16_mmx
#define ff_pred8x8l_horizontal_up_10_sse2 liteav_ff_pred8x8l_horizontal_up_10_sse2
#define ff_draw_round_to_sub liteav_ff_draw_round_to_sub
#define ff_intel_h263_decode_picture_header liteav_ff_intel_h263_decode_picture_header
#define avformat_alloc_output_context2 liteav_avformat_alloc_output_context2
#define ff_h264_draw_horiz_band liteav_ff_h264_draw_horiz_band
#define ffurl_seek liteav_ffurl_seek
#define av_mallocz_array liteav_av_mallocz_array
#define ff_cos_16384 liteav_ff_cos_16384
#define ff_hevc_put_qpel_v3_neon_8 liteav_ff_hevc_put_qpel_v3_neon_8
#define ff_avg_h264_qpel8_mc20_10_ssse3_cache64 liteav_ff_avg_h264_qpel8_mc20_10_ssse3_cache64
#define ff_update_link_current_pts liteav_ff_update_link_current_pts
#define av_frame_copy_props liteav_av_frame_copy_props
#define av_xtea_le_init liteav_av_xtea_le_init
#define ff_simple_idct248_put liteav_ff_simple_idct248_put
#define avpriv_align_put_bits liteav_avpriv_align_put_bits
#define ff_unpack_6ch_int32_to_float_a_sse2 liteav_ff_unpack_6ch_int32_to_float_a_sse2
#define ff_af_dynaudnorm liteav_ff_af_dynaudnorm
#define av_log_format_line liteav_av_log_format_line
#define vlc_css_unquoted liteav_vlc_css_unquoted
#define ff_put_h264_chroma_mc2_neon liteav_ff_put_h264_chroma_mc2_neon
#define ff_put_h264_qpel8_mc01_neon liteav_ff_put_h264_qpel8_mc01_neon
#define av_murmur3_init_seeded liteav_av_murmur3_init_seeded
#define av_samples_set_silence liteav_av_samples_set_silence
#define ff_inlink_consume_frame liteav_ff_inlink_consume_frame
#define ff_thread_get_buffer liteav_ff_thread_get_buffer
#define ff_ebur128_add_frames_int liteav_ff_ebur128_add_frames_int
#define ff_hevc_v_loop_filter_chroma_neon liteav_ff_hevc_v_loop_filter_chroma_neon
#define ff_vorbis_comment liteav_ff_vorbis_comment
#define avfilter_make_format64_list liteav_avfilter_make_format64_list
#define ff_pred8x8l_horizontal_10_avx liteav_ff_pred8x8l_horizontal_10_avx
#define av_parser_parse2 liteav_av_parser_parse2
#define ff_hevc_put_qpel_uw_pixels_w48_neon_8 liteav_ff_hevc_put_qpel_uw_pixels_w48_neon_8
#define ff_mp3on4_decoder liteav_ff_mp3on4_decoder
#define ff_hpeldsp_init liteav_ff_hpeldsp_init
#define ff_h264_idct_dc_add_10_c liteav_ff_h264_idct_dc_add_10_c
#define ff_imdct_half_sse liteav_ff_imdct_half_sse
#define ff_vf_scale2ref liteav_ff_vf_scale2ref
#define ff_aac_kbd_long_1024_fixed liteav_ff_aac_kbd_long_1024_fixed
#define ff_h264_idct_add16_9_c liteav_ff_h264_idct_add16_9_c
#define ff_pack_8ch_float_to_int32_u_avx liteav_ff_pack_8ch_float_to_int32_u_avx
#define ff_avg_h264_qpel8_mc03_10_sse2 liteav_ff_avg_h264_qpel8_mc03_10_sse2
#define ff_h264_idct_dc_add_12_c liteav_ff_h264_idct_dc_add_12_c
#define avio_seek liteav_avio_seek
#define av_rc4_crypt liteav_av_rc4_crypt
#define ff_h263_decode_picture_header liteav_ff_h263_decode_picture_header
#define ff_ps_hybrid_analysis_sse3 liteav_ff_ps_hybrid_analysis_sse3
#define av_murmur3_init liteav_av_murmur3_init
#define ff_mpadsp_apply_window_fixed liteav_ff_mpadsp_apply_window_fixed
#define ff_h264_idct_dc_add_14_c liteav_ff_h264_idct_dc_add_14_c
#define ff_ac3_bap_tab liteav_ff_ac3_bap_tab
#define ff_avg_h264_qpel8_h_lowpass_mmxext liteav_ff_avg_h264_qpel8_h_lowpass_mmxext
#define ff_mdct15_init_x86 liteav_ff_mdct15_init_x86
#define ff_mp4_parse_es_descr liteav_ff_mp4_parse_es_descr
#define ff_mp4_read_dec_config_descr liteav_ff_mp4_read_dec_config_descr
#define ff_sbrdsp_init liteav_ff_sbrdsp_init
#define ff_put_h264_chroma_mc8_10_sse2 liteav_ff_put_h264_chroma_mc8_10_sse2
#define ff_h264_sei_stereo_mode liteav_ff_h264_sei_stereo_mode
#define rgb16tobgr32 liteav_rgb16tobgr32
#define avpriv_pix_fmt_bps_avi liteav_avpriv_pix_fmt_bps_avi
#define av_utf8_decode liteav_av_utf8_decode
#define ff_avio_class liteav_ff_avio_class
#define ff_pack_8ch_float_to_float_u_avx liteav_ff_pack_8ch_float_to_float_u_avx
#define ff_hevc_idct_32x32_dc_neon_8_asm liteav_ff_hevc_idct_32x32_dc_neon_8_asm
#define ff_mpeg2_aspect liteav_ff_mpeg2_aspect
#define ff_avg_h264_qpel16_mc30_neon liteav_ff_avg_h264_qpel16_mc30_neon
#define av_opt_get_dict_val liteav_av_opt_get_dict_val
#define ff_h263_inter_MCBPC_bits liteav_ff_h263_inter_MCBPC_bits
#define ff_subtitles_queue_insert liteav_ff_subtitles_queue_insert
#define avcodec_descriptor_next liteav_avcodec_descriptor_next
#define ff_amr_nb_at_decoder liteav_ff_amr_nb_at_decoder
#define ff_h264_quant_rem6 liteav_ff_h264_quant_rem6
#define ff_mdct_calc_c_fixed_32 liteav_ff_mdct_calc_c_fixed_32
#define ff_connect_parallel liteav_ff_connect_parallel
#define ff_libfdk_aac_encoder liteav_ff_libfdk_aac_encoder
#define ff_w4_min_w2_hi liteav_ff_w4_min_w2_hi
#define ff_pw_512 liteav_ff_pw_512
#define avio_rb16 liteav_avio_rb16
#define ff_unpack_6ch_float_to_float_u_sse liteav_ff_unpack_6ch_float_to_float_u_sse
#define ff_deblock_v_chroma_10_sse2 liteav_ff_deblock_v_chroma_10_sse2
#define ff_copy_rectangle2 liteav_ff_copy_rectangle2
#define ff_mpa_enwindow liteav_ff_mpa_enwindow
#define ff_h264_dequant4_coeff_init liteav_ff_h264_dequant4_coeff_init
#define avio_open_dir liteav_avio_open_dir
#define ff_h264_pred_init liteav_ff_h264_pred_init
#define ff_hevc_mp4toannexb_bsf liteav_ff_hevc_mp4toannexb_bsf
#define ff_blend_rectangle liteav_ff_blend_rectangle
#define ff_avc_find_startcode liteav_ff_avc_find_startcode
#define ff_h264_idct_add16intra_8_sse2 liteav_ff_h264_idct_add16intra_8_sse2
#define ff_pred8x8l_horizontal_down_8_sse2 liteav_ff_pred8x8l_horizontal_down_8_sse2
#define ff_pred16x16_tm_vp8_8_sse2 liteav_ff_pred16x16_tm_vp8_8_sse2
#define ff_id3v2_4_metadata_conv liteav_ff_id3v2_4_metadata_conv
#define ff_ue_golomb_len liteav_ff_ue_golomb_len
#define ff_h264_b_sub_mb_type_info liteav_ff_h264_b_sub_mb_type_info
#define ff_rgb24toyv12_c liteav_ff_rgb24toyv12_c
#define sws_isSupportedInput liteav_sws_isSupportedInput
#define ff_resample_common_apply_filter_x4_float_neon liteav_ff_resample_common_apply_filter_x4_float_neon
#define avpriv_scalarproduct_float_c liteav_avpriv_scalarproduct_float_c
#define swri_rematrix liteav_swri_rematrix
#define av_packet_free liteav_av_packet_free
#define ff_deblock_h_chroma_intra_8_avx liteav_ff_deblock_h_chroma_intra_8_avx
#define ff_framequeue_free liteav_ff_framequeue_free
#define ff_pack_2ch_int32_to_int16_a_sse2 liteav_ff_pack_2ch_int32_to_int16_a_sse2
#define ff_aac_eld_window_480_fixed liteav_ff_aac_eld_window_480_fixed
#define av_mediacodec_default_free liteav_av_mediacodec_default_free
#define av_strtok liteav_av_strtok
#define ff_pred8x8l_horizontal_8_mmxext liteav_ff_pred8x8l_horizontal_8_mmxext
#define ff_avs3_profiles liteav_ff_avs3_profiles
#define ff_w5_plus_w7 liteav_ff_w5_plus_w7
#define ff_nv12_to_bgra_neon liteav_ff_nv12_to_bgra_neon
#define ff_vorbiscomment_write liteav_ff_vorbiscomment_write
#define ff_hevc_put_qpel_uw_h3_neon_8 liteav_ff_hevc_put_qpel_uw_h3_neon_8
#define ff_avg_h264_qpel16_mc20_neon liteav_ff_avg_h264_qpel16_mc20_neon
#define av_xtea_init liteav_av_xtea_init
#define ff_pred8x8_vertical_8_mmx liteav_ff_pred8x8_vertical_8_mmx
#define ff_deblock_h_luma_intra_10_avx liteav_ff_deblock_h_luma_intra_10_avx
#define ff_hevcdsp_init_neon_asm liteav_ff_hevcdsp_init_neon_asm
#define avfilter_configuration liteav_avfilter_configuration
#define ff_w7_plus_w3_hi liteav_ff_w7_plus_w3_hi
#define ff_hevc_put_epel_uw_pixels_w16_neon_8 liteav_ff_hevc_put_epel_uw_pixels_w16_neon_8
#define ff_hevc_transform_16x16_neon_8 liteav_ff_hevc_transform_16x16_neon_8
#define av_frame_set_pkt_duration liteav_av_frame_set_pkt_duration
#define ff_hevc_part_mode_decode liteav_ff_hevc_part_mode_decode
#define ff_ps_stereo_interpolate_ipdopd_neon liteav_ff_ps_stereo_interpolate_ipdopd_neon
#define ff_h264_idct_add_8_sse2 liteav_ff_h264_idct_add_8_sse2
#define av_frame_get_metadata liteav_av_frame_get_metadata
#define ff_hevc_put_qpel_h1v1_neon_8 liteav_ff_hevc_put_qpel_h1v1_neon_8
#define ff_fft_init_fixed_32 liteav_ff_fft_init_fixed_32
#define av_bsf_list_parse_str liteav_av_bsf_list_parse_str
#define ff_avg_h264_qpel16_mc30_10_sse2 liteav_ff_avg_h264_qpel16_mc30_10_sse2
#define ff_int16_to_int32_a_sse2 liteav_ff_int16_to_int32_a_sse2
#define ff_hevc_flush_dpb liteav_ff_hevc_flush_dpb
#define yyset_column liteav_yyset_column
#define ff_request_frame liteav_ff_request_frame
#define ff_pack_2ch_int16_to_int32_u_sse2 liteav_ff_pack_2ch_int16_to_int32_u_sse2
#define ff_mpa_synth_filter_float liteav_ff_mpa_synth_filter_float
#define ffio_fill liteav_ffio_fill
#define ff_mov_cenc_write_sinf_tag liteav_ff_mov_cenc_write_sinf_tag
#define av_find_input_format liteav_av_find_input_format
#define ff_mpv_common_init_neon liteav_ff_mpv_common_init_neon
#define ff_dct32_float liteav_ff_dct32_float
#define av_oformat_next liteav_av_oformat_next
#define av_audio_fifo_peek_at liteav_av_audio_fifo_peek_at
#define ff_put_h264_qpel4_hv_lowpass_v_mmxext liteav_ff_put_h264_qpel4_hv_lowpass_v_mmxext
#define av_pix_fmt_swap_endianness liteav_av_pix_fmt_swap_endianness
#define ff_hevc_pred_angular_16x16_v_neon_8 liteav_ff_hevc_pred_angular_16x16_v_neon_8
#define ff_mpv_common_frame_size_change liteav_ff_mpv_common_frame_size_change
#define ff_h264_idct_add_8_c liteav_ff_h264_idct_add_8_c
#define av_cast5_crypt liteav_av_cast5_crypt
#define ff_h264_weight_4_mmxext liteav_ff_h264_weight_4_mmxext
#define ff_graph_thread_init liteav_ff_graph_thread_init
#define av_filter_iterate liteav_av_filter_iterate
#define ff_avg_h264_qpel4_h_lowpass_l2_mmxext liteav_ff_avg_h264_qpel4_h_lowpass_l2_mmxext
#define ff_inlink_process_commands liteav_ff_inlink_process_commands
#define ff_pred8x8_hor_neon liteav_ff_pred8x8_hor_neon
#define ff_aac_codebook_vectors liteav_ff_aac_codebook_vectors
#define avcodec_encode_subtitle liteav_avcodec_encode_subtitle
#define ff_hevc_ref_idx_lx_decode liteav_ff_hevc_ref_idx_lx_decode
#define uyvytoyuv422 liteav_uyvytoyuv422
#define ff_hevc_sao_band_w32_neon_8 liteav_ff_hevc_sao_band_w32_neon_8
#define ff_hevc_pred_init_neon_intrinsics liteav_ff_hevc_pred_init_neon_intrinsics
#define ff_read_riff_info liteav_ff_read_riff_info
#define ff_mpeg_ref_picture liteav_ff_mpeg_ref_picture
#define av_d2q liteav_av_d2q
#define av_stristr liteav_av_stristr
#define ff_int32_to_int16_a_mmx liteav_ff_int32_to_int16_a_mmx
#define av_fifo_generic_peek liteav_av_fifo_generic_peek
#define ff_all_samplerates liteav_ff_all_samplerates
#define ff_pack_8ch_int32_to_float_u_sse2 liteav_ff_pack_8ch_int32_to_float_u_sse2
#define ff_pred8x8_plane_8_mmxext liteav_ff_pred8x8_plane_8_mmxext
#define ff_simple_idct48_add liteav_ff_simple_idct48_add
#define av_image_check_sar liteav_av_image_check_sar
#define av_copy_packet_side_data liteav_av_copy_packet_side_data
#define ff_parse_specific_params liteav_ff_parse_specific_params
#define swri_oldapi_conv_fltp_to_s16_2ch_neon liteav_swri_oldapi_conv_fltp_to_s16_2ch_neon
#define ff_avfilter_link_set_out_status liteav_ff_avfilter_link_set_out_status
#define ff_deblock_v_luma_intra_10_avx liteav_ff_deblock_v_luma_intra_10_avx
#define ff_av1_profiles liteav_ff_av1_profiles
#define avcodec_find_best_pix_fmt_of_2 liteav_avcodec_find_best_pix_fmt_of_2
#define swri_realloc_audio liteav_swri_realloc_audio
#define ff_add_format liteav_ff_add_format
#define ff_pred8x8l_vertical_10_avx liteav_ff_pred8x8l_vertical_10_avx
#define av_strtod liteav_av_strtod
#define av_encryption_init_info_get_side_data liteav_av_encryption_init_info_get_side_data
#define ff_avg_h264_qpel8_mc20_neon liteav_ff_avg_h264_qpel8_mc20_neon
#define ff_pred16x16_tm_vp8_8_mmxext liteav_ff_pred16x16_tm_vp8_8_mmxext
#define avformat_get_riff_audio_tags liteav_avformat_get_riff_audio_tags
#define ff_rotate_slice liteav_ff_rotate_slice
#define ff_hevc_mvp_lx_flag_decode liteav_ff_hevc_mvp_lx_flag_decode
#define ff_imdct_half_c_fixed_32 liteav_ff_imdct_half_c_fixed_32
#define swr_config_frame liteav_swr_config_frame
#define av_guess_codec liteav_av_guess_codec
#define ff_check_pixfmt_descriptors liteav_ff_check_pixfmt_descriptors
#define ff_http_auth_handle_header liteav_ff_http_auth_handle_header
#define ff_hevc_put_pixels_w32_neon_8 liteav_ff_hevc_put_pixels_w32_neon_8
#define ff_unpack_2ch_int32_to_int32_a_sse2 liteav_ff_unpack_2ch_int32_to_int32_a_sse2
#define ff_flac_get_max_frame_size liteav_ff_flac_get_max_frame_size
#define ff_subtitles_read_chunk liteav_ff_subtitles_read_chunk
#define vlc_css_selector_New liteav_vlc_css_selector_New
#define av_buffersrc_add_frame liteav_av_buffersrc_add_frame
#define ff_sine_256 liteav_ff_sine_256
#define ff_put_h264_qpel16_mc30_10_ssse3_cache64 liteav_ff_put_h264_qpel16_mc30_10_ssse3_cache64
#define ff_h263_show_pict_info liteav_ff_h263_show_pict_info
#define ff_jref_idct_put liteav_ff_jref_idct_put
#define ff_rtmpte_protocol liteav_ff_rtmpte_protocol
#define ff_formats_changeref liteav_ff_formats_changeref
#define ff_avg_pixels16_mmxext liteav_ff_avg_pixels16_mmxext
#define av_hwdevice_ctx_alloc liteav_av_hwdevice_ctx_alloc
#define ff_zigzag_direct liteav_ff_zigzag_direct
#define ff_get_codec_guid liteav_ff_get_codec_guid
#define ff_h263_loop_filter_strength liteav_ff_h263_loop_filter_strength
#define ff_inlink_queued_frames liteav_ff_inlink_queued_frames
#define ff_network_sleep_interruptible liteav_ff_network_sleep_interruptible
#define ff_hevc_put_qpel_uw_h1v3_neon_8 liteav_ff_hevc_put_qpel_uw_h1v3_neon_8
#define ff_put_guid liteav_ff_put_guid
#define av_bsf_get_class liteav_av_bsf_get_class
#define ff_hwframe_map_create liteav_ff_hwframe_map_create
#define ff_amf_read_null liteav_ff_amf_read_null
#define ff_aac_num_swb_512 liteav_ff_aac_num_swb_512
#define ff_hevc_put_pel_uw_pixels_w48_neon_8_asm liteav_ff_hevc_put_pel_uw_pixels_w48_neon_8_asm
#define ff_sine_8192 liteav_ff_sine_8192
#define vlc_css_selector_AddSpecifier liteav_vlc_css_selector_AddSpecifier
#define av_ripemd_size liteav_av_ripemd_size
#define ff_vf_crop liteav_ff_vf_crop
#define ff_float_to_int16_a_sse2 liteav_ff_float_to_int16_a_sse2
#define ff_h264_h_loop_filter_chroma_neon liteav_ff_h264_h_loop_filter_chroma_neon
#define ff_hevc_cbf_luma_decode liteav_ff_hevc_cbf_luma_decode
#define av_frame_unref liteav_av_frame_unref
#define ff_rdft_end liteav_ff_rdft_end
#define ff_put_qpel16_mc12_old_c liteav_ff_put_qpel16_mc12_old_c
#define vlc_css_parser_AddRule liteav_vlc_css_parser_AddRule
#define ff_mov_add_hinted_packet liteav_ff_mov_add_hinted_packet
#define ff_socket_nonblock liteav_ff_socket_nonblock
#define ff_put_h264_qpel16_mc23_10_sse2 liteav_ff_put_h264_qpel16_mc23_10_sse2
#define ff_er_frame_start liteav_ff_er_frame_start
#define avio_free_directory_entry liteav_avio_free_directory_entry
#define ff_fill_line_with_color liteav_ff_fill_line_with_color
#define av_bitstream_filter_filter liteav_av_bitstream_filter_filter
#define av_bprint_channel_layout liteav_av_bprint_channel_layout
#define ff_pcm_read_packet liteav_ff_pcm_read_packet
#define av_bitstream_filter_next liteav_av_bitstream_filter_next
#define ff_avg_h264_qpel8_mc23_10_sse2 liteav_ff_avg_h264_qpel8_mc23_10_sse2
#define av_opt_eval_float liteav_av_opt_eval_float
#define avformat_get_mov_video_tags liteav_avformat_get_mov_video_tags
#define ff_h264_idct_add8_422_8_c liteav_ff_h264_idct_add8_422_8_c
#define ff_put_h264_qpel8_mc13_neon liteav_ff_put_h264_qpel8_mc13_neon
#define ff_cos_128_fixed liteav_ff_cos_128_fixed
#define avcodec_find_decoder_by_name liteav_avcodec_find_decoder_by_name
#define avpriv_slicethread_execute liteav_avpriv_slicethread_execute
#define ff_ssa_decoder liteav_ff_ssa_decoder
#define av_audio_fifo_alloc liteav_av_audio_fifo_alloc
#define ff_unpack_6ch_float_to_float_a_sse liteav_ff_unpack_6ch_float_to_float_a_sse
#define av_dct_calc liteav_av_dct_calc
#define ff_framesync_init liteav_ff_framesync_init
#define ff_hevc_pred_angular_8x8_v_neon_8 liteav_ff_hevc_pred_angular_8x8_v_neon_8
#define av_find_info_tag liteav_av_find_info_tag
#define av_sha512_final liteav_av_sha512_final
#define swr_is_initialized liteav_swr_is_initialized
#define av_mastering_display_metadata_alloc liteav_av_mastering_display_metadata_alloc
#define av_filter_ffversion liteav_av_filter_ffversion
#define swr_init liteav_swr_init
#define ff_pred4x4_down_right_10_sse2 liteav_ff_pred4x4_down_right_10_sse2
#define av_dict_free liteav_av_dict_free
#define ff_id3v2_write_apic liteav_ff_id3v2_write_apic
#define ff_codec_movdata_tags liteav_ff_codec_movdata_tags
#define ff_fft_init_x86 liteav_ff_fft_init_x86
#define ff_hevc_put_epel_uw_bi_hv_neon_8 liteav_ff_hevc_put_epel_uw_bi_hv_neon_8
#define av_memcpy_backptr liteav_av_memcpy_backptr
#define ff_put_h264_qpel16_mc01_10_sse2 liteav_ff_put_h264_qpel16_mc01_10_sse2
#define yy_scan_bytes liteav_yy_scan_bytes
#define ff_avg_h264_chroma_mc4_3dnow liteav_ff_avg_h264_chroma_mc4_3dnow
#define ff_https_protocol liteav_ff_https_protocol
#define av_map_videotoolbox_format_to_pixfmt liteav_av_map_videotoolbox_format_to_pixfmt
#define av_opt_set_defaults liteav_av_opt_set_defaults
#define ff_text_init_avio liteav_ff_text_init_avio
#define av_opt_set_dict_val liteav_av_opt_set_dict_val
#define ff_put_no_rnd_qpel16_mc31_old_c liteav_ff_put_no_rnd_qpel16_mc31_old_c
#define av_frame_apply_cropping liteav_av_frame_apply_cropping
#define avfilter_inout_free liteav_avfilter_inout_free
#define av_dynarray2_add liteav_av_dynarray2_add
#define av_get_extended_channel_layout liteav_av_get_extended_channel_layout
#define ff_log2_tab liteav_ff_log2_tab
#define ff_init_desc_fmt_convert liteav_ff_init_desc_fmt_convert
#define ff_get_unscaled_swscale liteav_ff_get_unscaled_swscale
#define ff_shuffle_bytes_2103_ssse3 liteav_ff_shuffle_bytes_2103_ssse3
#define ff_listen liteav_ff_listen
#define ff_hevc_sao_edge_eo1_w32_neon_8 liteav_ff_hevc_sao_edge_eo1_w32_neon_8
#define ff_hevc_sao_band_filter_8_neon_asm liteav_ff_hevc_sao_band_filter_8_neon_asm
#define av_packet_alloc liteav_av_packet_alloc
#define ff_avg_h264_qpel8_mc20_10_sse2 liteav_ff_avg_h264_qpel8_mc20_10_sse2
#define ff_h264chroma_init liteav_ff_h264chroma_init
#define ff_put_h264_qpel8_mc21_10_sse2 liteav_ff_put_h264_qpel8_mc21_10_sse2
#define ff_h263_resync liteav_ff_h263_resync
#define ff_put_h264_qpel8_h_lowpass_mmxext liteav_ff_put_h264_qpel8_h_lowpass_mmxext
#define ff_unpack_2ch_int32_to_float_a_sse2 liteav_ff_unpack_2ch_int32_to_float_a_sse2
#define av_gcd liteav_av_gcd
#define ff_ps_add_squares_neon liteav_ff_ps_add_squares_neon
#define ff_free_vlc liteav_ff_free_vlc
#define ff_h264_demuxer liteav_ff_h264_demuxer
#define av_usleep liteav_av_usleep
#define ff_deblock_h_luma_mbaff_8_avx liteav_ff_deblock_h_luma_mbaff_8_avx
#define ff_int32_to_float_u_avx liteav_ff_int32_to_float_u_avx
#define ff_pred16x16_dc_10_sse2 liteav_ff_pred16x16_dc_10_sse2
#define ff_ac3_slow_decay_tab liteav_ff_ac3_slow_decay_tab
#define avfilter_graph_send_command liteav_avfilter_graph_send_command
#define avpriv_mpeg4audio_sample_rates liteav_avpriv_mpeg4audio_sample_rates
#define ff_null_get_video_buffer liteav_ff_null_get_video_buffer
#define ff_swb_offset_480 liteav_ff_swb_offset_480
#define ff_hevc_put_pel_bi_neon_8_asm liteav_ff_hevc_put_pel_bi_neon_8_asm
#define ff_eac3_custom_channel_map_locations liteav_ff_eac3_custom_channel_map_locations
#define av_log_get_level liteav_av_log_get_level
#define av_mastering_display_metadata_create_side_data liteav_av_mastering_display_metadata_create_side_data
#define ff_ebur128_loudness_global_multiple liteav_ff_ebur128_loudness_global_multiple
#define ff_mov_cenc_avc_write_nal_units liteav_ff_mov_cenc_avc_write_nal_units
#define ff_ebur128_add_frames_double liteav_ff_ebur128_add_frames_double
#define ff_simple_idct84_add liteav_ff_simple_idct84_add
#define ff_mpa_synth_init_fixed liteav_ff_mpa_synth_init_fixed
#define ff_avg_h264_qpel8_mc31_neon liteav_ff_avg_h264_qpel8_mc31_neon
#define ff_hevc_pred_planar_16x16_neon_8 liteav_ff_hevc_pred_planar_16x16_neon_8
#define ff_deblock_h_chroma_8_sse2 liteav_ff_deblock_h_chroma_8_sse2
#define ff_hevc_put_qpel_v2_neon_8 liteav_ff_hevc_put_qpel_v2_neon_8
#define ff_all_channel_layouts liteav_ff_all_channel_layouts
#define ff_pred16x16_top_dc_neon liteav_ff_pred16x16_top_dc_neon
#define av_malloc_array liteav_av_malloc_array
#define ff_mp4_obj_type liteav_ff_mp4_obj_type
#define ff_put_vc1_chroma_mc8_nornd_mmx liteav_ff_put_vc1_chroma_mc8_nornd_mmx
#define av_frame_new_side_data_from_buf liteav_av_frame_new_side_data_from_buf
#define ff_mpeg_flush liteav_ff_mpeg_flush
#define av_encryption_info_free liteav_av_encryption_info_free
#define av_parse_cpu_flags liteav_av_parse_cpu_flags
#define ff_avg_h264_qpel16_mc10_neon liteav_ff_avg_h264_qpel16_mc10_neon
#define ff_avg_h264_qpel16_mc02_neon liteav_ff_avg_h264_qpel16_mc02_neon
#define avfilter_graph_queue_command liteav_avfilter_graph_queue_command
#define avpriv_copy_bits liteav_avpriv_copy_bits
#define av_malloc liteav_av_malloc
#define ff_avg_pixels8_mmx liteav_ff_avg_pixels8_mmx
#define ff_sine_256_fixed liteav_ff_sine_256_fixed
#define av_hwframe_transfer_get_formats liteav_av_hwframe_transfer_get_formats
#define av_log_set_flags liteav_av_log_set_flags
#define ff_int16_to_int32_a_mmx liteav_ff_int16_to_int32_a_mmx
#define ff_amf_write_bool liteav_ff_amf_write_bool
#define avio_rb24 liteav_avio_rb24
#define ff_copy_rectangle liteav_ff_copy_rectangle
#define avpriv_split_xiph_headers liteav_avpriv_split_xiph_headers
#define ff_aac_eld_window_512_fixed liteav_ff_aac_eld_window_512_fixed
#define ff_avg_vc1_chroma_mc8_nornd_ssse3 liteav_ff_avg_vc1_chroma_mc8_nornd_ssse3
#define ff_rl_intra_aic liteav_ff_rl_intra_aic
#define avfilter_link_free liteav_avfilter_link_free
#define ff_weight_h264_pixels_16_neon liteav_ff_weight_h264_pixels_16_neon
#define ff_prefetch_aarch64 liteav_ff_prefetch_aarch64
#define vlc_css_unescape liteav_vlc_css_unescape
#define av_tea_init liteav_av_tea_init
#define ff_avg_h264_qpel16_mc11_neon liteav_ff_avg_h264_qpel16_mc11_neon
#define av_buffersrc_parameters_set liteav_av_buffersrc_parameters_set
#define av_picture_crop liteav_av_picture_crop
#define ff_h264_decode_mb_cavlc liteav_ff_h264_decode_mb_cavlc
#define ff_simple_idct_add_int16_8bit liteav_ff_simple_idct_add_int16_8bit
#define ff_put_h264_qpel16_mc20_neon liteav_ff_put_h264_qpel16_mc20_neon
#define ff_pred16x16_horizontal_8_mmxext liteav_ff_pred16x16_horizontal_8_mmxext
#define av_fast_malloc liteav_av_fast_malloc
#define ff_put_h264_qpel8_mc02_10_sse2 liteav_ff_put_h264_qpel8_mc02_10_sse2
#define ff_pack_6ch_int32_to_float_a_avx liteav_ff_pack_6ch_int32_to_float_a_avx
#define ff_thread_video_encode_frame liteav_ff_thread_video_encode_frame
#define ff_avg_h264_qpel4_mc32_10_mmxext liteav_ff_avg_h264_qpel4_mc32_10_mmxext
#define ff_jpeg2000_profiles liteav_ff_jpeg2000_profiles
#define avio_size liteav_avio_size
#define ff_fft_calc_avx liteav_ff_fft_calc_avx
#define av_aes_alloc liteav_av_aes_alloc
#define ff_pw_1019 liteav_ff_pw_1019
#define ff_sqrt_tab liteav_ff_sqrt_tab
#define ff_unpack_2ch_int32_to_int16_a_sse2 liteav_ff_unpack_2ch_int32_to_int16_a_sse2
#define ff_mpeg12_vlc_dc_lum_code liteav_ff_mpeg12_vlc_dc_lum_code
#define av_sub_i liteav_av_sub_i
#define ff_nv21_to_argb_neon liteav_ff_nv21_to_argb_neon
#define avio_alloc_context liteav_avio_alloc_context
#define avfilter_inout_alloc liteav_avfilter_inout_alloc
#define ff_imdct36_float_ssse3 liteav_ff_imdct36_float_ssse3
#define ff_sbr_qmf_pre_shuffle_neon liteav_ff_sbr_qmf_pre_shuffle_neon
#define ff_mdct_init_fixed_32 liteav_ff_mdct_init_fixed_32
#define ff_flac_lpc_16_arm liteav_ff_flac_lpc_16_arm
#define ff_avg_pixels16_x2_neon liteav_ff_avg_pixels16_x2_neon
#define ff_put_qpel8_mc12_old_c liteav_ff_put_qpel8_mc12_old_c
#define ff_mpeg4_frame_end liteav_ff_mpeg4_frame_end
#define av_sub_q liteav_av_sub_q
#define avpriv_register_devices liteav_avpriv_register_devices
#define ff_sine_128_fixed liteav_ff_sine_128_fixed
#define av_opt_set_bin liteav_av_opt_set_bin
#define ff_deblock_v_chroma_intra_8_mmxext liteav_ff_deblock_v_chroma_intra_8_mmxext
#define ff_h264_idct8_dc_add_10_avx liteav_ff_h264_idct8_dc_add_10_avx
#define ff_h264chroma_init_aarch64 liteav_ff_h264chroma_init_aarch64
#define ff_raw_data_read_header liteav_ff_raw_data_read_header
#define swresample_license liteav_swresample_license
#define ff_put_h264_qpel16_mc32_neon liteav_ff_put_h264_qpel16_mc32_neon
#define ff_cos_65536_fixed liteav_ff_cos_65536_fixed
#define ff_pw_15 liteav_ff_pw_15
#define ff_pw_16 liteav_ff_pw_16
#define ff_pw_17 liteav_ff_pw_17
#define ff_h264_remove_all_refs liteav_ff_h264_remove_all_refs
#define avio_put_str16le liteav_avio_put_str16le
#define webvtt_FillStyleFromCssDeclaration liteav_webvtt_FillStyleFromCssDeclaration
#define avpriv_float_dsp_alloc liteav_avpriv_float_dsp_alloc
#define codec_ism_tags liteav_codec_ism_tags
#define ff_raw_read_partial_packet liteav_ff_raw_read_partial_packet
#define av_pix_fmt_desc_get_id liteav_av_pix_fmt_desc_get_id
#define ff_pred8x8_tm_vp8_8_ssse3 liteav_ff_pred8x8_tm_vp8_8_ssse3
#define text_segment_new liteav_text_segment_new
#define ff_ebur128_sample_peak liteav_ff_ebur128_sample_peak
#define ff_framesync_dualinput_get_writable liteav_ff_framesync_dualinput_get_writable
#define ff_h264_idct8_dc_add_8_mmxext liteav_ff_h264_idct8_dc_add_8_mmxext
#define ffurl_shutdown liteav_ffurl_shutdown
#define ff_h264_idct8_add4_8_mmx liteav_ff_h264_idct8_add4_8_mmx
#define ff_avs3_muxer liteav_ff_avs3_muxer
#define ff_put_h264_qpel16_mc30_10_sse2_cache64 liteav_ff_put_h264_qpel16_mc30_10_sse2_cache64
#define av_strstart liteav_av_strstart
#define ff_h264_luma_dc_dequant_idct_9_c liteav_ff_h264_luma_dc_dequant_idct_9_c
#define ff_h264_pred_init_aarch64 liteav_ff_h264_pred_init_aarch64
#define av_free liteav_av_free
#define ff_simple_idct12_put_avx liteav_ff_simple_idct12_put_avx
#define av_aes_crypt liteav_av_aes_crypt
#define ff_hevc_dsp_init_aarch64 liteav_ff_hevc_dsp_init_aarch64
#define parse_sequence_header_info liteav_parse_sequence_header_info
#define ff_hevc_transform_8x8_neon_8 liteav_ff_hevc_transform_8x8_neon_8
#define ff_put_qpel8_mc31_old_c liteav_ff_put_qpel8_mc31_old_c
#define avio_wl32 liteav_avio_wl32
#define ff_mov_cenc_avc_parse_nal_units liteav_ff_mov_cenc_avc_parse_nal_units
#define ff_h264_chroma422_dc_dequant_idct_10_c liteav_ff_h264_chroma422_dc_dequant_idct_10_c
#define av_stereo3d_create_side_data liteav_av_stereo3d_create_side_data
#define av_hash_alloc liteav_av_hash_alloc
#define ff_wav_codec_get_id liteav_ff_wav_codec_get_id
#define ff_pack_6ch_int32_to_float_u_sse2 liteav_ff_pack_6ch_int32_to_float_u_sse2
#define ff_hevc_put_pixels_w6_neon_8_asm liteav_ff_hevc_put_pixels_w6_neon_8_asm
#define ff_id3v2_4_tags liteav_ff_id3v2_4_tags
#define ff_imdct_calc_c_fixed liteav_ff_imdct_calc_c_fixed
#define ff_hevc_transform_add_16x16_neon_8_asm liteav_ff_hevc_transform_add_16x16_neon_8_asm
#define ff_mdct_end_fixed_32 liteav_ff_mdct_end_fixed_32
#define ff_avg_h264_qpel4_mc02_10_mmxext liteav_ff_avg_h264_qpel4_mc02_10_mmxext
#define av_bsf_list_alloc liteav_av_bsf_list_alloc
#define ff_hevc_put_qpel_uw_h3v1_neon_8 liteav_ff_hevc_put_qpel_uw_h3v1_neon_8
#define avio_handshake liteav_avio_handshake
#define ff_mpeg4_video_profiles liteav_ff_mpeg4_video_profiles
#define ff_h2645_packet_uninit liteav_ff_h2645_packet_uninit
#define ff_h264_chroma422_dc_dequant_idct_14_c liteav_ff_h264_chroma422_dc_dequant_idct_14_c
#define ff_subtitles_queue_finalize liteav_ff_subtitles_queue_finalize
#define ff_hevc_merge_idx_decode liteav_ff_hevc_merge_idx_decode
#define ff_set_common_channel_layouts liteav_ff_set_common_channel_layouts
#define rgb15tobgr32 liteav_rgb15tobgr32
#define ff_h264_idct8_dc_add_10_c liteav_ff_h264_idct8_dc_add_10_c
#define ff_h264_decode_picture_parameter_set liteav_ff_h264_decode_picture_parameter_set
#define ff_deblock_v_chroma_intra_10_sse2 liteav_ff_deblock_v_chroma_intra_10_sse2
#define ff_flac_parse_streaminfo liteav_ff_flac_parse_streaminfo
#define ff_hls_demuxer liteav_ff_hls_demuxer
#define ff_h264_idct8_dc_add_12_c liteav_ff_h264_idct8_dc_add_12_c
#define av_bprint_chars liteav_av_bprint_chars
#define ff_pred8x8l_horizontal_up_10_avx liteav_ff_pred8x8l_horizontal_up_10_avx
#define ff_avg_h264_chroma_mc8_neon liteav_ff_avg_h264_chroma_mc8_neon
#define ff_mpa_sblimit_table liteav_ff_mpa_sblimit_table
#define ff_put_h264_chroma_mc4_10_mmxext liteav_ff_put_h264_chroma_mc4_10_mmxext
#define ff_framesync_activate liteav_ff_framesync_activate
#define ff_subtitles_queue_read_packet liteav_ff_subtitles_queue_read_packet
#define ff_h264_idct8_dc_add_14_c liteav_ff_h264_idct8_dc_add_14_c
#define av_sample_fmt_is_planar liteav_av_sample_fmt_is_planar
#define ff_pred8x8l_dc_10_sse2 liteav_ff_pred8x8l_dc_10_sse2
#define yyget_out liteav_yyget_out
#define sws_convVec liteav_sws_convVec
#define ff_vorbiscomment_length liteav_ff_vorbiscomment_length
#define ff_hevc_put_qpel_uw_pixels_w12_neon_8 liteav_ff_hevc_put_qpel_uw_pixels_w12_neon_8
#define ff_hevc_put_pixels_w2_neon_8 liteav_ff_hevc_put_pixels_w2_neon_8
#define ff_h264_biweight_8_sse2 liteav_ff_h264_biweight_8_sse2
#define ff_pack_6ch_float_to_int32_a_avx liteav_ff_pack_6ch_float_to_int32_a_avx
#define ff_thread_finish_setup liteav_ff_thread_finish_setup
#define avfilter_get_by_name liteav_avfilter_get_by_name
#define ff_h264_videotoolbox_hwaccel liteav_ff_h264_videotoolbox_hwaccel
#define ff_aac_profiles liteav_ff_aac_profiles
#define ff_slice_thread_free liteav_ff_slice_thread_free
#define rendition_matched_tags liteav_rendition_matched_tags
#define rgb64to48_bswap liteav_rgb64to48_bswap
#define ff_h264_chroma422_dc_dequant_idct_12_c liteav_ff_h264_chroma422_dc_dequant_idct_12_c
#define ff_ps_hybrid_analysis_neon liteav_ff_ps_hybrid_analysis_neon
#define ff_mpeg2_video_profiles liteav_ff_mpeg2_video_profiles
#define ff_w4_plus_w2_hi liteav_ff_w4_plus_w2_hi
#define ff_h264_idct_add16_10_avx liteav_ff_h264_idct_add16_10_avx
#define yyset_lineno liteav_yyset_lineno
#define av_des_alloc liteav_av_des_alloc
#define ff_pred8x8l_down_left_10_ssse3 liteav_ff_pred8x8l_down_left_10_ssse3
#define ff_h264_biweight_16_10_sse4 liteav_ff_h264_biweight_16_10_sse4
#define ff_framesync_dualinput_get liteav_ff_framesync_dualinput_get
#define ff_hevc_dsp_init liteav_ff_hevc_dsp_init
#define ff_h264_biweight_16_10_sse2 liteav_ff_h264_biweight_16_10_sse2
#define av_parse_video_rate liteav_av_parse_video_rate
#define av_register_bitstream_filter liteav_av_register_bitstream_filter
#define av_packet_rescale_ts liteav_av_packet_rescale_ts
#define ff_aac_scalefactor_code liteav_ff_aac_scalefactor_code
#define ff_rvlc_rl_inter liteav_ff_rvlc_rl_inter
#define ff_pred16x16_vert_neon liteav_ff_pred16x16_vert_neon
#define ff_crc04C11DB7_update liteav_ff_crc04C11DB7_update
#define ff_mov_write_chan liteav_ff_mov_write_chan
#define ff_sbr_apply liteav_ff_sbr_apply
#define ff_query_formats_all_layouts liteav_ff_query_formats_all_layouts
#define ff_h264_idct_add16intra_8_mmxext liteav_ff_h264_idct_add16intra_8_mmxext
#define ff_rtmp_packet_write liteav_ff_rtmp_packet_write
#define avfilter_add_matrix liteav_avfilter_add_matrix
#define yyrealloc liteav_yyrealloc
#define ff_hevc_put_qpel_uw_pixels_w32_neon_8 liteav_ff_hevc_put_qpel_uw_pixels_w32_neon_8
#define ff_yuv422p_to_rgba_neon liteav_ff_yuv422p_to_rgba_neon
#define ff_put_pixels16_mmx liteav_ff_put_pixels16_mmx
#define av_bprint_init_for_buffer liteav_av_bprint_init_for_buffer
#define av_aes_ctr_init liteav_av_aes_ctr_init
#define av_opt_free liteav_av_opt_free
#define ff_avg_h264_qpel16_mc32_10_sse2 liteav_ff_avg_h264_qpel16_mc32_10_sse2
#define ff_mjpeg_encode_huffman_close liteav_ff_mjpeg_encode_huffman_close
#define ff_clean_intra_table_entries liteav_ff_clean_intra_table_entries
#define ff_pred8x8_0l0_dc_neon liteav_ff_pred8x8_0l0_dc_neon
#define ff_mpeg1_clean_buffers liteav_ff_mpeg1_clean_buffers
#define ff_image_copy_plane_uc_from_x86 liteav_ff_image_copy_plane_uc_from_x86
#define ff_ebur128_add_frames_planar_double liteav_ff_ebur128_add_frames_planar_double
#define av_audio_fifo_write liteav_av_audio_fifo_write
#define ff_deblock_h_luma_intra_10_sse2 liteav_ff_deblock_h_luma_intra_10_sse2
#define av_rdft_calc liteav_av_rdft_calc
#define ff_pw_1023 liteav_ff_pw_1023
#define ff_inlink_consume_samples liteav_ff_inlink_consume_samples
#define av_get_alt_sample_fmt liteav_av_get_alt_sample_fmt
#define ff_hpeldsp_init_aarch64 liteav_ff_hpeldsp_init_aarch64
#define av_spherical_from_name liteav_av_spherical_from_name
#define ff_openssl_deinit liteav_ff_openssl_deinit
#define ff_hevc_pred_angular_16x16_h_neon_8 liteav_ff_hevc_pred_angular_16x16_h_neon_8
#define ff_subtitles_queue_clean liteav_ff_subtitles_queue_clean
#define ff_put_h264_qpel16_mc11_10_sse2 liteav_ff_put_h264_qpel16_mc11_10_sse2
#define ff_amf_read_string liteav_ff_amf_read_string
#define ff_id3v2_read liteav_ff_id3v2_read
#define ff_simple_idct8_sse2 liteav_ff_simple_idct8_sse2
#define av_base64_encode liteav_av_base64_encode
#define ff_hevc_sao_edge_eo0_w64_neon_8 liteav_ff_hevc_sao_edge_eo0_w64_neon_8
#define ff_hevc_transform_luma_4x4_neon_8_asm liteav_ff_hevc_transform_luma_4x4_neon_8_asm
#define av_buffer_ref liteav_av_buffer_ref
#define rgb48to64_nobswap liteav_rgb48to64_nobswap
#define ff_idctdsp_init liteav_ff_idctdsp_init
#define swresample_configuration liteav_swresample_configuration
#define openssl_mutexes liteav_openssl_mutexes
#define ff_alloc_entries liteav_ff_alloc_entries
#define ff_hevc_put_qpel_uw_h_neon_8 liteav_ff_hevc_put_qpel_uw_h_neon_8
#define av_bprint_append_data liteav_av_bprint_append_data
#define ff_h264_idct_add_neon liteav_ff_h264_idct_add_neon
#define ff_tns_max_bands_128 liteav_ff_tns_max_bands_128
#define ff_cos_512_fixed liteav_ff_cos_512_fixed
#define ff_sine_64 liteav_ff_sine_64
#define av_fifo_freep liteav_av_fifo_freep
#define ffurl_get_multi_file_handle liteav_ffurl_get_multi_file_handle
#define ff_prores_idct liteav_ff_prores_idct
#define ff_hevc_put_epel_v_neon_8 liteav_ff_hevc_put_epel_v_neon_8
#define ff_ac3_db_per_bit_tab liteav_ff_ac3_db_per_bit_tab
#define ff_put_h264_chroma_mc8_rnd_mmx liteav_ff_put_h264_chroma_mc8_rnd_mmx
#define ff_smil_get_attr_ptr liteav_ff_smil_get_attr_ptr
#define ff_pb_3 liteav_ff_pb_3
#define ff_pb_2 liteav_ff_pb_2
#define ff_pb_1 liteav_ff_pb_1
#define ff_pb_0 liteav_ff_pb_0
#define ff_w3_min_w1_lo liteav_ff_w3_min_w1_lo
#define ff_h264_biweight_8_mmxext liteav_ff_h264_biweight_8_mmxext
#define ff_hevc_put_pixels_w4_neon_8 liteav_ff_hevc_put_pixels_w4_neon_8
#define av_imdct_half liteav_av_imdct_half
#define av_add_i liteav_av_add_i
#define sws_alloc_context liteav_sws_alloc_context
#define ff_thread_report_progress liteav_ff_thread_report_progress
#define ff_h264_set_erpic liteav_ff_h264_set_erpic
#define ff_pred4x4_down_left_10_avx liteav_ff_pred4x4_down_left_10_avx
#define ff_init_gamma_convert liteav_ff_init_gamma_convert
#define ff_put_no_rnd_qpel8_mc32_old_c liteav_ff_put_no_rnd_qpel8_mc32_old_c
#define ff_subtitles_read_text_chunk liteav_ff_subtitles_read_text_chunk
#define swr_ffversion liteav_swr_ffversion
#define av_add_q liteav_av_add_q
#define ff_insert_pad liteav_ff_insert_pad
#define avio_w8 liteav_avio_w8
#define ff_zigzag_scan liteav_ff_zigzag_scan
#define ff_pred16x16_dc_10_mmxext liteav_ff_pred16x16_dc_10_mmxext
#define ff_choose_chroma_location liteav_ff_choose_chroma_location
#define ff_put_h264_qpel4_mc10_10_mmxext liteav_ff_put_h264_qpel4_mc10_10_mmxext
#define ff_deblock_h_chroma422_8_mmxext liteav_ff_deblock_h_chroma422_8_mmxext
#define ff_mpeg4_c_dc_scale_table liteav_ff_mpeg4_c_dc_scale_table
#define ff_put_h264_qpel4_mc00_10_mmxext liteav_ff_put_h264_qpel4_mc00_10_mmxext
#define ff_frame_pool_uninit liteav_ff_frame_pool_uninit
#define ff_ps_init liteav_ff_ps_init
#define ff_hevc_put_pixels_w48_neon_8 liteav_ff_hevc_put_pixels_w48_neon_8
#define av_rescale_delta liteav_av_rescale_delta
#define ff_unpack_2ch_int32_to_int32_u_sse2 liteav_ff_unpack_2ch_int32_to_int32_u_sse2
#define av_hash_update liteav_av_hash_update
#define ff_hevc_put_pixels_w48_neon_8_asm liteav_ff_hevc_put_pixels_w48_neon_8_asm
#define av_opt_set_int liteav_av_opt_set_int
#define av_mediacodec_alloc_context liteav_av_mediacodec_alloc_context
#define ff_avg_h264_chroma_mc8_10_sse2 liteav_ff_avg_h264_chroma_mc8_10_sse2
#define ff_filter_graph_run_once liteav_ff_filter_graph_run_once
#define ff_alternate_vertical_scan liteav_ff_alternate_vertical_scan
#define ff_avg_h264_qpel4_h_lowpass_mmxext liteav_ff_avg_h264_qpel4_h_lowpass_mmxext
#define av_gettime_relative liteav_av_gettime_relative
#define av_md5_size liteav_av_md5_size
#define ff_dct32_float_avx liteav_ff_dct32_float_avx
#define avio_rb32 liteav_avio_rb32
#define ff_hevc_no_residual_syntax_flag_decode liteav_ff_hevc_no_residual_syntax_flag_decode
#define rgb16tobgr15 liteav_rgb16tobgr15
#define ff_put_h264_chroma_mc4_ssse3 liteav_ff_put_h264_chroma_mc4_ssse3
#define ff_avg_h264_qpel8_mc22_neon liteav_ff_avg_h264_qpel8_mc22_neon
#define ff_sbr_autocorrelate_neon liteav_ff_sbr_autocorrelate_neon
#define ff_vc1_profiles liteav_ff_vc1_profiles
#define av_frame_alloc liteav_av_frame_alloc
#define av_hash_final_b64 liteav_av_hash_final_b64
#define ff_pred8x8l_down_left_8_ssse3 liteav_ff_pred8x8l_down_left_8_ssse3
#define ff_avg_h264_qpel16_mc33_neon liteav_ff_avg_h264_qpel16_mc33_neon
#define ff_pred8x8_dc_8_mmxext liteav_ff_pred8x8_dc_8_mmxext
#define avfilter_graph_alloc_filter liteav_avfilter_graph_alloc_filter
#define ff_avg_qpel16_mc11_old_c liteav_ff_avg_qpel16_mc11_old_c
#define ff_ebur128_relative_threshold liteav_ff_ebur128_relative_threshold
#define ff_ps_stereo_interpolate_ipdopd_sse3 liteav_ff_ps_stereo_interpolate_ipdopd_sse3
#define avio_rl32 liteav_avio_rl32
#define av_write_image_line liteav_av_write_image_line
#define ff_aac_spectral_codes liteav_ff_aac_spectral_codes
#define ff_pb_15 liteav_ff_pb_15
#define swri_audio_convert_init_x86 liteav_swri_audio_convert_init_x86
#define ff_dither_2x2_8 liteav_ff_dither_2x2_8
#define ff_mpeg4_decoder liteav_ff_mpeg4_decoder
#define ff_put_h264_qpel8_mc11_10_sse2 liteav_ff_put_h264_qpel8_mc11_10_sse2
#define ff_me_cmp_init_x86 liteav_ff_me_cmp_init_x86
#define ff_simple_idct_int16_12bit liteav_ff_simple_idct_int16_12bit
#define ff_pred16x16_tm_vp8_8_avx2 liteav_ff_pred16x16_tm_vp8_8_avx2
#define ff_pred4x4_horizontal_down_8_mmxext liteav_ff_pred4x4_horizontal_down_8_mmxext
#define ff_vector_fmul_reverse_vfp liteav_ff_vector_fmul_reverse_vfp
#define ffio_init_context liteav_ffio_init_context
#define ff_riff_info_conv liteav_ff_riff_info_conv
#define ff_hevc_put_pixels_w8_neon_8 liteav_ff_hevc_put_pixels_w8_neon_8
#define ff_avg_rv40_chroma_mc4_mmxext liteav_ff_avg_rv40_chroma_mc4_mmxext
#define av_frame_get_plane_buffer liteav_av_frame_get_plane_buffer
#define ff_hevc_put_qpel_uw_weight_h3v2_neon_8 liteav_ff_hevc_put_qpel_uw_weight_h3v2_neon_8
#define ff_img_tags liteav_ff_img_tags
#define ff_init_ff_cos_tabs_fixed_32 liteav_ff_init_ff_cos_tabs_fixed_32
#define ff_httpproxy_protocol liteav_ff_httpproxy_protocol
#define ff_h264_chroma_dc_dequant_idct_14_c liteav_ff_h264_chroma_dc_dequant_idct_14_c
#define ff_amf_tag_size liteav_ff_amf_tag_size
#define av_aes_ctr_free liteav_av_aes_ctr_free
#define ff_simple_idct_add_int16_12bit liteav_ff_simple_idct_add_int16_12bit
#define ff_pred4x4_dc_10_mmxext liteav_ff_pred4x4_dc_10_mmxext
#define ff_flac_set_channel_layout liteav_ff_flac_set_channel_layout
#define ff_put_no_rnd_qpel16_mc32_old_c liteav_ff_put_no_rnd_qpel16_mc32_old_c
#define swri_rematrix_free liteav_swri_rematrix_free
#define avpriv_solve_lls liteav_avpriv_solve_lls
#define ff_fft_init_fixed liteav_ff_fft_init_fixed
#define ff_h263_loop_filter liteav_ff_h263_loop_filter
#define ff_init_scantable liteav_ff_init_scantable
#define ff_put_h264_qpel16_mc20_10_sse2_cache64 liteav_ff_put_h264_qpel16_mc20_10_sse2_cache64
#define av_opt_eval_q liteav_av_opt_eval_q
#define av_downmix_info_update_side_data liteav_av_downmix_info_update_side_data
#define ff_flac_parser liteav_ff_flac_parser
#define ff_mp3_decoder liteav_ff_mp3_decoder
#define av_des_init liteav_av_des_init
#define ff_listen_connect liteav_ff_listen_connect
#define ff_hevc_bump_frame liteav_ff_hevc_bump_frame
#define ff_filter_alloc liteav_ff_filter_alloc
#define ff_parse_channel_layout liteav_ff_parse_channel_layout
#define av_frame_set_qp_table liteav_av_frame_set_qp_table
#define ff_h263_cbpy_vlc liteav_ff_h263_cbpy_vlc
#define ff_put_pixels16_l2_mmxext liteav_ff_put_pixels16_l2_mmxext
#define ff_mdct_win_float liteav_ff_mdct_win_float
#define ff_avg_h264_chroma_mc8_rnd_ssse3 liteav_ff_avg_h264_chroma_mc8_rnd_ssse3
#define ff_mpv_motion liteav_ff_mpv_motion
#define swri_rematrix_init liteav_swri_rematrix_init
#define ff_put_h264_qpel4_mc32_10_mmxext liteav_ff_put_h264_qpel4_mc32_10_mmxext
#define vlc_css_term_Clean liteav_vlc_css_term_Clean
#define ff_avc_mp4_find_startcode liteav_ff_avc_mp4_find_startcode
#define ff_h264_biweight_4_10_sse2 liteav_ff_h264_biweight_4_10_sse2
#define ff_vf_scale liteav_ff_vf_scale
#define ff_h264_biweight_4_10_sse4 liteav_ff_h264_biweight_4_10_sse4
#define avio_wl24 liteav_avio_wl24
#define ff_hevc_put_qpel_uw_h2_neon_8 liteav_ff_hevc_put_qpel_uw_h2_neon_8
#define ff_put_pixels4_l2_mmxext liteav_ff_put_pixels4_l2_mmxext
#define ff_hevc_put_pixels_w24_neon_8 liteav_ff_hevc_put_pixels_w24_neon_8
#define ff_pack_8ch_float_to_int32_u_sse2 liteav_ff_pack_8ch_float_to_int32_u_sse2
#define shuffle_bytes_0321 liteav_shuffle_bytes_0321
#define ff_pred8x8l_top_dc_8_mmxext liteav_ff_pred8x8l_top_dc_8_mmxext
#define av_image_copy_to_buffer liteav_av_image_copy_to_buffer
#define ff_vector_fmul_scalar_neon liteav_ff_vector_fmul_scalar_neon
#define ff_h264_idct_add16intra_9_c liteav_ff_h264_idct_add16intra_9_c
#define ff_put_pixels4_mmx liteav_ff_put_pixels4_mmx
#define av_color_transfer_from_name liteav_av_color_transfer_from_name
#define av_ripemd_alloc liteav_av_ripemd_alloc
#define ff_getSwsFunc liteav_ff_getSwsFunc
#define av_cast5_size liteav_av_cast5_size
#define ff_pw_8192 liteav_ff_pw_8192
#define ff_w_tab_sr liteav_ff_w_tab_sr
#define ff_hevc_decode_nal_vps liteav_ff_hevc_decode_nal_vps
#define av_get_channel_layout_channel_index liteav_av_get_channel_layout_channel_index
#define ff_tcp_protocol liteav_ff_tcp_protocol
#define ff_h264_golomb_to_intra4x4_cbp liteav_ff_h264_golomb_to_intra4x4_cbp
#define ff_avc_parse_nal_units liteav_ff_avc_parse_nal_units
#define ff_put_h264_qpel8_mc32_neon liteav_ff_put_h264_qpel8_mc32_neon
#define av_imdct_calc liteav_av_imdct_calc
#define ff_mpeg4_DCtab_chrom liteav_ff_mpeg4_DCtab_chrom
#define ff_unpack_2ch_int16_to_int16_u_sse2 liteav_ff_unpack_2ch_int16_to_int16_u_sse2
#define shuffle_bytes_3210 liteav_shuffle_bytes_3210
#define ff_codec_movaudio_tags liteav_ff_codec_movaudio_tags
#define ff_ps_apply liteav_ff_ps_apply
#define avpriv_get_trc_function_from_trc liteav_avpriv_get_trc_function_from_trc
#define ff_h264_check_intra4x4_pred_mode liteav_ff_h264_check_intra4x4_pred_mode
#define ff_sbrdsp_init_x86 liteav_ff_sbrdsp_init_x86
#define av_buffer_allocz liteav_av_buffer_allocz
#define ff_hevc_diag_scan4x4_x liteav_ff_hevc_diag_scan4x4_x
#define ff_hevc_diag_scan4x4_y liteav_ff_hevc_diag_scan4x4_y
#define ff_simple_idct_put_int16_12bit liteav_ff_simple_idct_put_int16_12bit
#define ff_imdct_calc_neon liteav_ff_imdct_calc_neon
#define swri_noise_shaping_float liteav_swri_noise_shaping_float
#define av_audio_fifo_drain liteav_av_audio_fifo_drain
#define ff_h264_idct_add16_8_sse2 liteav_ff_h264_idct_add16_8_sse2
#define ff_id3v2_3_tags liteav_ff_id3v2_3_tags
#define webvtt_parser_close liteav_webvtt_parser_close
#define avfilter_graph_parse2 liteav_avfilter_graph_parse2
#define ff_avg_pixels8_l2_mmxext liteav_ff_avg_pixels8_l2_mmxext
#define ff_h264_mp4toannexb_bsf liteav_ff_h264_mp4toannexb_bsf
#define ff_pcm_mulaw_at_decoder liteav_ff_pcm_mulaw_at_decoder
#define ff_avg_h264_qpel16_mc31_10_sse2 liteav_ff_avg_h264_qpel16_mc31_10_sse2
#define ff_hevc_put_qpel_h1v2_neon_8 liteav_ff_hevc_put_qpel_h1v2_neon_8
#define avpriv_init_lls liteav_avpriv_init_lls
#define av_pixelutils_get_sad_fn liteav_av_pixelutils_get_sad_fn
#define ff_avg_h264_chroma_mc2_mmxext liteav_ff_avg_h264_chroma_mc2_mmxext
#define av_d3d11va_alloc_context liteav_av_d3d11va_alloc_context
#define av_buffersrc_add_frame_flags liteav_av_buffersrc_add_frame_flags
#define ff_hevc_idct_8x8_dc_neon_8 liteav_ff_hevc_idct_8x8_dc_neon_8
#define ff_mpa_quant_bits liteav_ff_mpa_quant_bits
#define ff_h263_rl_inter liteav_ff_h263_rl_inter
#define ff_cos_131072 liteav_ff_cos_131072
#define ff_put_h264_qpel4_mc22_10_mmxext liteav_ff_put_h264_qpel4_mc22_10_mmxext
#define sws_getColorspaceDetails liteav_sws_getColorspaceDetails
#define av_stereo3d_alloc liteav_av_stereo3d_alloc
#define ff_mpeg4_DCtab_lum liteav_ff_mpeg4_DCtab_lum
#define av_bprint_get_buffer liteav_av_bprint_get_buffer
#define av_hash_final_bin liteav_av_hash_final_bin
#define ff_h264_idct8_add4_8_mmxext liteav_ff_h264_idct8_add4_8_mmxext
#define ff_hevc_put_qpel_uw_v_neon_8 liteav_ff_hevc_put_qpel_uw_v_neon_8
#define ff_sine_2048 liteav_ff_sine_2048
#define ff_unicode_ass_add_rect liteav_ff_unicode_ass_add_rect
#define ff_put_h264_chroma_mc8_neon liteav_ff_put_h264_chroma_mc8_neon
#define avfilter_process_command liteav_avfilter_process_command
#define avfilter_graph_free liteav_avfilter_graph_free
#define ff_subtitles_unicode_external_read_chunk liteav_ff_subtitles_unicode_external_read_chunk
#define ff_deblock_h_luma_intra_8_avx liteav_ff_deblock_h_luma_intra_8_avx
#define rgb64to48_nobswap liteav_rgb64to48_nobswap
#define ff_frame_thread_encoder_free liteav_ff_frame_thread_encoder_free
#define ff_ps_hybrid_synthesis_deint_sse4 liteav_ff_ps_hybrid_synthesis_deint_sse4
#define ff_frame_thread_free liteav_ff_frame_thread_free
#define av_buffersink_get_channels liteav_av_buffersink_get_channels
#define av_md5_final liteav_av_md5_final
#define ff_put_h264_qpel4_hv_lowpass_h_mmxext liteav_ff_put_h264_qpel4_hv_lowpass_h_mmxext
#define ff_reget_buffer liteav_ff_reget_buffer
#define ff_put_h264_qpel16_mc02_neon liteav_ff_put_h264_qpel16_mc02_neon
#define ff_framesync_uninit liteav_ff_framesync_uninit
#define ff_aac_kbd_long_1024 liteav_ff_aac_kbd_long_1024
#define av_cast5_crypt2 liteav_av_cast5_crypt2
#define ff_avg_h264_qpel8or16_hv2_lowpass_op_mmxext liteav_ff_avg_h264_qpel8or16_hv2_lowpass_op_mmxext
#define ff_hevc_put_qpel_uw_weight_h3_neon_8 liteav_ff_hevc_put_qpel_uw_weight_h3_neon_8
#define ff_pred8x8_plane_10_sse2 liteav_ff_pred8x8_plane_10_sse2
#define ff_avg_h264_qpel16_mc31_neon liteav_ff_avg_h264_qpel16_mc31_neon
#define av_parse_ratio liteav_av_parse_ratio
#define ff_put_qpel8_mc13_old_c liteav_ff_put_qpel8_mc13_old_c
#define rgb48tobgr48_nobswap liteav_rgb48tobgr48_nobswap
#define ff_put_h264_qpel8or16_v_lowpass_sse2 liteav_ff_put_h264_qpel8or16_v_lowpass_sse2
#define ff_af_queue_add liteav_ff_af_queue_add
#define ff_h263i_decoder liteav_ff_h263i_decoder
#define ff_avg_vc1_chroma_mc8_nornd_3dnow liteav_ff_avg_vc1_chroma_mc8_nornd_3dnow
#define ff_sbc_profiles liteav_ff_sbc_profiles
#define ff_frame_pool_get_audio_config liteav_ff_frame_pool_get_audio_config
#define av_twofish_crypt liteav_av_twofish_crypt
#define av_sha512_alloc liteav_av_sha512_alloc
#define avio_close_dyn_buf liteav_avio_close_dyn_buf
#define ff_ac3_window liteav_ff_ac3_window
#define ff_avg_h264_qpel16_mc10_10_sse2 liteav_ff_avg_h264_qpel16_mc10_10_sse2
#define av_buffersrc_get_nb_failed_requests liteav_av_buffersrc_get_nb_failed_requests
#define ff_avg_h264_qpel8_mc10_10_sse2_cache64 liteav_ff_avg_h264_qpel8_mc10_10_sse2_cache64
#define ff_ilbc_at_decoder liteav_ff_ilbc_at_decoder
#define ff_pred8x8l_vertical_right_10_sse2 liteav_ff_pred8x8l_vertical_right_10_sse2
#define ff_hevc_transform_add_4x4_neon_8_asm liteav_ff_hevc_transform_add_4x4_neon_8_asm
#define av_ripemd_final liteav_av_ripemd_final
#define ff_get_cpu_max_align_aarch64 liteav_ff_get_cpu_max_align_aarch64
#define ff_ebur128_add_frames_float liteav_ff_ebur128_add_frames_float
#define ff_avg_qpel16_mc12_old_c liteav_ff_avg_qpel16_mc12_old_c
#define ff_sbr_neg_odd_64_neon liteav_ff_sbr_neg_odd_64_neon
#define ff_pred8x8l_vertical_8_mmxext liteav_ff_pred8x8l_vertical_8_mmxext
#define ff_slice_thread_execute_with_mainfunc liteav_ff_slice_thread_execute_with_mainfunc
#define av_hmac_free liteav_av_hmac_free
#define av_thread_message_flush liteav_av_thread_message_flush
#define ff_hevc_hls_residual_coding liteav_ff_hevc_hls_residual_coding
#define av_get_output_timestamp liteav_av_get_output_timestamp
#define ff_tns_max_bands_512 liteav_ff_tns_max_bands_512
#define ff_h264_idct_add_10_c liteav_ff_h264_idct_add_10_c
#define ff_pred8x8_vert_neon liteav_ff_pred8x8_vert_neon
#define ff_av1_filter_obus liteav_ff_av1_filter_obus
#define ff_framesync_init_dualinput liteav_ff_framesync_init_dualinput
#define ff_init_ff_sine_windows_fixed liteav_ff_init_ff_sine_windows_fixed
#define ff_h264_weight_16_sse2 liteav_ff_h264_weight_16_sse2
#define ff_free_filters liteav_ff_free_filters
#define av_d2str liteav_av_d2str
#define ff_pw_255 liteav_ff_pw_255
#define av_probe_input_buffer2 liteav_av_probe_input_buffer2
#define ff_pw_256 liteav_ff_pw_256
#define avfilter_transform liteav_avfilter_transform
#define ff_cos_8192_fixed liteav_ff_cos_8192_fixed
#define av_parse_time liteav_av_parse_time
#define ff_pack_2ch_int32_to_int32_u_sse2 liteav_ff_pack_2ch_int32_to_int32_u_sse2
#define ff_simple_idct_put_neon liteav_ff_simple_idct_put_neon
#define av_color_range_name liteav_av_color_range_name
#define rgb15to16 liteav_rgb15to16
#define ff_fft_permute_neon liteav_ff_fft_permute_neon
#define av_dv_frame_profile liteav_av_dv_frame_profile
#define ff_h264_idct8_add_9_c liteav_ff_h264_idct8_add_9_c
#define ff_avg_h264_qpel8_h_lowpass_l2_ssse3 liteav_ff_avg_h264_qpel8_h_lowpass_l2_ssse3
#define av_buffersink_get_sample_rate liteav_av_buffersink_get_sample_rate
#define ff_mpeg4_workaround_bugs liteav_ff_mpeg4_workaround_bugs
#define ff_pred16x16_left_dc_10_mmxext liteav_ff_pred16x16_left_dc_10_mmxext
#define ff_id3v2_free_extra_meta liteav_ff_id3v2_free_extra_meta
#define ff_pw_2048 liteav_ff_pw_2048
#define vlc_css_parser_ParseString liteav_vlc_css_parser_ParseString
#define ff_pred8x8l_down_left_8_sse2 liteav_ff_pred8x8l_down_left_8_sse2
#define ff_h264_idct_dc_add_8_mmxext liteav_ff_h264_idct_dc_add_8_mmxext
#define ff_unpack_2ch_int16_to_int16_u_ssse3 liteav_ff_unpack_2ch_int16_to_int16_u_ssse3
#define sws_scale liteav_sws_scale
#define av_parse_video_size liteav_av_parse_video_size
#define ff_hevc_sao_band_w8_neon_8 liteav_ff_hevc_sao_band_w8_neon_8
#define ff_nv21_to_bgra_neon liteav_ff_nv21_to_bgra_neon
#define ff_nv21_to_abgr_neon liteav_ff_nv21_to_abgr_neon
#define deinterleaveBytes liteav_deinterleaveBytes
#define ff_put_pixels8_l2_shift5_mmxext liteav_ff_put_pixels8_l2_shift5_mmxext
#define av_opt_is_set_to_default_by_name liteav_av_opt_is_set_to_default_by_name
#define swri_resample_dsp_aarch64_init liteav_swri_resample_dsp_aarch64_init
#define ff_avg_pixels8_xy2_neon liteav_ff_avg_pixels8_xy2_neon
#define ff_hscale_8_to_15_neon liteav_ff_hscale_8_to_15_neon
#define ff_avg_h264_qpel16_mc01_10_sse2 liteav_ff_avg_h264_qpel16_mc01_10_sse2
#define ff_put_h264_chroma_mc2_mmxext liteav_ff_put_h264_chroma_mc2_mmxext
#define ff_simple_idct_put_int16_10bit liteav_ff_simple_idct_put_int16_10bit
#define ff_put_no_rnd_qpel8_mc31_old_c liteav_ff_put_no_rnd_qpel8_mc31_old_c
#define ff_simple_idct_add_int16_10bit liteav_ff_simple_idct_add_int16_10bit
#define av_timecode_init liteav_av_timecode_init
#define av_frame_get_buffer liteav_av_frame_get_buffer
#define ff_int32_to_float_a_sse2 liteav_ff_int32_to_float_a_sse2
#define ff_ue_golomb_vlc_code liteav_ff_ue_golomb_vlc_code
#define ff_ac3_hearing_threshold_tab liteav_ff_ac3_hearing_threshold_tab
#define ff_put_h264_qpel4_h_lowpass_mmxext liteav_ff_put_h264_qpel4_h_lowpass_mmxext
#define ff_put_h264_qpel8_mc12_10_sse2 liteav_ff_put_h264_qpel8_mc12_10_sse2
#define ff_h264_idct_add8_422_12_c liteav_ff_h264_idct_add8_422_12_c
#define av_frame_set_best_effort_timestamp liteav_av_frame_set_best_effort_timestamp
#define ff_h263_inter_MCBPC_vlc liteav_ff_h263_inter_MCBPC_vlc
#define ff_w4_plus_w6_hi liteav_ff_w4_plus_w6_hi
#define ffio_geturlcontext liteav_ffio_geturlcontext
#define av_fifo_space liteav_av_fifo_space
#define ff_h264_idct_add8_422_10_c liteav_ff_h264_idct_add8_422_10_c
#define ff_vector_fmul_window_neon liteav_ff_vector_fmul_window_neon
#define ff_deblock_h_luma_10_sse2 liteav_ff_deblock_h_luma_10_sse2
#define av_xtea_le_crypt liteav_av_xtea_le_crypt
#define ff_cos_8192 liteav_ff_cos_8192
#define rgb24to15 liteav_rgb24to15
#define ff_mpeg_framesize_alloc liteav_ff_mpeg_framesize_alloc
#define ff_aac_eld_window_480 liteav_ff_aac_eld_window_480
#define av_frame_remove_side_data liteav_av_frame_remove_side_data
#define ff_hevc_put_qpel_uw_hv_neon_8 liteav_ff_hevc_put_qpel_uw_hv_neon_8
#define ff_h264_idct8_dc_add_9_c liteav_ff_h264_idct8_dc_add_9_c
#define ff_inlink_request_frame liteav_ff_inlink_request_frame
#define ff_hevc_put_pixels_w32_neon_8_asm liteav_ff_hevc_put_pixels_w32_neon_8_asm
#define ff_mpegts_demuxer liteav_ff_mpegts_demuxer
#define sws_get_class liteav_sws_get_class
#define av_buffersink_get_frame_flags liteav_av_buffersink_get_frame_flags
#define av_frame_get_channels liteav_av_frame_get_channels
#define avcodec_get_type liteav_avcodec_get_type
#define ff_pred8x8l_vertical_right_8_mmxext liteav_ff_pred8x8l_vertical_right_8_mmxext
#define ff_fft16_vfp liteav_ff_fft16_vfp
#define ff_log2_run liteav_ff_log2_run
#define av_chroma_location_name liteav_av_chroma_location_name
#define av_blowfish_init liteav_av_blowfish_init
#define ff_avg_h264_qpel8_mc33_neon liteav_ff_avg_h264_qpel8_mc33_neon
#define ff_deblock_v_chroma_8_avx liteav_ff_deblock_v_chroma_8_avx
#define ff_h264_idct_add8_9_c liteav_ff_h264_idct_add8_9_c
#define av_tea_alloc liteav_av_tea_alloc
#define av_strncasecmp liteav_av_strncasecmp
#define av_bsf_next liteav_av_bsf_next
#define rgb24to16 liteav_rgb24to16
#define ff_pw_32 liteav_ff_pw_32
#define ff_put_h264_qpel4_mc33_10_mmxext liteav_ff_put_h264_qpel4_mc33_10_mmxext
#define ff_ac3_slow_gain_tab liteav_ff_ac3_slow_gain_tab
#define ff_h264_filter_mb liteav_ff_h264_filter_mb
#define ff_mdct15_uninit liteav_ff_mdct15_uninit
#define ff_h264_loop_filter_strength_mmxext liteav_ff_h264_loop_filter_strength_mmxext
#define avpriv_set_systematic_pal2 liteav_avpriv_set_systematic_pal2
#define ff_avg_h264_qpel8_mc30_10_sse2 liteav_ff_avg_h264_qpel8_mc30_10_sse2
#define ff_framequeue_add liteav_ff_framequeue_add
#define ff_simple_idct_put_int16_8bit liteav_ff_simple_idct_put_int16_8bit
#define av_hash_get_size liteav_av_hash_get_size
#define ff_hevc_put_qpel_uw_weight_v2_neon_8 liteav_ff_hevc_put_qpel_uw_weight_v2_neon_8
#define av_twofish_alloc liteav_av_twofish_alloc
#define ff_put_h264_qpel4_mc31_10_mmxext liteav_ff_put_h264_qpel4_mc31_10_mmxext
#define av_buffersink_params_alloc liteav_av_buffersink_params_alloc
#define avformat_write_header liteav_avformat_write_header
#define av_reduce liteav_av_reduce
#define ff_set_qscale liteav_ff_set_qscale
#define ff_hevc_sao_band_filter_8_neon liteav_ff_hevc_sao_band_filter_8_neon
#define ff_mpadsp_apply_window_fixed_neon liteav_ff_mpadsp_apply_window_fixed_neon
#define ff_pred8x8l_vertical_right_8_ssse3 liteav_ff_pred8x8l_vertical_right_8_ssse3
#define ff_text_r8 liteav_ff_text_r8
#define avfilter_graph_dump liteav_avfilter_graph_dump
#define ff_put_h264_qpel8_mc33_neon liteav_ff_put_h264_qpel8_mc33_neon
#define ff_avg_h264_qpel4_mc31_10_mmxext liteav_ff_avg_h264_qpel4_mc31_10_mmxext
#define rgb48to64_bswap liteav_rgb48to64_bswap
#define swri_audio_convert_init_aarch64 liteav_swri_audio_convert_init_aarch64
#define ff_flac_sample_rate_table liteav_ff_flac_sample_rate_table
#define ff_hevc_pred_angular_8x8_neon_8 liteav_ff_hevc_pred_angular_8x8_neon_8
#define ff_hevc_put_epel_uw_pixels_w64_neon_8 liteav_ff_hevc_put_epel_uw_pixels_w64_neon_8
#define ff_flacdsp_init liteav_ff_flacdsp_init
#define ff_put_h264_chroma_mc4_neon liteav_ff_put_h264_chroma_mc4_neon
#define av_hex_dump liteav_av_hex_dump
#define avio_wl16 liteav_avio_wl16
#define ff_hevc_put_qpel_h_neon_8_wrapper liteav_ff_hevc_put_qpel_h_neon_8_wrapper
#define av_bsf_list_finalize liteav_av_bsf_list_finalize
#define av_bprint_escape liteav_av_bprint_escape
#define av_hwframe_ctx_create_derived liteav_av_hwframe_ctx_create_derived
#define ff_deblock_v_chroma_intra_10_avx liteav_ff_deblock_v_chroma_intra_10_avx
#define ff_videotoolbox_avcc_extradata_create liteav_ff_videotoolbox_avcc_extradata_create
#define ff_pred16x16_top_dc_10_mmxext liteav_ff_pred16x16_top_dc_10_mmxext
#define av_jni_get_java_vm liteav_av_jni_get_java_vm
#define ff_gif_encoder liteav_ff_gif_encoder
#define ff_riff_write_info_tag liteav_ff_riff_write_info_tag
#define av_interleaved_write_frame liteav_av_interleaved_write_frame
#define ff_h264_biweight_8_10_sse4 liteav_ff_h264_biweight_8_10_sse4
#define ff_h264_biweight_8_10_sse2 liteav_ff_h264_biweight_8_10_sse2
#define avcodec_decode_subtitle2 liteav_avcodec_decode_subtitle2
#define ff_hevc_put_qpel_h2_neon_8 liteav_ff_hevc_put_qpel_h2_neon_8
#define av_crc_init liteav_av_crc_init
#define ff_hevc_intra_chroma_pred_mode_decode liteav_ff_hevc_intra_chroma_pred_mode_decode
#define ff_put_h264_qpel16_mc10_neon liteav_ff_put_h264_qpel16_mc10_neon
#define ff_mpeg1_default_non_intra_matrix liteav_ff_mpeg1_default_non_intra_matrix
#define rgb15tobgr16 liteav_rgb15tobgr16
#define ff_mov_read_chan liteav_ff_mov_read_chan
#define rgb15tobgr15 liteav_rgb15tobgr15
#define ff_amf_get_string liteav_ff_amf_get_string
#define av_parse_color liteav_av_parse_color
#define ff_pixblockdsp_init liteav_ff_pixblockdsp_init
#define ff_pred16x16_tm_vp8_8_mmx liteav_ff_pred16x16_tm_vp8_8_mmx
#define ff_deblock_v_chroma_10_avx liteav_ff_deblock_v_chroma_10_avx
#define vlc_css_unquotedunescaped liteav_vlc_css_unquotedunescaped
#define ff_rdft_init liteav_ff_rdft_init
#define ff_hevc_put_epel_uw_v_neon_8 liteav_ff_hevc_put_epel_uw_v_neon_8
#define ff_lzw_encode_init liteav_ff_lzw_encode_init
#define avfilter_graph_get_filter liteav_avfilter_graph_get_filter
#define yypush_buffer_state liteav_yypush_buffer_state
#define ff_hevc_put_qpel_h3_neon_8 liteav_ff_hevc_put_qpel_h3_neon_8
#define av_match_ext liteav_av_match_ext
#define ff_int32_to_float_u_sse2 liteav_ff_int32_to_float_u_sse2
#define avio_check liteav_avio_check
#define ff_openssl_init liteav_ff_openssl_init
#define ff_simple_idct8_put_avx liteav_ff_simple_idct8_put_avx
#define avcodec_receive_frame liteav_avcodec_receive_frame
#define ff_id3v2_write_simple liteav_ff_id3v2_write_simple
#define ff_pred4x4_tm_vp8_8_mmx liteav_ff_pred4x4_tm_vp8_8_mmx
#define av_sha_update liteav_av_sha_update
#define av_demuxer_iterate liteav_av_demuxer_iterate
#define ff_h264_idct_add16_14_c liteav_ff_h264_idct_add16_14_c
#define ff_deblock_h_luma_10_avx liteav_ff_deblock_h_luma_10_avx
#define av_hwdevice_iterate_types liteav_av_hwdevice_iterate_types
#define ff_vector_fmul_neon liteav_ff_vector_fmul_neon
#define ff_avg_h264_qpel8_mc21_10_sse2 liteav_ff_avg_h264_qpel8_mc21_10_sse2
#define ff_mpeg4_decode_studio_slice_header liteav_ff_mpeg4_decode_studio_slice_header
#define avio_accept liteav_avio_accept
#define ff_put_h264_qpel4_mc23_10_mmxext liteav_ff_put_h264_qpel4_mc23_10_mmxext
#define ff_vsrc_buffer liteav_ff_vsrc_buffer
#define ff_log_net_error liteav_ff_log_net_error
#define ff_set_common_formats liteav_ff_set_common_formats
#define avpriv_ac3_channel_layout_tab liteav_avpriv_ac3_channel_layout_tab
#define av_timecode_make_string liteav_av_timecode_make_string
#define av_tree_node_alloc liteav_av_tree_node_alloc
#define av_frame_free liteav_av_frame_free
#define ff_h264_idct_add8_8_mmx liteav_ff_h264_idct_add8_8_mmx
#define ff_put_pixels16_x2_no_rnd_neon liteav_ff_put_pixels16_x2_no_rnd_neon
#define av_opt_set_q liteav_av_opt_set_q
#define ff_raw_audio_read_header liteav_ff_raw_audio_read_header
#define swri_noise_shaping_double liteav_swri_noise_shaping_double
#define ff_modified_quant_tab liteav_ff_modified_quant_tab
#define ff_pack_8ch_float_to_float_a_avx liteav_ff_pack_8ch_float_to_float_a_avx
#define ff_sws_init_range_convert liteav_ff_sws_init_range_convert
#define ff_cos_512 liteav_ff_cos_512
#define ff_sine_1024 liteav_ff_sine_1024
#define av_frame_get_sample_rate liteav_av_frame_get_sample_rate
#define ff_hevc_put_qpel_uw_pixels_w16_neon_8 liteav_ff_hevc_put_qpel_uw_pixels_w16_neon_8
#define ff_pred8x8l_128_dc_10_mmxext liteav_ff_pred8x8l_128_dc_10_mmxext
#define ff_h264_idct8_add4_10_c liteav_ff_h264_idct8_add4_10_c
#define ff_h264_free_tables liteav_ff_h264_free_tables
#define ff_mpeg1_find_frame_end liteav_ff_mpeg1_find_frame_end
#define ff_hevc_put_qpel_uw_weight_h2v3_neon_8 liteav_ff_hevc_put_qpel_uw_weight_h2v3_neon_8
#define ff_cos_128 liteav_ff_cos_128
#define av_hmac_update liteav_av_hmac_update
#define ff_se_golomb_vlc_code liteav_ff_se_golomb_vlc_code
#define av_get_channel_layout liteav_av_get_channel_layout
#define ff_hevc_put_qpel_uw_weight_h3v3_neon_8 liteav_ff_hevc_put_qpel_uw_weight_h3v3_neon_8
#define av_bprint_strftime liteav_av_bprint_strftime
#define avcodec_flush_buffers liteav_avcodec_flush_buffers
#define ff_mpeg4_default_non_intra_matrix liteav_ff_mpeg4_default_non_intra_matrix
#define vlc_css_declarations_Append liteav_vlc_css_declarations_Append
#define ff_sine_8192_fixed liteav_ff_sine_8192_fixed
#define ff_pred4x4_tm_vp8_8_ssse3 liteav_ff_pred4x4_tm_vp8_8_ssse3
#define ff_rtmp_calc_digest_pos liteav_ff_rtmp_calc_digest_pos
#define ff_mpv_frame_end liteav_ff_mpv_frame_end
#define ff_h264_idct8_add4_12_c liteav_ff_h264_idct8_add4_12_c
#define ff_reset_entries liteav_ff_reset_entries
#define avfilter_graph_request_oldest liteav_avfilter_graph_request_oldest
#define ff_socket liteav_ff_socket
#define ff_fdctdsp_init_x86 liteav_ff_fdctdsp_init_x86
#define ff_mpeg4_studio_dc_luma liteav_ff_mpeg4_studio_dc_luma
#define av_blowfish_alloc liteav_av_blowfish_alloc
#define ff_put_pixels8_xy2_neon liteav_ff_put_pixels8_xy2_neon
#define ff_pred16x16_plane_rv40_8_sse2 liteav_ff_pred16x16_plane_rv40_8_sse2
#define ff_hevc_sao_eo_class_decode liteav_ff_hevc_sao_eo_class_decode
#define av_bsf_get_null_filter liteav_av_bsf_get_null_filter
#define avio_get_str liteav_avio_get_str
#define av_packet_clone liteav_av_packet_clone
#define ff_hevc_put_pel_uw_pixels_w6_neon_8_asm liteav_ff_hevc_put_pel_uw_pixels_w6_neon_8_asm
#define yuyvtoyuv422 liteav_yuyvtoyuv422
#define yuyvtoyuv420 liteav_yuyvtoyuv420
#define ff_pack_2ch_int32_to_float_u_sse2 liteav_ff_pack_2ch_int32_to_float_u_sse2
#define yypop_buffer_state liteav_yypop_buffer_state
#define ff_h264_idct_add8_422_8_mmx liteav_ff_h264_idct_add8_422_8_mmx
#define ff_pred8x8l_horizontal_up_10_ssse3 liteav_ff_pred8x8l_horizontal_up_10_ssse3
#define rgb16to15 liteav_rgb16to15
#define ff_avg_h264_chroma_mc2_neon liteav_ff_avg_h264_chroma_mc2_neon
#define av_packet_pack_dictionary liteav_av_packet_pack_dictionary
#define av_basename liteav_av_basename
#define ff_sws_context_class liteav_ff_sws_context_class
#define ff_w4_min_w2_lo liteav_ff_w4_min_w2_lo
#define ff_channel_layouts_unref liteav_ff_channel_layouts_unref
#define vlc_css_parser_Init liteav_vlc_css_parser_Init
#define ff_put_no_rnd_qpel8_mc12_old_c liteav_ff_put_no_rnd_qpel8_mc12_old_c
#define ff_deblock_v_luma_8_avx liteav_ff_deblock_v_luma_8_avx
#define av_write_uncoded_frame_query liteav_av_write_uncoded_frame_query
#define ff_hevc_h_loop_filter_chroma_neon liteav_ff_hevc_h_loop_filter_chroma_neon
#define avio_printf liteav_avio_printf
#define av_parser_init liteav_av_parser_init
#define ff_cos_64_fixed liteav_ff_cos_64_fixed
#define avcodec_send_packet liteav_avcodec_send_packet
#define ff_put_no_rnd_qpel16_mc13_old_c liteav_ff_put_no_rnd_qpel16_mc13_old_c
#define ff_h263_format liteav_ff_h263_format
#define ff_cos_tabs liteav_ff_cos_tabs
#define ff_url_join liteav_ff_url_join
#define av_aes_ctr_increment_iv liteav_av_aes_ctr_increment_iv
#define ff_network_init liteav_ff_network_init
#define ff_avg_h264_chroma_mc4_neon liteav_ff_avg_h264_chroma_mc4_neon
#define ff_hevc_profiles liteav_ff_hevc_profiles
#define ff_thread_get_format liteav_ff_thread_get_format
#define ff_flac_is_extradata_valid liteav_ff_flac_is_extradata_valid
#define ff_ass_subtitle_header_default liteav_ff_ass_subtitle_header_default
#define ff_hevc_put_epel_uw_pixels_w4_neon_8 liteav_ff_hevc_put_epel_uw_pixels_w4_neon_8
#define ff_read_line_to_bprint_overwrite liteav_ff_read_line_to_bprint_overwrite
#define rgb15to24 liteav_rgb15to24
#define ff_h264_idct8_dc_add_10_sse2 liteav_ff_h264_idct8_dc_add_10_sse2
#define ff_avg_qpel8_mc32_old_c liteav_ff_avg_qpel8_mc32_old_c
#define yyget_text liteav_yyget_text
#define shuffle_bytes_1230 liteav_shuffle_bytes_1230
#define ff_decode_bsfs_init liteav_ff_decode_bsfs_init
#define ff_fft_end liteav_ff_fft_end
#define ff_start_tag liteav_ff_start_tag
#define ff_crcA001_update liteav_ff_crcA001_update
#define ff_sbr_hf_apply_noise_1_neon liteav_ff_sbr_hf_apply_noise_1_neon
#define av_realloc_f liteav_av_realloc_f
#define ff_pred8x8l_horizontal_10_sse2 liteav_ff_pred8x8l_horizontal_10_sse2
#define av_image_copy_plane liteav_av_image_copy_plane
#define ff_mp3adufloat_decoder liteav_ff_mp3adufloat_decoder
#define av_buffersink_get_sample_aspect_ratio liteav_av_buffersink_get_sample_aspect_ratio
#define ff_vp9_profiles liteav_ff_vp9_profiles
#define ff_overlay_init_x86 liteav_ff_overlay_init_x86
#define av_bprint_clear liteav_av_bprint_clear
#define av_get_pix_fmt_name liteav_av_get_pix_fmt_name
#define av_tx_uninit liteav_av_tx_uninit
#define ff_hevc_sao_band_filter_neon_8 liteav_ff_hevc_sao_band_filter_neon_8
#define av_opt_flag_is_set liteav_av_opt_flag_is_set
#define ff_aac_sbr_init liteav_ff_aac_sbr_init
#define ff_ps_hybrid_analysis_ileave_sse liteav_ff_ps_hybrid_analysis_ileave_sse
#define ff_h264_build_ref_list liteav_ff_h264_build_ref_list
#define ff_h264_idct_dc_add_8_c liteav_ff_h264_idct_dc_add_8_c
#define ff_h263_intra_MCBPC_vlc liteav_ff_h263_intra_MCBPC_vlc
#define av_md5_init liteav_av_md5_init
#define av_thread_message_queue_free liteav_av_thread_message_queue_free
#define av_dynarray_add_nofree liteav_av_dynarray_add_nofree
#define ff_psdsp_init liteav_ff_psdsp_init
#define ff_avg_h264_qpel8_mc20_10_sse2_cache64 liteav_ff_avg_h264_qpel8_mc20_10_sse2_cache64
#define av_match_list liteav_av_match_list
#define ff_mpeg12_frame_rate_tab liteav_ff_mpeg12_frame_rate_tab
#define ff_thread_await_progress liteav_ff_thread_await_progress
#define ff_put_h264_qpel8_mc10_neon liteav_ff_put_h264_qpel8_mc10_neon
#define ff_pred4x4_horizontal_up_10_mmxext liteav_ff_pred4x4_horizontal_up_10_mmxext
#define ff_float_to_int32_a_avx2 liteav_ff_float_to_int32_a_avx2
#define ff_ps_mul_pair_single_neon liteav_ff_ps_mul_pair_single_neon
#define ff_null_get_audio_buffer liteav_ff_null_get_audio_buffer
#define ff_init_ff_cos_tabs liteav_ff_init_ff_cos_tabs
#define ff_h264_idct8_add4_8_c liteav_ff_h264_idct8_add4_8_c
#define ff_cos_1024_fixed liteav_ff_cos_1024_fixed
#define ff_fdct248_islow_8 liteav_ff_fdct248_islow_8
#define av_buffersink_set_frame_size liteav_av_buffersink_set_frame_size
#define yyset_lval liteav_yyset_lval
#define ff_aac_kbd_short_128_fixed liteav_ff_aac_kbd_short_128_fixed
#define ff_avg_h264_qpel16_mc00_neon liteav_ff_avg_h264_qpel16_mc00_neon
#define avio_rl16 liteav_avio_rl16
#define ff_hevc_put_epel_uw_hv_neon_8 liteav_ff_hevc_put_epel_uw_hv_neon_8
#define ff_hevc_hls_filter liteav_ff_hevc_hls_filter
#define ff_aac_pow2sf_tab liteav_ff_aac_pow2sf_tab
#define av_buffersrc_close liteav_av_buffersrc_close
#define avcodec_receive_packet liteav_avcodec_receive_packet
#define ff_mpegvideo_parser liteav_ff_mpegvideo_parser
#define ff_hevc_put_qpel_uw_weight_h1v2_neon_8 liteav_ff_hevc_put_qpel_uw_weight_h1v2_neon_8
#define swr_next_pts liteav_swr_next_pts
#define av_get_sample_fmt_string liteav_av_get_sample_fmt_string
#define av_thread_message_queue_send liteav_av_thread_message_queue_send
#define ff_h264_idct_add16_12_c liteav_ff_h264_idct_add16_12_c
#define ff_h264_idct8_add_neon liteav_ff_h264_idct8_add_neon
#define ff_yuv2rgb_get_func_ptr liteav_ff_yuv2rgb_get_func_ptr
#define av_packet_ref liteav_av_packet_ref
#define ff_fdct_ifast248 liteav_ff_fdct_ifast248
#define ff_pw_18 liteav_ff_pw_18
#define av_opt_set_dict liteav_av_opt_set_dict
#define ff_hevc_put_pixels_w8_neon_8_asm liteav_ff_hevc_put_pixels_w8_neon_8_asm
#define ff_ps_read_data liteav_ff_ps_read_data
#define av_channel_layout_extract_channel liteav_av_channel_layout_extract_channel
#define av_encryption_info_clone liteav_av_encryption_info_clone
#define sws_allocVec liteav_sws_allocVec
#define ff_hevc_set_neighbour_available liteav_ff_hevc_set_neighbour_available
#define ff_yuv2planeX_8_neon liteav_ff_yuv2planeX_8_neon
#define ff_flac_blocksize_table liteav_ff_flac_blocksize_table
#define vlc_css_selector_Append liteav_vlc_css_selector_Append
#define ff_parse_mpeg2_descriptor liteav_ff_parse_mpeg2_descriptor
#define ffio_read_varlen liteav_ffio_read_varlen
#define ffio_read_size liteav_ffio_read_size
#define ff_accept liteav_ff_accept
#define ff_ebur128_add_frames_planar_float liteav_ff_ebur128_add_frames_planar_float
#define ff_draw_color liteav_ff_draw_color
#define ff_isom_get_vpcc_features liteav_ff_isom_get_vpcc_features
#define ff_framesync_preinit liteav_ff_framesync_preinit
#define ff_hevc_parser liteav_ff_hevc_parser
#define ff_pred8x8l_down_right_8_ssse3 liteav_ff_pred8x8l_down_right_8_ssse3
#define av_buffer_pool_init2 liteav_av_buffer_pool_init2
#define ff_hevc_put_qpel_uni_neon_wrapper liteav_ff_hevc_put_qpel_uni_neon_wrapper
#define ff_avg_h264_qpel8or16_hv2_lowpass_ssse3 liteav_ff_avg_h264_qpel8or16_hv2_lowpass_ssse3
#define ffurl_get_short_seek liteav_ffurl_get_short_seek
#define ff_pred16x16_vertical_8_sse liteav_ff_pred16x16_vertical_8_sse
#define ff_mb_type_b_tab liteav_ff_mb_type_b_tab
#define ff_h263_decode_motion liteav_ff_h263_decode_motion
#define ff_hevc_put_pixels_w4_neon_8_asm liteav_ff_hevc_put_pixels_w4_neon_8_asm
#define ff_free_picture_tables liteav_ff_free_picture_tables
#define av_timecode_make_smpte_tc_string liteav_av_timecode_make_smpte_tc_string
#define av_murmur3_alloc liteav_av_murmur3_alloc
#define ff_deblock_v_luma_10_avx liteav_ff_deblock_v_luma_10_avx
#define ff_mpeg12_vlc_dc_chroma_bits liteav_ff_mpeg12_vlc_dc_chroma_bits
#define ff_put_pixels8_x2_no_rnd_neon liteav_ff_put_pixels8_x2_no_rnd_neon
#define ff_simple_idct8_avx liteav_ff_simple_idct8_avx
#define ff_nv21_to_rgba_neon liteav_ff_nv21_to_rgba_neon
#define ff_h264_chroma_dc_dequant_idct_8_c liteav_ff_h264_chroma_dc_dequant_idct_8_c
#define ff_hevc_sao_band_w16_neon_8 liteav_ff_hevc_sao_band_w16_neon_8
#define av_hwdevice_ctx_create liteav_av_hwdevice_ctx_create
#define av_muxer_iterate liteav_av_muxer_iterate
#define ff_faandct248 liteav_ff_faandct248
#define ff_pack_2ch_int16_to_float_a_sse2 liteav_ff_pack_2ch_int16_to_float_a_sse2
#define ff_mov_read_esds liteav_ff_mov_read_esds
#define avformat_init_output liteav_avformat_init_output
#define av_strndup liteav_av_strndup
#define ff_simple_idct12_sse2 liteav_ff_simple_idct12_sse2
#define av_msg liteav_av_msg
#define ff_hevc_compute_poc liteav_ff_hevc_compute_poc
#define text_style_merge liteav_text_style_merge
#define av_strlcat liteav_av_strlcat
#define ff_h2645_packet_split liteav_ff_h2645_packet_split
#define ff_avg_h264_qpel16_mc23_10_sse2 liteav_ff_avg_h264_qpel16_mc23_10_sse2
#define av_buffer_realloc liteav_av_buffer_realloc
#define ff_ass_split_dialog liteav_ff_ass_split_dialog
#define ff_hevc_deblocking_boundary_strengths liteav_ff_hevc_deblocking_boundary_strengths
#define ff_w1_plus_w3_lo liteav_ff_w1_plus_w3_lo
#define ff_hevc_transform_8x8_neon_8_asm liteav_ff_hevc_transform_8x8_neon_8_asm
#define av_thread_message_queue_set_err_send liteav_av_thread_message_queue_set_err_send
#define av_log_get_flags liteav_av_log_get_flags
#define ff_get_format liteav_ff_get_format
#define ff_framesync_get_class liteav_ff_framesync_get_class
#define ff_h264_cabac_tables liteav_ff_h264_cabac_tables
#define ff_hevc_put_qpel_h3v3_neon_8 liteav_ff_hevc_put_qpel_h3v3_neon_8
#define ff_update_picture_tables liteav_ff_update_picture_tables
#define ff_w3_min_w7_lo liteav_ff_w3_min_w7_lo
#define av_bsf_init liteav_av_bsf_init
#define av_frame_set_colorspace liteav_av_frame_set_colorspace
#define ff_fdct248_islow_10 liteav_ff_fdct248_islow_10
#define ff_put_h264_qpel16_mc33_neon liteav_ff_put_h264_qpel16_mc33_neon
#define ff_ac3_log_add_tab liteav_ff_ac3_log_add_tab
#define ff_hevc_put_qpel_uw_weight_h2v1_neon_8 liteav_ff_hevc_put_qpel_uw_weight_h2v1_neon_8
#define ff_tns_max_bands_1024 liteav_ff_tns_max_bands_1024
#define ff_avg_h264_qpel8_mc31_10_sse2 liteav_ff_avg_h264_qpel8_mc31_10_sse2
#define ff_pred8x8l_vertical_right_8_sse2 liteav_ff_pred8x8l_vertical_right_8_sse2
#define av_bsf_alloc liteav_av_bsf_alloc
#define ff_h263_find_frame_end liteav_ff_h263_find_frame_end
#define ff_dither_8x8_32 liteav_ff_dither_8x8_32
#define ff_h264_weight_4_10_sse4 liteav_ff_h264_weight_4_10_sse4
#define avcodec_default_get_format liteav_avcodec_default_get_format
#define ff_biweight_h264_pixels_16_neon liteav_ff_biweight_h264_pixels_16_neon
#define ff_pred8x8_dc_rv40_8_mmxext liteav_ff_pred8x8_dc_rv40_8_mmxext
#define av_set_options_string liteav_av_set_options_string
#define ff_srt_demuxer liteav_ff_srt_demuxer
#define swri_oldapi_conv_flt_to_s16_neon liteav_swri_oldapi_conv_flt_to_s16_neon
#define ff_h264_idct8_add_14_c liteav_ff_h264_idct8_add_14_c
#define swri_dither_init liteav_swri_dither_init
#define ff_h264_pred_weight_table liteav_ff_h264_pred_weight_table
#define ff_h264_h_loop_filter_luma_neon liteav_ff_h264_h_loop_filter_luma_neon
#define ff_put_pixels4_l2_shift5_mmxext liteav_ff_put_pixels4_l2_shift5_mmxext
#define yydebug liteav_yydebug
#define av_packet_unref liteav_av_packet_unref
#define ff_hevc_put_qpel_uw_h3v3_neon_8 liteav_ff_hevc_put_qpel_uw_h3v3_neon_8
#define ff_pack_8ch_int32_to_float_a_avx liteav_ff_pack_8ch_int32_to_float_a_avx
#define av_spherical_projection_name liteav_av_spherical_projection_name
#define ff_flac_demuxer liteav_ff_flac_demuxer
#define ff_tls_protocol liteav_ff_tls_protocol
#define avcodec_find_encoder_by_name liteav_avcodec_find_encoder_by_name
#define ff_mpeg4_decode_partitions liteav_ff_mpeg4_decode_partitions
#define ff_put_no_rnd_qpel8_mc13_old_c liteav_ff_put_no_rnd_qpel8_mc13_old_c
#define av_bsf_send_packet liteav_av_bsf_send_packet
#define ff_ass_add_rect liteav_ff_ass_add_rect
#define ff_faandct liteav_ff_faandct
#define ff_put_h264_qpel8or16_hv1_lowpass_op_sse2 liteav_ff_put_h264_qpel8or16_hv1_lowpass_op_sse2
#define ff_avg_h264_qpel8_mc30_10_sse2_cache64 liteav_ff_avg_h264_qpel8_mc30_10_sse2_cache64
#define ff_alloc_dir_entry liteav_ff_alloc_dir_entry
#define ff_hevc_qpel_filters liteav_ff_hevc_qpel_filters
#define ff_mdct_win_fixed liteav_ff_mdct_win_fixed
#define ff_mov_write_packet liteav_ff_mov_write_packet
#define ff_sine_512 liteav_ff_sine_512
#define ff_rtmp_check_alloc_array liteav_ff_rtmp_check_alloc_array
#define av_image_check_size liteav_av_image_check_size
#define ff_pred8x8_plane_neon liteav_ff_pred8x8_plane_neon
#define ff_h264_weight_8_mmxext liteav_ff_h264_weight_8_mmxext
#define ff_aac_codebook_vector_vals liteav_ff_aac_codebook_vector_vals
#define ff_af_queue_init liteav_ff_af_queue_init
#define ff_pred8x8l_top_dc_8_ssse3 liteav_ff_pred8x8l_top_dc_8_ssse3
#define ff_swb_offset_512 liteav_ff_swb_offset_512
#define vlc_css_expression_AddTerm liteav_vlc_css_expression_AddTerm
#define sws_getContext liteav_sws_getContext
#define ff_h264_update_thread_context liteav_ff_h264_update_thread_context
#define ff_hevc_put_qpel_uw_weight_h1_neon_8 liteav_ff_hevc_put_qpel_uw_weight_h1_neon_8
#define avio_read_dir liteav_avio_read_dir
#define sws_printVec2 liteav_sws_printVec2
#define av_get_channel_layout_string liteav_av_get_channel_layout_string
#define av_audio_fifo_read liteav_av_audio_fifo_read
#define ff_put_h264_qpel4_mc02_10_mmxext liteav_ff_put_h264_qpel4_mc02_10_mmxext
#define ff_filter_activate liteav_ff_filter_activate
#define av_cpu_count liteav_av_cpu_count
#define ff_put_pixels8_y2_neon liteav_ff_put_pixels8_y2_neon
#define av_parser_iterate liteav_av_parser_iterate
#define ffio_ensure_seekback liteav_ffio_ensure_seekback
#define ff_lzw_encode_flush liteav_ff_lzw_encode_flush
#define ff_sbr_sum_square_neon liteav_ff_sbr_sum_square_neon
#define ff_pw_m1 liteav_ff_pw_m1
#define av_get_known_color_name liteav_av_get_known_color_name
#define ffio_get_checksum liteav_ffio_get_checksum
#define ff_put_h264_qpel16_mc01_neon liteav_ff_put_h264_qpel16_mc01_neon
#define ff_live_flv_demuxer liteav_ff_live_flv_demuxer
#define avpriv_get_raw_pix_fmt_tags liteav_avpriv_get_raw_pix_fmt_tags
#define ff_mpa_synth_window_float liteav_ff_mpa_synth_window_float
#define av_display_rotation_set liteav_av_display_rotation_set
#define ff_rgb24toyv12 liteav_ff_rgb24toyv12
#define av_hex_dump_log liteav_av_hex_dump_log
#define av_encryption_init_info_free liteav_av_encryption_init_info_free
#define ff_pred8x8l_vertical_8_ssse3 liteav_ff_pred8x8l_vertical_8_ssse3
#define av_find_nearest_q_idx liteav_av_find_nearest_q_idx
#define ff_put_h264_qpel16_mc10_10_sse2 liteav_ff_put_h264_qpel16_mc10_10_sse2
#define ff_hevc_put_qpel_uw_h3v2_neon_8 liteav_ff_hevc_put_qpel_uw_h3v2_neon_8
#define ff_avg_h264_qpel16_mc23_neon liteav_ff_avg_h264_qpel16_mc23_neon
#define av_dict_set_int liteav_av_dict_set_int
#define ff_h264_weight_16_10_sse4 liteav_ff_h264_weight_16_10_sse4
#define av_get_planar_sample_fmt liteav_av_get_planar_sample_fmt
#define ff_w3_min_w7_hi liteav_ff_w3_min_w7_hi
#define ff_sine_windows_fixed liteav_ff_sine_windows_fixed
#define ff_deblock_v_luma_intra_8_avx liteav_ff_deblock_v_luma_intra_8_avx
#define ff_yuv420p_to_rgba_neon liteav_ff_yuv420p_to_rgba_neon
#define av_packet_add_side_data liteav_av_packet_add_side_data
#define ff_unpack_6ch_float_to_float_a_avx liteav_ff_unpack_6ch_float_to_float_a_avx
#define avio_read_partial liteav_avio_read_partial
#define avpriv_dict_set_timestamp liteav_avpriv_dict_set_timestamp
#define ff_h263_pred_dc liteav_ff_h263_pred_dc
#define ff_hevc_put_qpel_neon_wrapper liteav_ff_hevc_put_qpel_neon_wrapper
#define ff_sine_4096_fixed liteav_ff_sine_4096_fixed
#define ff_id3v2_write_metadata liteav_ff_id3v2_write_metadata
#define av_pkt_dump_log2 liteav_av_pkt_dump_log2
#define ff_rtp_codec_id liteav_ff_rtp_codec_id
#define av_get_random_seed liteav_av_get_random_seed
#define av_opt_eval_int liteav_av_opt_eval_int
#define ff_alac_at_decoder liteav_ff_alac_at_decoder
#define ff_ac3_parse_header liteav_ff_ac3_parse_header
#define ff_avg_h264_qpel4_mc12_10_mmxext liteav_ff_avg_h264_qpel4_mc12_10_mmxext
#define ff_ass_decoder_flush liteav_ff_ass_decoder_flush
#define ff_hevc_skip_flag_decode liteav_ff_hevc_skip_flag_decode
#define avpriv_vga16_font liteav_avpriv_vga16_font
#define av_tx_init liteav_av_tx_init
#define ff_af_aresample liteav_ff_af_aresample
#define av_ripemd_update liteav_av_ripemd_update
#define ff_hevc_h_loop_filter_luma_neon liteav_ff_hevc_h_loop_filter_luma_neon
#define ff_raw_write_packet liteav_ff_raw_write_packet
#define ff_null_bsf liteav_ff_null_bsf
#define ff_jpeg_fdct_islow_8 liteav_ff_jpeg_fdct_islow_8
#define ff_h264_idct_add16intra_10_c liteav_ff_h264_idct_add16intra_10_c
#define ff_write_chained liteav_ff_write_chained
#define ffio_close_null_buf liteav_ffio_close_null_buf
#define ff_pred8x8_plane_8_sse2 liteav_ff_pred8x8_plane_8_sse2
#define ff_filter_frame liteav_ff_filter_frame
#define ff_filter_get_nb_threads liteav_ff_filter_get_nb_threads
#define ff_h263_decode_end liteav_ff_h263_decode_end
#define avpriv_cga_font liteav_avpriv_cga_font
#define ff_hevc_decode_short_term_rps liteav_ff_hevc_decode_short_term_rps
#define ff_pred8x8l_horizontal_8_ssse3 liteav_ff_pred8x8l_horizontal_8_ssse3
#define ff_aac_adtstoasc_bsf liteav_ff_aac_adtstoasc_bsf
#define ff_hevc_cu_qp_delta_sign_flag liteav_ff_hevc_cu_qp_delta_sign_flag
#define av_bprint_finalize liteav_av_bprint_finalize
#define ff_hevc_unref_frame liteav_ff_hevc_unref_frame
#define ff_mpegaudio_parser liteav_ff_mpegaudio_parser
#define ff_put_h264_qpel8_mc03_neon liteav_ff_put_h264_qpel8_mc03_neon
#define av_packet_make_writable liteav_av_packet_make_writable
#define av_force_cpu_flags liteav_av_force_cpu_flags
#define av_fast_realloc liteav_av_fast_realloc
#define ff_default_chroma_qscale_table liteav_ff_default_chroma_qscale_table
#define av_bsf_list_free liteav_av_bsf_list_free
#define av_frame_set_channels liteav_av_frame_set_channels
#define ff_put_h264_qpel16_mc30_neon liteav_ff_put_h264_qpel16_mc30_neon
#define ff_vorbis_channel_layouts liteav_ff_vorbis_channel_layouts
#define ff_cos_32768_fixed liteav_ff_cos_32768_fixed
#define ff_flv_muxer liteav_ff_flv_muxer
#define ff_hevc_idct_16x16_dc_neon_8 liteav_ff_hevc_idct_16x16_dc_neon_8
#define ff_h264_execute_decode_slices liteav_ff_h264_execute_decode_slices
#define ff_af_queue_remove liteav_ff_af_queue_remove
#define avpicture_alloc liteav_avpicture_alloc
#define ff_mpeg2_non_linear_qscale liteav_ff_mpeg2_non_linear_qscale
#define ff_mpegvideodsp_init liteav_ff_mpegvideodsp_init
#define ff_hevc_sao_edge_eo1_w64_neon_8 liteav_ff_hevc_sao_edge_eo1_w64_neon_8
#define interleaveBytes liteav_interleaveBytes
#define ff_avg_h264_qpel16_mc13_10_sse2 liteav_ff_avg_h264_qpel16_mc13_10_sse2
#define av_parser_next liteav_av_parser_next
#define ff_pred8x8_top_dc_10_sse2 liteav_ff_pred8x8_top_dc_10_sse2
#define ff_avg_h264_qpel4_mc11_10_mmxext liteav_ff_avg_h264_qpel4_mc11_10_mmxext
#define ff_wavpack_decoder liteav_ff_wavpack_decoder
#define avio_seek_time liteav_avio_seek_time
#define ff_hevc_add_residual_32x32_neon_8 liteav_ff_hevc_add_residual_32x32_neon_8
#define av_small_strptime liteav_av_small_strptime
#define ff_put_pixels16_y2_neon liteav_ff_put_pixels16_y2_neon
#define ff_hevc_put_epel_h_neon_8 liteav_ff_hevc_put_epel_h_neon_8
#define ff_imdct_calc_sse liteav_ff_imdct_calc_sse
#define av_picture_copy liteav_av_picture_copy
#define av_stereo3d_type_name liteav_av_stereo3d_type_name
#define av_frame_set_metadata liteav_av_frame_set_metadata
#define av_hwdevice_ctx_create_derived liteav_av_hwdevice_ctx_create_derived
#define av_sdp_create liteav_av_sdp_create
#define ff_mpeg4_intra_level liteav_ff_mpeg4_intra_level
#define ff_hevc_transform_32x32_neon_8 liteav_ff_hevc_transform_32x32_neon_8
#define ff_pred8x8_dc_10_mmxext liteav_ff_pred8x8_dc_10_mmxext
#define rgb15to32 liteav_rgb15to32
#define av_opt_set liteav_av_opt_set
#define ff_h264_luma_dc_dequant_idct_mmx liteav_ff_h264_luma_dc_dequant_idct_mmx
#define ff_avg_h264_qpel8_h_lowpass_l2_mmxext liteav_ff_avg_h264_qpel8_h_lowpass_l2_mmxext
#define avio_pause liteav_avio_pause
#define ff_fill_rgba_map liteav_ff_fill_rgba_map
#define ff_yuv420p_to_bgra_neon liteav_ff_yuv420p_to_bgra_neon
#define av_dict_get_string liteav_av_dict_get_string
#define ff_hcscale_fast_c liteav_ff_hcscale_fast_c
#define ff_is_multicast_address liteav_ff_is_multicast_address
#define ff_replaygain_export_raw liteav_ff_replaygain_export_raw
#define ff_fft_permute_sse liteav_ff_fft_permute_sse
#define ff_mba_max liteav_ff_mba_max
#define vlc_css_rules_Delete liteav_vlc_css_rules_Delete
#define ff_shuffle_bytes_3210_ssse3 liteav_ff_shuffle_bytes_3210_ssse3
#define ff_put_h264_qpel16_mc20_10_sse2 liteav_ff_put_h264_qpel16_mc20_10_sse2
#define ff_htmlmarkup_to_ass liteav_ff_htmlmarkup_to_ass
#define av_frame_get_color_range liteav_av_frame_get_color_range
#define ff_h263_pred_motion liteav_ff_h263_pred_motion
#define av_fifo_free liteav_av_fifo_free
#define ff_urlcontext_child_class_next liteav_ff_urlcontext_child_class_next
#define ff_avg_h264_qpel8_mc33_10_sse2 liteav_ff_avg_h264_qpel8_mc33_10_sse2
#define ff_pw_53 liteav_ff_pw_53
#define ff_h263_decode_mba liteav_ff_h263_decode_mba
#define ff_avg_h264_qpel4_hv_lowpass_v_mmxext liteav_ff_avg_h264_qpel4_hv_lowpass_v_mmxext
#define ff_sbr_sum64x5_neon liteav_ff_sbr_sum64x5_neon
#define av_samples_alloc_array_and_samples liteav_av_samples_alloc_array_and_samples
#define av_audio_fifo_realloc liteav_av_audio_fifo_realloc
#define ff_thread_release_buffer liteav_ff_thread_release_buffer
#define ff_pack_2ch_int32_to_int32_a_sse2 liteav_ff_pack_2ch_int32_to_int32_a_sse2
#define ff_hevc_put_qpel_uni_w_neon_8 liteav_ff_hevc_put_qpel_uni_w_neon_8
#define ff_hevc_pred_angular_8x8_h_neon_8 liteav_ff_hevc_pred_angular_8x8_h_neon_8
#define ff_mpv_export_qp_table liteav_ff_mpv_export_qp_table
#define vlc_css_unescaped liteav_vlc_css_unescaped
#define ff_avg_h264_qpel8_mc10_neon liteav_ff_avg_h264_qpel8_mc10_neon
#define av_rescale_q liteav_av_rescale_q
#define ff_psdsp_init_aarch64 liteav_ff_psdsp_init_aarch64
#define av_q2intfloat liteav_av_q2intfloat
#define ff_pred8x8l_down_left_10_sse2 liteav_ff_pred8x8l_down_left_10_sse2
#define ff_aac_at_decoder liteav_ff_aac_at_decoder
#define ff_filter_process_command liteav_ff_filter_process_command
#define ff_ass_split_free liteav_ff_ass_split_free
#define av_stristart liteav_av_stristart
#define ff_simple_idct8_put_sse2 liteav_ff_simple_idct8_put_sse2
#define ff_mp3_at_decoder liteav_ff_mp3_at_decoder
#define avcodec_find_best_pix_fmt_of_list liteav_avcodec_find_best_pix_fmt_of_list
#define ff_hevc_put_qpel_h1_neon_8 liteav_ff_hevc_put_qpel_h1_neon_8
#define ff_avg_h264_qpel16_mc20_10_sse2 liteav_ff_avg_h264_qpel16_mc20_10_sse2
#define avpriv_io_delete liteav_avpriv_io_delete
#define ff_h264_v_loop_filter_luma_neon liteav_ff_h264_v_loop_filter_luma_neon
#define ff_ebur128_add_frames_planar_short liteav_ff_ebur128_add_frames_planar_short
#define ff_mpeg4_decode_picture_header liteav_ff_mpeg4_decode_picture_header
#define av_buffersrc_write_frame liteav_av_buffersrc_write_frame
#define av_crc_get_table liteav_av_crc_get_table
#define ff_ebur128_set_channel liteav_ff_ebur128_set_channel
#define ff_dither_4x4_16 liteav_ff_dither_4x4_16
#define ff_framesync_get_frame liteav_ff_framesync_get_frame
#define ff_aac_ac3_parse liteav_ff_aac_ac3_parse
#define ff_vector_fmul_reverse_neon liteav_ff_vector_fmul_reverse_neon
#define ff_mpeg2_dc_scale_table liteav_ff_mpeg2_dc_scale_table
#define webvtt_parser_init liteav_webvtt_parser_init
#define sws_alloc_set_opts liteav_sws_alloc_set_opts
#define ff_text_peek_r8 liteav_ff_text_peek_r8
#define ff_framesync_configure liteav_ff_framesync_configure
#define ff_aac_parser liteav_ff_aac_parser
#define ff_hevc_put_qpel_h2v1_neon_8 liteav_ff_hevc_put_qpel_h2v1_neon_8
#define ff_put_h264_qpel8_mc20_10_ssse3_cache64 liteav_ff_put_h264_qpel8_mc20_10_ssse3_cache64
#define ff_hevc_transform_add_8x8_neon_8_asm liteav_ff_hevc_transform_add_8x8_neon_8_asm
#define ff_pred8x8_horizontal_8_ssse3 liteav_ff_pred8x8_horizontal_8_ssse3
#define ff_w4_plus_w6_lo liteav_ff_w4_plus_w6_lo
#define av_fifo_alloc_array liteav_av_fifo_alloc_array
#define ff_fft_end_fixed_32 liteav_ff_fft_end_fixed_32
#define ff_avg_pixels8_y2_neon liteav_ff_avg_pixels8_y2_neon
#define ff_init_vlc_sparse liteav_ff_init_vlc_sparse
#define ff_hevc_put_pel_uw_pixels_w24_neon_8_asm liteav_ff_hevc_put_pel_uw_pixels_w24_neon_8_asm
#define ff_sbr_qmf_deint_bfly_neon liteav_ff_sbr_qmf_deint_bfly_neon
#define sws_subVec liteav_sws_subVec
#define ff_ac3_channels_tab liteav_ff_ac3_channels_tab
#define avformat_get_riff_video_tags liteav_avformat_get_riff_video_tags
#define ff_faanidct_put liteav_ff_faanidct_put
#define ff_pred4x4_down_right_10_avx liteav_ff_pred4x4_down_right_10_avx
#define ff_put_h264_qpel4_h_lowpass_l2_mmxext liteav_ff_put_h264_qpel4_h_lowpass_l2_mmxext
#define ff_get_cpu_flags_aarch64 liteav_ff_get_cpu_flags_aarch64
#define ffurl_alloc liteav_ffurl_alloc
#define av_set_cpu_flags_mask liteav_av_set_cpu_flags_mask
#define ff_avg_h264_chroma_mc8_rnd_3dnow liteav_ff_avg_h264_chroma_mc8_rnd_3dnow
#define ff_merge_formats liteav_ff_merge_formats
#define ff_h264_p_sub_mb_type_info liteav_ff_h264_p_sub_mb_type_info
#define ff_mpeg1_decode_block_intra liteav_ff_mpeg1_decode_block_intra
#define yylex liteav_yylex
#define ff_sdp_write_media liteav_ff_sdp_write_media
#define ff_idctdsp_init_x86 liteav_ff_idctdsp_init_x86
#define ff_hevc_rem_intra_luma_pred_mode_decode liteav_ff_hevc_rem_intra_luma_pred_mode_decode
#define ff_aac_sbr_ctx_init liteav_ff_aac_sbr_ctx_init
#define ff_put_h264_qpel16_mc02_10_sse2 liteav_ff_put_h264_qpel16_mc02_10_sse2
#define ff_mpeg12_mbAddrIncrTable liteav_ff_mpeg12_mbAddrIncrTable
#define av_get_cpu_flags liteav_av_get_cpu_flags
#define ff_avg_h264_qpel8_mc11_10_sse2 liteav_ff_avg_h264_qpel8_mc11_10_sse2
#define ff_eac3_default_cpl_band_struct liteav_ff_eac3_default_cpl_band_struct
#define ff_h264_decode_init_vlc liteav_ff_h264_decode_init_vlc
#define ff_frame_pool_audio_init liteav_ff_frame_pool_audio_init
#define ff_tlog_ref liteav_ff_tlog_ref
#define ff_w4_plus_w2_lo liteav_ff_w4_plus_w2_lo
#define ff_h264_quant_div6 liteav_ff_h264_quant_div6
#define ff_get_guid liteav_ff_get_guid
#define ff_pack_2ch_float_to_int16_a_sse2 liteav_ff_pack_2ch_float_to_int16_a_sse2
#define avcodec_send_frame liteav_avcodec_send_frame
#define ff_hevc_pred_angular_32x32_h_zero_neon_8 liteav_ff_hevc_pred_angular_32x32_h_zero_neon_8
#define ff_put_h264_qpel16_mc30_10_sse2 liteav_ff_put_h264_qpel16_mc30_10_sse2
#define ff_h264_init_poc liteav_ff_h264_init_poc
#define avfilter_graph_set_auto_convert liteav_avfilter_graph_set_auto_convert
#define ff_adts_header_parse liteav_ff_adts_header_parse
#define ff_h264dsp_init liteav_ff_h264dsp_init
#define ff_jref_idct_add liteav_ff_jref_idct_add
#define ff_bswapdsp_init liteav_ff_bswapdsp_init
#define av_des_crypt liteav_av_des_crypt
#define ff_put_h264_qpel16_mc00_10_sse2 liteav_ff_put_h264_qpel16_mc00_10_sse2
#define ff_deblock_v_chroma_8_mmxext liteav_ff_deblock_v_chroma_8_mmxext
#define rgb48tobgr64_bswap liteav_rgb48tobgr64_bswap
#define ff_yuv2rgb_coeffs liteav_ff_yuv2rgb_coeffs
#define ff_hevc_pred_planar_4x4_neon_8_1 liteav_ff_hevc_pred_planar_4x4_neon_8_1
#define av_samples_get_buffer_size liteav_av_samples_get_buffer_size
#define ff_pred16x16_plane_svq3_8_ssse3 liteav_ff_pred16x16_plane_svq3_8_ssse3
#define ff_filter_set_ready liteav_ff_filter_set_ready
#define avcodec_find_best_pix_fmt2 liteav_avcodec_find_best_pix_fmt2
#define ff_w5_min_w1 liteav_ff_w5_min_w1
#define ff_mpeg4_intra_vlc liteav_ff_mpeg4_intra_vlc
#define ff_metadata_conv_ctx liteav_ff_metadata_conv_ctx
#define ff_raw_pix_fmt_tags liteav_ff_raw_pix_fmt_tags
#define ff_avg_h264_qpel16_mc33_10_sse2 liteav_ff_avg_h264_qpel16_mc33_10_sse2
#define ff_framequeue_init liteav_ff_framequeue_init
#define ff_mdct_calc_c liteav_ff_mdct_calc_c
#define ff_h264qpel_init_x86 liteav_ff_h264qpel_init_x86
#define ff_pred8x8_left_dc_neon liteav_ff_pred8x8_left_dc_neon
#define ff_hevc_pred_angular_4x4_neon_8 liteav_ff_hevc_pred_angular_4x4_neon_8
#define ff_unpack_6ch_float_to_int32_u_sse2 liteav_ff_unpack_6ch_float_to_int32_u_sse2
#define ff_mov_cenc_free liteav_ff_mov_cenc_free
#define ff_text_pos liteav_ff_text_pos
#define ff_int16_to_int32_u_sse2 liteav_ff_int16_to_int32_u_sse2
#define av_opt_get_sample_fmt liteav_av_opt_get_sample_fmt
#define swr_set_channel_mapping liteav_swr_set_channel_mapping
#define av_hwdevice_hwconfig_alloc liteav_av_hwdevice_hwconfig_alloc
#define ff_hevc_sao_band_position_decode liteav_ff_hevc_sao_band_position_decode
#define ff_unpack_2ch_int16_to_float_u_ssse3 liteav_ff_unpack_2ch_int16_to_float_u_ssse3
#define av_content_light_metadata_create_side_data liteav_av_content_light_metadata_create_side_data
#define ff_lzw_encode_state_size liteav_ff_lzw_encode_state_size
#define ff_mpv_decode_init liteav_ff_mpv_decode_init
#define ff_mov_demuxer liteav_ff_mov_demuxer
#define ff_cos_32_fixed liteav_ff_cos_32_fixed
#define ffio_init_checksum liteav_ffio_init_checksum
#define ff_h264_idct_add_8_mmx liteav_ff_h264_idct_add_8_mmx
#define ff_h264_profiles liteav_ff_h264_profiles
#define ffurl_context_class liteav_ffurl_context_class
#define ff_ebur128_loudness_shortterm liteav_ff_ebur128_loudness_shortterm
#define ff_framequeue_global_init liteav_ff_framequeue_global_init
#define ff_latm_muxer liteav_ff_latm_muxer
#define av_hwframe_ctx_init liteav_av_hwframe_ctx_init
#define ff_qdmc_at_decoder liteav_ff_qdmc_at_decoder
#define ff_avg_h264_qpel16_mc20_10_sse2_cache64 liteav_ff_avg_h264_qpel16_mc20_10_sse2_cache64
#define yy_switch_to_buffer liteav_yy_switch_to_buffer
#define ff_thread_await_progress2 liteav_ff_thread_await_progress2
#define avpriv_mpa_bitrate_tab liteav_avpriv_mpa_bitrate_tab
#define ff_avg_h264_qpel8_mc32_10_sse2 liteav_ff_avg_h264_qpel8_mc32_10_sse2
#define ff_mpeg4audio_get_config_gb liteav_ff_mpeg4audio_get_config_gb
#define ff_hevc_put_qpel_uw_weight_v1_neon_8 liteav_ff_hevc_put_qpel_uw_weight_v1_neon_8
#define av_fopen_utf8 liteav_av_fopen_utf8
#define ff_fft_calc_sse liteav_ff_fft_calc_sse
#define ff_h264_parser liteav_ff_h264_parser
#define ff_aac_sbr_ctx_close liteav_ff_aac_sbr_ctx_close
#define ff_avg_h264_qpel8_mc23_neon liteav_ff_avg_h264_qpel8_mc23_neon
#define rgb16to32 liteav_rgb16to32
#define ff_shuffle_bytes_3012_ssse3 liteav_ff_shuffle_bytes_3012_ssse3
#define av_sha512_size liteav_av_sha512_size
#define ff_pred16x16_plane_rv40_8_ssse3 liteav_ff_pred16x16_plane_rv40_8_ssse3
#define ff_h264_queue_decode_slice liteav_ff_h264_queue_decode_slice
#define ff_weight_h264_pixels_8_neon liteav_ff_weight_h264_pixels_8_neon
#define ff_pred8x8l_horizontal_up_8_ssse3 liteav_ff_pred8x8l_horizontal_up_8_ssse3
#define av_packet_split_side_data liteav_av_packet_split_side_data
#define ff_put_h264_qpel8_mc00_10_sse2 liteav_ff_put_h264_qpel8_mc00_10_sse2
#define av_color_space_from_name liteav_av_color_space_from_name
#define ff_nv12_to_rgba_neon liteav_ff_nv12_to_rgba_neon
#define ff_put_h264_qpel8_mc12_neon liteav_ff_put_h264_qpel8_mc12_neon
#define ff_mdct_calc_neon liteav_ff_mdct_calc_neon
#define ff_init_desc_no_chr liteav_ff_init_desc_no_chr
#define ff_unpack_2ch_int16_to_int16_a_ssse3 liteav_ff_unpack_2ch_int16_to_int16_a_ssse3
#define ff_hevc_put_qpel_bi_w_neon_8 liteav_ff_hevc_put_qpel_bi_w_neon_8
#define ff_init_slice_from_src liteav_ff_init_slice_from_src
#define ff_mpa_synth_window_fixed liteav_ff_mpa_synth_window_fixed
#define ff_all_channel_counts liteav_ff_all_channel_counts
#define ff_me_cmp_init liteav_ff_me_cmp_init
#define ff_pred4x4_dc_8_mmxext liteav_ff_pred4x4_dc_8_mmxext
#define av_opt_set_double liteav_av_opt_set_double
#define av_hash_init liteav_av_hash_init
#define ff_weight_h264_pixels_4_neon liteav_ff_weight_h264_pixels_4_neon
#define av_fifo_realloc2 liteav_av_fifo_realloc2
#define ff_fft_end_fixed liteav_ff_fft_end_fixed
#define ff_amf_write_number liteav_ff_amf_write_number
#define ff_sbr_qmf_deint_neg_neon liteav_ff_sbr_qmf_deint_neg_neon
#define ff_poll_frame liteav_ff_poll_frame
#define av_codec_iterate liteav_av_codec_iterate
#define ff_unpack_6ch_int32_to_float_u_sse2 liteav_ff_unpack_6ch_int32_to_float_u_sse2
#define ff_mpeg4audio_channels liteav_ff_mpeg4audio_channels
#define ff_hevc_sao_edge_filter_8_neon liteav_ff_hevc_sao_edge_filter_8_neon
#define ff_h263_hwaccel_pixfmt_list_420 liteav_ff_h263_hwaccel_pixfmt_list_420
#define av_buffersink_get_channel_layout liteav_av_buffersink_get_channel_layout
#define av_buffer_alloc liteav_av_buffer_alloc
#define yyget_leng liteav_yyget_leng
#define av_buffer_pool_get liteav_av_buffer_pool_get
#define ff_pred16x16_plane_svq3_8_sse2 liteav_ff_pred16x16_plane_svq3_8_sse2
#define ff_hevc_decode_nal_sei liteav_ff_hevc_decode_nal_sei
#define ff_pack_6ch_int32_to_float_a_sse2 liteav_ff_pack_6ch_int32_to_float_a_sse2
#define av_stereo3d_from_name liteav_av_stereo3d_from_name
#define ff_guess_image2_codec liteav_ff_guess_image2_codec
#define ff_mba_length liteav_ff_mba_length
#define ff_id3v2_picture_types liteav_ff_id3v2_picture_types
#define ff_pred8x8l_128_dc_10_sse2 liteav_ff_pred8x8l_128_dc_10_sse2
#define avio_read_to_bprint liteav_avio_read_to_bprint
#define ff_decode_frame_props liteav_ff_decode_frame_props
#define av_encryption_info_get_side_data liteav_av_encryption_info_get_side_data
#define ff_pack_6ch_float_to_float_a_avx liteav_ff_pack_6ch_float_to_float_a_avx
#define ff_hevc_pred_angular_16x16_h_zero_neon_8 liteav_ff_hevc_pred_angular_16x16_h_zero_neon_8
#define avpriv_get_gamma_from_trc liteav_avpriv_get_gamma_from_trc
#define av_fifo_drain liteav_av_fifo_drain
#define ff_inter_level liteav_ff_inter_level
#define ff_xvid_idct_init liteav_ff_xvid_idct_init
#define av_vorbis_parse_frame liteav_av_vorbis_parse_frame
#define ff_h264_flush_change liteav_ff_h264_flush_change
#define ff_h264_idct_dc_add_8_avx liteav_ff_h264_idct_dc_add_8_avx
#define ff_dither_8x8_73 liteav_ff_dither_8x8_73
#define ff_hevc_put_qpel_uw_h2v2_neon_8 liteav_ff_hevc_put_qpel_uw_h2v2_neon_8
#define ff_id3v2_finish liteav_ff_id3v2_finish
#define av_buffersink_get_frame_rate liteav_av_buffersink_get_frame_rate
#define ff_ps_add_squares_sse liteav_ff_ps_add_squares_sse
#define ff_aac_eld_window_512 liteav_ff_aac_eld_window_512
#define ff_mov_cenc_init liteav_ff_mov_cenc_init
#define av_expr_parse_and_eval liteav_av_expr_parse_and_eval
#define ff_af_queue_close liteav_ff_af_queue_close
#define av_bitstream_filter_init liteav_av_bitstream_filter_init
#define ff_put_h264_qpel16_mc32_10_sse2 liteav_ff_put_h264_qpel16_mc32_10_sse2
#define av_color_primaries_from_name liteav_av_color_primaries_from_name
#define ff_pred16x16_left_dc_10_sse2 liteav_ff_pred16x16_left_dc_10_sse2
#define ff_hevc_sao_merge_flag_decode liteav_ff_hevc_sao_merge_flag_decode
#define ff_avg_h264_qpel4_mc01_10_mmxext liteav_ff_avg_h264_qpel4_mc01_10_mmxext
#define av_init_packet liteav_av_init_packet
#define ff_cos_64 liteav_ff_cos_64
#define avpicture_fill liteav_avpicture_fill
#define swri_get_dither liteav_swri_get_dither
#define av_frame_is_writable liteav_av_frame_is_writable
#define ff_pred8x8l_horizontal_10_ssse3 liteav_ff_pred8x8l_horizontal_10_ssse3
#define ff_hevc_put_qpel_uw_weight_h2v2_neon_8 liteav_ff_hevc_put_qpel_uw_weight_h2v2_neon_8
#define ff_ac3_fast_gain_tab liteav_ff_ac3_fast_gain_tab
#define ffio_rewind_with_probe_data liteav_ffio_rewind_with_probe_data
#define ff_unpack_2ch_float_to_int16_u_sse2 liteav_ff_unpack_2ch_float_to_int16_u_sse2
#define ff_h264_idct_add16_8_c liteav_ff_h264_idct_add16_8_c
#define ff_sbr_qmf_post_shuffle_neon liteav_ff_sbr_qmf_post_shuffle_neon
#define ff_h264_chroma422_dc_dequant_idct_8_c liteav_ff_h264_chroma422_dc_dequant_idct_8_c
#define ff_ps_ctx_init liteav_ff_ps_ctx_init
#define ff_rtmpt_protocol liteav_ff_rtmpt_protocol
#define ff_h264_biweight_8_ssse3 liteav_ff_h264_biweight_8_ssse3
#define ff_put_no_rnd_qpel16_mc33_old_c liteav_ff_put_no_rnd_qpel16_mc33_old_c
#define ff_pred8x8_tm_vp8_8_mmxext liteav_ff_pred8x8_tm_vp8_8_mmxext
#define ff_put_h264_chroma_mc8_rnd_ssse3 liteav_ff_put_h264_chroma_mc8_rnd_ssse3
#define avpriv_h264_has_num_reorder_frames liteav_avpriv_h264_has_num_reorder_frames
#define ff_vorbis_vwin liteav_ff_vorbis_vwin
#define ff_put_h264_qpel8or16_v_lowpass_op_mmxext liteav_ff_put_h264_qpel8or16_v_lowpass_op_mmxext
#define ff_hevc_transform_4x4_neon_8_asm liteav_ff_hevc_transform_4x4_neon_8_asm
#define ff_mpa_alloc_tables liteav_ff_mpa_alloc_tables
#define ff_hevc_split_coding_unit_flag_decode liteav_ff_hevc_split_coding_unit_flag_decode
#define ff_channel_layouts_ref liteav_ff_channel_layouts_ref
#define ff_mdct_end_fixed liteav_ff_mdct_end_fixed
#define ff_gmc_c liteav_ff_gmc_c
#define ff_pred8x8_l0t_dc_neon liteav_ff_pred8x8_l0t_dc_neon
#define ff_pw_42 liteav_ff_pw_42
#define avcodec_dct_get_class liteav_avcodec_dct_get_class
#define ff_h263p_decoder liteav_ff_h263p_decoder
#define ff_sine_window_init_fixed liteav_ff_sine_window_init_fixed
#define ff_w3_min_w1_hi liteav_ff_w3_min_w1_hi
#define ff_hevc_get_ref_list liteav_ff_hevc_get_ref_list
#define av_hmac_final liteav_av_hmac_final
#define av_vorbis_parse_frame_flags liteav_av_vorbis_parse_frame_flags
#define ff_h264_golomb_to_pict_type liteav_ff_h264_golomb_to_pict_type
#define ff_h264_pred_init_x86 liteav_ff_h264_pred_init_x86
#define av_tea_size liteav_av_tea_size
#define av_display_matrix_flip liteav_av_display_matrix_flip
#define avfilter_init_str liteav_avfilter_init_str
#define ff_ass_style_get liteav_ff_ass_style_get
#define av_md5_alloc liteav_av_md5_alloc
#define rgb48tobgr48_bswap liteav_rgb48tobgr48_bswap
#define ff_avg_h264_qpel16_mc13_neon liteav_ff_avg_h264_qpel16_mc13_neon
#define ff_rtp_enc_name liteav_ff_rtp_enc_name
#define ff_mpadsp_init_aarch64 liteav_ff_mpadsp_init_aarch64
#define ff_avg_pixels4_mmx liteav_ff_avg_pixels4_mmx
#define av_bsf_list_append liteav_av_bsf_list_append
#define av_vorbis_parse_free liteav_av_vorbis_parse_free
#define swri_noise_shaping_int16 liteav_swri_noise_shaping_int16
#define av_mallocz liteav_av_mallocz
#define ff_cpu_xgetbv liteav_ff_cpu_xgetbv
#define ff_pred8x8_128_dc_neon liteav_ff_pred8x8_128_dc_neon
#define ff_hevc_v_loop_filter_luma_neon liteav_ff_hevc_v_loop_filter_luma_neon
#define ff_pack_6ch_float_to_float_u_mmx liteav_ff_pack_6ch_float_to_float_u_mmx
#define ff_hevc_idct_16x16_dc_neon_8_asm liteav_ff_hevc_idct_16x16_dc_neon_8_asm
#define avio_wl64 liteav_avio_wl64
#define vlc_css_parser_Clean liteav_vlc_css_parser_Clean
#define av_videotoolbox_alloc_context liteav_av_videotoolbox_alloc_context
#define ff_h264_idct_add16intra_neon liteav_ff_h264_idct_add16intra_neon
#define ff_pred8x8l_vertical_10_sse2 liteav_ff_pred8x8l_vertical_10_sse2
#define av_escape liteav_av_escape
#define ff_draw_horiz_band liteav_ff_draw_horiz_band
#define ff_hevc_put_qpel_bi_neon_wrapper liteav_ff_hevc_put_qpel_bi_neon_wrapper
#define ff_mpeg_er_init liteav_ff_mpeg_er_init
#define ff_hevc_hls_filters liteav_ff_hevc_hls_filters
#define av_freep liteav_av_freep
#define ff_pred4x4_vertical_left_10_sse2 liteav_ff_pred4x4_vertical_left_10_sse2
#define av_tempfile liteav_av_tempfile
#define ff_ps_add_squares_sse3 liteav_ff_ps_add_squares_sse3
#define ff_ape_write_tag liteav_ff_ape_write_tag
#define ff_pred8x8_tm_vp8_8_mmx liteav_ff_pred8x8_tm_vp8_8_mmx
#define ff_avg_h264_qpel16_mc12_neon liteav_ff_avg_h264_qpel16_mc12_neon
#define ff_hevc_cbf_cb_cr_decode liteav_ff_hevc_cbf_cb_cr_decode
#define ff_pred4x4_vertical_right_10_avx liteav_ff_pred4x4_vertical_right_10_avx
#define ff_h264_idct_add16_neon liteav_ff_h264_idct_add16_neon
#define ff_mdct_init_fixed liteav_ff_mdct_init_fixed
#define ff_put_pixels16x16_c liteav_ff_put_pixels16x16_c
#define ff_hevc_put_pixels_w64_neon_8 liteav_ff_hevc_put_pixels_w64_neon_8
#define ff_simple_idct12_put_sse2 liteav_ff_simple_idct12_put_sse2
#define av_asprintf liteav_av_asprintf
#define ff_dither_8x8_220 liteav_ff_dither_8x8_220
#define av_dict_get liteav_av_dict_get
#define ff_h264_idct8_add4_neon liteav_ff_h264_idct8_add4_neon
#define ff_hevc_pred_planar_32x32_neon_8 liteav_ff_hevc_pred_planar_32x32_neon_8
#define avpriv_alloc_fixed_dsp liteav_avpriv_alloc_fixed_dsp
#define ff_hevc_sao_edge_filter_neon_8 liteav_ff_hevc_sao_edge_filter_neon_8
#define ff_put_h264_qpel8_mc22_neon liteav_ff_put_h264_qpel8_mc22_neon
#define ff_http_do_new_request liteav_ff_http_do_new_request
#define ff_pred4x4_down_right_10_ssse3 liteav_ff_pred4x4_down_right_10_ssse3
#define ff_pcm_bluray_decoder liteav_ff_pcm_bluray_decoder
#define ff_aac_num_swb_480 liteav_ff_aac_num_swb_480
#define ff_put_qpel16_mc33_old_c liteav_ff_put_qpel16_mc33_old_c
#define ff_put_h264_qpel8_mc20_10_sse2_cache64 liteav_ff_put_h264_qpel8_mc20_10_sse2_cache64
#define ff_hevc_put_qpel_uw_weight_h1v3_neon_8 liteav_ff_hevc_put_qpel_uw_weight_h1v3_neon_8
#define ff_sine_32 liteav_ff_sine_32
#define av_log2_16bit liteav_av_log2_16bit
#define avio_write liteav_avio_write
#define rgb16tobgr16 liteav_rgb16tobgr16
#define ff_hevc_sao_edge_eo2_w32_neon_8 liteav_ff_hevc_sao_edge_eo2_w32_neon_8
#define ff_simple_idct10_put_sse2 liteav_ff_simple_idct10_put_sse2
#define ff_mpeg_er_frame_start liteav_ff_mpeg_er_frame_start
#define yylex_init liteav_yylex_init
#define ff_rtp_chain_mux_open liteav_ff_rtp_chain_mux_open
#define ff_h264_idct_add_9_c liteav_ff_h264_idct_add_9_c
#define ff_deblock_v_chroma_intra_8_sse2 liteav_ff_deblock_v_chroma_intra_8_sse2
#define ff_pred16x16_plane_svq3_8_mmx liteav_ff_pred16x16_plane_svq3_8_mmx
#define ff_h264_idct8_add_10_sse2 liteav_ff_h264_idct8_add_10_sse2
#define ffurl_write liteav_ffurl_write
#define ff_avg_h264_qpel8_mc12_10_sse2 liteav_ff_avg_h264_qpel8_mc12_10_sse2
#define av_opt_get liteav_av_opt_get
#define ff_qpeldsp_init_x86 liteav_ff_qpeldsp_init_x86
#define ff_hevc_pred_angular_16x16_neon_8 liteav_ff_hevc_pred_angular_16x16_neon_8
#define ff_avg_h264_qpel16_mc11_10_sse2 liteav_ff_avg_h264_qpel16_mc11_10_sse2
#define ff_rtp_get_codec_info liteav_ff_rtp_get_codec_info
#define ff_butterflies_float_vfp liteav_ff_butterflies_float_vfp
#define ff_hevc_put_qpel_uw_bi_v_neon_8 liteav_ff_hevc_put_qpel_uw_bi_v_neon_8
#define ff_resample_common_apply_filter_x4_s16_neon liteav_ff_resample_common_apply_filter_x4_s16_neon
#define ff_j_rev_dct2 liteav_ff_j_rev_dct2
#define av_videotoolbox_default_init liteav_av_videotoolbox_default_init
#define av_fft_end liteav_av_fft_end
#define ff_set_cmp liteav_ff_set_cmp
#define ff_sine_960 liteav_ff_sine_960
#define ff_hevc_transform_add_32x32_neon_8_asm liteav_ff_hevc_transform_add_32x32_neon_8_asm
#define ff_avg_h264_chroma_mc4_ssse3 liteav_ff_avg_h264_chroma_mc4_ssse3
#define ff_unpack_2ch_float_to_int32_u_sse2 liteav_ff_unpack_2ch_float_to_int32_u_sse2
#define ff_avg_qpel16_mc13_old_c liteav_ff_avg_qpel16_mc13_old_c
#define ff_avg_pixels8_l2_shift5_mmxext liteav_ff_avg_pixels8_l2_shift5_mmxext
#define ff_pred8x8l_down_right_8_sse2 liteav_ff_pred8x8l_down_right_8_sse2
#define avcodec_encode_video2 liteav_avcodec_encode_video2
#define ff_pred4x4_vertical_right_10_sse2 liteav_ff_pred4x4_vertical_right_10_sse2
#define rgb24tobgr32 liteav_rgb24tobgr32
#define ff_sine_1024_fixed liteav_ff_sine_1024_fixed
#define ff_avg_h264_qpel4_mc22_10_mmxext liteav_ff_avg_h264_qpel4_mc22_10_mmxext
#define av_mdct_calc liteav_av_mdct_calc
#define swscale_license liteav_swscale_license
#define ff_ass_split liteav_ff_ass_split
#define ff_pred8x8l_down_right_10_avx liteav_ff_pred8x8l_down_right_10_avx
#define ff_avg_h264_qpel4_hv_lowpass_h_mmxext liteav_ff_avg_h264_qpel4_hv_lowpass_h_mmxext
#define av_i2int liteav_av_i2int
#define ff_pred4x4_horizontal_down_10_sse2 liteav_ff_pred4x4_horizontal_down_10_sse2
#define ff_avg_h264_qpel8_mc30_neon liteav_ff_avg_h264_qpel8_mc30_neon
#define av_frame_set_pkt_size liteav_av_frame_set_pkt_size
#define ff_cos_4096_fixed liteav_ff_cos_4096_fixed
#define ff_put_h264_chroma_mc2_10_mmxext liteav_ff_put_h264_chroma_mc2_10_mmxext
#define av_strlcatf liteav_av_strlcatf
#define ff_mpeg1_aspect liteav_ff_mpeg1_aspect
#define av_strcasecmp liteav_av_strcasecmp
#define ff_id3v2_34_metadata_conv liteav_ff_id3v2_34_metadata_conv
#define ff_thread_report_progress2 liteav_ff_thread_report_progress2
#define ff_simple_idct8_add_sse2 liteav_ff_simple_idct8_add_sse2
#define avcodec_default_get_buffer2 liteav_avcodec_default_get_buffer2
#define ff_mpv_common_defaults liteav_ff_mpv_common_defaults
#define ff_pred8x8l_down_right_10_sse2 liteav_ff_pred8x8l_down_right_10_sse2
#define ff_ps_neg liteav_ff_ps_neg
#define ff_pack_2ch_int32_to_float_a_sse2 liteav_ff_pack_2ch_int32_to_float_a_sse2
#define ff_pcm_dvd_decoder liteav_ff_pcm_dvd_decoder
#define ff_unpack_2ch_int16_to_float_u_sse2 liteav_ff_unpack_2ch_int16_to_float_u_sse2
#define ff_pred8x8l_vertical_right_10_ssse3 liteav_ff_pred8x8l_vertical_right_10_ssse3
#define av_opt_get_key_value liteav_av_opt_get_key_value
#define rgb16to24 liteav_rgb16to24
#define ff_aac_kbd_short_128 liteav_ff_aac_kbd_short_128
#define ff_hevc_split_transform_flag_decode liteav_ff_hevc_split_transform_flag_decode
#define ff_init_vscale_pfn liteav_ff_init_vscale_pfn
#define ff_hevc_pred_angular_32x32_neon_8 liteav_ff_hevc_pred_angular_32x32_neon_8
#define ff_pack_6ch_int32_to_float_u_avx liteav_ff_pack_6ch_int32_to_float_u_avx
#define ff_pred16x16_hor_neon liteav_ff_pred16x16_hor_neon
#define av_default_item_name liteav_av_default_item_name
#define ff_h263_intra_MCBPC_bits liteav_ff_h263_intra_MCBPC_bits
#define av_timegm liteav_av_timegm
#define ff_pred8x8l_top_dc_10_avx liteav_ff_pred8x8l_top_dc_10_avx
#define ff_h264_idct_add16intra_10_sse2 liteav_ff_h264_idct_add16intra_10_sse2
#define ff_h264_ref_picture liteav_ff_h264_ref_picture
#define ff_mp1_at_decoder liteav_ff_mp1_at_decoder
#define av_buffer_get_ref_count liteav_av_buffer_get_ref_count
#define ff_rawvideo_options liteav_ff_rawvideo_options
#define ff_parse_sample_format liteav_ff_parse_sample_format
#define ff_ac3_fast_decay_tab liteav_ff_ac3_fast_decay_tab
#define ff_avg_h264_qpel8_mc12_neon liteav_ff_avg_h264_qpel8_mc12_neon
#define av_spherical_tile_bounds liteav_av_spherical_tile_bounds
#define av_fifo_size liteav_av_fifo_size
#define ff_avg_h264_qpel16_mc03_10_sse2 liteav_ff_avg_h264_qpel16_mc03_10_sse2
#define ff_avc_write_annexb_extradata liteav_ff_avc_write_annexb_extradata
#define av_buffer_pool_init liteav_av_buffer_pool_init
#define av_shrink_packet liteav_av_shrink_packet
#define ff_sine_512_fixed liteav_ff_sine_512_fixed
#define swr_inject_silence liteav_swr_inject_silence
#define ff_pred8x8l_vertical_right_10_avx liteav_ff_pred8x8l_vertical_right_10_avx
#define ff_hevc_transform_32x32_neon_8_asm liteav_ff_hevc_transform_32x32_neon_8_asm
#define ff_hevc_cu_chroma_qp_offset_flag liteav_ff_hevc_cu_chroma_qp_offset_flag
#define ff_put_h264_qpel16_mc33_10_sse2 liteav_ff_put_h264_qpel16_mc33_10_sse2
#define ff_hevc_pred_angular_8x8_h_zero_neon_8 liteav_ff_hevc_pred_angular_8x8_h_zero_neon_8
#define ff_m4v_demuxer liteav_ff_m4v_demuxer
#define ff_hevc_sao_edge_eo3_w64_neon_8 liteav_ff_hevc_sao_edge_eo3_w64_neon_8
#define av_opt_set_video_rate liteav_av_opt_set_video_rate
#define ff_vorbis_codec liteav_ff_vorbis_codec
#define ff_h264_idct_add8_8_sse2 liteav_ff_h264_idct_add8_8_sse2
#define ff_mdct_calc_c_fixed liteav_ff_mdct_calc_c_fixed
#define ff_avg_h264_qpel8_mc02_10_sse2 liteav_ff_avg_h264_qpel8_mc02_10_sse2
#define avpriv_put_string liteav_avpriv_put_string
#define ff_h264_idct8_add4_8_sse2 liteav_ff_h264_idct8_add4_8_sse2
#define av_sha_size liteav_av_sha_size
#define ff_id3v2_mime_tags liteav_ff_id3v2_mime_tags
#define ff_init_mpadsp_tabs_fixed liteav_ff_init_mpadsp_tabs_fixed
#define ff_put_h264_qpel8_mc32_10_sse2 liteav_ff_put_h264_qpel8_mc32_10_sse2
#define av_dict_copy liteav_av_dict_copy
#define ff_pred8x8l_vertical_left_8_sse2 liteav_ff_pred8x8l_vertical_left_8_sse2
#define ff_kbd_window_init_fixed liteav_ff_kbd_window_init_fixed
#define avfilter_link_get_channels liteav_avfilter_link_get_channels
#define ff_command_queue_pop liteav_ff_command_queue_pop
#define ff_hevc_put_epel_uw_pixels_w32_neon_8 liteav_ff_hevc_put_epel_uw_pixels_w32_neon_8
#define ff_imdct_half_vfp liteav_ff_imdct_half_vfp
#define ff_put_h264_qpel8_h_lowpass_l2_mmxext liteav_ff_put_h264_qpel8_h_lowpass_l2_mmxext
#define ff_rtmp_packet_destroy liteav_ff_rtmp_packet_destroy
#define ff_mpeg4_dc_threshold liteav_ff_mpeg4_dc_threshold
#define ff_hevc_transform_4x4_neon_8 liteav_ff_hevc_transform_4x4_neon_8
#define av_adler32_update liteav_av_adler32_update
#define swresample_version liteav_swresample_version
#define ff_avg_qpel8_mc33_old_c liteav_ff_avg_qpel8_mc33_old_c
#define ff_update_duplicate_context liteav_ff_update_duplicate_context
#define ff_h264_check_intra_pred_mode liteav_ff_h264_check_intra_pred_mode
#define av_frame_get_decode_error_flags liteav_av_frame_get_decode_error_flags
#define ff_inlink_queued_samples liteav_ff_inlink_queued_samples
#define ff_avg_h264_qpel8_mc13_10_sse2 liteav_ff_avg_h264_qpel8_mc13_10_sse2
#define ff_init_desc_cfmt_convert liteav_ff_init_desc_cfmt_convert
#define av_rescale_rnd liteav_av_rescale_rnd
#define av_hwframe_ctx_alloc liteav_av_hwframe_ctx_alloc
#define ff_find_unused_picture liteav_ff_find_unused_picture
#define swr_build_matrix liteav_swr_build_matrix
#define ff_simple_idct10_put_avx liteav_ff_simple_idct10_put_avx
#define ff_alloc_picture liteav_ff_alloc_picture
#define ff_cos_16 liteav_ff_cos_16
#define avpicture_free liteav_avpicture_free
#define ff_put_no_rnd_qpel16_mc11_old_c liteav_ff_put_no_rnd_qpel16_mc11_old_c
#define av_hwdevice_get_type_name liteav_av_hwdevice_get_type_name
#define ff_rvlc_rl_intra liteav_ff_rvlc_rl_intra
#define av_log2 liteav_av_log2
#define ff_pred16x16_plane_neon liteav_ff_pred16x16_plane_neon
#define ff_avg_pixels16x16_c liteav_ff_avg_pixels16x16_c
#define ff_check_h264_startcode liteav_ff_check_h264_startcode
#define ff_aac_num_swb_1024 liteav_ff_aac_num_swb_1024
#define ff_mov_iso639_to_lang liteav_ff_mov_iso639_to_lang
#define ff_pred8x8_l00_dc_neon liteav_ff_pred8x8_l00_dc_neon
#define ff_af_volume liteav_ff_af_volume
#define ff_put_pixels16_x2_neon liteav_ff_put_pixels16_x2_neon
#define ff_pb_80 liteav_ff_pb_80
#define ff_mpeg4_studio_dc_chroma liteav_ff_mpeg4_studio_dc_chroma
#define ffurl_accept liteav_ffurl_accept
#define ff_vorbis_encoding_channel_layout_offsets liteav_ff_vorbis_encoding_channel_layout_offsets
#define ff_pred16x16_plane_svq3_8_mmxext liteav_ff_pred16x16_plane_svq3_8_mmxext
#define ff_put_vc1_chroma_mc8_nornd_ssse3 liteav_ff_put_vc1_chroma_mc8_nornd_ssse3
#define ff_avg_h264_qpel8_mc00_10_sse2 liteav_ff_avg_h264_qpel8_mc00_10_sse2
#define ff_avg_h264_qpel16_mc10_10_ssse3_cache64 liteav_ff_avg_h264_qpel16_mc10_10_ssse3_cache64
#define av_samples_copy liteav_av_samples_copy
#define ff_text_read liteav_ff_text_read
#define avio_close liteav_avio_close
#define ff_init_block_index liteav_ff_init_block_index
#define ff_put_h264_qpel16_mc12_10_sse2 liteav_ff_put_h264_qpel16_mc12_10_sse2
#define ff_mov_lang_to_iso639 liteav_ff_mov_lang_to_iso639
#define ff_avg_h264_chroma_mc2_10_mmxext liteav_ff_avg_h264_chroma_mc2_10_mmxext
#define ff_put_h264_qpel4_mc13_10_mmxext liteav_ff_put_h264_qpel4_mc13_10_mmxext
#define yy_flush_buffer liteav_yy_flush_buffer
#define av_dict_set liteav_av_dict_set
#define ff_pred4x4_horizontal_down_10_avx liteav_ff_pred4x4_horizontal_down_10_avx
#define vlc_css_expression_Delete liteav_vlc_css_expression_Delete
#define av_twofish_size liteav_av_twofish_size
#define ff_put_pixels8_l2_8 liteav_ff_put_pixels8_l2_8
#define ff_imdct36_blocks_float liteav_ff_imdct36_blocks_float
#define ff_h263_decode_frame liteav_ff_h263_decode_frame
#define ff_pw_1024 liteav_ff_pw_1024
#define ff_hevc_cu_transquant_bypass_flag_decode liteav_ff_hevc_cu_transquant_bypass_flag_decode
#define ff_h264_idct8_add4_10_avx liteav_ff_h264_idct8_add4_10_avx
#define av_mediacodec_render_buffer_at_time liteav_av_mediacodec_render_buffer_at_time
#define ff_pack_8ch_float_to_int32_a_avx liteav_ff_pack_8ch_float_to_int32_a_avx
#define ff_mpeg12_vlc_dc_chroma_code liteav_ff_mpeg12_vlc_dc_chroma_code
#define ff_flac_decode_frame_header liteav_ff_flac_decode_frame_header
#define ff_id3v2_start liteav_ff_id3v2_start
#define ff_put_h264_qpel16_mc22_neon liteav_ff_put_h264_qpel16_mc22_neon
#define ff_put_h264_qpel8_mc31_neon liteav_ff_put_h264_qpel8_mc31_neon
#define ff_pred16x16_128_dc_10_sse2 liteav_ff_pred16x16_128_dc_10_sse2
#define ff_avg_h264_qpel8_h_lowpass_ssse3 liteav_ff_avg_h264_qpel8_h_lowpass_ssse3
#define av_default_get_category liteav_av_default_get_category
#define ff_pack_6ch_float_to_int32_u_sse2 liteav_ff_pack_6ch_float_to_int32_u_sse2
#define ff_cos_1024 liteav_ff_cos_1024
#define ff_crcEDB88320_update liteav_ff_crcEDB88320_update
#define yyalloc liteav_yyalloc
#define ff_hevc_parse_sps liteav_ff_hevc_parse_sps
#define ff_avg_h264_qpel8_mc01_10_sse2 liteav_ff_avg_h264_qpel8_mc01_10_sse2
#define ff_merge_channel_layouts liteav_ff_merge_channel_layouts
#define av_hwframe_transfer_data liteav_av_hwframe_transfer_data
#define ff_all_formats liteav_ff_all_formats
#define ff_h264_weight_16_10_sse2 liteav_ff_h264_weight_16_10_sse2
#define ff_mdct15_init liteav_ff_mdct15_init
#define av_thread_message_queue_recv liteav_av_thread_message_queue_recv
#define ff_avg_h264_qpel16_mc30_10_ssse3_cache64 liteav_ff_avg_h264_qpel16_mc30_10_ssse3_cache64
#define av_samples_alloc liteav_av_samples_alloc
#define ff_http_protocol liteav_ff_http_protocol
#define avio_closep liteav_avio_closep
#define ff_add_channel_layout liteav_ff_add_channel_layout
#define ff_h264_hl_decode_mb liteav_ff_h264_hl_decode_mb
#define ff_hevc_put_epel_uw_pixels_w24_neon_8 liteav_ff_hevc_put_epel_uw_pixels_w24_neon_8
#define ff_dca_profiles liteav_ff_dca_profiles
#define ff_deblock_h_chroma_10_sse2 liteav_ff_deblock_h_chroma_10_sse2
#define ff_avg_h264_qpel16_mc32_neon liteav_ff_avg_h264_qpel16_mc32_neon
#define avfilter_link_set_closed liteav_avfilter_link_set_closed
#define ff_pred8x8l_down_left_8_mmxext liteav_ff_pred8x8l_down_left_8_mmxext
#define avpriv_slicethread_create liteav_avpriv_slicethread_create
#define ff_put_h264_chroma_mc4_mmx liteav_ff_put_h264_chroma_mc4_mmx
#define ff_ac3_parser liteav_ff_ac3_parser
#define ff_uyvytoyuv422_sse2 liteav_ff_uyvytoyuv422_sse2
#define av_opt_get_video_rate liteav_av_opt_get_video_rate
#define ffio_fdopen liteav_ffio_fdopen
#define avfilter_register liteav_avfilter_register
#define ff_init_ff_cos_tabs_fixed liteav_ff_init_ff_cos_tabs_fixed
#define yyget_lval liteav_yyget_lval
#define av_file_unmap liteav_av_file_unmap
#define ff_hevc_sao_type_idx_decode liteav_ff_hevc_sao_type_idx_decode
#define ff_hevc_idct_4x4_dc_neon_8_asm liteav_ff_hevc_idct_4x4_dc_neon_8_asm
#define av_compare_mod liteav_av_compare_mod
#define av_realloc liteav_av_realloc
#define yyset_debug liteav_yyset_debug
#define av_fifo_generic_read liteav_av_fifo_generic_read
#define avio_put_str16be liteav_avio_put_str16be
#define ff_ebur128_add_frames_planar_int liteav_ff_ebur128_add_frames_planar_int
#define avfilter_graph_config liteav_avfilter_graph_config
#define ff_mpa_synth_init_float liteav_ff_mpa_synth_init_float
#define av_md5_update liteav_av_md5_update
#define ff_h264_idct_add8_10_sse2 liteav_ff_h264_idct_add8_10_sse2
#define av_cast5_init liteav_av_cast5_init
#define ff_imdct_calc_c liteav_ff_imdct_calc_c
#define ff_resample_common_apply_filter_x8_s16_neon liteav_ff_resample_common_apply_filter_x8_s16_neon
#define ff_unpack_2ch_int16_to_int32_a_ssse3 liteav_ff_unpack_2ch_int16_to_int32_a_ssse3
#define ff_put_pixels8_xy2_no_rnd_neon liteav_ff_put_pixels8_xy2_no_rnd_neon
#define ff_bsf_child_class_next liteav_ff_bsf_child_class_next
#define av_xtea_alloc liteav_av_xtea_alloc
#define ff_pcm_alaw_at_decoder liteav_ff_pcm_alaw_at_decoder
#define av_fifo_grow liteav_av_fifo_grow
#define ff_biweight_h264_pixels_8_neon liteav_ff_biweight_h264_pixels_8_neon
#define av_image_fill_black liteav_av_image_fill_black
#define av_sha512_init liteav_av_sha512_init
#define ff_avg_h264_qpel16_mc20_10_ssse3_cache64 liteav_ff_avg_h264_qpel16_mc20_10_ssse3_cache64
#define ff_hevc_save_states liteav_ff_hevc_save_states
#define ff_mdct_init liteav_ff_mdct_init
#define ff_put_h264_qpel8_mc30_10_sse2_cache64 liteav_ff_put_h264_qpel8_mc30_10_sse2_cache64
#define rgb16tobgr24 liteav_rgb16tobgr24
#define av_tree_enumerate liteav_av_tree_enumerate
#define swscale_version liteav_swscale_version
#define ff_sbr_hf_apply_noise_2_neon liteav_ff_sbr_hf_apply_noise_2_neon
#define ff_slice_thread_init liteav_ff_slice_thread_init
#define av_dict_parse_string liteav_av_dict_parse_string
#define ff_fixed_dsp_init_x86 liteav_ff_fixed_dsp_init_x86
#define ff_ps_stereo_interpolate_sse3 liteav_ff_ps_stereo_interpolate_sse3
#define av_buffer_get_opaque liteav_av_buffer_get_opaque
#define ff_pack_2ch_int16_to_int32_a_sse2 liteav_ff_pack_2ch_int16_to_int32_a_sse2
#define ff_imdct_calc_c_fixed_32 liteav_ff_imdct_calc_c_fixed_32
#define av_base64_decode liteav_av_base64_decode
#define av_reallocp liteav_av_reallocp
#define av_jni_set_java_vm liteav_av_jni_set_java_vm
#define ff_cos_256 liteav_ff_cos_256
#define ff_h263dsp_init liteav_ff_h263dsp_init
#define ff_pack_2ch_float_to_int32_u_sse2 liteav_ff_pack_2ch_float_to_int32_u_sse2
#define ff_rl_free liteav_ff_rl_free
#define ff_h264_chroma_dc_scan liteav_ff_h264_chroma_dc_scan
#define av_packet_copy_props liteav_av_packet_copy_props
#define yyget_lineno liteav_yyget_lineno
#define ff_pred8x8l_horizontal_up_8_mmxext liteav_ff_pred8x8l_horizontal_up_8_mmxext
#define ff_h264_weight_8_sse2 liteav_ff_h264_weight_8_sse2
#define ff_hevc_idct_4x4_dc_neon_8 liteav_ff_hevc_idct_4x4_dc_neon_8
#define ff_ebur128_loudness_global liteav_ff_ebur128_loudness_global
#define ff_j_rev_dct liteav_ff_j_rev_dct
#define ff_pred16x16_horizontal_10_sse2 liteav_ff_pred16x16_horizontal_10_sse2
#define ff_hevc_put_epel_h_neon_8_wrapper liteav_ff_hevc_put_epel_h_neon_8_wrapper
#define yy_scan_string liteav_yy_scan_string
#define ff_end_tag liteav_ff_end_tag
#define rgb24tobgr24 liteav_rgb24tobgr24
#define avcodec_find_decoder liteav_avcodec_find_decoder
#define av_dict_count liteav_av_dict_count
#define sws_convertPalette8ToPacked32 liteav_sws_convertPalette8ToPacked32
#define ff_hevc_mpm_idx_decode liteav_ff_hevc_mpm_idx_decode
#define ff_put_pixels16_y2_no_rnd_neon liteav_ff_put_pixels16_y2_no_rnd_neon
#define ff_avg_h264_chroma_mc8_10_avx liteav_ff_avg_h264_chroma_mc8_10_avx
#define av_pkt_dump2 liteav_av_pkt_dump2
#define ff_put_h264_qpel8_mc01_10_sse2 liteav_ff_put_h264_qpel8_mc01_10_sse2
#define av_buffersink_get_format liteav_av_buffersink_get_format
#define avfilter_next liteav_avfilter_next
#define ff_hwcontext_type_videotoolbox liteav_ff_hwcontext_type_videotoolbox
#define ff_combine_frame liteav_ff_combine_frame
#define ff_dnxhd_profiles liteav_ff_dnxhd_profiles
#define ff_id3v1_read liteav_ff_id3v1_read
#define ff_hevc_clear_refs liteav_ff_hevc_clear_refs
#define ff_pred8x8_plane_8_ssse3 liteav_ff_pred8x8_plane_8_ssse3
#define av_timecode_make_mpeg_tc_string liteav_av_timecode_make_mpeg_tc_string
#define ff_ass_bprint_text_event liteav_ff_ass_bprint_text_event
#define av_log_format_line2 liteav_av_log_format_line2
#define ff_h264_idct_add16intra_8_mmx liteav_ff_h264_idct_add16intra_8_mmx
#define ff_uyvytoyuv422_avx liteav_ff_uyvytoyuv422_avx
#define ff_inter_run liteav_ff_inter_run
#define ff_id3v2_parse_apic liteav_ff_id3v2_parse_apic
#define ff_deblock_h_chroma422_intra_8_avx liteav_ff_deblock_h_chroma422_intra_8_avx
#define ff_formats_ref liteav_ff_formats_ref
#define ff_rtmpe_protocol liteav_ff_rtmpe_protocol
#define ff_mov_cenc_write_stbl_atoms liteav_ff_mov_cenc_write_stbl_atoms
#define ff_faanidct liteav_ff_faanidct
#define av_aes_ctr_alloc liteav_av_aes_ctr_alloc
#define ff_put_rv40_chroma_mc4_mmx liteav_ff_put_rv40_chroma_mc4_mmx
#define ff_h264_ps_uninit liteav_ff_h264_ps_uninit
#define sws_normalizeVec liteav_sws_normalizeVec
#define ff_h264_chroma_qp liteav_ff_h264_chroma_qp
#define av_vorbis_parse_init liteav_av_vorbis_parse_init
#define ff_h264_sei_uninit liteav_ff_h264_sei_uninit
#define ff_pred8x8_horizontal_8_mmxext liteav_ff_pred8x8_horizontal_8_mmxext
#define ff_hevc_put_qpel_uw_pixels_w64_neon_8 liteav_ff_hevc_put_qpel_uw_pixels_w64_neon_8
#define ff_avg_rv40_chroma_mc8_3dnow liteav_ff_avg_rv40_chroma_mc8_3dnow
#define ff_videotoolbox_uninit liteav_ff_videotoolbox_uninit
#define ff_vorbis_floor1_inverse_db_table liteav_ff_vorbis_floor1_inverse_db_table
#define ff_cos_256_fixed liteav_ff_cos_256_fixed
#define ff_inverse liteav_ff_inverse
#define avpriv_mpegts_parse_packet liteav_avpriv_mpegts_parse_packet
#define ff_hevc_annexb2mp4_buf liteav_ff_hevc_annexb2mp4_buf
#define ff_amf_get_field_value liteav_ff_amf_get_field_value
#define ff_pred16x16_128_dc_10_mmxext liteav_ff_pred16x16_128_dc_10_mmxext
#define ff_pred8x8_horizontal_10_sse2 liteav_ff_pred8x8_horizontal_10_sse2
#define ff_hevc_pred_planar_16x16_neon_8_1 liteav_ff_hevc_pred_planar_16x16_neon_8_1
#define ff_hevc_transform_luma_4x4_neon_8 liteav_ff_hevc_transform_luma_4x4_neon_8
#define av_expr_parse liteav_av_expr_parse
#define ff_mpegtsraw_demuxer liteav_ff_mpegtsraw_demuxer
#define ff_put_qpel8_mc33_old_c liteav_ff_put_qpel8_mc33_old_c
#define av_crc liteav_av_crc
#define ff_hevc_demuxer liteav_ff_hevc_demuxer
#define ff_hevc_put_qpel_uw_v1_neon_8 liteav_ff_hevc_put_qpel_uw_v1_neon_8
#define av_opt_set_from_string liteav_av_opt_set_from_string
#define ff_http_auth_create_response liteav_ff_http_auth_create_response
#define vlc_css_rule_New liteav_vlc_css_rule_New
#define avfilter_pad_get_name liteav_avfilter_pad_get_name
#define ff_hevc_set_qPy liteav_ff_hevc_set_qPy
#define av_picture_pad liteav_av_picture_pad
#define ff_yuv420p_to_abgr_neon liteav_ff_yuv420p_to_abgr_neon
#define av_probe_input_format2 liteav_av_probe_input_format2
#define ff_vector_fmac_scalar_neon liteav_ff_vector_fmac_scalar_neon
#define av_frame_clone liteav_av_frame_clone
#define ff_pred16x16_dc_8_mmxext liteav_ff_pred16x16_dc_8_mmxext
#define ff_mov_cenc_write_packet liteav_ff_mov_cenc_write_packet
#define ff_h264_decode_ref_pic_list_reordering liteav_ff_h264_decode_ref_pic_list_reordering
#define ff_aac_pred_sfb_max liteav_ff_aac_pred_sfb_max
#define ff_put_wav_header liteav_ff_put_wav_header
#define ff_put_h264_qpel8_mc30_10_sse2 liteav_ff_put_h264_qpel8_mc30_10_sse2
#define av_gettime liteav_av_gettime
#define ff_pw_20 liteav_ff_pw_20
#define ff_framequeue_skip_samples liteav_ff_framequeue_skip_samples
#define ff_h263_cbpy_tab liteav_ff_h263_cbpy_tab
#define ff_avg_pixels16_y2_no_rnd_neon liteav_ff_avg_pixels16_y2_no_rnd_neon
#define ff_read_line_to_bprint liteav_ff_read_line_to_bprint
#define ff_draw_rectangle liteav_ff_draw_rectangle
#define ff_kbd_window_init liteav_ff_kbd_window_init
#define av_dirac_parse_sequence_header liteav_av_dirac_parse_sequence_header
#define ff_put_h264_qpel8_mc10_10_ssse3_cache64 liteav_ff_put_h264_qpel8_mc10_10_ssse3_cache64
#define av_color_primaries_name liteav_av_color_primaries_name
#define av_log2_i liteav_av_log2_i
#define ff_h264_idct8_add_10_avx liteav_ff_h264_idct8_add_10_avx
#define av_parser_change liteav_av_parser_change
#define ff_hevc_put_epel_uw_bi_h_neon_8 liteav_ff_hevc_put_epel_uw_bi_h_neon_8
#define ff_aac_demuxer liteav_ff_aac_demuxer
#define ff_hevc_sao_edge_eo2_w64_neon_8 liteav_ff_hevc_sao_edge_eo2_w64_neon_8
#define ff_h264_idct_add8_12_c liteav_ff_h264_idct_add8_12_c
#define av_aes_ctr_crypt liteav_av_aes_ctr_crypt
#define ff_hevc_log2_res_scale_abs liteav_ff_hevc_log2_res_scale_abs
#define av_timecode_adjust_ntsc_framenum2 liteav_av_timecode_adjust_ntsc_framenum2
#define ff_interleaved_dirac_golomb_vlc_code liteav_ff_interleaved_dirac_golomb_vlc_code
#define swr_convert liteav_swr_convert
#define ff_hevc_put_qpel_uw_pixels_w4_neon_8 liteav_ff_hevc_put_qpel_uw_pixels_w4_neon_8
#define av_color_range_from_name liteav_av_color_range_from_name
#define ff_hevc_put_qpel_uw_weight_h1v1_neon_8 liteav_ff_hevc_put_qpel_uw_weight_h1v1_neon_8
#define av_buffersink_get_samples liteav_av_buffersink_get_samples
#define ff_pred8x8l_dc_10_avx liteav_ff_pred8x8l_dc_10_avx
#define ff_fill_rectangle liteav_ff_fill_rectangle
#define ff_ebur128_init liteav_ff_ebur128_init
#define ff_unpack_6ch_int32_to_float_u_avx liteav_ff_unpack_6ch_int32_to_float_u_avx
#define ff_put_h264_qpel8_mc23_neon liteav_ff_put_h264_qpel8_mc23_neon
#define ff_unpack_2ch_int32_to_int16_u_sse2 liteav_ff_unpack_2ch_int32_to_int16_u_sse2
#define ff_unpack_6ch_float_to_float_u_avx liteav_ff_unpack_6ch_float_to_float_u_avx
#define av_thread_message_queue_set_err_recv liteav_av_thread_message_queue_set_err_recv
#define ff_put_h264_qpel8_mc10_10_sse2 liteav_ff_put_h264_qpel8_mc10_10_sse2
#define av_dct_end liteav_av_dct_end
#define ff_float_dsp_init_aarch64 liteav_ff_float_dsp_init_aarch64
#define ff_h264_idct_add8_422_10_avx liteav_ff_h264_idct_add8_422_10_avx
#define ff_ass_subtitle_header liteav_ff_ass_subtitle_header
#define avfilter_pad_get_type liteav_avfilter_pad_get_type
#define ff_hevc_put_epel_hv_neon_8_wrapper liteav_ff_hevc_put_epel_hv_neon_8_wrapper
#define ff_adts_muxer liteav_ff_adts_muxer
#define ff_mpeg1_default_intra_matrix liteav_ff_mpeg1_default_intra_matrix
#define ff_gsm_ms_at_decoder liteav_ff_gsm_ms_at_decoder
#define ff_pack_2ch_int16_to_int16_a_sse2 liteav_ff_pack_2ch_int16_to_int16_a_sse2
#define ff_cpu_cpuid liteav_ff_cpu_cpuid
#define av_opt_get_q liteav_av_opt_get_q
#define ff_avg_h264_qpel4_mc21_10_mmxext liteav_ff_avg_h264_qpel4_mc21_10_mmxext
#define ff_vector_fmul_window_vfp liteav_ff_vector_fmul_window_vfp
#define av_image_copy_uc_from liteav_av_image_copy_uc_from
#define ffurl_get_protocols liteav_ffurl_get_protocols
#define av_frame_get_colorspace liteav_av_frame_get_colorspace
#define avfilter_graph_alloc liteav_avfilter_graph_alloc
#define ff_avg_h264_qpel8_mc32_neon liteav_ff_avg_h264_qpel8_mc32_neon
#define av_mul_q liteav_av_mul_q
#define ff_hevc_cu_qp_delta_abs liteav_ff_hevc_cu_qp_delta_abs
#define ff_unpack_2ch_int32_to_float_u_sse2 liteav_ff_unpack_2ch_int32_to_float_u_sse2
#define av_mul_i liteav_av_mul_i
#define ff_sws_init_input_funcs liteav_ff_sws_init_input_funcs
#define ff_h264_init_cabac_states liteav_ff_h264_init_cabac_states
#define ff_alloc_packet liteav_ff_alloc_packet
#define ff_ac3_demuxer liteav_ff_ac3_demuxer
#define av_add_stable liteav_av_add_stable
#define ff_pw_64 liteav_ff_pw_64
#define ff_imdct36_float_sse3 liteav_ff_imdct36_float_sse3
#define ff_imdct36_float_sse2 liteav_ff_imdct36_float_sse2
#define ff_deblock_v_chroma_8_sse2 liteav_ff_deblock_v_chroma_8_sse2
#define ff_mov_muxer liteav_ff_mov_muxer
#define av_rdft_init liteav_av_rdft_init
#define ff_hevc_put_qpel_uw_h1_neon_8 liteav_ff_hevc_put_qpel_uw_h1_neon_8
#define ff_hevc_pcm_flag_decode liteav_ff_hevc_pcm_flag_decode
#define ff_deblock_v_luma_8_sse2 liteav_ff_deblock_v_luma_8_sse2
#define ff_ebur128_loudness_range_multiple liteav_ff_ebur128_loudness_range_multiple
#define ff_tls_init liteav_ff_tls_init
#define ff_avg_pixels8x8_c liteav_ff_avg_pixels8x8_c
#define av_blowfish_crypt liteav_av_blowfish_crypt
#define av_image_copy liteav_av_image_copy
#define av_frame_new_side_data liteav_av_frame_new_side_data
#define ff_put_h264_qpel8_h_lowpass_ssse3 liteav_ff_put_h264_qpel8_h_lowpass_ssse3
#define av_register_input_format liteav_av_register_input_format
#define ff_pred16x16_plane_h264_8_ssse3 liteav_ff_pred16x16_plane_h264_8_ssse3
#define ff_h264_idct8_add4_9_c liteav_ff_h264_idct8_add4_9_c
#define av_bsf_free liteav_av_bsf_free
#define ff_pred4x4_vertical_right_10_ssse3 liteav_ff_pred4x4_vertical_right_10_ssse3
#define ff_unpack_2ch_int16_to_int32_u_ssse3 liteav_ff_unpack_2ch_int16_to_int32_u_ssse3
#define variant_matched_tags liteav_variant_matched_tags
#define ff_amf_write_string2 liteav_ff_amf_write_string2
#define av_register_output_format liteav_av_register_output_format
#define ff_pred16x16_vertical_10_mmxext liteav_ff_pred16x16_vertical_10_mmxext
#define ff_mpeg4_set_direct_mv liteav_ff_mpeg4_set_direct_mv
#define av_rescale_q_rnd liteav_av_rescale_q_rnd
#define ff_amf_read_bool liteav_ff_amf_read_bool
#define av_opt_set_image_size liteav_av_opt_set_image_size
#define av_audio_fifo_free liteav_av_audio_fifo_free
#define ff_h264_idct8_dc_add_neon liteav_ff_h264_idct8_dc_add_neon
#define av_packet_get_side_data liteav_av_packet_get_side_data
#define av_blowfish_crypt_ecb liteav_av_blowfish_crypt_ecb
#define ff_deblock_h_chroma_intra_8_sse2 liteav_ff_deblock_h_chroma_intra_8_sse2
#define ff_eac3_default_chmap liteav_ff_eac3_default_chmap
#define ffurl_read_complete liteav_ffurl_read_complete
#define ff_fft_calc_vfp liteav_ff_fft_calc_vfp
#define avcodec_encode_audio2 liteav_avcodec_encode_audio2
#define swri_noise_shaping_int32 liteav_swri_noise_shaping_int32
#define ff_avg_pixels8_x2_neon liteav_ff_avg_pixels8_x2_neon
#define ff_avg_h264_qpel8or16_v_lowpass_sse2 liteav_ff_avg_h264_qpel8or16_v_lowpass_sse2
#define ff_text_init_buf liteav_ff_text_init_buf
#define ff_int16_to_float_u_sse2 liteav_ff_int16_to_float_u_sse2
#define avio_rl24 liteav_avio_rl24
#define ff_network_wait_fd liteav_ff_network_wait_fd
#define ff_avg_rv40_chroma_mc4_3dnow liteav_ff_avg_rv40_chroma_mc4_3dnow
#define ff_hevc_annexb2mp4 liteav_ff_hevc_annexb2mp4
#define ff_put_qpel16_mc32_old_c liteav_ff_put_qpel16_mc32_old_c
#define ff_h264_get_slice_type liteav_ff_h264_get_slice_type
#define ff_w1_plus_w3_hi liteav_ff_w1_plus_w3_hi
#define ff_h264_idct_add8_422_9_c liteav_ff_h264_idct_add8_422_9_c
#define ff_planar_sample_fmts liteav_ff_planar_sample_fmts
#define ff_simple_idct8_add_avx liteav_ff_simple_idct8_add_avx
#define ff_init_vscale liteav_ff_init_vscale
#define ff_deblock_v_chroma_intra_8_avx liteav_ff_deblock_v_chroma_intra_8_avx
#define ff_put_h264_qpel16_mc12_neon liteav_ff_put_h264_qpel16_mc12_neon
#define ff_hevc_pred_angular_32x32_v_zero_neon_8 liteav_ff_hevc_pred_angular_32x32_v_zero_neon_8
#define av_msg_set_callback liteav_av_msg_set_callback
#define ff_hevc_put_epel_v_neon_8_wrapper liteav_ff_hevc_put_epel_v_neon_8_wrapper
#define yyget_extra liteav_yyget_extra
#define ff_init_cabac_decoder liteav_ff_init_cabac_decoder
#define ff_scale_eval_dimensions liteav_ff_scale_eval_dimensions
#define avfilter_version liteav_avfilter_version
#define ff_fft_init liteav_ff_fft_init
#define av_get_sample_fmt_name liteav_av_get_sample_fmt_name
#define av_hwdevice_find_type_by_name liteav_av_hwdevice_find_type_by_name
#define ff_deblock_h_chroma_8_mmxext liteav_ff_deblock_h_chroma_8_mmxext
#define ff_put_h264_qpel4_mc01_10_mmxext liteav_ff_put_h264_qpel4_mc01_10_mmxext
#define ff_channel_layouts_changeref liteav_ff_channel_layouts_changeref
#define ff_ass_split_dialog2 liteav_ff_ass_split_dialog2
#define ff_rl_init_vlc liteav_ff_rl_init_vlc
#define ff_wait_thread liteav_ff_wait_thread
#define ff_put_h264_qpel8_mc20_neon liteav_ff_put_h264_qpel8_mc20_neon
#define avfilter_insert_filter liteav_avfilter_insert_filter
#define ff_square_tab liteav_ff_square_tab
#define av_frame_make_writable liteav_av_frame_make_writable
#define ff_pb_FE liteav_ff_pb_FE
#define ff_pb_FC liteav_ff_pb_FC
#define ff_pred16x16_plane_h264_8_mmxext liteav_ff_pred16x16_plane_h264_8_mmxext
#define ff_urldecode liteav_ff_urldecode
#define ff_pack_2ch_int16_to_float_u_sse2 liteav_ff_pack_2ch_int16_to_float_u_sse2
#define ff_interleaved_se_golomb_vlc_code liteav_ff_interleaved_se_golomb_vlc_code
#define ff_avg_h264_qpel8_mc21_neon liteav_ff_avg_h264_qpel8_mc21_neon
#define ff_pd_1 liteav_ff_pd_1
#define ff_biweight_h264_pixels_4_neon liteav_ff_biweight_h264_pixels_4_neon
#define av_image_get_buffer_size liteav_av_image_get_buffer_size
#define av_get_standard_channel_layout liteav_av_get_standard_channel_layout
#define ff_rl_mpeg2 liteav_ff_rl_mpeg2
#define av_cast5_alloc liteav_av_cast5_alloc
#define ff_rl_mpeg1 liteav_ff_rl_mpeg1
#define ff_hevc_pel_bi_pixels_w8_neon_8 liteav_ff_hevc_pel_bi_pixels_w8_neon_8
#define av_codec_next liteav_av_codec_next
#define ff_lzw_encode liteav_ff_lzw_encode
#define ff_unpack_6ch_float_to_int32_a_sse2 liteav_ff_unpack_6ch_float_to_int32_a_sse2
#define av_hwframe_constraints_free liteav_av_hwframe_constraints_free
#define ff_avg_h264_qpel8_mc02_neon liteav_ff_avg_h264_qpel8_mc02_neon
#define ff_pack_6ch_float_to_int32_u_avx liteav_ff_pack_6ch_float_to_int32_u_avx
#define av_bsf_flush liteav_av_bsf_flush
#define ff_fft_init_aarch64 liteav_ff_fft_init_aarch64
#define ff_vf_transpose liteav_ff_vf_transpose
#define ff_get_video_buffer liteav_ff_get_video_buffer
#define ff_avfilter_link_set_in_status liteav_ff_avfilter_link_set_in_status
#define av_encryption_init_info_add_side_data liteav_av_encryption_init_info_add_side_data
#define av_buffersink_get_time_base liteav_av_buffersink_get_time_base
#define av_expr_free liteav_av_expr_free
#define ff_h264_idct8_add_8_sse2 liteav_ff_h264_idct8_add_8_sse2
#define avio_open2 liteav_avio_open2
#define ff_simple_idct44_add liteav_ff_simple_idct44_add
#define ff_put_h264_qpel16_mc00_neon liteav_ff_put_h264_qpel16_mc00_neon
#define ff_replaygain_export liteav_ff_replaygain_export
#define ff_eac3_at_decoder liteav_ff_eac3_at_decoder
#define rgb24tobgr16 liteav_rgb24tobgr16
#define ff_hevc_put_pel_uw_pixels_w12_neon_8_asm liteav_ff_hevc_put_pel_uw_pixels_w12_neon_8_asm
#define text_style_create liteav_text_style_create
#define ff_h264dsp_init_x86 liteav_ff_h264dsp_init_x86
#define ff_pred16x16_vertical_10_sse2 liteav_ff_pred16x16_vertical_10_sse2
#define swri_audio_convert liteav_swri_audio_convert
#define av_twofish_init liteav_av_twofish_init
#define av_free_packet liteav_av_free_packet
#define vlc_css_selectors_Delete liteav_vlc_css_selectors_Delete
#define ff_hevc_pred_planar_32x32_neon_8_1 liteav_ff_hevc_pred_planar_32x32_neon_8_1
#define ff_hevc_put_qpel_v1_neon_8 liteav_ff_hevc_put_qpel_v1_neon_8
#define av_bsf_iterate liteav_av_bsf_iterate
#define sws_convertPalette8ToPacked24 liteav_sws_convertPalette8ToPacked24
#define ff_h264dsp_init_aarch64 liteav_ff_h264dsp_init_aarch64
#define ff_dct_end liteav_ff_dct_end
#define rgb32to24 liteav_rgb32to24
#define ff_sbr_hf_g_filt_neon liteav_ff_sbr_hf_g_filt_neon
#define ff_av1_filter_obus_buf liteav_ff_av1_filter_obus_buf
#define ff_pack_2ch_float_to_int16_u_sse2 liteav_ff_pack_2ch_float_to_int16_u_sse2
#define ff_wav_demuxer liteav_ff_wav_demuxer
#define ff_put_h264_qpel8_mc00_neon liteav_ff_put_h264_qpel8_mc00_neon
#define av_mediacodec_release_buffer liteav_av_mediacodec_release_buffer
#define av_get_bytes_per_sample liteav_av_get_bytes_per_sample
#define av_mediacodec_default_init liteav_av_mediacodec_default_init
#define avfilter_register_all liteav_avfilter_register_all
#define avio_wb64 liteav_avio_wb64
#define av_opt_ptr liteav_av_opt_ptr
#define ff_asink_abuffer liteav_ff_asink_abuffer
#define ff_cos_65536 liteav_ff_cos_65536
#define swr_close liteav_swr_close
#define av_aes_ctr_set_full_iv liteav_av_aes_ctr_set_full_iv
#define ff_aac_spectral_sizes liteav_ff_aac_spectral_sizes
#define shuffle_bytes_2103 liteav_shuffle_bytes_2103
#define ff_id3v2_tag_len liteav_ff_id3v2_tag_len
#define ff_hevc_put_epel_hv_neon_8 liteav_ff_hevc_put_epel_hv_neon_8
#define ff_mjpeg_encode_huffman_init liteav_ff_mjpeg_encode_huffman_init
#define ff_vector_dmul_scalar_neon liteav_ff_vector_dmul_scalar_neon
#define ff_ac3_dec_channel_map liteav_ff_ac3_dec_channel_map
#define ff_get_bmp_header liteav_ff_get_bmp_header
#define rgb64tobgr48_bswap liteav_rgb64tobgr48_bswap
#define ff_get_wav_header liteav_ff_get_wav_header
#define av_videotoolbox_default_init2 liteav_av_videotoolbox_default_init2
#define ff_ac3_floor_tab liteav_ff_ac3_floor_tab
#define ff_h264_idct8_add_8_mmx liteav_ff_h264_idct8_add_8_mmx
#define ff_mp3_demuxer liteav_ff_mp3_demuxer
#define ff_aac_kbd_short_120 liteav_ff_aac_kbd_short_120
#define ff_hevc_put_pel_uw_pixels_w64_neon_8_asm liteav_ff_hevc_put_pel_uw_pixels_w64_neon_8_asm
#define av_shr_i liteav_av_shr_i
#define ff_unpack_6ch_int32_to_float_a_avx liteav_ff_unpack_6ch_int32_to_float_a_avx
#define ff_h2645_extract_rbsp liteav_ff_h2645_extract_rbsp
#define ff_h264qpel_init_aarch64 liteav_ff_h264qpel_init_aarch64
#define ff_avg_h264_qpel16_mc12_10_sse2 liteav_ff_avg_h264_qpel16_mc12_10_sse2
#define ff_ebur128_add_frames_short liteav_ff_ebur128_add_frames_short
#define av_guess_format liteav_av_guess_format
#define ff_avg_h264_qpel4_mc30_10_mmxext liteav_ff_avg_h264_qpel4_mc30_10_mmxext
#define ff_h264_idct8_add_12_c liteav_ff_h264_idct8_add_12_c
#define ff_hevc_decode_nal_sps liteav_ff_hevc_decode_nal_sps
#define ff_reverse liteav_ff_reverse
#define yuy2toyv12 liteav_yuy2toyv12
#define av_frame_set_decode_error_flags liteav_av_frame_set_decode_error_flags
#define sws_shiftVec liteav_sws_shiftVec
#define ff_pred4x4_down_left_8_mmxext liteav_ff_pred4x4_down_left_8_mmxext
#define ff_put_h264_qpel16_mc03_10_sse2 liteav_ff_put_h264_qpel16_mc03_10_sse2
#define av_pix_fmt_desc_get liteav_av_pix_fmt_desc_get
#define ff_pred8x8_tm_vp8_8_sse2 liteav_ff_pred8x8_tm_vp8_8_sse2
#define ff_hevc_put_pixels_w16_neon_8 liteav_ff_hevc_put_pixels_w16_neon_8
#define ff_h264_unref_picture liteav_ff_h264_unref_picture
#define ff_adpcm_ima_qt_at_decoder liteav_ff_adpcm_ima_qt_at_decoder
#define av_read_image_line2 liteav_av_read_image_line2
#define ff_brktimegm liteav_ff_brktimegm
#define ff_hevc_pred_angular_8x8_v_zero_neon_8 liteav_ff_hevc_pred_angular_8x8_v_zero_neon_8
#define avpriv_report_missing_feature liteav_avpriv_report_missing_feature
#define ff_mp2_at_decoder liteav_ff_mp2_at_decoder
#define ff_h264_biweight_4_mmxext liteav_ff_h264_biweight_4_mmxext
#define ff_tns_max_bands_480 liteav_ff_tns_max_bands_480
#define av_hash_freep liteav_av_hash_freep
#define ff_golomb_vlc_len liteav_ff_golomb_vlc_len
#define ff_pred8x8l_vertical_left_8_ssse3 liteav_ff_pred8x8l_vertical_left_8_ssse3
#define ff_hevc_frame_rps liteav_ff_hevc_frame_rps
#define av_pix_fmt_count_planes liteav_av_pix_fmt_count_planes
#define av_camellia_init liteav_av_camellia_init
#define ff_emulated_edge_mc_16 liteav_ff_emulated_edge_mc_16
#define ff_put_h264_qpel8_mc13_10_sse2 liteav_ff_put_h264_qpel8_mc13_10_sse2
#define ff_ps_stereo_interpolate_neon liteav_ff_ps_stereo_interpolate_neon
#define ff_subtitles_utf8_external_read_chunk liteav_ff_subtitles_utf8_external_read_chunk
#define av_opt_next liteav_av_opt_next
#define ff_avg_h264_qpel8_mc01_neon liteav_ff_avg_h264_qpel8_mc01_neon
#define avpriv_mpegts_parse_close liteav_avpriv_mpegts_parse_close
#define ff_four_imdct36_float_sse liteav_ff_four_imdct36_float_sse
#define ff_sbr_noise_table liteav_ff_sbr_noise_table
#define codec_mp4_tags liteav_codec_mp4_tags
#define ff_h264_idct_add8_10_c liteav_ff_h264_idct_add8_10_c
#define av_opt_show2 liteav_av_opt_show2
#define ff_decode_sbr_extension liteav_ff_decode_sbr_extension
#define ff_avg_h264_qpel16_h_lowpass_l2_ssse3 liteav_ff_avg_h264_qpel16_h_lowpass_l2_ssse3
#define ff_make_absolute_url liteav_ff_make_absolute_url
#define ff_unpack_2ch_int16_to_int32_a_sse2 liteav_ff_unpack_2ch_int16_to_int32_a_sse2
#define ff_mpeg4_find_frame_end liteav_ff_mpeg4_find_frame_end
#define ff_float_dsp_init_x86 liteav_ff_float_dsp_init_x86
#define ff_pack_6ch_float_to_int32_a_sse2 liteav_ff_pack_6ch_float_to_int32_a_sse2
#define ff_deblock_v_luma_10_sse2 liteav_ff_deblock_v_luma_10_sse2
#define ff_mpeg4_clean_buffers liteav_ff_mpeg4_clean_buffers
#define ffurl_connect liteav_ffurl_connect
#define avpriv_open liteav_avpriv_open
#define ff_h264_idct_add16_10_sse2 liteav_ff_h264_idct_add16_10_sse2
#define ff_put_h264_qpel4_mc21_10_mmxext liteav_ff_put_h264_qpel4_mc21_10_mmxext
#define ff_pred16x16_dc_neon liteav_ff_pred16x16_dc_neon
#define ff_int32_to_float_a_avx liteav_ff_int32_to_float_a_avx
#define ff_put_h264_qpel4_mc30_10_mmxext liteav_ff_put_h264_qpel4_mc30_10_mmxext
#define ff_h263_intra_MCBPC_code liteav_ff_h263_intra_MCBPC_code
#define ff_h264_idct_add8_14_c liteav_ff_h264_idct_add8_14_c
#define ff_mpeg12_mbMotionVectorTable liteav_ff_mpeg12_mbMotionVectorTable
#define avformat_get_mov_audio_tags liteav_avformat_get_mov_audio_tags
#define ff_inlink_make_frame_writable liteav_ff_inlink_make_frame_writable
#define rgb24tobgr15 liteav_rgb24tobgr15
#define ff_h264_idct_add_10_avx liteav_ff_h264_idct_add_10_avx
#define ff_shuffle_bytes_0321_ssse3 liteav_ff_shuffle_bytes_0321_ssse3
#define av_opt_set_pixel_fmt liteav_av_opt_set_pixel_fmt
#define ff_ass_free_dialog liteav_ff_ass_free_dialog
#define ff_xvid_idct_init_x86 liteav_ff_xvid_idct_init_x86
#define ff_cos_32 liteav_ff_cos_32
#define avpriv_mpegaudio_decode_header liteav_avpriv_mpegaudio_decode_header
#define ff_vf_overlay liteav_ff_vf_overlay
#define ff_imdct_half_neon liteav_ff_imdct_half_neon
#define ff_hevc_put_epel_uw_pixels_w8_neon_8 liteav_ff_hevc_put_epel_uw_pixels_w8_neon_8
#define ff_put_h264_qpel16_mc21_neon liteav_ff_put_h264_qpel16_mc21_neon
#define ff_avg_h264_qpel16_mc01_neon liteav_ff_avg_h264_qpel16_mc01_neon
#define ff_id3v2_read_dict liteav_ff_id3v2_read_dict
#define ff_put_h264_qpel16_mc03_neon liteav_ff_put_h264_qpel16_mc03_neon
#define ff_riff_write_info liteav_ff_riff_write_info
#define av_dct_init liteav_av_dct_init
#define ff_mpeg4_videotoolbox_hwaccel liteav_ff_mpeg4_videotoolbox_hwaccel
#define ff_pred16x16_horizontal_8_mmx liteav_ff_pred16x16_horizontal_8_mmx
#define swscale_configuration liteav_swscale_configuration
#define av_packet_side_data_name liteav_av_packet_side_data_name
#define ff_h264_luma_dc_dequant_idct_8_c liteav_ff_h264_luma_dc_dequant_idct_8_c
#define ff_xvid_idct liteav_ff_xvid_idct
#define ff_scalarproduct_float_neon liteav_ff_scalarproduct_float_neon
#define vlc_css_parser_Debug liteav_vlc_css_parser_Debug
#define avformat_close liteav_avformat_close
#define avformat_new_stream liteav_avformat_new_stream
#define avformat_close_input liteav_avformat_close_input
#define avformat_free_context liteav_avformat_free_context
#define avformat_alloc_context liteav_avformat_alloc_context
#define avformat_open_input liteav_avformat_open_input
#define avformat_find_stream_info liteav_avformat_find_stream_info
#define av_find_best_stream liteav_av_find_best_stream
#define avcodec_open2 liteav_avcodec_open2
#define av_read_frame liteav_av_read_frame
#define av_seek_frame liteav_av_seek_frame
#define av_codec_get_tag liteav_av_codec_get_tag
#define avcodec_parameters_from_context liteav_avcodec_parameters_from_context
#define avcodec_parameters_to_context liteav_avcodec_parameters_to_context
#define avcodec_alloc_context3 liteav_avcodec_alloc_context3
#define avcodec_get_name liteav_avcodec_get_name
#define avcodec_free_context liteav_avcodec_free_context
#define avcodec_close liteav_avcodec_close
//vod
#define avsubtitle_free liteav_avsubtitle_free
#define av_version_info liteav_av_version_info
#define av_find_default_stream_index liteav_av_find_default_stream_index
#define av_stream_get_side_data liteav_av_stream_get_side_data
#define av_get_media_type_string liteav_av_get_media_type_string
#define avcodec_parameters_alloc liteav_avcodec_parameters_alloc
#define avformat_network_init liteav_avformat_network_init
#define av_int_list_length_for_size liteav_av_int_list_length_for_size
#define avcodec_parameters_free liteav_avcodec_parameters_free
#define avcodec_parameters_copy liteav_avcodec_parameters_copy
#define av_find_program_from_stream liteav_av_find_program_from_stream
// clang-format on

#endif  // THIRD_PARTY_FFMPEG_FFMPEG_RENAME_DEFINES_H
