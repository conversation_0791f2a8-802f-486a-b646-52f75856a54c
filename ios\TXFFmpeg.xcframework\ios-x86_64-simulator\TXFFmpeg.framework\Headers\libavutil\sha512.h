#include "third_party/ffmpeg/ffmpeg_rename_defines.h" // add by source_replacer.py 
/*
 * Copyright (C) 2007 <PERSON> <mi<PERSON><PERSON><PERSON>@gmx.at>
 * Copyright (C) 2013 <PERSON> <<EMAIL>>
 *
 * This file is part of FFmpeg.
 *
 * FFmpeg is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * FFmpeg is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with FFmpeg; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA
 */

/**
 * @file
 * @ingroup lavu_sha512
 * Public header for SHA-512 implementation.
 */

#ifndef AVUTIL_SHA512_H
#define AVUTIL_SHA512_H

#include <stddef.h>
#include <stdint.h>

#include "attributes.h"
#include "version.h"

/**
 * @defgroup lavu_sha512 SHA-512
 * @ingroup lavu_hash
 * SHA-512 (Secure Hash Algorithm) hash function implementations.
 *
 * This module supports the following SHA-2 hash functions:
 *
 * - SHA-512/224: 224 bits
 * - SHA-512/256: 256 bits
 * - SHA-384: 384 bits
 * - SHA-512: 512 bits
 *
 * @see For SHA-1, SHA-256, and variants thereof, see @ref lavu_sha.
 *
 * @{
 */

extern const int liteav_av_sha512_size;

struct AVSHA512;

/**
 * Allocate an AVSHA512 context.
 */
struct AVSHA512 *liteav_av_sha512_alloc(void);

/**
 * Initialize SHA-2 512 hashing.
 *
 * @param context pointer to the function context (of size liteav_av_sha512_size)
 * @param bits    number of bits in digest (224, 256, 384 or 512 bits)
 * @return        zero if initialization succeeded, -1 otherwise
 */
int liteav_av_sha512_init(struct AVSHA512* context, int bits);

/**
 * Update hash value.
 *
 * @param context hash function context
 * @param data    input data to update hash with
 * @param len     input data length
 */
#if FF_API_CRYPTO_SIZE_T
void liteav_av_sha512_update(struct AVSHA512* context, const uint8_t* data, unsigned int len);
#else
void liteav_av_sha512_update(struct AVSHA512* context, const uint8_t* data, size_t len);
#endif

/**
 * Finish hashing and output digest value.
 *
 * @param context hash function context
 * @param digest  buffer where output digest value is stored
 */
void liteav_av_sha512_final(struct AVSHA512* context, uint8_t *digest);

/**
 * @}
 */

#endif /* AVUTIL_SHA512_H */
