# Generated code do not commit.
file(TO_CMAKE_PATH "E:\\top\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "E:\\zzf_code\\pinim" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+2" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 2 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=E:\\top\\flutter"
  "PROJECT_DIR=E:\\zzf_code\\pinim"
  "FLUTTER_ROOT=E:\\top\\flutter"
  "FLUTTER_EPHEMERAL_DIR=E:\\zzf_code\\pinim\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=E:\\zzf_code\\pinim"
  "FLUTTER_TARGET=E:\\zzf_code\\pinim\\lib\\main.dart"
  "DART_DEFINES=Zmx1dHRlci5pbnNwZWN0b3Iuc3RydWN0dXJlZEVycm9ycz10cnVl"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=E:\\zzf_code\\pinim\\.dart_tool\\package_config.json"
)
