/* include/soundtouch_config.h.  Generated from soundtouch_config.h.in by configure.  */
/* include/soundtouch_config.h.in.  Generated from configure.ac by autoheader.  */

#ifndef SoundTouchConfig_H
#define SoundTouchConfig_H

namespace liteav_soundtouch 
{

/* Never allow x86 optimizations in iOS simulator build */
#define ALLOW_X86_OPTIMIZATIONS 0

/* Use Integer as Sample type */
#define INTEGER_SAMPLES 1
#define SOUNDTOUCH_INTEGER_SAMPLES 1

/* Use Float as Sample type */
//#define FLOAT_SAMPLES 1

/* Define to 1 if you have the <dlfcn.h> header file. */
#define HAVE_DLFCN_H 1

/* Define to 1 if you have the <inttypes.h> header file. */
#define HAVE_INTTYPES_H 1

/* Define to 1 if you have the `m' library (-lm). */
#define HAVE_LIBM 1

/* Define to 1 if your system has a GNU libc compatible `malloc' function, and
   to 0 otherwise. */
#define HAVE_MALLOC 1

/* Define to 1 if you have the <memory.h> header file. */
#define HAVE_MEMORY_H 1

/* Define to 1 if you have the <stdint.h> header file. */
#define HAVE_STDINT_H 1

/* Define to 1 if you have the <stdlib.h> header file. */
#define HAVE_STDLIB_H 1

/* Define to 1 if you have the <strings.h> header file. */
#define HAVE_STRINGS_H 1

/* Define to 1 if you have the <string.h> header file. */
#define HAVE_STRING_H 1

/* Define to 1 if you have the <sys/stat.h> header file. */
#define HAVE_SYS_STAT_H 1

/* Define to 1 if you have the <sys/types.h> header file. */
#define HAVE_SYS_TYPES_H 1

/* Define to 1 if you have the <unistd.h> header file. */
#define HAVE_UNISTD_H 1

/* Use Integer as Sample type */
/* #undef INTEGER_SAMPLES */

/* Define to the sub-directory in which libtool stores uninstalled libraries.
   */
#define LT_OBJDIR ".libs/"

/* Name of package */
#define PACKAGE "soundtouch"

/* Define to the address where bug reports for this package should be sent. */
#define PACKAGE_BUGREPORT "http://www.surina.net/soundtouch"

/* Define to the full name of this package. */
#define PACKAGE_NAME "SoundTouch"

/* Define to the full name and version of this package. */
#define PACKAGE_STRING "SoundTouch 1.4.0"

/* Define to the one symbol short name of this package. */
#define PACKAGE_TARNAME "soundtouch"

/* Define to the version of this package. */
#define PACKAGE_VERSION "1.4.0"

/* Define as the return type of signal handlers (`int' or `void'). */
#define RETSIGTYPE void

/* Define to 1 if you have the ANSI C header files. */
#define STDC_HEADERS 1

/* Version number of package */
#define VERSION "1.4.0"

/* Define to empty if `const' does not conform to ANSI C. */
/* #undef const */

/* Define to `__inline__' or `__inline' if that's what the C compiler
   calls it, or to nothing if 'inline' is not supported under any name.  */
#ifndef __cplusplus
/* #undef inline */
#endif

/* Define to rpl_malloc if the replacement function should be used. */
/* #undef malloc */

}

#endif // SoundTouchConfig_H
