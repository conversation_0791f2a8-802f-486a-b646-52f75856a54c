'use strict';var t=function(t,e){return t(e={exports:{}},e.exports),e.exports}((function(t){var e=Object.prototype.hasOwnProperty,n="~";function i(){}function s(t,e,n){this.fn=t,this.context=e,this.once=n||!1}function o(t,e,i,o,a){if("function"!=typeof i)throw new TypeError("The listener must be a function");var r=new s(i,o||t,a),l=n?n+e:e;return t._events[l]?t._events[l].fn?t._events[l]=[t._events[l],r]:t._events[l].push(r):(t._events[l]=r,t._eventsCount++),t}function a(t,e){0==--t._eventsCount?t._events=new i:delete t._events[e]}function r(){this._events=new i,this._eventsCount=0}Object.create&&(i.prototype=Object.create(null),(new i).__proto__||(n=!1)),r.prototype.eventNames=function(){var t,i,s=[];if(0===this._eventsCount)return s;for(i in t=this._events)e.call(t,i)&&s.push(n?i.slice(1):i);return Object.getOwnPropertySymbols?s.concat(Object.getOwnPropertySymbols(t)):s},r.prototype.listeners=function(t){var e=n?n+t:t,i=this._events[e];if(!i)return[];if(i.fn)return[i.fn];for(var s=0,o=i.length,a=new Array(o);s<o;s++)a[s]=i[s].fn;return a},r.prototype.listenerCount=function(t){var e=n?n+t:t,i=this._events[e];return i?i.fn?1:i.length:0},r.prototype.emit=function(t,e,i,s,o,a){var r=n?n+t:t;if(!this._events[r])return!1;var l,g,c=this._events[r],u=arguments.length;if(c.fn){switch(c.once&&this.removeListener(t,c.fn,void 0,!0),u){case 1:return c.fn.call(c.context),!0;case 2:return c.fn.call(c.context,e),!0;case 3:return c.fn.call(c.context,e,i),!0;case 4:return c.fn.call(c.context,e,i,s),!0;case 5:return c.fn.call(c.context,e,i,s,o),!0;case 6:return c.fn.call(c.context,e,i,s,o,a),!0}for(g=1,l=new Array(u-1);g<u;g++)l[g-1]=arguments[g];c.fn.apply(c.context,l)}else{var h,d=c.length;for(g=0;g<d;g++)switch(c[g].once&&this.removeListener(t,c[g].fn,void 0,!0),u){case 1:c[g].fn.call(c[g].context);break;case 2:c[g].fn.call(c[g].context,e);break;case 3:c[g].fn.call(c[g].context,e,i);break;case 4:c[g].fn.call(c[g].context,e,i,s);break;default:if(!l)for(h=1,l=new Array(u-1);h<u;h++)l[h-1]=arguments[h];c[g].fn.apply(c[g].context,l)}}return!0},r.prototype.on=function(t,e,n){return o(this,t,e,n,!1)},r.prototype.once=function(t,e,n){return o(this,t,e,n,!0)},r.prototype.removeListener=function(t,e,i,s){var o=n?n+t:t;if(!this._events[o])return this;if(!e)return a(this,o),this;var r=this._events[o];if(r.fn)r.fn!==e||s&&!r.once||i&&r.context!==i||a(this,o);else{for(var l=0,g=[],c=r.length;l<c;l++)(r[l].fn!==e||s&&!r[l].once||i&&r[l].context!==i)&&g.push(r[l]);g.length?this._events[o]=1===g.length?g[0]:g:a(this,o)}return this},r.prototype.removeAllListeners=function(t){var e;return t?(e=n?n+t:t,this._events[e]&&a(this,e)):(this._events=new i,this._eventsCount=0),this},r.prototype.off=r.prototype.removeListener,r.prototype.addListener=r.prototype.on,r.prefixed=n,r.EventEmitter=r,t.exports=r}));const e=2,n=11,i=12,s=15,o=20,a=23,r=27,l="onMessageModified",g="error";class c{constructor(t=0,e=0){this.high=t,this.low=e}equal(t){return null!==t&&(this.low===t.low&&this.high===t.high)}toString(){const t=Number(this.high).toString(16);let e=Number(this.low).toString(16);if(e.length<8){let t=8-e.length;for(;t;)e="0"+e,t--}return t+e}}const u={TEST:{CHINA:{DEFAULT:"wss://wss-dev.tim.qq.com"},OVERSEA:{DEFAULT:"wss://wss-dev.tim.qq.com"},SINGAPORE:{DEFAULT:"wss://wsssgp-dev.im.qcloud.com"},KOREA:{DEFAULT:"wss://wsskr-dev.im.qcloud.com"},GERMANY:{DEFAULT:"wss://wssger-dev.im.qcloud.com"},IND:{DEFAULT:"wss://wssind-dev.im.qcloud.com"},JPN:{DEFAULT:"wss://wssjpn-dev.im.qcloud.com"},USA:{DEFAULT:"wss://wssusa-dev.im.qcloud.com"}},PRODUCTION:{CHINA:{DEFAULT:"wss://wss.im.qcloud.com",BACKUP:"wss://wss.tim.qq.com",STAT:"https://events.im.qcloud.com",ANYCAST:"wss://162.14.13.203"},OVERSEA:{DEFAULT:"wss://wss.im.qcloud.com",BACKUP:"wss://wss.my-imcloud.com",STAT:"https://api.my-imcloud.com"},SINGAPORE:{DEFAULT:"wss://wsssgp.im.qcloud.com",BACKUP:"wss://wsssgp.my-imcloud.com",STAT:"https://apisgp.my-imcloud.com"},KOREA:{DEFAULT:"wss://wsskr.im.qcloud.com",BACKUP:"wss://wsskr.my-imcloud.com",STAT:"https://apikr.my-imcloud.com"},GERMANY:{DEFAULT:"wss://wssger.im.qcloud.com",BACKUP:"wss://wssger.my-imcloud.com",STAT:"https://apiger.my-imcloud.com"},IND:{DEFAULT:"wss://wssind.my-imcloud.com",BACKUP:"wss://wssind.im.qcloud.com",STAT:"https://apiind.my-imcloud.com"},JPN:{DEFAULT:"wss://wssjpn.im.qcloud.com",BACKUP:"wss://wssjpn.my-imcloud.com",STAT:"https://apijpn.my-imcloud.com"},USA:{DEFAULT:"wss://wssusa.im.qcloud.com",BACKUP:"wss://wssusa.my-imcloud.com",STAT:"https://apiusa.my-imcloud.com"}}},h={ANDROID:2,IOS:3,MAC:4,WEB:7,WX_MP:8,QQ_MP:9,TT_MP:10,BAIDU_MP:11,ALI_MP:12,IPAD:13,UNI_NATIVE_APP:15},d="CHINA",_={HOST:{CURRENT:{DEFAULT:"wss://wss.im.qcloud.com",STAT:"https://events.im.qcloud.com"},setCurrent(t=d){this.CURRENT=u.PRODUCTION[t]}},NAME:{OPEN_IM:"openim",OPEN_IM_MSG_EXT:"openim_msg_ext_http_svc",GROUP:"group_open_http_svc",GROUP_AVCHATROOM:"group_open_avchatroom_http_svc",GROUP_COMMUNITY:"million_group_open_http_svc",GROUP_ATTR:"group_open_attr_http_svc",FRIEND:"sns",PROFILE:"profile",RECENT_CONTACT:"recentcontact",PIC:"openpic",BIG_GROUP_NO_AUTH:"group_open_http_noauth_svc",BIG_GROUP_LONG_POLLING:"group_open_long_polling_http_svc",BIG_GROUP_LONG_POLLING_NO_AUTH:"group_open_long_polling_http_noauth_svc",IM_OPEN_STAT:"imopenstat",WEB_IM:"webim",IM_COS_SIGN:"im_cos_sign_svr",CUSTOM_UPLOAD:"im_cos_msg",HEARTBEAT:"heartbeat",IM_OPEN_PUSH:"im_open_push",IM_OPEN_STATUS:"im_open_status",IM_LONG_MESSAGE:"im_long_msg",IM_CONFIG_MANAGER:"im_sdk_config_mgr",STAT_SERVICE:"StatSvc",OVERLOAD_PUSH:"OverLoadPush",IM_MSG_AUDIT_MGR:"im_msg_audit_mgr",TUIROOM_SVR:"tui_room_svr",IM_OPEN_TRANSLATE:"im_open_translate",IM_OPEN_SPEECH:"im_open_speech",MESSAGE_SEARCH:"message_search"},CHANNEL:{SOCKET:1,XHR:2,AUTO:0},NAME_VERSION:{openim:"v4",group_open_http_svc:"v4",sns:"v4",profile:"v4",recentcontact:"v4",openpic:"v4",group_open_http_noauth_svc:"v4",group_open_long_polling_http_svc:"v4",group_open_long_polling_http_noauth_svc:"v4",imopenstat:"v4",im_cos_sign_svr:"v4",im_cos_msg:"v4",webim:"v4",im_open_push:"v4",im_open_status:"v4"}};new c(0,Math.pow(2,0)).toString(),new c(0,Math.pow(2,1)).toString(),new c(0,Math.pow(2,2)).toString(),new c(0,Math.pow(2,3)).toString(),new c(0,Math.pow(2,4)).toString(),new c(0,Math.pow(2,6)).toString(),new c(0,Math.pow(2,7)).toString(),new c(0,Math.pow(2,9)).toString(),new c(0,Math.pow(2,10)).toString(),new c(0,Math.pow(2,11)).toString(),new c(0,Math.pow(2,13)).toString(),new c(0,Math.pow(2,15)).toString(),_.HOST.setCurrent(d);const m="undefined"!=typeof wx&&"function"==typeof wx.getSystemInfoSync&&Boolean(wx.getSystemInfoSync().fontSizeSetting),I="undefined"!=typeof qq&&"function"==typeof qq.getSystemInfoSync&&Boolean(qq.getSystemInfoSync().fontSizeSetting),f="undefined"!=typeof tt&&"function"==typeof tt.getSystemInfoSync&&Boolean(tt.getSystemInfoSync().fontSizeSetting),v="undefined"!=typeof swan&&"function"==typeof swan.getSystemInfoSync&&Boolean(swan.getSystemInfoSync().fontSizeSetting),p="undefined"!=typeof my&&"function"==typeof my.getSystemInfoSync&&Boolean(my.getSystemInfoSync().fontSizeSetting),D="undefined"!=typeof jd&&"function"==typeof jd.getSystemInfoSync,M="undefined"!=typeof uni&&"undefined"==typeof window&&"function"==typeof uni.requireNativePlugin,S=m||I||f||v||p||M||D,y=("undefined"!=typeof uni||"undefined"!=typeof window)&&!S;I?qq:f?tt:v?swan:p?my:m?wx:M?uni:!D||jd;const w=y&&window&&window.navigator&&window.navigator.userAgent||"",C=/(micromessenger|webbrowser)/i.test(w),T=/AppleWebKit\/([\d.]+)/i.exec(w);T&&parseFloat(T.pop());const E=function(){let t="WEB";return C?t="WEB":I?t="QQ_MP":f?t="TT_MP":v?t="BAIDU_MP":p?t="ALI_MP":m?t="WX_MP":M&&(t="UNI_NATIVE_APP"),h[t]}();!function(){const t=w.match(/OS (\d+)_/i);t&&t[1]&&t[1]}(),function(){const t=w.match(/Android (\d+)(?:\.(\d+))?(?:\.(\d+))*/i);if(!t)return null;const e=t[1]&&parseFloat(t[1]),n=t[2]&&parseFloat(t[2]);e&&n&&parseFloat(t[1]+"."+t[2])}(),function(){const t=w.match(/Chrome\/(\d+)/);t&&t[1]&&parseFloat(t[1])}();const L=/MSIE/.test(w)||w.indexOf("Trident")>-1&&w.indexOf("rv:11.0")>-1;!function(){const t=/MSIE\s(\d+)\.\d/.exec(w);let e=t&&parseFloat(t[1]);!e&&/Trident\/7.0/i.test(w)&&/rv:11.0/.test(w)&&(e=11)}(),function(){const t=w.match(/TBS\/(\d+)/i);if(t&&t[1])t[1]}();const A="TIMCustomElem",O="High",N="C2C",$="GROUP",U="@TIM#SYSTEM";let P,R;P="undefined"!=typeof console?console:"undefined"!=typeof global&&global.console?global.console:"undefined"!=typeof window&&window.console?window.console:{};const H=function(){},x=["assert","clear","count","debug","dir","dirxml","error","group","groupCollapsed","groupEnd","info","log","profile","profileEnd","table","time","timeEnd","timeStamp","trace","warn"];let b=x.length;for(;b--;)R=x[b],console[R]||(P[R]=H);var F=P;const q=function(){const t=new Date;return t.setTime((new Date).getTime()+0),t};let j=0;function G(){return z()?"%c Chat %c":"Chat"}function k(){const t=q();return t.toLocaleTimeString("en-US",{hour12:!1})+"."+function(t){let e;switch(t.toString().length){case 1:e="00"+t;break;case 2:e="0"+t;break;default:e=t}return e}(t.getMilliseconds())}const B={arguments2String(t){let e="";if(1===t.length)e=t[0];else for(let n=0,i=t.length;n<i;n++)K(t[n])?V(t[n])?e+=Y(t[n]):e+=JSON.stringify(t[n]):e+=t[n],e+=" ";return e},_exec(t,e){z()?F[t](G(),"background:#0abf5b; padding:1px; border-radius:3px; color: #fff","background:transparent",k(),e):F[t](`${G()} ${k()} ${e}`)},d:function(){if(j<=-1){const t=this.arguments2String(arguments);this._exec("debug",t)}},l:function(){if(j<=0){const t=this.arguments2String(arguments);this._exec("log",t)}},log:function(){if(j<=0){const t=this.arguments2String(arguments);this._exec("log",t)}},i:function(){if(j<=1){const t=this.arguments2String(arguments);this._exec("info",t)}},w:function(){if(j<=2){const t=this.arguments2String(arguments);this._exec("warn",t)}},e:function(){if(j<=3){const t=this.arguments2String(arguments);this._exec("error",t)}},setLevel:function(t){t<4&&this._exec("log","set level from "+j+" to "+t),j=t},getLevel:function(){return j}},J=function(t){return void 0===t},K=function(t){return function(t){return"function"==typeof Array.isArray?Array.isArray(t):"array"===W(t)}(t)||function(t){return null!==t&&"object"==typeof t}(t)},V=function(t){return t instanceof Error},W=function(t){return Object.prototype.toString.call(t).match(/^\[object (.*)\]$/)[1].toLowerCase()};Date.now||(Date.now=function(){return(new Date).getTime()});const Y=function(t){return JSON.stringify(t,["message","code"])};function z(){return!L&&!S}const X=Object.prototype.hasOwnProperty;function Q(t){if(null==t)return!0;if("boolean"==typeof t)return!1;if("number"==typeof t)return 0===t;if("string"==typeof t)return 0===t.length;if("function"==typeof t)return 0===t.length;if(Array.isArray(t))return 0===t.length;if(t instanceof Error)return""===t.message;if(function(t){if("object"!=typeof t||null===t)return!1;const e=Object.getPrototypeOf(t);if(null===e)return!0;let n=e;for(;null!==Object.getPrototypeOf(n);)n=Object.getPrototypeOf(n);return e===n}(t)){for(const e in t)if(X.call(t,e))return!1;return!0}return!("map"!==W(t)&&!function(t){return"set"===W(t)}(t)&&!function(t){return"file"===W(t)}(t))&&0===t.size}class Z extends Error{constructor(t){super();const{code:e,message:n,data:i}=t;this.code=e,this.message=n||this._getErrorMessage(this.code),this.data=i||{}}}const et=2903,nt=3122,it=8010,st=8011,ot=8020,at=8021;let rt=null;const lt=function(t,e=!1){if(t instanceof Z)return e&&null!==rt&&rt.emit(g,t),Promise.reject(t);if(t instanceof Error){const t=new Z({code:et});return e&&null!==rt&&rt.emit(g,t),Promise.reject(t)}if(J(t)||J(t.code))return Promise.reject(new Z({code:et}));const n=new Z(t);return e&&null!==rt&&rt.emit(g,n),Promise.reject(n)};const gt="newInvitationReceived",ct="ts_invitee_accepted",ut="ts_invitee_rejected",ht="ts_invitation_cancelled",dt="ts_invitation_timeout",_t="ts_invitation_modified",mt=1,It=2,ft=3,vt=4,pt=5;class Dt{constructor(t){this._n="RemoteSignalingHandler",this._signalingModule=t}onNewMessageList(t){const e=this._signalingModule.filterMessageList(t);e.length>0&&e.forEach(t=>{const e=this.getPayloadData(t);e&&this._handleActionType(e,t)})}onMessageModified(t){const e=this._signalingModule.filterMessageList(t);e.length>0&&e.forEach(t=>{const e=this.getPayloadData(t);e&&this._onInvitationModified(e,t)})}getPayloadData(t){const e=this._n+".getPayloadData",{data:n}=t.payload;try{return JSON.parse(n)}catch(i){return B.e(`${e} JSON parse error. signalingData:${n}`),null}}_handleActionType(t,e){const{actionType:n}=t;switch(n){case mt:this._onNewInvitationReceived(t,e);break;case vt:this._onInviteeRejected(t);break;case ft:this._onInviteeAccepted(t);break;case It:this._onInvitationCancelled(t);break;case pt:this._onInvitationTimeout(t)}}_createDefaultEmitData(t){const{inviteID:e,inviter:n,groupID:i,data:s}=t;return{inviteID:e,inviter:n,groupID:i,data:s||""}}_onNewInvitationReceived(t,e){const n=this._n+"._onNewInvitationReceived",{inviteID:i,inviteeList:s,groupID:o}=t,a=this._signalingModule.getMyUserID(),r=s.includes(a),l=Math.round(t.timeout-((new Date).getTime()-1e3*e.time)/1e3);if(B.l(`${n} myselfIncluded:${r} groupID:${o} timeout:${l}s signalObj:${JSON.stringify(t)}`),o&&r||!o){const n=this._signalingModule.getInviteInfo(i);if(n&&n===t)return;n||this._signalingModule.setInviteInfo(i,{...t,message:e}),this._signalingModule.emitEvent(gt,{...this._createDefaultEmitData(t),inviteeList:s}),this._signalingModule.startTimer({...t,timeout:l})}}_onInviteeRejected(t){const e=this._n+"._onInviteeRejected",{inviteID:n,inviter:i,groupID:s}=t,o=this._signalingModule.hasInviteInfo(n);B.l(`${e} inviteID:${n} hasInviteID:${o} inviter:${i} groupID:${s}`),(s&&o||!s)&&(this._signalingModule.updateInviteInfo(t),this._signalingModule.emitEvent(ut,{...this._createDefaultEmitData(t),invitee:t.inviteeList[0]}))}_onInviteeAccepted(t){const e=this._n+"._onInviteeAccepted",{inviteID:n,inviter:i,groupID:s}=t,o=this._signalingModule.hasInviteInfo(n);B.l(`${e} inviteID:${n} hasInviteID:${o} inviter:${i} groupID:${s}`),(s&&o||!s)&&(this._signalingModule.updateInviteInfo(t),this._signalingModule.emitEvent(ct,{...this._createDefaultEmitData(t),invitee:t.inviteeList[0]}))}_onInvitationCancelled(t){const e=this._n+"._onInvitationCancelled",{inviteID:n,inviter:i,groupID:s}=t,o=this._signalingModule.hasInviteInfo(n);B.l(`${e} inviteID:${n} hasInviteID:${o} inviter:${i} groupID:${s}`),(s&&o||!s)&&(this._signalingModule.deleteInviteInfo(n),this._signalingModule.emitEvent(ht,this._createDefaultEmitData(t)))}_onInvitationTimeout(t){const e=this._n+"._onInvitationTimeout",{inviteID:n,inviter:i,groupID:s,inviteeList:o}=t,a=this._signalingModule.hasInviteInfo(n);B.l(`${e} inviteID:${n} hasInviteID:${a} inviter:${i} groupID:${s}  data:${t.data}`),(s&&a||!s)&&(this._signalingModule.updateInviteInfo(t),this._signalingModule.emitEvent(dt,{...this._createDefaultEmitData(t),inviteeList:o,isSelfTimeout:!1}))}_onInvitationModified(t,e){const n=this._n+"._onInvitationModified",{inviteID:i,data:s}=t;B.l(`${n} inviteID:${i} data:${s}`),this._signalingModule.setInviteInfo(i,{...t,message:e}),this._signalingModule.emitEvent(_t,{inviteID:i,data:s})}}const Mt=function(t){if(t<0||t>53)return NaN;const e=0|1073741824*Math.random();return t>30?e+1073741824*(0|Math.random()*(1<<t-30)):e>>>30-t},St=function(t,e){let n=t.toString(16),i=e-n.length,s="0";for(;i>0;i>>>=1,s+=s)1&i&&(n=s+n);return n};class yt{constructor(t){this._n="LocalSignalingHandler",this._signalingModule=t}generateInviteID(){const t=function(){const t=Mt,e=St;return e(t(32),8)+"-"+e(t(16),4)+"-"+e(16384|t(12),4)+"-"+e(32768|t(14),4)+"-"+e(t(48),12)}();return B.l(`${this._n}.generateInviteID inviteID:${t}`),t}createInviteInfo(t){const e=this.generateInviteID(),n=this.createInviteCustomData({...t,inviteID:e}),{groupID:i,inviteeList:s}=n,o=i||s[0];return{customData:n,message:this.createSignalingMessage(n,o),inviteID:e}}_createDefaultCustomData(t){const{data:e="",inviteID:n="",groupID:i=""}=t;return{businessID:1,timeout:0,data:e,inviteID:n,groupID:i}}createInviteCustomData(t){const{userID:e,timeout:n=0,groupID:i=""}=t,s=this._signalingModule.getMyUserID(),o={...this._createDefaultCustomData(t),actionType:mt,inviter:s,inviteeList:i?t.inviteeList:[e],timeout:n};return B.l(`${this._n}.createInviteCustomData customData:${JSON.stringify(o)}`),o}createCancelCustomData(t){const e=this._n+".createCancelCustomData",{inviteID:n}=t;let i;const s=this._signalingModule.getMyUserID(),{inviteeList:o,groupID:a,inviter:r}=this._signalingModule.getInviteInfo(n);return r===s?i={...this._createDefaultCustomData(t),actionType:It,groupID:a,inviter:s,inviteeList:o}:B.e(`${e} unmatched inviter:${r} and my userID:${s}`),B.l(`${e} customData:${JSON.stringify(i)}`),i}createAcceptCustomData(t){const e=this._n+".createAcceptCustomData",{inviteID:n}=t;let i;const s=this._signalingModule.getMyUserID(),{inviter:o,groupID:a,inviteeList:r}=this._signalingModule.getInviteInfo(n);return r.includes(s)?i={...this._createDefaultCustomData(t),actionType:ft,groupID:a,inviter:o,inviteeList:[s]}:B.e(`${e} userID:${s} not in inviteeList. inviteID:${n} groupID:${a}`),B.l(`${e} customData:${JSON.stringify(i)}`),i}createRejectCustomData(t){const e=this._n+".createRejectCustomData",{inviteID:n}=t;let i;const s=this._signalingModule.getMyUserID(),{inviter:o,groupID:a,inviteeList:r}=this._signalingModule.getInviteInfo(n);return r.includes(s)?i={...this._createDefaultCustomData(t),actionType:vt,groupID:a,inviter:o,inviteeList:[s]}:B.e(`${e} userID:${s} not in inviteeList. inviteID:${n} groupID:${a}`),B.l(`${e} customData:${JSON.stringify(i)}`),i}createTimeoutCustomData(t){const e=this._n+".createTimeoutCustomData",{inviteeList:n,inviter:i,isInviter:s=!1}=t,o=this._signalingModule.getMyUserID(),a={...this._createDefaultCustomData(t),actionType:pt,inviter:i,inviteeList:s?n:[o]};return B.l(`${e} customData:${JSON.stringify(a)}`),a}createSignalingMessage(t,n){const{groupID:i,inviter:s}=t,o=this._signalingModule.getModule(e),a={to:n||i||s,conversationType:i?$:N,priority:O,payload:{data:JSON.stringify(t)}},r=o.createCustomMessage(a);return B.d(`${this._n}.createSignalingMessage. message:${JSON.stringify(r)}`),r}}class wt{constructor(t){this._n="HistorySignalingHandler",this._signalingModule=t,this.EXPIRED_TIME=3e4,this.COUNT=20,this._signalingMap=new Map,this._signalingRelatedToMeMap=new Map}getHistorySignaling(){const t=this._signalingModule.getModule(n).getLocalConversationList();Q(t)||this._getSignalingList(t).then(t=>{this._handleSignalingList(t)})}_getSignalingList(t){const e=this._getValidConversationList(t),n=this._createPromiseList(e);return Q(n)?Promise.resolve([]):this._concurrentGetMessageList(n).then(t=>{let e=new Map;return t.forEach(t=>{const{signalingList:n}=t,i=this._getSignalingRelatedToMeMap(n);e=new Map([...e,...i])}),this._sortSignaling(e)})}_handleSignalingList(t){Q(t)||(B.d(`${this._n}._handleSignalingList signalingList:${JSON.stringify(t)}`),this._signalingModule.onNewMessageList(t))}_isSignalingNotExpired(t,e){return t[e]&&1e3*t[e]>(new Date).getTime()-this.EXPIRED_TIME}_getValidConversationList(t){const e=[];for(let n=0;n<t.length;n++){const{type:i,unreadCount:s,lastMessage:o}=t[n],a=this._isSignalingNotExpired(o,"lastTime");i!==U&&s>0&&a&&e.push(t[n])}return e}_createPromiseList(t){const e=[];for(let i=0;i<t.length;i++){const s=t[i],{conversationID:o,unreadCount:a,type:r}=s,l=r===N?a:this.COUNT;this._signalingMap.set(o,{needMessageCount:l,signalingList:[]});const g=this._signalingModule.getModule(n).getMessageList({conversationID:o});e.push(g)}return e}_concurrentGetMessageList(t){const e=[];return Promise.all(t).then(t=>{for(let n=0;n<t.length;n++){const{code:i,data:s}=t[n];if(0!==i||0===s.messageList.length)continue;this._handleMessageList(s.messageList);const o=this._relayGetMessageList(s);o&&e.push(o)}return e.length>0?this._concurrentGetMessageList(e):this._signalingMap})}_relayGetMessageList(t){const{messageList:e,nextReqMessageID:i,isCompleted:s}=t,o=e.length;if(0===o)return null;const{conversationID:a,conversationType:r}=e[0],{needMessageCount:l}=this._signalingMap.get(a),g=r===$,c=0===l||s,u=this._isSignalingNotExpired(e[o-1],"time");if(g||c||!u)return null;return this._signalingModule.getModule(n).getMessageList({conversationID:a,nextReqMessageID:i,count:l})}_handleMessageList(t){const e=t.length,{conversationID:n}=t[0],{needMessageCount:i,signalingList:s}=this._signalingMap.get(n),o=i-e>0?i-e:0,a=[];for(let l=0;l<t.length;l++){const e=t[l];this._isSignalingNotExpired(e,"time")&&a.push(e)}const r=this._signalingModule.filterMessageList(a);this._signalingMap.set(n,{needMessageCount:o,signalingList:s.concat(r)})}_getSignalingRelatedToMeMap(t){for(let e=0;e<t.length;e++){const n=t[e];this._saveSignalingRelatedToMe(n)}return this._signalingRelatedToMeMap}_saveSignalingRelatedToMe(t){const e=this._signalingModule.getRemoteSignalingHandler().getPayloadData(t)||{},{actionType:n="",inviteID:i="",inviteeList:s=[]}=e,o=this._signalingModule.getMyUserID();switch(n){case mt:s.includes(o)&&this._signalingRelatedToMeMap.set(i,{...e,messageList:[t]});break;case vt:case ft:this.updateHistoryInviteInfo(t);break;case It:this.deleteHistoryInviteInfo(i);break;case pt:this.updateHistoryInviteInfo(t)}}deleteHistoryInviteInfo(t){this._signalingRelatedToMeMap.has(t)&&this._signalingRelatedToMeMap.delete(t)}updateHistoryInviteInfo(t){const e=this._signalingModule.getRemoteSignalingHandler().getPayloadData(t)||{},{inviteID:n="",inviteeList:i=[]}=e;if(this._signalingRelatedToMeMap.has(n)){const{inviteeList:e,messageList:s}=this._signalingRelatedToMeMap.get(n);for(let t=0;t<i.length;t++){const n=i[t];e.includes(n)&&e.splice(e.indexOf(n),1)}0===e.length?this.deleteHistoryInviteInfo(n):s.push(t)}else this.deleteHistoryInviteInfo(n)}_sortSignaling(t){let e=[];t.forEach(t=>{e=[...e,...t.messageList]});return e.sort((function(t,e){return(t.time?t.time:0)-(e.time?e.time:0)}))}reset(){this._signalingMap.clear(),this._signalingRelatedToMeMap.clear()}}class Ct{constructor(t,e){this.businessID=t.businessID||1,this.inviteID=t.inviteID,this.groupID=t.groupID||"",this.inviter=t.inviter||"",this.inviteeList=t.inviteeList||[],this.data=t.data||"",this.actionType=t.actionType||mt,this.timeout=t.timeout||0}}const Tt={A2KEY_AND_TINYID_UPDATED:"_inner1",CLOUD_CONFIG_UPDATED:"_inner2",PROFILE_UPDATED:"_inner3",CONV_SYNC_COMPLETED:"_inner4",C2C_UNREAD_HANDLE_COMPLETED:"_inner5"};class Et extends class{constructor(t){this._m=t,this._n=""}isLoggedIn(){return this._m.getModule(i).isLoggedIn()}isOversea(){return this._m.getModule(i).isOversea()}isPrivateNetWork(){return this._m.getModule(i).isPrivateNetWork()}getFileDownloadProxy(){return this._m.getModule(i).getFileDownloadProxy()}getMyUserID(){return this._m.getModule(i).getUserID()}getMyTinyID(){return this._m.getModule(i).getTinyID()}getSDKAppID(){return this._m.getModule(i).getSDKAppID()}isIntl(){return this._m.getModule(i).isIntl()}isDevMode(){return this._m.getModule(i).isDevMode()}getModule(t){return this._m.getModule(t)}getPlatform(){return E}getNetworkType(){return this._m.getModule(s).getNetworkType()}probeNetwork(t){return this._m.getModule(s).probe(t)}getCloudConfig(t){return this._m.getModule(a).getCloudConfig(t)}emitOuterEvent(t,e){this._m.getOuterEmitterInstance().emit(t,e)}emitInnerEvent(t,e){this._m.getInnerEmitterInstance().emit(t,e)}getInnerEmitterInstance(){return this._m.getInnerEmitterInstance()}generateTjgID(t){return this._m.getModule(i).getTinyID()+"-"+t.random}filterModifiedMessage(t){if(Q(t))return;const e=t.filter(t=>!0===t.isModified);e.length>0&&this.emitOuterEvent(l,e)}filterUnmodifiedMessage(t){if(Q(t))return[];return t.filter(t=>!1===t.isModified)}request(t){return this._m.getModule(o).request(t)}canIUse(t){return this._m.getModule(r).canIUse(t)}getErrorMessage(t,e,n){return this._m.getErrorMessage(t,e,n)}outputWarning(t,e,n){const i=this.getErrorMessage(t,e,n);i&&B.w(i)}cannotUseCommercialAbility(t){const e=nt;return lt({code:e,message:this.getErrorMessage(e,t)})}}{constructor(e){super(e),this._n="SignalingModule",this._inviteInfoMap=new Map,this._outerEmitter=new t,this._outerEmitter._emit=this._outerEmitter.emit,this._outerEmitter.emit=function(...t){const e=[t[0],{name:t[0],data:t[1]}];this._outerEmitter._emit.apply(this._outerEmitter,[...e])}.bind(this),this._canIUseSignaling=!1,this._isHandling=!1,this._remoteSignalingHandler=new Dt(this),this._localSignalingHandler=new yt(this),this._historySignalingHandler=new wt(this),this._isC2CUnreadHandleCompleted=!1,this._isConvSyncCompleted=!1,this._isSyncCompleted=!1,this.getInnerEmitterInstance().on(Tt.C2C_UNREAD_HANDLE_COMPLETED,this.onC2CUnreadHandleCompleted,this),this.getInnerEmitterInstance().on(Tt.CONV_SYNC_COMPLETED,this.onConvSyncCompleted,this)}onC2CUnreadHandleCompleted(){this._isC2CUnreadHandleCompleted=!0,this._isC2CUnreadHandleCompleted&&this._isConvSyncCompleted&&!this._isSyncCompleted&&this.onReady()}onConvSyncCompleted(){this._isConvSyncCompleted=!0,this._isC2CUnreadHandleCompleted&&this._isConvSyncCompleted&&!this._isSyncCompleted&&this.onReady()}onReady(){B.l(this._n+".onReady"),this._isSyncCompleted=!0,this._historySignalingHandler.getHistorySignaling()}onNewMessageList(t){return this._remoteSignalingHandler.onNewMessageList(t)}onMessageModified(t){return this._remoteSignalingHandler.onMessageModified(t)}hasInviteInfo(t){return this._inviteInfoMap.has(t)}getInviteInfo(t){return this._inviteInfoMap.get(t)}setInviteInfo(t,e){const{message:n,...i}=e;B.l(`${this._n}.setInviteInfo inviteID:${t} data:${JSON.stringify(i)}`),this._inviteInfoMap.set(t,{...i,message:n})}deleteInviteInfo(t){this.hasInviteInfo(t)&&(B.l(`${this._n}.deleteInviteInfo inviteID:${t}.`),this._inviteInfoMap.delete(t))}updateInviteInfo(t){const e=this._n+".updateInviteInfo",{inviteID:n,inviter:i,inviteeList:s,groupID:o}=t;if(B.l(`${e} inviteID:${n} inviter:${i} groupID:${o}`),o&&this.hasInviteInfo(n)){const t=s[0],{inviteeList:i}=this.getInviteInfo(n);i.includes(t)&&(i.splice(i.indexOf(t),1),B.l(`${e} remove ${t}. localInviteeList.length:${i.length}`)),0===i.length&&this.deleteInviteInfo(n)}else this.deleteInviteInfo(n)}getLocalSignalingHandler(){return this._localSignalingHandler}getRemoteSignalingHandler(){return this._remoteSignalingHandler}canIUseSignaling(){return this._canIUseSignaling}emitEvent(t,e){this._outerEmitter.emit(t,e)}addSignalingListener(t,e,n){this._canIUseSignaling||(this._canIUseSignaling=!0),this._outerEmitter.on(t,e,n)}removeSignalingListener(t,e,n){this._outerEmitter.off(t,e,n),0===this._outerEmitter.eventNames().length&&(this._canIUseSignaling=!1)}invite(t){const e=this._n+".invite",{message:n,customData:i,inviteID:s}=this._localSignalingHandler.createInviteInfo(t);return B.l(`${e} options:${JSON.stringify(t)} inviteID:${s}`),this.sendSignaling(n,t).then(t=>t&&0===t.code?(this.setInviteInfo(s,{...i,message:n}),this.startTimer({...i,inviteID:s}),{...t,inviteID:s}):t).catch(t=>t)}inviteSync(t,e,n){const i=this._n+".inviteSync",{message:s,customData:o,inviteID:a}=this._localSignalingHandler.createInviteInfo(t);return B.l(`${i} options:${JSON.stringify(t)} inviteID:${a}`),this.sendSignaling(s,t).then(t=>{if(t&&0===t.code)return this.setInviteInfo(a,{...o,message:s}),this.startTimer({...o,inviteID:a}),e&&e({inviteID:a}),{inviteID:a};n&&n(0===t.code,t.message||"")}).catch(t=>(n&&n(t.code,t.message),t)),a}_handleImResponse(t,e,n){e&&0===e.code&&(this._isHandling=!1,n?this.deleteInviteInfo(t.inviteID):this.updateInviteInfo(t))}cancel(t){const e=this._n+".cancel";if(B.l(`${e} options:${JSON.stringify(t)}`),!this.hasInviteInfo(t.inviteID)||this._isHandling)return lt({code:ot});this._isHandling=!0;const n=this._localSignalingHandler.createCancelCustomData(t);if(!n)return this._isHandling=!1,lt({code:st});const{groupID:i,inviteeList:s}=n,o=i||s[0],a=this._localSignalingHandler.createSignalingMessage(n,o);return this.sendSignaling(a,t).then(e=>(this._handleImResponse(n,e,!0),0===e.code?{...e,inviteID:t.inviteID}:e)).catch(t=>t)}accept(t){const e=this._n+".accept";if(B.l(`${e} options:${JSON.stringify(t)}`),!this.hasInviteInfo(t.inviteID)||this._isHandling)return lt({code:it});this._isHandling=!0;const n=this._localSignalingHandler.createAcceptCustomData(t);if(!n)return this._isHandling=!1,lt({code:st});const i=this._localSignalingHandler.createSignalingMessage(n);return this.sendSignaling(i,t).then(e=>(this._handleImResponse(n,e),0===e.code?{...e,inviteID:t.inviteID}:e)).catch(t=>t)}reject(t){const e=this._n+".reject";if(B.l(`${e} options:${JSON.stringify(t)}`),!this.hasInviteInfo(t.inviteID)||this._isHandling)return lt({code:it});this._isHandling=!0;const n=this._localSignalingHandler.createRejectCustomData(t);if(!n)return this._isHandling=!1,lt({code:st});const i=this._localSignalingHandler.createSignalingMessage(n);return this.sendSignaling(i,t).then(e=>(this._handleImResponse(n,e,!0),0===e.code?{...e,inviteID:t.inviteID}:e)).catch(t=>t)}getSignalingInfo(t){const e=this._n+".getSignalingInfo",{ID:n,from:i,to:s}=t,o=this._filterSignalingMessage(t);let a=null;if(o){const e=this._remoteSignalingHandler.getPayloadData(t);a=new Ct(e)}const r=o?"actionType:"+a.actionType:"";return B.l(`${e} messageID:${n} from:${i} to:${s}${r} isSignaling:${o}`),a}modifyInvitation(t){const{inviteID:n,data:i}=t;if(!this.hasInviteInfo(t.inviteID)||this._isHandling)return lt({code:it});this._isHandling=!0;const{message:s,...o}=this.getInviteInfo(n),a=s.payload.data;o.data=i,s.payload.data=JSON.stringify(o);return this.getModule(e).modifyRemoteMessage(s).then(t=>(this.setInviteInfo(n,{...o,message:s}),this._isHandling=!1,t)).catch(t=>(this._isHandling=!1,s.payload.data=a,t))}_genMessageControlInfo(t={}){const{data:e="",onlineUserOnly:n,inviteID:i="",offlinePushInfo:s,actionType:o}=t;let a={_onlineOnlyFlag:!1};i&&this.getInviteInfo(i)&&(a=this.getInviteInfo(i).message);const r={onlineUserOnly:a._onlineOnlyFlag||n||!1,offlinePushInfo:s,messageControlInfo:{excludedFromContentModeration:!0,excludedFromUnreadCount:!1,excludedFromLastMessage:!1}};if(o===pt){const t=!!e.match(/excludeTimeoutSignalingFromHistoryMessage/);return r.messageControlInfo.excludedFromUnreadCount=t,r.messageControlInfo.excludedFromLastMessage=t,r}const l=!!e.match(/excludeFromHistoryMessage/),g=!!e.match(/excludeOriginalSignalingFromHistoryMessage/);return r.messageControlInfo.excludedFromUnreadCount=l||g,r.messageControlInfo.excludedFromLastMessage=l||g,r}sendSignaling(t,n){return this.getModule(e).sendMessageInstance(t,this._genMessageControlInfo(n)).catch(t=>(this._isHandling=!1,lt({code:at})))}filterMessageList(t){return t.filter(t=>this._filterSignalingMessage(t))}_filterSignalingMessage(t){let e=!1;if(t.type&&t.type===A){const{cloudCustomData:n="",payload:{data:i=""}}=t,s=n.match(/"type":"tsignaling"/),o=i.match(/inviteID/),a=i.match(/actionType/);e=s||o&&a}return!!e}startTimer(t){const e=this._n+".startTimer",{timeout:n,inviteID:i,inviter:s,groupID:o}=t,a=s===this.getMyUserID();if(B.l(`${e} timeout:${n} isInviter:${a} groupID:${o}`),n<=0)return;const r=a?n+5:n;let l=1;const g=setInterval(()=>{const n=this._hasLocalInviteInfo(t,a);l<r&&n?++l:(n&&this._sendTimeoutNotice(i,a),B.l(e+" end."),clearInterval(g))},1e3)}_hasLocalInviteInfo(t,e){const{inviteID:n,groupID:i}=t;if(!this.hasInviteInfo(n))return!1;const s=this._n+"._hasLocalInviteInfo",{inviteeList:o}=this.getInviteInfo(n);return B.l(`${s} inviteID:${n} inviteeList:${o} groupID:${i}`),!i||(e?o.length>0:o.length>0&&o.includes(this.getMyUserID()))}_getReceiver(t,e){const{groupID:n,inviteeList:i,inviter:s}=e;return t?n||i[0]:n||s}_sendTimeoutNotice(t,e){const n=this.getInviteInfo(t),i=this._getReceiver(e,n);B.l(`${this._n}._sendTimeoutNotice inviteID:${t} to:${i} isInviter:${e}`);const s=this._localSignalingHandler.createTimeoutCustomData({...n,isInviter:e}),o=this._localSignalingHandler.createSignalingMessage(s,i);return this.sendSignaling(o,s).then(n=>{if(n&&0===n.code){const{data:n,groupID:i,inviteeList:a,inviter:r}=s;this.emitEvent(dt,{data:n,groupID:i,inviteID:t,inviteeList:a,inviter:r,isSelfTimeout:!0,message:o}),e?this.deleteInviteInfo(t):this.updateInviteInfo(s)}})}reset(){B.l(this._n+".reset"),this._inviteInfoMap.clear(),this._canIUseSignaling=!1,this._isHandling=!1,this._historySignalingHandler.reset(),this._isC2CUnreadHandleCompleted=!1,this._isConvSyncCompleted=!1,this._isSyncCompleted=!1}}export{Et as default};
