import 'package:dio/dio.dart';
import '../http/dio_instance.dart';
import '../models/base_response.dart';
import '../postData/send_red_packet_data.dart';
import '../postData/get_red_packet_cover_list.dart';
import '../models/coverList_response.dart';
import '../models/getPacket_response.dart';
import '../postData/get_red_packet_list.dart';
import '../models/getPacketRecord_response.dart';
import '../models/packetAmount_response.dart';
import '../models/getPacketDetail_response.dart';


class Api {
  static Api instance = Api._();

  Api._();

  // 发送红包
  Future<BaseResponse> sendRedPacket(SendRedPacketData data) async {
    Response response = await DioInstance.instance().post(path: '/redPacket/send', data: data.toJson());
    BaseResponse uploadResponse = BaseResponse.fromJson(response.data);
    return uploadResponse;
  }

  // 分页查询红包封面列表
  Future<CoverList> getRedPacketCoverList(GetRedPacketCoverListData data) async {
    Response response = await DioInstance.instance().post(path: '/redPacket/coverPage', data: data.toJson());
    CoverList uploadResponse = CoverList.fromJson(response.data);
    return uploadResponse;
  }

  // 领取红包
  Future<GetPacketData> receiveRedPacket(dynamic packetId) async {
    Response response = await DioInstance.instance().get(path: '/redPacket/get', queryParameters: {'packetId': packetId});
    GetPacketData uploadResponse = GetPacketData.fromJson(response.data);
    return uploadResponse;
  }
  // 查询红包记录
  Future<PacketRecordData> getRedPacketRecord(GetRedPacketListData data) async {
    Response response = await DioInstance.instance().post(path: '/redPacket/queryRecord', data: data.toJson());
    PacketRecordData uploadResponse = PacketRecordData.fromJson(response.data);
    return uploadResponse;
  }
  // 查询红包金额汇总
  Future<PacketAmountResponse> getRedPacketAmount(GetRedPacketListData data) async {
    Response response = await DioInstance.instance().post(path: '/redPacket/queryRecordTotal', data: data.toJson());
    PacketAmountResponse uploadResponse = PacketAmountResponse.fromJson(response.data);
    return uploadResponse;
  }

  // 查询单个红包
  Future<PacketDetailResponse> getRedPacketDetail(String packetId) async {
    Response response = await DioInstance.instance().get(path: '/redPacket/queryDetail', queryParameters: {'packetId': packetId});
    PacketDetailResponse uploadResponse = PacketDetailResponse.fromJson(response.data);
    return uploadResponse;
  }
}
