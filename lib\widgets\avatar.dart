import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

class Avatar extends StatelessWidget {
  final String? avatarUrl;
  final double size;
  final double radius;
  final bool showBorder;
  final Color borderColor;
  final double borderWidth;
  static const String defaultAvatarPath = 'assets/default_c2c_head.png';

  const Avatar({
    Key? key,
    this.avatarUrl,
    this.size = 50,
    this.radius = 25,
    this.showBorder = false,
    this.borderColor = Colors.white,
    this.borderWidth = 2,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Widget avatar = ClipRRect(
      borderRadius: BorderRadius.circular(radius),
      child: avatarUrl != null && avatarUrl!.isNotEmpty
          ? CachedNetworkImage(
              imageUrl: avatarUrl!,
              width: size,
              height: size,
              fit: BoxFit.cover,
              errorWidget: (context, error, stackTrace) {
                return Image.asset(
                  defaultAvatarPath,
                  width: size,
                  height: size,
                  fit: BoxFit.cover,
                );
              },
            )
          : Image.asset(
              defaultAvatarPath,
              width: size,
              height: size,
              fit: BoxFit.cover,
            ),
    );

    if (showBorder) {
      avatar = Container(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
            color: borderColor,
            width: borderWidth,
          ),
        ),
        child: avatar,
      );
    }

    return avatar;
  }
}