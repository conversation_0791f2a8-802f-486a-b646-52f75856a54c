class PacketRecordData {
  int? code;
  String? level;
  String? msg;
  bool? ok;
  Data? data;
  int? dataType;

  PacketRecordData({this.code, this.level, this.msg, this.ok, this.data, this.dataType});

  PacketRecordData.fromJson(Map<String, dynamic> json) {
    if(json["code"] is int) {
      code = json["code"];
    }
    if(json["level"] is String) {
      level = json["level"];
    }
    if(json["msg"] is String) {
      msg = json["msg"];
    }
    if(json["ok"] is bool) {
      ok = json["ok"];
    }
    if(json["data"] is Map) {
      data = json["data"] == null ? null : Data.fromJson(json["data"]);
    }
    if(json["dataType"] is int) {
      dataType = json["dataType"];
    }
  }

  static List<PacketRecordData> fromList(List<Map<String, dynamic>> list) {
    return list.map(PacketRecordData.fromJson).toList();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> _data = <String, dynamic>{};
    _data["code"] = code;
    _data["level"] = level;
    _data["msg"] = msg;
    _data["ok"] = ok;
    if(data != null) {
      _data["data"] = data?.toJson();
    }
    _data["dataType"] = dataType;
    return _data;
  }
}

class Data {
  int? pageNum;
  int? pageSize;
  int? total;
  int? pages;
  List<RecordItem>? list;
  bool? emptyFlag;

  Data({this.pageNum, this.pageSize, this.total, this.pages, this.list, this.emptyFlag});

  Data.fromJson(Map<String, dynamic> json) {
    if(json["pageNum"] is int) {
      pageNum = json["pageNum"];
    }
    if(json["pageSize"] is int) {
      pageSize = json["pageSize"];
    }
    if(json["total"] is int) {
      total = json["total"];
    }
    if(json["pages"] is int) {
      pages = json["pages"];
    }
    if(json["list"] is List) {
      list = json["list"] == null ? null : (json["list"] as List).map((e) => RecordItem.fromJson(e)).toList();
    }
    if(json["emptyFlag"] is bool) {
      emptyFlag = json["emptyFlag"];
    }
  }

  static List<Data> fromList(List<Map<String, dynamic>> list) {
    return list.map(Data.fromJson).toList();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> _data = <String, dynamic>{};
    _data["pageNum"] = pageNum;
    _data["pageSize"] = pageSize;
    _data["total"] = total;
    _data["pages"] = pages;
    if(list != null) {
      _data["list"] = list?.map((e) => e.toJson()).toList();
    }
    _data["emptyFlag"] = emptyFlag;
    return _data;
  }
}

class RecordItem {
  int? id;
  String? nick;
  String? phone;
  String? chatType;
  int? chatId;
  String? totalAmount;
  String? createTime;

  RecordItem({this.id, this.nick, this.phone, this.chatType, this.chatId, this.totalAmount, this.createTime});

  RecordItem.fromJson(Map<String, dynamic> json) {
    if(json["id"] is int) {
      id = json["id"];
    }
    if(json["nick"] is String) {
      nick = json["nick"];
    }
    if(json["phone"] is String) {
      phone = json["phone"];
    }
    if(json["chatType"] is String) {
      chatType = json["chatType"];
    }
    if(json["chatId"] is int) {
      chatId = json["chatId"];
    }
    if(json["totalAmount"] != null) {
      if(json["totalAmount"] is int) {
        totalAmount = json["totalAmount"].toString();
      } else if(json["totalAmount"] is double) {
        totalAmount = json["totalAmount"].toString();
      } else if(json["totalAmount"] is String) {
        totalAmount = json["totalAmount"];
      }
    }
    if(json["createTime"] is String) {
      createTime = json["createTime"];
    }
  }

  static List<RecordItem> fromList(List<Map<String, dynamic>> list) {
    return list.map(RecordItem.fromJson).toList();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> _data = <String, dynamic>{};
    _data["id"] = id;
    _data["nick"] = nick;
    _data["phone"] = phone;
    _data["chatType"] = chatType;
    _data["chatId"] = chatId;
    _data["totalAmount"] = totalAmount;
    _data["createTime"] = createTime;
    return _data;
  }
}