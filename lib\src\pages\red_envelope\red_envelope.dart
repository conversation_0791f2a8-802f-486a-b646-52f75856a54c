import 'package:flutter/material.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';
import 'package:tencent_cloud_chat_demo/models/coverList_response.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_message.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_msg_create_info_result.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_value_callback.dart';
import 'package:tencent_cloud_chat_sdk/tencent_im_sdk_plugin.dart';
import 'package:tencent_cloud_chat_uikit/ui/controller/tim_uikit_chat_controller.dart';
import '../../../utils/toast.dart';
import './setting_red_envelope.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tencent_cloud_chat_demo/utils/user_info_local.dart';
import './red_envelope_record.dart';
import '../../../apis/redPacket_api.dart';
import '../../../postData/send_red_packet_data.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_conversation.dart';
import '../../../utils/toast.dart';
import '../../provider/cover_provide.dart';
import 'package:provider/provider.dart';

class RedEnvelopePage extends StatefulWidget {
  final TIMUIKitChatController? chatController;
  final V2TimConversation? conversation;

  const RedEnvelopePage({Key? key, this.chatController, this.conversation})
      : super(key: key);

  @override
  State<RedEnvelopePage> createState() => _RedEnvelopePageState();
}

class _RedEnvelopePageState extends State<RedEnvelopePage> {
  final TextEditingController _amountController = TextEditingController(); // 金额
  final TextEditingController _remarkController =
      TextEditingController(); // 红包备注
  // 红包个数
  final TextEditingController _countController = TextEditingController();
  late TIMUIKitChatController _timuiKitChatController;

  // 默认祝福语
  final String _defaultRemark = TIM_t('恭喜发财，大吉大利');

  // 默认红包显示金额
  String _defaultAmount = '0.00';

  // 默认红包个数
  int _defaultCount = 1;

  // 默认货币符号
  final String _defaultCurrencySymbol = '₱';

  // 类型单聊和群聊 1:单聊 2:群聊
  int _chatType = 1;

  // 单聊下默认选择普通红包
  bool _isRandomRedEnvelope = false;

  late CoverProvider coverProvider;

  // 读取本地缓存的红包封面
  CoverItem selectedRedEnvelopeType = CoverItem();

  // 输入金额变化
  void _onAmountChanged(String value) {
    // 判断输入的金额是否合法, 只能是数字
    if (value.isEmpty || !RegExp(r'^[0-9]+(\.[0-9]{1,2})?$').hasMatch(value)) {
      _defaultAmount = '0.00';
    } else {
      _defaultAmount = double.parse(value).toStringAsFixed(2);
    }
    setState(() {});
  }

  // 输入红包个数变化
  void _onCountChanged(String value) {
    // 判断输入的个数是否合法, 只能是数字
    if (value.isEmpty || !RegExp(r'^[0-9]+$').hasMatch(value)) {
      _defaultCount = 1;
    } else {
      _defaultCount = int.parse(value);
    }
    setState(() {});
  }

  // 发红包
  handleSendRedEnvelope() {
    if (_isRandomRedEnvelope) {
      if (_countController.text.isEmpty) {
        ToastUtils.toastTop(TIM_t('请输入红包个数'));
        return;
      }
      if (int.parse(_countController.text) < 2) {
        ToastUtils.toastTop(TIM_t('拼手气红包个数不能小于2'));
        return;
      }
    }

    // 校验输入
    if (_amountController.text.isEmpty) {
      ToastUtils.toastTop(TIM_t('请输入红包金额'));
      return;
    }
    if (double.parse(_amountController.text) <= 0) {
      ToastUtils.toastTop(TIM_t('请输入合法的红包金额'));
      return;
    }
    if (!RegExp(r'^[0-9]+(\.[0-9]{1,2})?$').hasMatch(_amountController.text)) {
      ToastUtils.toastTop(TIM_t('请输入合法的红包金额'));
      return;
    }

    // 对方用户ID
    final chatId = widget.conversation?.userID;
    final groupID = widget.conversation?.groupID;
 
    // 打印需要传输的值
    debugPrint('发送红包id: ${chatId}');
    final params = SendRedPacketData(
      amount: _amountController.text,
      remark: _remarkController.text.isNotEmpty
          ? _remarkController.text
          : _defaultRemark,
      coverId: selectedRedEnvelopeType.id.toString(),
      chat: _chatType == 1 ? chatId!.toString() : groupID!.toString(),
      chatType: _chatType == 1 ? 'private' : 'group',
      type: _isRandomRedEnvelope ? 'lucky' : 'normal',
      totalCount: _defaultCount,
    );
    debugPrint('发送红包参数: ${widget.conversation?.toJson()}');
    ToastUtils.showLoading();
    Api.instance.sendRedPacket(params).then((res) {
      if (res.code == 0 && res.ok!) {
        final redPacketId = res.data;
        _sendCustomMessage(params, redPacketId, selectedRedEnvelopeType.sort,
            selectedRedEnvelopeType.url);
            
      }
    }).catchError((err) {
      ToastUtils.hideLoading();
    });
  }

  // 跳转红包设置页
  void _navigateToSettingRedEnvelope() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const SettingRedEnvelopePage()),
    );
    
    if (result != null) {
      // 处理返回的数据
      setState(() {
        // 更新状态
        coverProvider = Provider.of<CoverProvider>(context, listen: false);

        //  设置红包封面
        selectedRedEnvelopeType = coverProvider.selectedRedEnvelopeType;
        debugPrint('设置红包封面: ${selectedRedEnvelopeType.toJson()}');
      });
    }
  }

  _sendCustomMessage(data, redPacketId, sort, url) async {
    // 创建自定义消息，下方的data/desc/extension可由您自行定义内容。
    V2TimValueCallback<V2TimMsgCreateInfoResult> createCustomMessageRes =
        await TencentImSDKPlugin.v2TIMManager
            .getMessageManager()
            .createCustomMessage(
              data:
                  '{"redEnvelopeID":"red_envelope","isOpen":false,"status":"","image":"$url","remark":"${data.remark}","amount":"${data.amount}","isDefault":${sort == 0},"redPacketId":"$redPacketId","version":4, "type": "${data.type}", "totalCount":"${data.totalCount}", "chatType":"${data.chatType}", "openUsers":[]}',
            );
    if (createCustomMessageRes.code == 0 &&
        createCustomMessageRes.data != null &&
        createCustomMessageRes.data?.messageInfo != null) {
      String? id = createCustomMessageRes.data?.id;
      debugPrint('创建自定义消息: ${createCustomMessageRes.data?.id} url: $url');
      // 发送自定义消息
      V2TimValueCallback<V2TimMessage>? sendMessageRes =
          await _timuiKitChatController.sendMessage(
              messageInfo: createCustomMessageRes.data!.messageInfo!);
      debugPrint('发送自定义消息: ${sendMessageRes?.data?.toJson()}');
      if (sendMessageRes != null && sendMessageRes.code == 0) {
        // 发送成功
        sendMessageRes.data?.customElem?.data; //自定义data
        sendMessageRes.data?.customElem?.desc; //自定义desc
        sendMessageRes.data?.customElem?.extension; //自定义extension
        debugPrint(
            'sendMessageRes.data?.customElem?.data${sendMessageRes.data?.customElem?.data}');
        Navigator.pop(context);
        ToastUtils.hideLoading();
        return true;
      }
      return false;
    }
  }

  @override
  void initState() {
    super.initState();
    coverProvider = Provider.of<CoverProvider>(context, listen: false);

    //  设置红包封面
    selectedRedEnvelopeType = coverProvider.selectedRedEnvelopeType;
    debugPrint('设置红包封面: ${selectedRedEnvelopeType.toJson()}');

    _timuiKitChatController = widget.chatController ?? TIMUIKitChatController();
    _chatType = widget.conversation?.type ?? 1;
    // 群聊下默认选择拼手气红包
    _isRandomRedEnvelope = _chatType == 2 ? true : false;
  }

  @override
  void dispose() {
    _amountController.dispose();
    _remarkController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(
      context,
      designSize: const Size(375, 812),
      minTextAdapt: true,
    );
    // 判断是否选择的红包封面并且选择的封面不是默认封面

    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: const Color(0xFFF4F7F8),
      appBar: AppBar(
        backgroundColor: const Color(0xFFF4F7F8),
        automaticallyImplyLeading: false,
        title: Row(
          children: [
            GestureDetector(
              onTap: () => Navigator.pop(context),
              child: Row(
                children: [
                  Image.asset('assets/icon_right_back.png',
                      width: 24, height: 24),
                  const SizedBox(width: 8),
                  Text(TIM_t('发红包'),
                      style: TextStyle(color: Color(0xFF333333), fontSize: 16))
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => const RedEnvelopeRecordPage()));
            },
            child: Text(TIM_t('红包记录'),
                style: const TextStyle(
                    color: Color(0xFF666666),
                    fontSize: 16,
                    fontWeight: FontWeight.w400)),
          ),
        ],
      ),
      body: Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            if (_chatType == 2) _buildRandomRedEnvelope(),
            if (_chatType == 1)
              Container(
                padding:
                    const EdgeInsets.symmetric(vertical: 16, horizontal: 17),
                decoration: BoxDecoration(
                  color: const Color(0xFFFFFFFF),
                  borderRadius: BorderRadius.circular(5),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(TIM_t('金额'),
                        style: const TextStyle(
                            color: Color(0xFF333333),
                            fontSize: 14,
                            fontWeight: FontWeight.w400)),
                    const Spacer(),
                    SizedBox(
                      width: 150,
                      height: 24,
                      child: TextField(
                        onChanged: (value) {
                          _onAmountChanged(value);
                        },
                        keyboardType: TextInputType.number,
                        controller: _amountController,
                        textAlign: TextAlign.right,
                        decoration: InputDecoration(
                          contentPadding: const EdgeInsets.symmetric(
                              vertical: 0, horizontal: 8),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(5),
                            borderSide: BorderSide.none,
                          ),
                          hintText: _defaultCurrencySymbol + '0.00',
                          hintStyle: const TextStyle(
                              color: Color(0xFF999999),
                              fontSize: 14,
                              fontWeight: FontWeight.w400),
                        ),
                      ),
                    )
                  ],
                ),
              ),
            Container(
              margin: const EdgeInsets.only(top: 16),
              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 17),
              decoration: BoxDecoration(
                color: const Color(0xFFFFFFFF),
                borderRadius: BorderRadius.circular(5),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(TIM_t('祝福语'),
                      style: const TextStyle(
                          color: Color(0xFF333333),
                          fontSize: 14,
                          fontWeight: FontWeight.w400)),
                  const Spacer(),
                  SizedBox(
                    width: 150,
                    height: 24,
                    child: TextField(
                      controller: _remarkController,
                      textAlign: TextAlign.right,
                      decoration: InputDecoration(
                        contentPadding: const EdgeInsets.symmetric(
                            vertical: 0, horizontal: 8),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(5),
                          borderSide: BorderSide.none,
                        ),
                        hintText: _defaultRemark,
                        hintStyle: const TextStyle(
                            color: Color(0xFF999999),
                            fontSize: 14,
                            fontWeight: FontWeight.w400),
                      ),
                    ),
                  )
                ],
              ),
            ),
            _coverView(),
            Container(
              margin: const EdgeInsets.only(top: 32, bottom: 32),
              child: Text.rich(TextSpan(
                  text: _defaultCurrencySymbol,
                  style: const TextStyle(
                      color: Color(0xFF333333),
                      fontSize: 24,
                      fontWeight: FontWeight.w400),
                  children: [
                    TextSpan(
                      text: _defaultAmount,
                      style: const TextStyle(
                          color: Color(0xFF333333),
                          fontSize: 36,
                          fontWeight: FontWeight.w400),
                    )
                  ])),
            ),
            InkWell(
              onTap: () {
                handleSendRedEnvelope();
              },
              child: Container(
                width: 200,
                height: 40,
                decoration: BoxDecoration(
                  color: const Color(0xFFFF5D5E),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: Text(TIM_t('塞钱进红包'),
                      style: const TextStyle(
                          color: Color(0xFFFFFFFF),
                          fontSize: 16,
                          fontWeight: FontWeight.w400)),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _coverView() {
    return Consumer<CoverProvider>(builder: (context, coverProvider, child) {
      bool isDefaultRedEnvelopeType =
          coverProvider.selectedRedEnvelopeType.sort != 0;
      if (coverProvider.selectedRedEnvelopeType.id == null) {
        isDefaultRedEnvelopeType = true;
      }
      debugPrint('是否选择的红包封面并且选择的封面不是默认封面: $isDefaultRedEnvelopeType selectedRedEnvelopeType: ${coverProvider.selectedRedEnvelopeType.toJson()}');

      return !isDefaultRedEnvelopeType
          ? Container(
              margin: const EdgeInsets.only(top: 16),
              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 17),
              decoration: BoxDecoration(
                color: const Color(0xFFFFFFFF),
                borderRadius: BorderRadius.circular(5),
              ),
              child: InkWell(
                onTap: _navigateToSettingRedEnvelope,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(TIM_t('红包封面'),
                        style: const TextStyle(
                            color: Color(0xFF333333),
                            fontSize: 14,
                            fontWeight: FontWeight.w400)),
                    const Spacer(),
                    SizedBox(
                      width: 16,
                      height: 16,
                      child: Image.asset('assets/icon_right.png',
                          width: 16, height: 16),
                    )
                  ],
                ),
              ))
          : Container(
              margin: const EdgeInsets.only(top: 16),
              padding: const EdgeInsets.symmetric(vertical: 26, horizontal: 16),
              height: 68,
              decoration: BoxDecoration(
                color: const Color(0xFFFFFFFF),
                borderRadius: BorderRadius.circular(5),
                image: DecorationImage(
                  image: NetworkImage(
                      coverProvider.selectedRedEnvelopeType.url ?? ''),
                  fit: BoxFit.cover,
                ),
              ),
              child: InkWell(
                onTap: _navigateToSettingRedEnvelope,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(TIM_t('红包封面'),
                        style: const TextStyle(
                            color: Color(0xFF333333),
                            fontSize: 14,
                            fontWeight: FontWeight.w400)),
                    const Spacer(),
                    SizedBox(
                      width: 16,
                      height: 16,
                      child: Image.asset(
                        'assets/icon_right.png',
                        width: 16,
                        height: 16,
                        color: const Color(0xFF000000),
                      ),
                    )
                  ],
                ),
              ));
    });
  }

  Widget _buildRandomRedEnvelope() {
    return Column(
      children: [
        GestureDetector(
          onTap: () =>
              {_isRandomRedEnvelope = !_isRandomRedEnvelope, setState(() {})},
          child: Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: Row(
              children: [
                Text(_isRandomRedEnvelope ? TIM_t('拼手气红包') : TIM_t('普通红包'),
                    style: const TextStyle(
                        color: Color(0xFFD3A425),
                        fontSize: 12,
                        fontWeight: FontWeight.w400)),
                const SizedBox(
                  width: 4,
                ),
                Image.asset('assets/red_envelope/down.png',
                    width: 16, height: 16)
              ],
            ),
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 17),
          margin: const EdgeInsets.only(bottom: 16),
          decoration: BoxDecoration(
            color: const Color(0xFFFFFFFF),
            borderRadius: BorderRadius.circular(5),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(TIM_t('红包个数'),
                  style: const TextStyle(
                      color: Color(0xFF333333),
                      fontSize: 14,
                      fontWeight: FontWeight.w400)),
              const Spacer(),
              SizedBox(
                width: 150,
                height: 24,
                child: TextField(
                  onChanged: (value) {
                    _onCountChanged(value);
                  },
                  keyboardType: TextInputType.number,
                  controller: _countController,
                  textAlign: TextAlign.right,
                  decoration: InputDecoration(
                    contentPadding:
                        const EdgeInsets.symmetric(vertical: 0, horizontal: 8),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(5),
                      borderSide: BorderSide.none,
                    ),
                    hintText: TIM_t('填写红包个数'),
                    hintStyle: const TextStyle(
                        color: Color(0xFF999999),
                        fontSize: 14,
                        fontWeight: FontWeight.w400),
                  ),
                ),
              )
            ],
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 17),
          decoration: BoxDecoration(
            color: const Color(0xFFFFFFFF),
            borderRadius: BorderRadius.circular(5),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(_isRandomRedEnvelope ? TIM_t('总金额') : TIM_t('单个金额'),
                  style: const TextStyle(
                      color: Color(0xFF333333),
                      fontSize: 14,
                      fontWeight: FontWeight.w400)),
              const Spacer(),
              SizedBox(
                width: 150,
                height: 24,
                child: TextField(
                  onChanged: (value) {
                    _onAmountChanged(value);
                  },
                  keyboardType: TextInputType.number,
                  controller: _amountController,
                  textAlign: TextAlign.right,
                  decoration: InputDecoration(
                    contentPadding:
                        const EdgeInsets.symmetric(vertical: 0, horizontal: 8),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(5),
                      borderSide: BorderSide.none,
                    ),
                    hintText: _defaultCurrencySymbol + '0.00',
                    hintStyle: const TextStyle(
                        color: Color(0xFF999999),
                        fontSize: 14,
                        fontWeight: FontWeight.w400),
                  ),
                ),
              )
            ],
          ),
        ),
      ],
    );
  }
}
