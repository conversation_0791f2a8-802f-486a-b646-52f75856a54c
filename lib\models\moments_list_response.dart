class MomentsListResponse {
  int? code;
  String? level;
  String? msg;
  bool? ok;
  Data? data;
  int? dataType;

  MomentsListResponse({this.code, this.level, this.msg, this.ok, this.data, this.dataType});

  MomentsListResponse.fromJson(Map<String, dynamic> json) {
    if(json["code"] is int) {
      code = json["code"];
    }
    if(json["level"] is String) {
      level = json["level"];
    }
    if(json["msg"] is String) {
      msg = json["msg"];
    }
    if(json["ok"] is bool) {
      ok = json["ok"];
    }
    if(json["data"] is Map) {
      data = json["data"] == null ? null : Data.fromJson(json["data"]);
    }
    if(json["dataType"] is int) {
      dataType = json["dataType"];
    }
  }

  static List<MomentsListResponse> fromList(List<Map<String, dynamic>> list) {
    return list.map((e) => MomentsListResponse.fromJson(e)).toList();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> _data = <String, dynamic>{};
    _data["code"] = code;
    _data["level"] = level;
    _data["msg"] = msg;
    _data["ok"] = ok;
    if(data != null) {
      _data["data"] = data?.toJson();
    }
    _data["dataType"] = dataType;
    return _data;
  }
}

class Data {
  int? pageNum;
  int? pageSize;
  int? total;
  int? pages;
  List<MomentItem>? list;
  bool? emptyFlag;

  Data({this.pageNum, this.pageSize, this.total, this.pages, this.list, this.emptyFlag});

  Data.fromJson(Map<String, dynamic> json) {
    if(json["pageNum"] is int) {
      pageNum = json["pageNum"];
    }
    if(json["pageSize"] is int) {
      pageSize = json["pageSize"];
    }
    if(json["total"] is int) {
      total = json["total"];
    }
    if(json["pages"] is int) {
      pages = json["pages"];
    }
    if(json["list"] is List) {
      list = json["list"] == null ? null : (json["list"] as List).map((e) => MomentItem.fromJson(e)).toList();
    }
    if(json["emptyFlag"] is bool) {
      emptyFlag = json["emptyFlag"];
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> _data = <String, dynamic>{};
    _data["pageNum"] = pageNum;
    _data["pageSize"] = pageSize;
    _data["total"] = total;
    _data["pages"] = pages;
    if(list != null) {
      _data["list"] = list?.map((e) => e.toJson()).toList();
    }
    _data["emptyFlag"] = emptyFlag;
    return _data;
  }
}

class MomentItem {
  int? id;
  int? userId;
  String? phone;
  String? name;
  String? avatar;
  String? content;
  String? permissionType;
  String? longitude;
  String? latitude;
  String? address;
  String? mediaType;
  String? mediaUrl;
  int? sortOrder;
  String? createTime;
  List<MediaList>? mediaList;
  List<LikeUserList>? likeUserList;
  List<CommentList>? commentList;

  MomentItem({this.id, this.userId, this.phone, this.name, this.avatar, this.content, this.permissionType, this.longitude, this.latitude, this.address, this.mediaType, this.mediaUrl, this.sortOrder, this.createTime, this.mediaList, this.likeUserList, this.commentList});

  MomentItem.fromJson(Map<String, dynamic> json) {
    if(json["id"] != null) {
      if(json["id"] is int) {
        id = json["id"];
      } else if(json["id"] is String) {
        id = int.tryParse(json["id"]);
      } else if(json["id"] is double) {
        id = json["id"].toInt();
      }
    }
    if(json["userId"] != null) {
      if(json["userId"] is int) {
        userId = json["userId"];
      } else if(json["userId"] is String) {
        userId = int.tryParse(json["userId"]);
      } else if(json["userId"] is double) {
        userId = json["userId"].toInt();
      }
    }
    if(json["phone"] is String) {
      phone = json["phone"];
    }
    if(json["name"] is String) {
      name = json["name"];
    }
    if(json["avatar"] is String) {
      avatar = json["avatar"];
    }
    if(json["content"] is String) {
      content = json["content"];
    }
    if(json["permissionType"] is String) {
      permissionType = json["permissionType"];
    }
    if(json["longitude"] is String) {
      longitude = json["longitude"];
    }
    if(json["latitude"] is String) {
      latitude = json["latitude"];
    }
    if(json["address"] is String) {
      address = json["address"];
    }
    if(json["mediaType"] is String) {
      mediaType = json["mediaType"];
    }
    if(json["mediaUrl"] is String) {
      mediaUrl = json["mediaUrl"];
    }
    if(json["sortOrder"] is int) {
      sortOrder = json["sortOrder"];
    }
    if(json["createTime"] is String) {
      createTime = json["createTime"];
    }
    if(json["mediaList"] is List) {
      mediaList = json["mediaList"] == null ? null : (json["mediaList"] as List).map((e) => MediaList.fromJson(e)).toList();
    }
    if(json["likeUserList"] is List) {
      likeUserList = (json["likeUserList"] as List).map((e) => LikeUserList.fromJson(e)).toList();
    }
    if(json["commentList"] is List) {
      commentList = json["commentList"] == null ? null : (json["commentList"] as List).map((e) => CommentList.fromJson(e)).toList();
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> _data = <String, dynamic>{};
    _data["id"] = id;
    _data["userId"] = userId;
    _data["phone"] = phone;
    _data["name"] = name;
    _data["avatar"] = avatar;
    _data["content"] = content;
    _data["permissionType"] = permissionType;
    _data["longitude"] = longitude;
    _data["latitude"] = latitude;
    _data["address"] = address;
    _data["mediaType"] = mediaType;
    _data["mediaUrl"] = mediaUrl;
    _data["sortOrder"] = sortOrder;
    _data["createTime"] = createTime;
    if(mediaList != null) {
      _data["mediaList"] = mediaList?.map((e) => e.toJson()).toList();
    }
    if(likeUserList != null) {
      _data["likeUserList"] = likeUserList;
    }
    if(commentList != null) {
      _data["commentList"] = commentList?.map((e) => e.toJson()).toList();
    }
    return _data;
  }
}

class CommentList {
  int? id;
  String? userPhone;
  int? momentId;
  int? friendUserId;
  String? content;
  int? replyCommentId;
  int? replyUserId;
  String? replyName;
  String? name;
  String? friendNick;
  String? friendFaceUrl;
  String? replyNick;
  String? replyFaceUrl;

  CommentList({this.id, this.userPhone, this.momentId, this.friendUserId, this.content,
   this.replyCommentId, this.replyUserId, this.replyName, this.friendNick, this.friendFaceUrl, this.replyNick, this.replyFaceUrl});

  CommentList.fromJson(Map<String, dynamic> json) {
    if(json["id"] != null) {
      if(json["id"] is int) {
        id = json["id"];
      } else if(json["id"] is String) {
        id = int.tryParse(json["id"]);
      } else if(json["id"] is double) {
        id = json["id"].toInt();
      }
    }
    if(json["userPhone"] is String) {
      userPhone = json["userPhone"];
    }
    if(json["momentId"] != null) {
      if(json["momentId"] is int) {
        momentId = json["momentId"];
      } else if(json["momentId"] is String) {
        momentId = int.tryParse(json["momentId"]);
      } else if(json["momentId"] is double) {
        momentId = json["momentId"].toInt();
      }
    }
    if(json["friendUserId"] != null) {
      if(json["friendUserId"] is int) {
        friendUserId = json["friendUserId"];
      } else if(json["friendUserId"] is String) {
        friendUserId = int.tryParse(json["friendUserId"]);
      } else if(json["friendUserId"] is double) {
        friendUserId = json["friendUserId"].toInt();
      }
    }
    if(json["replyName"] is String) {
      replyName = json["replyName"];
    }
    if(json["replyUserId"] != null) {
      if(json["replyUserId"] is int) {
        replyUserId = json["replyUserId"];
      } else if(json["replyUserId"] is String) {
        replyUserId = int.tryParse(json["replyUserId"]);
      } else if(json["replyUserId"] is double) {
        replyUserId = json["replyUserId"].toInt();
      }
    }
    if(json["content"] is String) {
      content = json["content"];
    }
    if(json["replyCommentId"] != null) {
      if(json["replyCommentId"] is int) {
        replyCommentId = json["replyCommentId"];
      } else if(json["replyCommentId"] is String) {
        replyCommentId = int.tryParse(json["replyCommentId"]);
      } else if(json["replyCommentId"] is double) {
        replyCommentId = json["replyCommentId"].toInt();
      }
    }
    if(json["friendNick"] != null) {
      friendNick = json["friendNick"];
    }
    if(json["friendFaceUrl"] != null) {
      friendFaceUrl = json["friendFaceUrl"];
    }
    if(json["replyNick"] != null) {
      replyNick = json["replyNick"];
    }
    if(json["replyFaceUrl"] != null) {
      replyFaceUrl = json["replyFaceUrl"];
    }
    if(json["name"] != null) {
      name = json["name"];
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> _data = <String, dynamic>{};
    _data["id"] = id;
    _data["userPhone"] = userPhone;
    _data["momentId"] = momentId;
    _data["friendUserId"] = friendUserId;
    _data["content"] = content;
    _data["replyCommentId"] = replyCommentId;
    _data["replyUserId"] = replyUserId;
    _data["friendNick"] = friendNick;
    _data["friendFaceUrl"] = friendFaceUrl;
    _data["replyNick"] = replyNick;
    _data["replyFaceUrl"] = replyFaceUrl;
    _data["replyName"] = replyName;
    return _data;
  }
}
// friendUserId: 1938441520618770434, friendNick: 哥几个333, friendFaceUrl: null}]
class LikeUserList {
  int? friendUserId;
  String? friendNick;
  String? friendFaceUrl;

  LikeUserList({this.friendFaceUrl, this.friendNick, this.friendUserId});

  LikeUserList.fromJson(Map<String, dynamic> json) {
    if(json["friendUserId"] != null) {
      if(json["friendUserId"] is int) {
        friendUserId = json["friendUserId"];
      } else if(json["friendUserId"] is String) {
        friendUserId = int.tryParse(json["friendUserId"]);
      } else if(json["friendUserId"] is double) {
        friendUserId = json["friendUserId"].toInt();
      }
    }
    if(json["friendNick"] != null) {
      friendNick = json["friendNick"];
    }
    if(json["friendFaceUrl"] != null) {
      friendFaceUrl = json["friendFaceUrl"];
    }
  }
  Map<String, dynamic> toJson() {
     final Map<String, dynamic> _data = <String, dynamic>{};
     _data["friendUserId"] = friendUserId;
     _data["friendNick"] = friendNick;
     _data["friendFaceUrl"] = friendFaceUrl;
     return _data;
  }
}

class MediaList {
  String? mediaType;
  String? mediaUrl;
  int? sortOrder;

  MediaList({this.mediaType, this.mediaUrl, this.sortOrder});

  MediaList.fromJson(Map<String, dynamic> json) {
    if(json["mediaType"] is String) {
      mediaType = json["mediaType"];
    }
    if(json["mediaUrl"] is String) {
      mediaUrl = json["mediaUrl"];
    }
    if(json["sortOrder"] is int) {
      sortOrder = json["sortOrder"];
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> _data = <String, dynamic>{};
    _data["mediaType"] = mediaType;
    _data["mediaUrl"] = mediaUrl;
    _data["sortOrder"] = sortOrder;
    return _data;
  }
}