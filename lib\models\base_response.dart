
class BaseResponse {
  int? code;
  String? level;
  String? msg;
  bool? ok;
  String? data;
  int? dataType;

  BaseResponse({this.code, this.level, this.msg, this.ok, this.data, this.dataType});

  BaseResponse.fromJson(Map<String, dynamic> json) {
    if(json["code"] is int) {
      code = json["code"];
    }
    if(json["level"] is String) {
      level = json["level"];
    }
    if(json["msg"] is String) {
      msg = json["msg"];
    }
    if(json["ok"] is bool) {
      ok = json["ok"];
    }
    if(json["data"] is String) {
      data = json["data"];
    }
    if(json["dataType"] is int) {
      dataType = json["dataType"];
    }
  }

  static List<BaseResponse> fromList(List<Map<String, dynamic>> list) {
    return list.map(BaseResponse.fromJson).toList();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> _data = <String, dynamic>{};
    _data["code"] = code;
    _data["level"] = level;
    _data["msg"] = msg;
    _data["ok"] = ok;
    _data["data"] = data;
    _data["dataType"] = dataType;
    return _data;
  }
}