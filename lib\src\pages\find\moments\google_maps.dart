import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';
import 'package:geolocator/geolocator.dart';

class GoogleMapsPage extends StatefulWidget {
  const GoogleMapsPage({super.key});

  @override
  _GoogleMapsPageState createState() => _GoogleMapsPageState();
}
class _GoogleMapsPageState extends State<GoogleMapsPage> {
  late GoogleMapController mapController;
  LatLng _currentPosition = const LatLng(39.9042, 116.4074); // 默认北京位置
  LatLng? _selectedPosition; // 用户选择的位置
  Set<Marker> _markers = {}; // 地图标记
  bool _isLoading = true; // 加载状态

  @override
  void initState() {
    super.initState();
    _getCurrentLocation();
  }

  // 获取当前位置
  Future<void> _getCurrentLocation() async {
    try {
      // 检查位置权限
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        setState(() {
          _isLoading = false;
        });
        _showLocationServiceDialog();
        return;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          setState(() {
            _isLoading = false;
          });
          _showPermissionDeniedDialog();
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        setState(() {
          _isLoading = false;
        });
        _showPermissionDeniedForeverDialog();
        return;
      }

      // 获取当前位置
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      setState(() {
        _currentPosition = LatLng(position.latitude, position.longitude);
        _selectedPosition = _currentPosition; // 默认选择当前位置
        _isLoading = false;
        _updateMarker(_currentPosition);
      });

      // 移动地图到当前位置
      if (mapController != null) {
        mapController.animateCamera(
          CameraUpdate.newLatLngZoom(_currentPosition, 15.0),
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorDialog('获取位置失败: $e');
    }
  }

  // 更新地图标记
  void _updateMarker(LatLng position) {
    setState(() {
      _markers = {
        Marker(
          markerId: const MarkerId('selected_location'),
          position: position,
          infoWindow: InfoWindow(
            title: '选择的位置',
            snippet: '纬度: ${position.latitude.toStringAsFixed(6)}, 经度: ${position.longitude.toStringAsFixed(6)}',
          ),
        ),
      };
    });
  }

  // 地图点击事件
  void _onMapTap(LatLng position) {
    setState(() {
      _selectedPosition = position;
      _updateMarker(position);
    });
  }

  void _onMapCreated(GoogleMapController controller) {
    mapController = controller;
    if (!_isLoading) {
      controller.animateCamera(
        CameraUpdate.newLatLngZoom(_currentPosition, 15.0),
      );
    }
  }

  // 确认选择位置
  void handleMapsConfirm() {
    if (_selectedPosition != null) {
      // 返回选择的位置信息
      Navigator.pop(context, {
        'latitude': _selectedPosition!.latitude,
        'longitude': _selectedPosition!.longitude,
        'address': '纬度: ${_selectedPosition!.latitude.toStringAsFixed(6)}, 经度: ${_selectedPosition!.longitude.toStringAsFixed(6)}',
      });
    } else {
      Navigator.pop(context);
    }
  }

  // 显示位置服务未开启对话框
  void _showLocationServiceDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(TIM_t('位置服务未开启')),
          content: Text(TIM_t('请在设置中开启位置服务')),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(TIM_t('确定')),
            ),
          ],
        );
      },
    );
  }

  // 显示权限被拒绝对话框
  void _showPermissionDeniedDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(TIM_t('位置权限被拒绝')),
          content: Text(TIM_t('需要位置权限来获取当前位置')),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(TIM_t('确定')),
            ),
          ],
        );
      },
    );
  }

  // 显示权限永久被拒绝对话框
  void _showPermissionDeniedForeverDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(TIM_t('位置权限被永久拒绝')),
          content: Text(TIM_t('请在设置中手动开启位置权限')),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(TIM_t('取消')),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                Geolocator.openAppSettings();
              },
              child: Text(TIM_t('去设置')),
            ),
          ],
        );
      },
    );
  }

  // 显示错误对话框
  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(TIM_t('错误')),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(TIM_t('确定')),
            ),
          ],
        );
      },
    );
  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,

        automaticallyImplyLeading: false,
        // 禁用自动生成的返回按钮
        leading: null,
        // 移除leading
        leadingWidth: 0,
        // 设置leading宽度为0
        actions: [
          // 在actions中添加自定义宽度的取消按钮
          Padding(
            padding: const EdgeInsets.only(left: 16),
            child: GestureDetector(
              onTap: () {
                Navigator.pop(context);
              },
              child: Container(
                padding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                margin: const EdgeInsets.only(right: 16),
                height: 32,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: const Color(0xFFE9E9E9),
                ),
                child: Center(
                  child: Text(TIM_t('取消'),
                      style: const TextStyle(color: Color(0XFF666666))),
                ),
              ),
            ),
          ),
          const Spacer(), // 添加一个Spacer将发布按钮推到右边

          Text(TIM_t("选择发布位置")),

          const Spacer(), // 添加一个Spacer将发布按钮推到右边
          // 添加发布按钮
          Padding(
            padding: const EdgeInsets.only(right: 16),
            child: GestureDetector(
              onTap: () {
                // 处理选择的权限类型
                handleMapsConfirm();
              },
              child: Container(
                padding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                height: 32,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: const Color(0xFF0072FC),
                ),
                child: Center(
                  child: Text(TIM_t('确定'),
                      style: const TextStyle(color: Colors.white)),
                ),
              ),
            ),
          ),
        ],
      ),
      body: Stack(
        children: [
          GoogleMap(
            onMapCreated: _onMapCreated,
            onTap: _onMapTap,
            initialCameraPosition: CameraPosition(
              target: _currentPosition,
              zoom: 15.0,
            ),
            markers: _markers,
            myLocationEnabled: true,
            myLocationButtonEnabled: true,
            mapType: MapType.normal,
          ),
          if (_isLoading)
            Container(
              color: Colors.black26,
              child: const Center(
                child: CircularProgressIndicator(),
              ),
            ),
          // 显示当前选择的坐标信息
          if (_selectedPosition != null && !_isLoading)
            Positioned(
              bottom: 20,
              left: 20,
              right: 20,
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      TIM_t('选择的位置'),
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '纬度: ${_selectedPosition!.latitude.toStringAsFixed(6)}',
                      style: const TextStyle(fontSize: 12),
                    ),
                    Text(
                      '经度: ${_selectedPosition!.longitude.toStringAsFixed(6)}',
                      style: const TextStyle(fontSize: 12),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }
}