plugins {
    id "com.android.application"
    id "kotlin-android"
    id 'com.google.android.libraries.mapsplatform.secrets-gradle-plugin'
    id "dev.flutter.flutter-gradle-plugin"
}
secrets {
    // Optionally specify a different file name containing your secrets.
    // The plugin defaults to "local.properties"
    propertiesFileName = "secrets.properties"

    // A properties file containing default secret values. This file can be
    // checked in version control.
    defaultPropertiesFileName = "local.defaults.properties"

    // Configure which keys should be ignored by the plugin by providing regular expressions.
    // "sdk.dir" is ignored by default.
    ignoreList.add("keyToIgnore") // Ignore the key "keyToIgnore"
    ignoreList.add("sdk.*")       // Ignore all keys matching the regexp "sdk.*"
}

apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.huawei.agconnect'
apply plugin: 'com.hihonor.mcs.asplugin'



def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}


def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

android {
    compileSdkVersion 34
    namespace("com.tencent.flutter.tuikit")
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    lintOptions {
        disable 'InvalidPackage'
        disable "Instantiatable"
        checkReleaseBuilds false
        abortOnError false
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        applicationId "com.tencent.qcloud.tim.tuikit"
        minSdkVersion 21
        targetSdkVersion 34
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        manifestPlaceholders = [
                VIVO_APPID:"",
                VIVO_APPKEY:"",
                "HONOR_APPID" : ""
        ]
    }

    signingConfigs {
        release {
            storeFile file('keystore_tuikit.jks')
            storePassword 'my_secure_password_123'
            keyAlias 'key'
            keyPassword 'my_secure_password_123'
        }
    }

    buildTypes {
        debug {
            signingConfig signingConfigs.release
        }
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig signingConfigs.release
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

    packagingOptions {
        pickFirst 'lib/arm64-v8a/libc++_shared.so'
        pickFirst 'lib/armeabi-v7a/libc++_shared.so'
        pickFirst 'lib/x86/libc++_shared.so'
        pickFirst 'lib/x86_64/libc++_shared.so'
    }
}

flutter {
    source '../..'
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"

    implementation 'com.tencent.timpush:huawei:8.5.6864'
    implementation 'com.tencent.timpush:xiaomi:8.5.6864'
    implementation 'com.tencent.timpush:oppo:8.5.6864'
    implementation 'com.tencent.timpush:vivo:8.5.6864'
    implementation 'com.tencent.timpush:honor:8.5.6864'
    implementation 'com.tencent.timpush:meizu:8.5.6864'
    implementation 'com.tencent.timpush:fcm:8.5.6864'
    implementation 'androidx.work:work-runtime-ktx:2.8.1'
    implementation platform('com.google.firebase:firebase-bom:32.2.3')
    implementation 'com.google.firebase:firebase-analytics'
}
