import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';
import 'package:tencent_cloud_chat_demo/config.dart';
import 'package:tencent_cloud_chat_demo/src/provider/wallet_provider.dart';
import '../../../utils/toast.dart';
class BankAddPage extends StatefulWidget {
  const BankAddPage({Key? key}) : super(key: key);

  @override
  State<BankAddPage> createState() => _BankAddPageState();
}

class _BankAddPageState extends State<BankAddPage> {
  final TextEditingController _nameController = TextEditingController(); // 开户名
  final TextEditingController _cardNumberController = TextEditingController(); // 卡号
  final TextEditingController _bankNameController = TextEditingController(); // 银行名称
  @override
  void initState() {
    super.initState();
    // Provider.of<WalletProvider>(context, listen: false).bankVM.getBankList();
  }

  handleAddBank() {
    final name = _nameController.text;
    final cardNumber = _cardNumberController.text;
    final bankName = _bankNameController.text;

    if(name.isEmpty) {
      ToastUtils.toast(TIM_t('请输入开户名'));
      return;
    }
    if(cardNumber.isEmpty) {
      ToastUtils.toast(TIM_t('请输入卡号'));
      return;
    }
    if(bankName.isEmpty) {
      ToastUtils.toast(TIM_t('请输入银行名称'));
      return;
    }
    Provider.of<WalletProvider>(context, listen: false).addBank(bankName, cardNumber, name).then((res) {
      if(res.code == 0 && res.ok == true) {
        ToastUtils.toast(TIM_t('添加成功，请等待审核'));
        Navigator.pop(context, true);
      }else {
        ToastUtils.toast(res.msg ?? TIM_t('添加失败'));
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(
      context,
      designSize: const Size(375, 812),
      minTextAdapt: true,
    );
    return Scaffold(
        backgroundColor: const Color(0xFFF9F9F9),
        appBar: AppBar(
          elevation: 1,
          title: Text(TIM_t("添加银行卡"),style: const TextStyle(fontSize: IMDemoConfig.appBarTitleFontSize),),
          backgroundColor: const Color(0xFFFFFFFF),
          surfaceTintColor: Colors.white,
        ),
        body: Column(
          children: [
            Expanded(child: Container(child:  _addBankView(), alignment: Alignment.topCenter,)),
            _addButton(),
          ],
        )
      );

  }

  Widget _addBankView() {
    return Container(
      margin: EdgeInsets.only(top: 16.h, left: 16.w, right: 16.w),
      padding: EdgeInsets.only(left: 16.w, right: 16.w),
      height: 142.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.w),
        color: Colors.white,
      ),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.only(top: 10.h, bottom: 10.h),
            decoration: BoxDecoration(
              border: Border(bottom: BorderSide(color: const Color(0xFFE9E9E9), width: 1.w))
            ),
            child: Row(
              children: [
                Text(TIM_t('开户名'), style: TextStyle(fontSize: 14.sp, color: const Color(0xFF333333))),
                const Spacer(),
                SizedBox(
                  width: 150,
                  height: 24.h,
                  child: TextField(
                    controller: _nameController,
                    keyboardType: TextInputType.text,
                    textAlign: TextAlign.right,
                    style: TextStyle(fontSize: 14.sp, color: const Color(0xFF333333)),
                    decoration: InputDecoration(
                      contentPadding: const EdgeInsets.symmetric(vertical: 0, horizontal: 8),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(5),
                        borderSide: BorderSide.none,
                      ),
                      hintText: TIM_t('请输入开户名'),
                      hintStyle: const TextStyle(color: Color(0xFF999999), fontSize: 14, fontWeight: FontWeight.w400),
                    ),
                  ),
                )
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.only(top: 10.h, bottom: 10.h),
            decoration: BoxDecoration(
              border: Border(bottom: BorderSide(color: const Color(0xFFE9E9E9), width: 1.w))
            ),
            child: Row(
              children: [
                Text(TIM_t('卡号'), style: TextStyle(fontSize: 14.sp, color: const Color(0xFF333333))),
                const Spacer(),
                SizedBox(
                  width: 150,
                  height: 24.h,
                  child: TextField(
                    controller: _cardNumberController,
                    keyboardType: TextInputType.number,
                    textAlign: TextAlign.right,
                     style: TextStyle(fontSize: 14.sp, color: const Color(0xFF333333)),
                    decoration: InputDecoration(
                      contentPadding: const EdgeInsets.symmetric(vertical: 0, horizontal: 8),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(5),
                        borderSide: BorderSide.none,
                      ),
                      hintText: TIM_t('请输入银行卡号'),
                      hintStyle: const TextStyle(color: Color(0xFF999999), fontSize: 14, fontWeight: FontWeight.w400),
                    ),
                  ),
                )
              ],
            ),
          ),

          Container(
            padding: EdgeInsets.only(top: 10.h, bottom: 10.h),
            child: Row(
              children: [
                Text(TIM_t('银行名称'), style: TextStyle(fontSize: 14.sp, color: const Color(0xFF333333))),
                const Spacer(),
                SizedBox(
                width: 150,
                height: 24.h,
                child: TextField(
                  controller: _bankNameController,
                  keyboardType: TextInputType.text,
                  textAlign: TextAlign.right,
                  style: TextStyle(fontSize: 14.sp, color: const Color(0xFF333333)),
                  decoration: InputDecoration(
                    contentPadding: const EdgeInsets.symmetric(vertical: 0, horizontal: 8),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(5),
                      borderSide: BorderSide.none,
                    ),
                    hintText: TIM_t('请输入银行名称'),
                    hintStyle: const TextStyle(color: Color(0xFF999999), fontSize: 14, fontWeight: FontWeight.w400),
                  ),
                ),
              )
            ],
          ),
        ),
        ],
      ),
    );
  }

  Widget _addButton() {
    return Padding(
      padding: EdgeInsets.only(left: 16.w, right: 16.w, bottom: 32.h),
      child: InkWell(
          onTap: () {
            handleAddBank();
          },
          child: Container(
            width: double.infinity,
            height: 40.h,
            decoration: BoxDecoration(
              color: const Color(0xFF0072FC),
              borderRadius: BorderRadius.circular(8.w),
            ),
            child: Center(
              child: Text(TIM_t("添加银行卡"),
                  style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w500,
                      color: Colors.white)),
            ),
          )),
    );
  }
}
