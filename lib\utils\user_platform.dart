import 'package:tencent_cloud_chat_uikit/ui/utils/platform.dart';

class LoginDevice {
  static const int PC = 1;
  static const int ANDROID = 2;
  static const int APPLE = 3;
  static const int H5 = 4;
  static const int WEIXIN_MP = 5;
}

class UserPlatform {
  static final _platform = PlatformUtils();

  /// 获取当前登录设备类型
  static int getLoginDevice() {
    if (_platform.isAndroid) {
      return LoginDevice.ANDROID;
    } else if (_platform.isIOS) {
      return LoginDevice.APPLE;
    } else if (_platform.isDesktop) {
      return LoginDevice.PC;
    } else {
      // 默认返回H5
      return LoginDevice.H5;
    }
  }

  /// 获取当前登录设备描述
  static String getLoginDeviceDesc() {
    switch (getLoginDevice()) {
      case LoginDevice.PC:
        return '电脑端';
      case LoginDevice.ANDROID:
        return '安卓';
      case LoginDevice.APPLE:
        return '苹果';
      case LoginDevice.H5:
        return 'H5';
      case LoginDevice.WEIXIN_MP:
        return '微信小程序';
      default:
        return 'H5';
    }
  }
}