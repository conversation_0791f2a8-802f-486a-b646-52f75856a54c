class PacketAmountResponse {
  int? code;
  String? level;
  String? msg;
  bool? ok;
  AmountData? data;
  int? dataType;

  PacketAmountResponse({this.code, this.level, this.msg, this.ok, this.data, this.dataType});

  PacketAmountResponse.fromJson(Map<String, dynamic> json) {
    if(json["code"] is int) {
      code = json["code"];
    }
    if(json["level"] is String) {
      level = json["level"];
    }
    if(json["msg"] is String) {
      msg = json["msg"];
    }
    if(json["ok"] is bool) {
      ok = json["ok"];
    }
    if(json["data"] is Map) {
      data = json["data"] == null ? null : AmountData.fromJson(json["data"]);
    }
    if(json["dataType"] is int) {
      dataType = json["dataType"];
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> _data = <String, dynamic>{};
    _data["code"] = code;
    _data["level"] = level;
    _data["msg"] = msg;
    _data["ok"] = ok;
    if(data != null) {
      _data["data"] = data?.toJson();
    }
    _data["dataType"] = dataType;
    return _data;
  }
}

class AmountData {
  String? totalAmount;
  int? totalCount;

  AmountData({this.totalAmount, this.totalCount});

  AmountData.fromJson(Map<String, dynamic> json) {
    if(json["totalAmount"] != null) {
      if(json["totalAmount"] is int) {
        double value = json["totalAmount"].toDouble();
        totalAmount = value.toStringAsFixed(2);
      } else if(json["totalAmount"] is double) {
        totalAmount = (json["totalAmount"] as double).toStringAsFixed(2);
      } else if(json["totalAmount"] is String) {
        double? value = double.tryParse(json["totalAmount"]);
        if(value != null) {
          totalAmount = value.toStringAsFixed(2);
        } else {
          totalAmount = "0.00";
        }
      }
    } else {
      totalAmount = "0.00";
    }
    if(json["totalCount"] != null) {
      if(json["totalCount"] is int) {
        totalCount = json["totalCount"];
      } else if(json["totalCount"] is String) {
        totalCount = int.tryParse(json["totalCount"]) ?? 0;
      }
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> _data = <String, dynamic>{};
    _data["totalAmount"] = totalAmount;
    _data["totalCount"] = totalCount;
    return _data;
  }
}