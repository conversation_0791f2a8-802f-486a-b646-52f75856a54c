import 'package:dio/dio.dart';
import '../http/dio_instance.dart';
import '../models/base_response.dart';
import '../models/account_response.dart';
import '../postData/get_bank_list_data.dart';
import '../models/backList_response.dart';

class AccountApi {
  static AccountApi instance = AccountApi._();

  AccountApi._();

  // 开通账户
  Future<BaseResponse> openAccount() async {
    Response response = await DioInstance.instance().post(path: '/account/open');
    BaseResponse openAccountResponse = BaseResponse.fromJson(response.data);
    return openAccountResponse;
  }
  // 查询账户
  Future<AccountData> queryAccount() async {
    Response response = await DioInstance.instance().get(path: '/account/query');
    AccountData queryAccountResponse = AccountData.fromJson(response.data);
    return queryAccountResponse;
  }
  // 充值
  Future<BaseResponse> recharge({required String cardId, required String amount}) async {
    Response response = await DioInstance.instance().post(path: '/account/recharge', data: {'cardId': cardId, 'amount': amount});
    BaseResponse rechargeResponse = BaseResponse.fromJson(response.data);
    return rechargeResponse;
  }
  // 提现
  Future<BaseResponse> withdraw({required String cardId, required String amount}) async {
    Response response = await DioInstance.instance().post(path: '/account/withdraw', data: {'cardId': cardId, 'amount': amount});
    BaseResponse withdrawResponse = BaseResponse.fromJson(response.data);
    return withdrawResponse;
  }
  // 分页查询银行卡
  Future<BankListData> getBankList({required BankListParams params}) async {
    Response response = await DioInstance.instance().post(path: '/bankCard/queryPage', data: params.toJson());
    BankListData getBankListResponse = BankListData.fromJson(response.data);
    return getBankListResponse;
  }
  // 新增银行卡
  Future<BaseResponse> addBankCard({required String bankName, required String cardNo, required String cardHolder}) async {
    Response response = await DioInstance.instance().post(path: '/bankCard/add', data: {'bankName': bankName, 'cardNo': cardNo, 'cardHolder': cardHolder});
    BaseResponse addBankCardResponse = BaseResponse.fromJson(response.data);
    return addBankCardResponse;
  }

  // 删除银行卡
  Future<BaseResponse> deleteBankCard({required String bankCardId}) async {
    Response response = await DioInstance.instance().delete(path: '/bankCard/delete', queryParameters: {'bankCardId': bankCardId});
    BaseResponse deleteBankCardResponse = BaseResponse.fromJson(response.data);
    return deleteBankCardResponse;
  }
}