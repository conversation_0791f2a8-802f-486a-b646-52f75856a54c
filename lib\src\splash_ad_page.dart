import 'dart:async';
import 'package:flutter/material.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';

import '../models/language_local.dart';

class SplashAdPage extends StatefulWidget {
  final VoidCallback onInit;
  const SplashAdPage({super.key,required this.onInit});

  @override
  State<SplashAdPage> createState() => _SplashAdPageState();
}

class _SplashAdPageState extends State<SplashAdPage> {
  int _countdown = 6; // 倒计时秒数
  Timer? _timer;
  bool flag=false;
  bool _showSkipButton = false; // 控制跳过按钮显示

  @override
  void initState() {
    super.initState();
    _startCountdown();
    // 1秒后显示跳过按钮
    Timer(Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _showSkipButton = true;
        });
      }
    });
  }

  void _startCountdown() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_countdown <= 1) {
        _goToHome();
      } else {
        setState(() {
          _countdown--;
        });
      }
    });
  }

  void _goToHome() {
    if(flag)return;
    flag=true;
    _timer?.cancel();
    print(_countdown);
    widget.onInit();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {

    return Stack(
      children: [
        // 广告图片（可以替换为网络图片）
        SizedBox.expand(
          child: Image.asset(
            'assets/turnScreen.png',
            fit: BoxFit.cover,
          ),
        ),
        // 跳过按钮
        if (_showSkipButton) // 只在_showSkipButton为true时显示
        Positioned(
          top: 40,
          right: 20,
          child: InkWell(
            onTap: (){
              print("onTap");
              _goToHome();
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.black54,
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                TIM_t('跳过')+' $_countdown',
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
