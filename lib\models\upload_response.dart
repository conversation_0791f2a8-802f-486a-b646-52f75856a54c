
class UploadResponse {
  int? code;
  String? level;
  String? msg;
  bool? ok;
  Data? data;
  int? dataType;

  UploadResponse({this.code, this.level, this.msg, this.ok, this.data, this.dataType});

  UploadResponse.fromJson(Map<String, dynamic> json) {
    if(json["code"] is int) {
      code = json["code"];
    }
    if(json["level"] is String) {
      level = json["level"];
    }
    if(json["msg"] is String) {
      msg = json["msg"];
    }
    if(json["ok"] is bool) {
      ok = json["ok"];
    }
    if(json["data"] is Map) {
      data = json["data"] == null ? null : Data.fromJson(json["data"]);
    }
    if(json["dataType"] is int) {
      dataType = json["dataType"];
    }
  }

  static List<UploadResponse> fromList(List<Map<String, dynamic>> list) {
    return list.map(UploadResponse.fromJson).toList();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> _data = <String, dynamic>{};
    _data["code"] = code;
    _data["level"] = level;
    _data["msg"] = msg;
    _data["ok"] = ok;
    if(data != null) {
      _data["data"] = data?.toJson();
    }
    _data["dataType"] = dataType;
    return _data;
  }
}

class Data {
  int? fileId;
  String? fileName;
  String? fileUrl;
  String? fileKey;
  int? fileSize;
  String? fileType;

  Data({this.fileId, this.fileName, this.fileUrl, this.fileKey, this.fileSize, this.fileType});

  Data.fromJson(Map<String, dynamic> json) {
    if(json["fileId"] is int) {
      fileId = json["fileId"];
    }
    if(json["fileName"] is String) {
      fileName = json["fileName"];
    }
    if(json["fileUrl"] is String) {
      fileUrl = json["fileUrl"];
    }
    if(json["fileKey"] is String) {
      fileKey = json["fileKey"];
    }
    if(json["fileSize"] is int) {
      fileSize = json["fileSize"];
    }
    if(json["fileType"] is String) {
      fileType = json["fileType"];
    }
  }

  static List<Data> fromList(List<Map<String, dynamic>> list) {
    return list.map(Data.fromJson).toList();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> _data = <String, dynamic>{};
    _data["fileId"] = fileId;
    _data["fileName"] = fileName;
    _data["fileUrl"] = fileUrl;
    _data["fileKey"] = fileKey;
    _data["fileSize"] = fileSize;
    _data["fileType"] = fileType;
    return _data;
  }
}