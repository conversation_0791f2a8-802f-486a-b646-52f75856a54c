import 'package:dio/dio.dart';
import 'base_model.dart';
import 'package:flutter/material.dart';
import '../utils/toast.dart';

class ResPostIntercepter extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    options.extra['requestTime'] = DateTime.now();
    handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    // 计算请求耗时
    final requestTime = response.requestOptions.extra['requestTime'] as DateTime?;
    final responseTime = DateTime.now();
    final duration = requestTime != null 
        ? responseTime.difference(requestTime)
        : Duration.zero;
    
    debugPrint('请求耗时: ${duration.inMilliseconds}ms');
    debugPrint('请求路径: ${response.requestOptions.path}');
    if (response.statusCode == 200) {
      try {
        // errorCode = 0 代表执行成功，不建议依赖任何非0的 errorCode.
        // errorCode = -1001 代表登录失效，需要重新登录。
        var rsp = BaseModel.fromJson(response.data);
        if (rsp.code == 0) {
          if(rsp.data == null) {
            handler.next(Response(requestOptions: response.requestOptions, data: response.data));
          }else {
            handler.next(Response(requestOptions: response.requestOptions, data: response.data));
          }
        } else if(rsp.code == -1001){
          handler.reject(DioError(requestOptions: response.requestOptions, error: "登录失效，请重新登录"));
        } else {
          handler.reject(DioError(requestOptions: response.requestOptions, error: rsp.msg));
          debugPrint("[ResPostIntercepter] error: ${rsp.msg}");
          ToastUtils.toast(rsp.msg ?? '请求错误');
       }
      } catch (e) {
        handler.reject(DioError(requestOptions: response.requestOptions, error: e.toString()));
      }
    } else {
      handler.reject(DioError(requestOptions: response.requestOptions, error: "网络错误"));
    }
  }

  @override
  void onError(DioError err, ErrorInterceptorHandler handler) {
    switch (err.type) {
      case DioErrorType.connectTimeout:
        err.error = "连接超时";
        break;
      case DioErrorType.sendTimeout:
        err.error = "请求超时";
        break;
      case DioErrorType.receiveTimeout:
        err.error = "响应超时";
        break;
      case DioErrorType.response:
        err.error = "服务器异常";
        break;
      case DioErrorType.cancel:
        err.error = "请求取消";
        break;
      case DioErrorType.other:
        err.error = "网络错误: ${err.message}";
        ToastUtils.toast("网络错误");
        break;
      default:
        err.error = "未知错误: ${err.message}";
        ToastUtils.toast("未知错误");
        break;
    }
    handler.next(err);
  }
}