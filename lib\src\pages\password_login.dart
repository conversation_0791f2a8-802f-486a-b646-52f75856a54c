import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:dio/dio.dart';
import 'dart:async';

class RegisterPage extends StatefulWidget {
  const RegisterPage({Key? key}) : super(key: key);

  @override
  State<RegisterPage> createState() => _RegisterPageState();
}

class _RegisterPageState extends State<RegisterPage> {
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _verifyCodeController = TextEditingController();
  final TextEditingController _nicknameController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  bool _isAgreed = false;
  // 验证码时间 60秒
  int _verifyCodeTime = 60;
  bool _isVerifyCodeCounting = false;
  Timer? _timer;

  @override
  void dispose() {
    _phoneController.dispose();
    _verifyCodeController.dispose();
    _nicknameController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  Future<void> handleRegister() async {
    final phone = _phoneController.text;
    final verifyCode = _verifyCodeController.text;
    final nickname = _nicknameController.text;
    final password = _passwordController.text;
    debugPrint(
        '[Register] 注册信息: 手机号: ${phone}, 验证码: ${verifyCode}, 昵称: ${nickname}');
    Dio dio = Dio();
    Response response;
    response =
        await dio.post("http://101.34.90.82:18082/user/registerByPhone", data: {
      "loginName": phone,
      "nick": nickname,
      "password": password,
      "code": verifyCode,
      "loginDevice": 2
    });
    debugPrint('[Register] 注册结果: ${response.data}');
    if (response.statusCode == 200 && response.data['ok'] == true) {
      debugPrint('[Register] 注册成功');
    } else {
      debugPrint('[Register] 注册失败');
    }
  }

  getVerifyCode() {
    // 点击获取验证码，开始倒计时
    debugPrint('[Register] 获取验证码');
    setState(() {
      _isVerifyCodeCounting = true;
      _verifyCodeTime = 60;
    });
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_verifyCodeTime > 0) {
        setState(() {
          _verifyCodeTime--;
        });
      } else {
        timer.cancel();
        setState(() {
          _isVerifyCodeCounting = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // 背景图片
          Image.asset(
            'assets/login_bg.png',
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height,
            fit: BoxFit.cover,
          ),
          SafeArea(
            child: Stack(
              children: [
                // 密码登录按钮
                Positioned(
                  right: 16,
                  top: 16,
                  child: TextButton(
                    onPressed: () {
                      Navigator.pushNamed(context, '/login');
                    },
                    style: TextButton.styleFrom(
                      foregroundColor: const Color(0xFF0072FC), // #0072FC
                      textStyle: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    child: const Text('密码登录'),
                  ),
                ),
                // 主要内容
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 48),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const SizedBox(height: 64), // 32 + 32(标题到顶部的距离)
                      const Text(
                        '欢迎注册',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 48),
                      // 手机号输入框
                      Container(
                          height: 48,
                          decoration: BoxDecoration(
                            color: const Color(0xFFFFFFFF),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          child: Row(children: [
                            Image.asset(
                              'assets/login_phone.png',
                              width: 24,
                              height: 24,
                            ),
                            const SizedBox(width: 4),
                            Expanded(
                                child: TextField(
                              controller: _phoneController,
                              keyboardType: TextInputType.phone,
                              decoration: const InputDecoration(
                                hintText: '请输入手机号',
                                border: InputBorder.none,
                                hintStyle: TextStyle(
                                  color: Color(0xFF999999),
                                  fontSize: 14,
                                ),
                              ),
                              style: const TextStyle(
                                color: Color(0xFF333333),
                                fontSize: 14,
                              ),
                            ))
                          ])),
                      const SizedBox(height: 16),
                      // 密码输入框
                      Container(
                        height: 48,
                        decoration: BoxDecoration(
                          color: const Color(0xFFFFFFFF),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: Row(
                          children: [
                            Image.asset(
                              'assets/login_lock.png',
                              width: 24,
                              height: 24,
                            ),
                            const SizedBox(width: 4),
                            Expanded(
                              child: TextField(
                                controller: _passwordController,
                                keyboardType: TextInputType.number,
                                decoration: const InputDecoration(
                                  hintText: '请输入密码',
                                  border: InputBorder.none,
                                  hintStyle: TextStyle(
                                    color: Color(0xFF999999),
                                    fontSize: 14,
                                  ),
                                ),
                                style: const TextStyle(
                                  color: Color(0xFF333333),
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 16),
                      // 验证码输入框
                      Container(
                        height: 48,
                        decoration: BoxDecoration(
                          color: const Color(0xFFFFFFFF),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: Row(
                          children: [
                            Image.asset(
                              'assets/login_code.png',
                              width: 24,
                              height: 24,
                            ),
                            const SizedBox(width: 4),
                            Expanded(
                              child: TextField(
                                controller: _verifyCodeController,
                                keyboardType: TextInputType.number,
                                decoration: const InputDecoration(
                                  hintText: '请输入验证码',
                                  border: InputBorder.none,
                                  hintStyle: TextStyle(
                                    color: Color(0xFF999999),
                                    fontSize: 14,
                                  ),
                                ),
                                style: const TextStyle(
                                  color: Color(0xFF333333),
                                  fontSize: 14,
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            TextButton(
                              onPressed: () {
                                // TODO: 实现获取验证码逻辑
                                getVerifyCode();
                              },
                              child: _isVerifyCodeCounting
                                  ? Text(
                                      '$_verifyCodeTime s',
                                      style: const TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    )
                                  : const Text(
                                      '获取验证码',
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                              style: TextButton.styleFrom(
                                foregroundColor:
                                    const Color(0xFF0072FC), // #0072FC
                                textStyle: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 16),
                      // 昵称输入框
                      Container(
                          height: 48,
                          decoration: BoxDecoration(
                            color: const Color(0xFFFFFFFF),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          child: Row(
                            children: [
                              Image.asset(
                                'assets/login_user.png',
                                width: 24,
                                height: 24,
                              ),
                              const SizedBox(width: 4),
                              Expanded(
                                child: TextField(
                                  controller: _nicknameController,
                                  decoration: const InputDecoration(
                                    hintText: '请输入昵称',
                                    border: InputBorder.none,
                                    hintStyle: TextStyle(
                                      color: Color(0xFF999999),
                                      fontSize: 14,
                                    ),
                                  ),
                                  style: const TextStyle(
                                    color: Color(0xFF333333),
                                    fontSize: 14,
                                  ),
                                ),
                              )
                            ],
                          )),
                      const SizedBox(height: 18),
                      // 协议勾选框
                      Row(
                        children: [
                          SizedBox(
                            width: 16,
                            height: 16,
                            child: Checkbox(
                              value: _isAgreed,
                              onChanged: (value) {
                                setState(() {
                                  _isAgreed = value ?? false;
                                });
                              },
                              materialTapTargetSize:
                                  MaterialTapTargetSize.shrinkWrap,
                              visualDensity: VisualDensity.compact,
                              checkColor: Colors.white,
                              fillColor:
                                  MaterialStateProperty.resolveWith<Color>(
                                (Set<MaterialState> states) {
                                  if (states.contains(MaterialState.selected)) {
                                    return const Color(0xFF0072FC);
                                  }
                                  return Colors.transparent;
                                },
                              ),
                            ),
                          ),
                          const Padding(
                            padding: EdgeInsets.only(left: 8),
                            child:
                                Text('阅读并同意', style: TextStyle(fontSize: 12)),
                          ),
                          TextButton(
                            onPressed: () {
                              // TODO: 跳转到用户服务协议
                            },
                            child: const Text('《用户服务协议》'),
                            style: TextButton.styleFrom(
                              foregroundColor:
                                  const Color(0xFF0072FC), // #0072FC
                              textStyle: const TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                              padding: EdgeInsets.zero,
                              minimumSize: Size.zero,
                              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                            ),
                          ),
                          const Text('和', style: TextStyle(fontSize: 12)),
                          TextButton(
                            onPressed: () {
                              // TODO: 跳转到隐私政策
                            },
                            child: const Text('《隐私政策》'),
                            style: TextButton.styleFrom(
                              foregroundColor:
                                  const Color(0xFF0072FC), // #0072FC
                              textStyle: const TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                              padding: EdgeInsets.zero,
                              minimumSize: Size.zero,
                              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 32),
                      // 注册按钮
                      SizedBox(
                        width: double.infinity,
                        height: 40,
                        child: ElevatedButton(
                          onPressed: _isAgreed
                              ? () {
                                  // TODO: 实现注册逻辑
                                  handleRegister();
                                }
                              : null,
                          child: const Text('注册'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF0072FC),
                            foregroundColor: const Color(0xFFCBDBFF),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
