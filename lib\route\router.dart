

import 'package:flutter/material.dart';
import 'package:tencent_cloud_chat_demo/src/profile.dart';

import '../src/my_profile.dart';

class Routes {
  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case RouterPath.setting:
        return pageRoute(const MyProfile(), settings: settings);
      default:
        return MaterialPageRoute(
          builder: (context) => const Scaffold(
            body: SafeArea(
              child: Center(child: Text("No found")),
            ),
          ),
        );
    }
  }

  static MaterialPageRoute pageRoute(Widget page, {RouteSettings? settings, bool? fullscreenDialog, bool? maintainState, bool? allowSnapshotting}) {
    return MaterialPageRoute(builder: (context) => page, 
    settings: settings,
    fullscreenDialog: fullscreenDialog ?? false,
    maintainState: maintainState ?? true,
    allowSnapshotting: allowSnapshotting ?? true,
    );
  }
}

class RouterPath {
  static const String setting = '/setting_page';
} 