import 'dart:async';
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';
import 'package:permission_handler/permission_handler.dart';

class CustomCameraPage extends StatefulWidget {
  const CustomCameraPage({Key? key}) : super(key: key);

  @override
  State<CustomCameraPage> createState() => _CustomCameraPageState();
}

class _CustomCameraPageState extends State<CustomCameraPage>
    with WidgetsBindingObserver, TickerProviderStateMixin {
  CameraController? _cameraController;
  List<CameraDescription> _cameras = [];
  bool _isInitialized = false;
  bool _isRecording = false;
  bool _isFlashOn = false;
  bool _isFrontCamera = false;
  String? _errorMessage;
  
  // 录制相关
  Timer? _recordingTimer;
  int _recordingSeconds = 0;
  static const int _maxRecordingSeconds = 60; // 最大录制60秒
  
  // 动画控制器
  late AnimationController _recordButtonController;
  late AnimationController _progressController;
  late Animation<double> _recordButtonAnimation;
  late Animation<double> _progressAnimation;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeAnimations();
    _initializeCamera();
  }

  void _initializeAnimations() {
    _recordButtonController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _progressController = AnimationController(
      duration: const Duration(seconds: _maxRecordingSeconds),
      vsync: this,
    );

    _recordButtonAnimation = Tween<double>(
      begin: 1.0,
      end: 0.6,
    ).animate(CurvedAnimation(
      parent: _recordButtonController,
      curve: Curves.easeInOut,
    ));

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.linear,
    ));
  }

  Future<void> _initializeCamera() async {
    try {
      // 检查相机权限
      final cameraStatus = await Permission.camera.status;
      final microphoneStatus = await Permission.microphone.status;

      if (!cameraStatus.isGranted) {
        final result = await Permission.camera.request(); 
        if (!result.isGranted) {
          setState(() {
            _errorMessage = TIM_t("需要相机权限才能使用此功能");
          });
          return;
        }
      }

      if (!microphoneStatus.isGranted) {
        final result = await Permission.microphone.request();
        if (!result.isGranted) {
          setState(() {
            _errorMessage = TIM_t("需要麦克风权限才能录制视频");
          });
          return;
        }
      }

      // 获取可用相机
      _cameras = await availableCameras();
      if (_cameras.isEmpty) {
        setState(() {
          _errorMessage = TIM_t("未找到可用的相机");
        });
        return;
      }

      // 设置相机
      await _setupCamera(_cameras.first);
    } catch (e) {
      debugPrint('Error initializing camera: $e');
      setState(() {
        _errorMessage = TIM_t("相机初始化失败：") + e.toString();
      });
    }
  }

  Future<void> _setupCamera(CameraDescription camera) async {
    try {
      if (_cameraController != null) {
        await _cameraController!.dispose();
      }

      _cameraController = CameraController(
        camera,
        ResolutionPreset.high,
        enableAudio: true,
        imageFormatGroup: ImageFormatGroup.jpeg,
      );

      await _cameraController!.initialize();

      if (mounted) {
        setState(() {
          _isInitialized = true;
          _errorMessage = null;
        });
      }
    } catch (e) {
      debugPrint('Error setting up camera: $e');
      if (mounted) {
        setState(() {
          _errorMessage = TIM_t("相机设置失败：") + e.toString();
          _isInitialized = false;
        });
      }
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _recordingTimer?.cancel();
    _recordButtonController.dispose();
    _progressController.dispose();
    _cameraController?.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (_cameraController == null || !_cameraController!.value.isInitialized) {
      return;
    }

    if (state == AppLifecycleState.inactive) {
      _cameraController?.dispose();
    } else if (state == AppLifecycleState.resumed) {
      _initializeCamera();
    }
  }

  // 拍照
  Future<void> _takePicture() async {
    if (_cameraController == null || !_cameraController!.value.isInitialized || _isRecording) {
      return;
    }

    try {
      final XFile image = await _cameraController!.takePicture();
      Navigator.pop(context, {'type': 'image', 'file': image});
    } catch (e) {
      debugPrint('Error taking picture: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(TIM_t("拍照失败，请重试")),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // 开始录制视频
  Future<void> _startVideoRecording() async {
    if (_cameraController == null || !_cameraController!.value.isInitialized || _isRecording) {
      return;
    }

    try {
      await _cameraController!.startVideoRecording();
      setState(() {
        _isRecording = true;
        _recordingSeconds = 0;
      });

      // 开始动画
      _recordButtonController.forward();
      _progressController.forward();

      // 开始计时
      _recordingTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
        if (mounted) {
          setState(() {
            _recordingSeconds++;
          });

          // 达到最大录制时间自动停止
          if (_recordingSeconds >= _maxRecordingSeconds) {
            _stopVideoRecording();
          }
        }
      });

      // 震动反馈
      HapticFeedback.mediumImpact();
    } catch (e) {
      debugPrint('Error starting video recording: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(TIM_t("开始录制失败，请重试")),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // 停止录制视频
  Future<void> _stopVideoRecording() async {
    if (!_isRecording || _cameraController == null) return;

    try {
      final XFile video = await _cameraController!.stopVideoRecording();

      // 停止计时和动画
      _recordingTimer?.cancel();
      _recordButtonController.reverse();
      _progressController.reset();

      if (mounted) {
        setState(() {
          _isRecording = false;
          _recordingSeconds = 0;
        });
      }

      // 震动反馈
      HapticFeedback.lightImpact();

      Navigator.pop(context, {'type': 'video', 'file': video});
    } catch (e) {
      debugPrint('Error stopping video recording: $e');

      // 重置状态
      _recordingTimer?.cancel();
      _recordButtonController.reverse();
      _progressController.reset();

      if (mounted) {
        setState(() {
          _isRecording = false;
          _recordingSeconds = 0;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(TIM_t("录制失败，请重试")),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // 切换摄像头
  Future<void> _switchCamera() async {
    if (_cameras.length < 2) return;

    final newCamera = _isFrontCamera ? _cameras.first : _cameras.last;
    await _setupCamera(newCamera);
    setState(() {
      _isFrontCamera = !_isFrontCamera;
    });
  }

  // 切换闪光灯
  Future<void> _toggleFlash() async {
    if (_cameraController == null) return;

    try {
      await _cameraController!.setFlashMode(
        _isFlashOn ? FlashMode.off : FlashMode.torch,
      );
      setState(() {
        _isFlashOn = !_isFlashOn;
      });
    } catch (e) {
      debugPrint('Error toggling flash: $e');
    }
  }



  String _formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  Widget _buildTopControls() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 关闭按钮
          GestureDetector(
            onTap: () => Navigator.pop(context),
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.5),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.close,
                color: Colors.white,
                size: 24,
              ),
            ),
          ),

          // 闪光灯按钮
          if (!_isFrontCamera)
            GestureDetector(
              onTap: _toggleFlash,
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.5),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  _isFlashOn ? Icons.flash_on : Icons.flash_off,
                  color: Colors.white,
                  size: 24,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildBottomControls() {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.black,
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 30),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SizedBox(width: 50),
              // 占位空间，保持拍摄按钮居中
              // 拍摄/录制按钮
              _buildCaptureButton(),

              // 切换摄像头按钮
              GestureDetector(
                onTap: _switchCamera,
                child: Container(
                  margin: const EdgeInsets.only(left: 20),
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.flip_camera_ios,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCaptureButton() {
    return GestureDetector(
      onTap: _isRecording ? null : _takePicture,
      onLongPressStart: (_) => _startVideoRecording(),
      onLongPressEnd: (_) => _stopVideoRecording(),
      child: AnimatedBuilder(
        animation: _recordButtonAnimation,
        builder: (context, child) {
          return Stack(
            alignment: Alignment.center,
            children: [
              // 进度环
              if (_isRecording)
                AnimatedBuilder(
                  animation: _progressAnimation,
                  builder: (context, child) {
                    return SizedBox(
                      width: 90,
                      height: 90,
                      child: CircularProgressIndicator(
                        value: _progressAnimation.value,
                        strokeWidth: 4,
                        valueColor: const AlwaysStoppedAnimation<Color>(Colors.red),
                        backgroundColor: Colors.white.withOpacity(0.3),
                      ),
                    );
                  },
                ),

              // 外圈
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: Colors.white,
                    width: 4,
                  ),
                ),
              ),

              // 内圈
              Transform.scale(
                scale: _recordButtonAnimation.value,
                child: Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: _isRecording ? Colors.red : Colors.white,
                    shape: _isRecording ? BoxShape.rectangle : BoxShape.circle,
                    borderRadius: _isRecording ? BorderRadius.circular(8) : null,
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // 显示错误信息
    if (_errorMessage != null) {
      return Scaffold(
        backgroundColor: Colors.black,
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                color: Colors.white,
                size: 64,
              ),
              const SizedBox(height: 16),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32),
                child: Text(
                  _errorMessage!,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: 32),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _errorMessage = null;
                  });
                  _initializeCamera();
                },
                child: Text(TIM_t("重试")),
              ),
              const SizedBox(height: 16),
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  TIM_t("返回"),
                  style: const TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
        ),
      );
    }

    // 显示加载状态
    if (!_isInitialized || _cameraController == null) {
      return Scaffold(
        backgroundColor: Colors.black,
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const CircularProgressIndicator(color: Colors.white),
              const SizedBox(height: 16),
              // Text(
              //   TIM_t("正在初始化相机..."),
              //   style: const TextStyle(
              //     color: Colors.white,
              //     fontSize: 16,
              //   ),
              // ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // 相机预览
          Positioned.fromRect(
            rect: Rect.fromLTWH(
              0,
              0,
              MediaQuery.of(context).size.width,
              MediaQuery.of(context).size.height -150,
            ),
            child: CameraPreview(_cameraController!),
          ),

          // 顶部控制栏
          Positioned(
            top: MediaQuery.of(context).padding.top + 10,
            left: 0,
            right: 0,
            child: _buildTopControls(),
          ),

          // 底部控制栏
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: _buildBottomControls(),
          ),

          // 录制时间显示
          if (_isRecording)
            Positioned(
              top: MediaQuery.of(context).padding.top + 60,
              left: 0,
              right: 0,
              child: Center(
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        width: 8,
                        height: 8,
                        decoration: const BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                        ),
                      ),
                      const SizedBox(width: 6),
                      Text(
                        _formatTime(_recordingSeconds),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

          // 操作提示
          if (!_isRecording)
            Positioned(
              bottom: 180,
              left: 0,
              right: 0,
              child: Center(
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    TIM_t("轻触拍照，长按录像"),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
