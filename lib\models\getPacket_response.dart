class GetPacketData {
  int? code;
  String? level;
  String? msg;
  bool? ok;
  Data? data;
  int? dataType;

  GetPacketData({this.code, this.level, this.msg, this.ok, this.data, this.dataType});

  GetPacketData.fromJson(Map<String, dynamic> json) {
    if(json["code"] is int) {
      code = json["code"];
    }
    if(json["level"] is String) {
      level = json["level"];
    }
    if(json["msg"] is String) {
      msg = json["msg"];
    }
    if(json["ok"] is bool) {
      ok = json["ok"];
    }
    if(json["data"] is Map) {
      data = json["data"] == null ? null : Data.fromJson(json["data"]);
    }
    if(json["dataType"] is int) {
      dataType = json["dataType"];
    }
  }

  static List<GetPacketData> fromList(List<Map<String, dynamic>> list) {
    return list.map(GetPacketData.fromJson).toList();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> _data = <String, dynamic>{};
    _data["code"] = code;
    _data["level"] = level;
    _data["msg"] = msg;
    _data["ok"] = ok;
    if(data != null) {
      _data["data"] = data?.toJson();
    }
    _data["dataType"] = dataType;
    return _data;
  }
}

class Data {
  String? status;
  String? amount;
  String? detailId;

  Data({this.status, this.amount, this.detailId});

  Data.fromJson(Map<String, dynamic> json) {
    if(json["status"] is String) {
      status = json["status"];
    }
    if(json["amount"] != null) {
      if(json["amount"] is int) {
        amount = json["amount"].toString();
      } else if(json["amount"] is double) {
        amount = json["amount"].toString();
      } else if(json["amount"] is String) {
        amount = json["amount"];
      }
    }
    if(json["detailId"] is String) {
      detailId = json["detailId"];
    }
  }

  static List<Data> fromList(List<Map<String, dynamic>> list) {
    return list.map(Data.fromJson).toList();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> _data = <String, dynamic>{};
    _data["status"] = status;
    _data["amount"] = amount;
    _data["detailId"] = detailId;
    return _data;
  }
}