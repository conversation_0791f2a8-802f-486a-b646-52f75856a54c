// lib/src/utils/moment_item_extension.dart
import 'dart:convert';
import '../../../models/moments_list_response.dart';

extension MomentItemExtension on MomentItem {
  // 将 MomentItem 转换为可存储在数据库中的 Map
  Map<String, dynamic> toMap() {
    return {
      'id': id?.toString(),
      'content': content,
      'userId': userId?.toString(),
      'userName': phone, // 使用 phone 作为用户名
      'name': name,
      'avatar': avatar,
      'createTime': createTime,
      'updateTime': createTime, // 使用创建时间作为更新时间
      'mediaList': mediaList != null ? jsonEncode(mediaList!.map((e) => e.toJson()).toList()) : null,
      'likeUserList': likeUserList != null ? jsonEncode(likeUserList!.map((e) => e.toJson()).toList()) : null,
      'commentList': commentList != null ? jsonEncode(commentList!.map((e) => e.toJson()).toList()) : null,
    };
  }

  // 从数据库 Map 创建 MomentItem
  static MomentItem fromMap(Map<String, dynamic> map) {
    return MomentItem(
      id: map['id'] != null ? int.tryParse(map['id']) : null,
      content: map['content'],
      userId: map['userId'] != null ? int.tryParse(map['userId']) : null,
      phone: map['userName'],
      name: map['name'],
      avatar: map['avatar'],
      createTime: map['createTime'],
      mediaList: map['mediaList'] != null 
          ? (jsonDecode(map['mediaList']) as List)
              .map((e) => MediaList.fromJson(e))
              .toList() 
          : null,
      likeUserList: map['likeUserList'] != null 
          ? (jsonDecode(map['likeUserList']) as List)
              .map((e) => LikeUserList.fromJson(e as Map<String, dynamic>))
              .toList()
          : null,
      commentList: map['commentList'] != null 
          ? (jsonDecode(map['commentList']) as List)
              .map((e) => CommentList.fromJson(e))
              .toList() 
          : null,
    );
  }
}