// lib/src/utils/database_helper.dart
import 'dart:async';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path_provider/path_provider.dart';
import '../../../../models/moments_list_response.dart';
import './moment_item_extension.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  factory DatabaseHelper() => _instance;

  DatabaseHelper._internal();

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    final documentsDirectory = await getApplicationDocumentsDirectory();
    final path = join(documentsDirectory.path, 'moments.db');
    return await openDatabase(
      path,
      version: 1,
      onCreate: _createDb,
    );
  }

  Future<void> _createDb(Database db, int version) async {
    await db.execute('''
      CREATE TABLE moments(
        id TEXT PRIMARY KEY,
        content TEXT,
        userId TEXT,
        userName TEXT,
        name TEXT,
        avatar TEXT,
        createTime TEXT,
        updateTime TEXT,
        mediaList TEXT,
        likeUserList TEXT,
        commentList TEXT
      )
    ''');
  }

  // 保存朋友圈列表
  Future<void> saveMoments(List<MomentItem> moments) async {
    final db = await database;
    await db.transaction((txn) async {
      for (var moment in moments) {
        await txn.insert(
          'moments',
          moment.toMap(),
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }
    });
  }

  // 获取缓存的朋友圈列表，带分页
  Future<List<MomentItem>> getMoments({int pageSize = 10, int pageNum = 1}) async {
    final db = await database;
    final offset = (pageNum - 1) * pageSize;
    final List<Map<String, dynamic>> maps = await db.query(
      'moments', 
      orderBy: 'createTime DESC',
      limit: pageSize,
      offset: offset,
    );
    
    return List.generate(maps.length, (i) {
      return MomentItemExtension.fromMap(maps[i]);
    });
  }

  // 获取朋友圈总数
  Future<int> getMomentsCount() async {
    final db = await database;
    final result = await db.rawQuery('SELECT COUNT(*) as count FROM moments');
    return Sqflite.firstIntValue(result) ?? 0;
  }

  // 清除所有缓存
  Future<void> clearMoments() async {
    final db = await database;
    await db.delete('moments');
  }
}