<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/TXFFmpeg.h</key>
		<data>
		wAAGh76bSEqKCfAs897bo5qlUy0=
		</data>
		<key>Headers/ffmpeg_rename_defines.h</key>
		<data>
		jN/rJFZdRPnevz/q/cja7964jDk=
		</data>
		<key>Headers/libavcodec/ac3_parser.h</key>
		<data>
		JAgjDJI7SrqpReJX52ds71cI0GY=
		</data>
		<key>Headers/libavcodec/adts_parser.h</key>
		<data>
		9bB49T+cXEmqDdPo6GSSVnzMdHs=
		</data>
		<key>Headers/libavcodec/ass_split.h</key>
		<data>
		2gSE6QPVTLr3Fwb7x5Rp7nAeeuY=
		</data>
		<key>Headers/libavcodec/avcodec.h</key>
		<data>
		EYbMZ/gfAhygBo5zVWnRKH1+6NA=
		</data>
		<key>Headers/libavcodec/avdct.h</key>
		<data>
		z4ebCZiTA7moXaH1HQiQ/RpV2f0=
		</data>
		<key>Headers/libavcodec/avfft.h</key>
		<data>
		5X20LvdznV9Hu/iYWeE2eh8EDQI=
		</data>
		<key>Headers/libavcodec/bytestream.h</key>
		<data>
		NJr44v0+hNWThiXFzSoULe9pDZ4=
		</data>
		<key>Headers/libavcodec/d3d11va.h</key>
		<data>
		o+GDJsyrbvu8MJGxk+yYfi3Ae6o=
		</data>
		<key>Headers/libavcodec/dirac.h</key>
		<data>
		7n0TC7nMr0mW71JsDRvuvJzOobA=
		</data>
		<key>Headers/libavcodec/dv_profile.h</key>
		<data>
		guXsVYZN/bgaKOwDNtlLt3dqZqI=
		</data>
		<key>Headers/libavcodec/dxva2.h</key>
		<data>
		5SUPYgLm9ioE4lTY4w/t2Tw+SRs=
		</data>
		<key>Headers/libavcodec/jni.h</key>
		<data>
		WPMasodKOLFl3EsOu6fdxWE28fg=
		</data>
		<key>Headers/libavcodec/mediacodec.h</key>
		<data>
		MSELn4Jgi4Auvw8qzG9K+Ggm8mY=
		</data>
		<key>Headers/libavcodec/qsv.h</key>
		<data>
		1KRNnP4fvdfr/j0Gtfv45MRuulY=
		</data>
		<key>Headers/libavcodec/vaapi.h</key>
		<data>
		UH1HvIX1OB9iLGKW16h59gWRHsM=
		</data>
		<key>Headers/libavcodec/vdpau.h</key>
		<data>
		xEESTfuhg5BnjQneIMcHtKwrwmg=
		</data>
		<key>Headers/libavcodec/version.h</key>
		<data>
		ErhIfkY1EQblKaYEUtIAQDzSiY4=
		</data>
		<key>Headers/libavcodec/videotoolbox.h</key>
		<data>
		Eme7NuKv25EK11QV9FBqQLMShHg=
		</data>
		<key>Headers/libavcodec/vorbis_parser.h</key>
		<data>
		/lGHHKxDUTXaK5Q8gXunJzibEEw=
		</data>
		<key>Headers/libavcodec/xvmc.h</key>
		<data>
		RDdNy/WNo7OpRiYv3wswkHDdaT0=
		</data>
		<key>Headers/libavfilter/avfilter.h</key>
		<data>
		u0DKxqmASU/oydnrkFtIMDsT838=
		</data>
		<key>Headers/libavfilter/buffersink.h</key>
		<data>
		CBWRojxU6g2sf6hDPYLlDgYgIM0=
		</data>
		<key>Headers/libavfilter/buffersrc.h</key>
		<data>
		9lkEbYA6QGq4WE+faE/RQXGQn+k=
		</data>
		<key>Headers/libavfilter/version.h</key>
		<data>
		QNv9uW4PiHmrX4rYdCr4Eijn9lk=
		</data>
		<key>Headers/libavformat/avc.h</key>
		<data>
		zuqmQJJga00mHwv3JvRwDZ7W3ss=
		</data>
		<key>Headers/libavformat/avformat.h</key>
		<data>
		aTOjkKeL0fKCsRqI2252ZZEoLKM=
		</data>
		<key>Headers/libavformat/avio.h</key>
		<data>
		TvxR4bTfR0ebD+1+1mLkx2I047c=
		</data>
		<key>Headers/libavformat/internal.h</key>
		<data>
		QXs+4qytv9It5QEn/N4lG8eV4LA=
		</data>
		<key>Headers/libavformat/os_support.h</key>
		<data>
		r3y76nrBi/M4NjjmgvnKS4MFWsI=
		</data>
		<key>Headers/libavformat/url.h</key>
		<data>
		uPlmh3/kutiSIqdouSWgF9TGd4s=
		</data>
		<key>Headers/libavformat/version.h</key>
		<data>
		q058GgXAZefiNhmyMW8y6wzr72k=
		</data>
		<key>Headers/libavutil/adler32.h</key>
		<data>
		aRoCfxwfiorsxUUZBhmKMvIuYwQ=
		</data>
		<key>Headers/libavutil/aes.h</key>
		<data>
		wY3d/TmjZ+L+0lBYTeB3SDnK3gA=
		</data>
		<key>Headers/libavutil/aes_ctr.h</key>
		<data>
		tJqUdskpa1J1ST/hwjCjzYZPPZk=
		</data>
		<key>Headers/libavutil/attributes.h</key>
		<data>
		70oWd8rEensjK/Wkt05TaouQt1g=
		</data>
		<key>Headers/libavutil/audio_fifo.h</key>
		<data>
		MyfFVDwuNy23nV+HCjf5LDUMlpI=
		</data>
		<key>Headers/libavutil/avassert.h</key>
		<data>
		RIdU9QoB0MlOcHg/FOLQh3ry0cw=
		</data>
		<key>Headers/libavutil/avconfig.h</key>
		<data>
		JlEMbqYlqDb0kReMt9J40giJ9UA=
		</data>
		<key>Headers/libavutil/avstring.h</key>
		<data>
		Dlf4bnL2Cv+OdiQ1G4A218IfKec=
		</data>
		<key>Headers/libavutil/avutil.h</key>
		<data>
		WWLUOXyAUQWOv1gPxkpgjnqfhfw=
		</data>
		<key>Headers/libavutil/base64.h</key>
		<data>
		7ZmPk39bIx21Sw6INED2MTWlcfQ=
		</data>
		<key>Headers/libavutil/blowfish.h</key>
		<data>
		Y9Zv6Z/88dPAZKNrrIMIT/+zOhE=
		</data>
		<key>Headers/libavutil/bprint.h</key>
		<data>
		YwSb7qayyAMzJlxrOilOD95cl8w=
		</data>
		<key>Headers/libavutil/bswap.h</key>
		<data>
		SIsW8QQ0vwQjdxgf+YQA0/rZIvM=
		</data>
		<key>Headers/libavutil/buffer.h</key>
		<data>
		b77YKIsgzgKANadOdnC7bdfnC8Q=
		</data>
		<key>Headers/libavutil/camellia.h</key>
		<data>
		6Y1W2qlgJZNd/acjPXmHPOjYQcg=
		</data>
		<key>Headers/libavutil/cast5.h</key>
		<data>
		Wr4N03V6Yg9e31D6enE579F6usY=
		</data>
		<key>Headers/libavutil/channel_layout.h</key>
		<data>
		4lmWj6nibdRMM5yh2an699qGYJI=
		</data>
		<key>Headers/libavutil/common.h</key>
		<data>
		o1DZkTDxdA2VoSa2sIonZwMN53Y=
		</data>
		<key>Headers/libavutil/cpu.h</key>
		<data>
		poL0tWRXEqKqMMdSK8jvx+FxZuw=
		</data>
		<key>Headers/libavutil/crc.h</key>
		<data>
		h6hOII6Y0QxM5gXexZSpMyQOsI8=
		</data>
		<key>Headers/libavutil/des.h</key>
		<data>
		h44uOKiIksv6CBdAzvIR4nEIM9c=
		</data>
		<key>Headers/libavutil/dict.h</key>
		<data>
		2RVUdxvQMqj7rYbS9pAcYLvpCo0=
		</data>
		<key>Headers/libavutil/display.h</key>
		<data>
		CjwGoQnsHBNtYcXSjpZ6VHD9ljo=
		</data>
		<key>Headers/libavutil/dovi_meta.h</key>
		<data>
		jgL4vW+RU/SEhdm7/vMmVO/D2f0=
		</data>
		<key>Headers/libavutil/downmix_info.h</key>
		<data>
		Jn88Zz4VTRMH+QB/1RMfHfYULfk=
		</data>
		<key>Headers/libavutil/encryption_info.h</key>
		<data>
		mrmcZta06xj53KIp0Cm7FBcFklU=
		</data>
		<key>Headers/libavutil/error.h</key>
		<data>
		/uaC22OgcgEwtZ/vsDnXUG4j5q4=
		</data>
		<key>Headers/libavutil/eval.h</key>
		<data>
		dV5+ybxFt1K3b0qi7PO756+w8Kw=
		</data>
		<key>Headers/libavutil/ffversion.h</key>
		<data>
		nchDwlNd7heEk30DXx4RotQVW3A=
		</data>
		<key>Headers/libavutil/fifo.h</key>
		<data>
		RAsTjx+uSqDZZaQbQNtiDdZNMCk=
		</data>
		<key>Headers/libavutil/file.h</key>
		<data>
		9t0Hlq7GMrH9412iqXeTpepgBjs=
		</data>
		<key>Headers/libavutil/frame.h</key>
		<data>
		Zxxxn408v4+FMna3byPWrtfjhIk=
		</data>
		<key>Headers/libavutil/hash.h</key>
		<data>
		KIoZivXG3iTI/z2P7AQdktvS9zA=
		</data>
		<key>Headers/libavutil/hmac.h</key>
		<data>
		Ld+IIkmN2Fp4XDAE0kGfxjR5WYY=
		</data>
		<key>Headers/libavutil/hwcontext.h</key>
		<data>
		ZoD0DamnTt2TLsKvCtIJZ10lrCY=
		</data>
		<key>Headers/libavutil/hwcontext_cuda.h</key>
		<data>
		hGYrwQwmC2b+w7bV8o8y4KqqHYk=
		</data>
		<key>Headers/libavutil/hwcontext_d3d11va.h</key>
		<data>
		KT+5fMsTpn7n69UemyC1MF3h3Bo=
		</data>
		<key>Headers/libavutil/hwcontext_drm.h</key>
		<data>
		FZVjI4QRwnf8TSm4+6IgBRuB+x8=
		</data>
		<key>Headers/libavutil/hwcontext_dxva2.h</key>
		<data>
		bW/uaU54JcOvFlGqZMT4UgLn6NY=
		</data>
		<key>Headers/libavutil/hwcontext_mediacodec.h</key>
		<data>
		4/3jXjzvwoTyUm2ywD0lwsDAD0U=
		</data>
		<key>Headers/libavutil/hwcontext_qsv.h</key>
		<data>
		oayjX4sqJViEwQ2+2FrA8Nv2hE8=
		</data>
		<key>Headers/libavutil/hwcontext_vaapi.h</key>
		<data>
		uclVGx0FOk8ORJcgVEnjY4lmgYI=
		</data>
		<key>Headers/libavutil/hwcontext_vdpau.h</key>
		<data>
		sowp4hhJVNNDKM7l460gbhytXdw=
		</data>
		<key>Headers/libavutil/hwcontext_videotoolbox.h</key>
		<data>
		oXfOysLb5pmt8YWLYNBxhP6NR2Y=
		</data>
		<key>Headers/libavutil/imgutils.h</key>
		<data>
		bX6wm+mdyZ/NTzkpBCeObqhdxMc=
		</data>
		<key>Headers/libavutil/intfloat.h</key>
		<data>
		Do3338v8xnexknlgiCxBDd/Z7dI=
		</data>
		<key>Headers/libavutil/intreadwrite.h</key>
		<data>
		AORLNvPykErsDfgLQbLkXsXc37U=
		</data>
		<key>Headers/libavutil/lfg.h</key>
		<data>
		lEuoQAEo3HU2WPh14O8X5G5Okpw=
		</data>
		<key>Headers/libavutil/log.h</key>
		<data>
		7hJ7VhfM2Ki4DJ7slFit4Sbfdr8=
		</data>
		<key>Headers/libavutil/lzo.h</key>
		<data>
		QDynH/VFfo/qe29xqd2gf9gQ8fw=
		</data>
		<key>Headers/libavutil/macros.h</key>
		<data>
		yHjMvEMu8fWttm4Bhfq+/kC1Mp4=
		</data>
		<key>Headers/libavutil/mastering_display_metadata.h</key>
		<data>
		v3q3czOkhWuQvw6WtKTTS7hQfrU=
		</data>
		<key>Headers/libavutil/mathematics.h</key>
		<data>
		toiRItVKx8jXPvwDgAlHdDjabvA=
		</data>
		<key>Headers/libavutil/md5.h</key>
		<data>
		oRQXNj26NE+wSB+GHGOlbA6eCkY=
		</data>
		<key>Headers/libavutil/mem.h</key>
		<data>
		Zuzi93CE5HtGcTKo9GPQKivJizc=
		</data>
		<key>Headers/libavutil/motion_vector.h</key>
		<data>
		hN3ZouLVNQGuyZv5XdiFbN/VTFE=
		</data>
		<key>Headers/libavutil/murmur3.h</key>
		<data>
		dPRY7Jz5EJmt/REAwNp19+XnEzc=
		</data>
		<key>Headers/libavutil/opt.h</key>
		<data>
		9+HjzT2PEH960GklNbFsVq/am/4=
		</data>
		<key>Headers/libavutil/parseutils.h</key>
		<data>
		AOixHu9pz71HplBrKcwrrEYhhUA=
		</data>
		<key>Headers/libavutil/pixdesc.h</key>
		<data>
		fgA9UFV8u7gZjpqox9jqqJ0CMAY=
		</data>
		<key>Headers/libavutil/pixfmt.h</key>
		<data>
		IdsgzoPU0Oor21h1NXfT04ZLvrE=
		</data>
		<key>Headers/libavutil/pthread_helper.h</key>
		<data>
		OnXVjSK6V9vdg+ASxPQXI7c5vVI=
		</data>
		<key>Headers/libavutil/random_seed.h</key>
		<data>
		AXgcmZ7VW7TmxR3Up9F6SQg19ic=
		</data>
		<key>Headers/libavutil/rational.h</key>
		<data>
		+rETPNnCzcBFt1hAa/5FJwDEPu8=
		</data>
		<key>Headers/libavutil/rc4.h</key>
		<data>
		Xnq4U5iEWUcl8hIy5yUmbWEPzGw=
		</data>
		<key>Headers/libavutil/replaygain.h</key>
		<data>
		8BBzMUHr8oeBDOkmHYWyvXV61sk=
		</data>
		<key>Headers/libavutil/ripemd.h</key>
		<data>
		2xdEI18L4oxGGt3xGjNrzeIu1iI=
		</data>
		<key>Headers/libavutil/samplefmt.h</key>
		<data>
		TVu8dhUgu/GEaZlUWSvd3g8iwM0=
		</data>
		<key>Headers/libavutil/sha.h</key>
		<data>
		6gq59PlmcHFbB2Aczp4PW4wcgVc=
		</data>
		<key>Headers/libavutil/sha512.h</key>
		<data>
		Gc4KXKVfm0GpfoVPnywVzOuF3OY=
		</data>
		<key>Headers/libavutil/spherical.h</key>
		<data>
		DkHFpaWsxp7ZfEuRskLeitkItKs=
		</data>
		<key>Headers/libavutil/stereo3d.h</key>
		<data>
		tRa/S3QSmLa+ZTsnSvrsTbdQelY=
		</data>
		<key>Headers/libavutil/tea.h</key>
		<data>
		KQ3wag+Qa3uvwOdCeUa1DxTsJVE=
		</data>
		<key>Headers/libavutil/threadmessage.h</key>
		<data>
		H63wn8WZm/rZKqZRlD4q27vHmIg=
		</data>
		<key>Headers/libavutil/time.h</key>
		<data>
		Io486SJ0DbYyCxUQnFSK+/g2vRc=
		</data>
		<key>Headers/libavutil/timecode.h</key>
		<data>
		ioljUwWFjXeR6c3AarVDmpT/h6I=
		</data>
		<key>Headers/libavutil/timestamp.h</key>
		<data>
		0fF82f4iMTWJh2gCdO+3St2A8rc=
		</data>
		<key>Headers/libavutil/tree.h</key>
		<data>
		5npf5m5g/6xp69csIaG52vJLirw=
		</data>
		<key>Headers/libavutil/twofish.h</key>
		<data>
		b5pahKBqay13rNwLG69tRnKyFSU=
		</data>
		<key>Headers/libavutil/tx.h</key>
		<data>
		a3xvfwr6/J/NzBsBO/3LYKvhMK8=
		</data>
		<key>Headers/libavutil/version.h</key>
		<data>
		5coIxEGXTdwphKHiv0OASICUcaM=
		</data>
		<key>Headers/libavutil/xtea.h</key>
		<data>
		odwtaIhHtQUxydhq2vTu0p+kqeE=
		</data>
		<key>Headers/libswresample/swresample.h</key>
		<data>
		ZGRGg9h9I33MieNCrbsPppMj+cQ=
		</data>
		<key>Headers/libswresample/version.h</key>
		<data>
		tj8KzUO28Y8vJtdaoFhmFo0gkyc=
		</data>
		<key>Headers/libswscale/swscale.h</key>
		<data>
		/+sRIbXfTFkVek+hgypoaRgVjJg=
		</data>
		<key>Headers/libswscale/version.h</key>
		<data>
		q0RoUTVrBUCyybeKh2GGnpIUY/8=
		</data>
		<key>Info.plist</key>
		<data>
		QBqv5jmXeqPerOd+rWJ9C6+eAgU=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		s/Mc0hdiuH7zOubtnON2Skw6z+k=
		</data>
		<key>TXFFmpeg_arm</key>
		<data>
		aFswECGIkhTz+/IwXeer+KbbJm4=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/TXFFmpeg.h</key>
		<dict>
			<key>hash</key>
			<data>
			wAAGh76bSEqKCfAs897bo5qlUy0=
			</data>
			<key>hash2</key>
			<data>
			Bti/V+wGN69ht9D1yB4McB6exQLU33OUdsg09mv9Dco=
			</data>
		</dict>
		<key>Headers/ffmpeg_rename_defines.h</key>
		<dict>
			<key>hash</key>
			<data>
			jN/rJFZdRPnevz/q/cja7964jDk=
			</data>
			<key>hash2</key>
			<data>
			cfe9jdAcNjs5cDl345sCJXetDyX1ZmFxiO1PxbeqJFE=
			</data>
		</dict>
		<key>Headers/libavcodec/ac3_parser.h</key>
		<dict>
			<key>hash</key>
			<data>
			JAgjDJI7SrqpReJX52ds71cI0GY=
			</data>
			<key>hash2</key>
			<data>
			6ea0NUY+FEuSiOezHI+7Sg7giVWUS94WDdoLTutwVc4=
			</data>
		</dict>
		<key>Headers/libavcodec/adts_parser.h</key>
		<dict>
			<key>hash</key>
			<data>
			9bB49T+cXEmqDdPo6GSSVnzMdHs=
			</data>
			<key>hash2</key>
			<data>
			awLJKfEhbTXjl56roojQKM/m7qBdAezB9Rdh7++5Z70=
			</data>
		</dict>
		<key>Headers/libavcodec/ass_split.h</key>
		<dict>
			<key>hash</key>
			<data>
			2gSE6QPVTLr3Fwb7x5Rp7nAeeuY=
			</data>
			<key>hash2</key>
			<data>
			QUNSYdZMeBmZB988UWKjkwPq2A6CGWP2ku5hYW9BpTM=
			</data>
		</dict>
		<key>Headers/libavcodec/avcodec.h</key>
		<dict>
			<key>hash</key>
			<data>
			EYbMZ/gfAhygBo5zVWnRKH1+6NA=
			</data>
			<key>hash2</key>
			<data>
			OtpszEkLH3KBUVPzqybcRQ7Tu/tUPMG2x66ZJwcqU8U=
			</data>
		</dict>
		<key>Headers/libavcodec/avdct.h</key>
		<dict>
			<key>hash</key>
			<data>
			z4ebCZiTA7moXaH1HQiQ/RpV2f0=
			</data>
			<key>hash2</key>
			<data>
			F4LzJ1qJqlNy0uGFiBw3XSRVOnsVIlZb+WlJIwLdtZw=
			</data>
		</dict>
		<key>Headers/libavcodec/avfft.h</key>
		<dict>
			<key>hash</key>
			<data>
			5X20LvdznV9Hu/iYWeE2eh8EDQI=
			</data>
			<key>hash2</key>
			<data>
			gwP8w2toSNpDFQkkJjdBRMDfZerBzWXf770yRwabqUk=
			</data>
		</dict>
		<key>Headers/libavcodec/bytestream.h</key>
		<dict>
			<key>hash</key>
			<data>
			NJr44v0+hNWThiXFzSoULe9pDZ4=
			</data>
			<key>hash2</key>
			<data>
			Qdax+Z0u4aAysI6DHL7U8zUrXOnTL0wMbSlXBFoQhuY=
			</data>
		</dict>
		<key>Headers/libavcodec/d3d11va.h</key>
		<dict>
			<key>hash</key>
			<data>
			o+GDJsyrbvu8MJGxk+yYfi3Ae6o=
			</data>
			<key>hash2</key>
			<data>
			8sggK+tMujC2TuBo1KpqJcv/ygWMjB+TgnRzzNoAwNA=
			</data>
		</dict>
		<key>Headers/libavcodec/dirac.h</key>
		<dict>
			<key>hash</key>
			<data>
			7n0TC7nMr0mW71JsDRvuvJzOobA=
			</data>
			<key>hash2</key>
			<data>
			7Wr1ZHVvuzkan8Erg/VRFvRfxyU+oA7dYlX76defZ3k=
			</data>
		</dict>
		<key>Headers/libavcodec/dv_profile.h</key>
		<dict>
			<key>hash</key>
			<data>
			guXsVYZN/bgaKOwDNtlLt3dqZqI=
			</data>
			<key>hash2</key>
			<data>
			yvsR2YaGKFCjl9pLo5CAYl9mNul5CLtOktfUNSlm1Q8=
			</data>
		</dict>
		<key>Headers/libavcodec/dxva2.h</key>
		<dict>
			<key>hash</key>
			<data>
			5SUPYgLm9ioE4lTY4w/t2Tw+SRs=
			</data>
			<key>hash2</key>
			<data>
			5p3EWn1akgazv+rRDK8xFxFwBc1TLE/FmdmXZjfBueM=
			</data>
		</dict>
		<key>Headers/libavcodec/jni.h</key>
		<dict>
			<key>hash</key>
			<data>
			WPMasodKOLFl3EsOu6fdxWE28fg=
			</data>
			<key>hash2</key>
			<data>
			pSxBpNFpeBl7tz0VOcOWoWVSfrv+3talnh6aTlWaAqM=
			</data>
		</dict>
		<key>Headers/libavcodec/mediacodec.h</key>
		<dict>
			<key>hash</key>
			<data>
			MSELn4Jgi4Auvw8qzG9K+Ggm8mY=
			</data>
			<key>hash2</key>
			<data>
			8AiIzjpQX/03c/YUUHdSjlV/vKgmSmgRt/CgeWbWZqc=
			</data>
		</dict>
		<key>Headers/libavcodec/qsv.h</key>
		<dict>
			<key>hash</key>
			<data>
			1KRNnP4fvdfr/j0Gtfv45MRuulY=
			</data>
			<key>hash2</key>
			<data>
			RZ/7kzsIKv43rWvxQkwamHby8jdQmp44if2ll2ROyvk=
			</data>
		</dict>
		<key>Headers/libavcodec/vaapi.h</key>
		<dict>
			<key>hash</key>
			<data>
			UH1HvIX1OB9iLGKW16h59gWRHsM=
			</data>
			<key>hash2</key>
			<data>
			F9xxFsSnnxklRXkX5bR8zyzlRsRzVyQfdxVf4lyuus8=
			</data>
		</dict>
		<key>Headers/libavcodec/vdpau.h</key>
		<dict>
			<key>hash</key>
			<data>
			xEESTfuhg5BnjQneIMcHtKwrwmg=
			</data>
			<key>hash2</key>
			<data>
			2qgrBoK9PcQ7EF6mDEftPRgFkxhFcDqVoSFJkGO5+68=
			</data>
		</dict>
		<key>Headers/libavcodec/version.h</key>
		<dict>
			<key>hash</key>
			<data>
			ErhIfkY1EQblKaYEUtIAQDzSiY4=
			</data>
			<key>hash2</key>
			<data>
			SIACvdQfgwssT6H2LKtInTbwe3MhtPbdq/oJ5KnRxvw=
			</data>
		</dict>
		<key>Headers/libavcodec/videotoolbox.h</key>
		<dict>
			<key>hash</key>
			<data>
			Eme7NuKv25EK11QV9FBqQLMShHg=
			</data>
			<key>hash2</key>
			<data>
			MfQs5Wh2GDacBbvwPIN2tcGpmEQju6ZATZ3Leole51k=
			</data>
		</dict>
		<key>Headers/libavcodec/vorbis_parser.h</key>
		<dict>
			<key>hash</key>
			<data>
			/lGHHKxDUTXaK5Q8gXunJzibEEw=
			</data>
			<key>hash2</key>
			<data>
			lRLO2xWjXDrohsoeiQe1P27AFPE/nCU3W4szNKnLbWU=
			</data>
		</dict>
		<key>Headers/libavcodec/xvmc.h</key>
		<dict>
			<key>hash</key>
			<data>
			RDdNy/WNo7OpRiYv3wswkHDdaT0=
			</data>
			<key>hash2</key>
			<data>
			VFongSYRpKB9dGfKAOWeBg5G615QCCf+CwUgXKpI0/k=
			</data>
		</dict>
		<key>Headers/libavfilter/avfilter.h</key>
		<dict>
			<key>hash</key>
			<data>
			u0DKxqmASU/oydnrkFtIMDsT838=
			</data>
			<key>hash2</key>
			<data>
			zo1iUdZQTJcF+BePCNdHZvPSP+1OyQKXyYJdvcw7XTs=
			</data>
		</dict>
		<key>Headers/libavfilter/buffersink.h</key>
		<dict>
			<key>hash</key>
			<data>
			CBWRojxU6g2sf6hDPYLlDgYgIM0=
			</data>
			<key>hash2</key>
			<data>
			4zV5LK+HwWX7ImASv8EweS+2tx5VhMRfYhOOrKeTbkI=
			</data>
		</dict>
		<key>Headers/libavfilter/buffersrc.h</key>
		<dict>
			<key>hash</key>
			<data>
			9lkEbYA6QGq4WE+faE/RQXGQn+k=
			</data>
			<key>hash2</key>
			<data>
			arnpltxhcX/AdLqqkZWWsOTPyAjxlCxrZMeWD60a8Tk=
			</data>
		</dict>
		<key>Headers/libavfilter/version.h</key>
		<dict>
			<key>hash</key>
			<data>
			QNv9uW4PiHmrX4rYdCr4Eijn9lk=
			</data>
			<key>hash2</key>
			<data>
			df7KuAVAQVEvpDX+EGVjLUxbG5KsWgO0ke0w8IuKx+w=
			</data>
		</dict>
		<key>Headers/libavformat/avc.h</key>
		<dict>
			<key>hash</key>
			<data>
			zuqmQJJga00mHwv3JvRwDZ7W3ss=
			</data>
			<key>hash2</key>
			<data>
			QX89Ubh2Zkun00AyELQfJIWZjTU8EPQn1Bnzh4ka2hI=
			</data>
		</dict>
		<key>Headers/libavformat/avformat.h</key>
		<dict>
			<key>hash</key>
			<data>
			aTOjkKeL0fKCsRqI2252ZZEoLKM=
			</data>
			<key>hash2</key>
			<data>
			oTWB05cK4WYiWt8pUuQHMD2u0TeoDwORTuJ/OHawxX4=
			</data>
		</dict>
		<key>Headers/libavformat/avio.h</key>
		<dict>
			<key>hash</key>
			<data>
			TvxR4bTfR0ebD+1+1mLkx2I047c=
			</data>
			<key>hash2</key>
			<data>
			AyQp8VCFZbasB96+tQNOLjf9zPakRT3pyp8gnhWmi88=
			</data>
		</dict>
		<key>Headers/libavformat/internal.h</key>
		<dict>
			<key>hash</key>
			<data>
			QXs+4qytv9It5QEn/N4lG8eV4LA=
			</data>
			<key>hash2</key>
			<data>
			omlUXmkJSAYj7Pzrg7krXI4bVI2LoBUpH4+h3gJpNIc=
			</data>
		</dict>
		<key>Headers/libavformat/os_support.h</key>
		<dict>
			<key>hash</key>
			<data>
			r3y76nrBi/M4NjjmgvnKS4MFWsI=
			</data>
			<key>hash2</key>
			<data>
			dsMiVG7kxTwuaDrykoPLzNzWnqReCDmZpOmKBIfENgE=
			</data>
		</dict>
		<key>Headers/libavformat/url.h</key>
		<dict>
			<key>hash</key>
			<data>
			uPlmh3/kutiSIqdouSWgF9TGd4s=
			</data>
			<key>hash2</key>
			<data>
			5jxIOthtlAxeBeJv9s7H/qdF/sw+AocLgsBJQTZYZzI=
			</data>
		</dict>
		<key>Headers/libavformat/version.h</key>
		<dict>
			<key>hash</key>
			<data>
			q058GgXAZefiNhmyMW8y6wzr72k=
			</data>
			<key>hash2</key>
			<data>
			lbm+zY0RSlta1dRMamK/lUaUzBtzLbmr2zLJIJuZXiw=
			</data>
		</dict>
		<key>Headers/libavutil/adler32.h</key>
		<dict>
			<key>hash</key>
			<data>
			aRoCfxwfiorsxUUZBhmKMvIuYwQ=
			</data>
			<key>hash2</key>
			<data>
			mKWtd1by2+4T1K1QCIyp2SsiZ0nvjBUM8GRmDQFYESs=
			</data>
		</dict>
		<key>Headers/libavutil/aes.h</key>
		<dict>
			<key>hash</key>
			<data>
			wY3d/TmjZ+L+0lBYTeB3SDnK3gA=
			</data>
			<key>hash2</key>
			<data>
			TqhWJD9vs7ZqylxdKD2OJx4YtF+0wnJEw3PJtw00wwk=
			</data>
		</dict>
		<key>Headers/libavutil/aes_ctr.h</key>
		<dict>
			<key>hash</key>
			<data>
			tJqUdskpa1J1ST/hwjCjzYZPPZk=
			</data>
			<key>hash2</key>
			<data>
			6UG3kNXY0MnRx/oKZ2llv/azbNyQvc8bISIFw1zaDTQ=
			</data>
		</dict>
		<key>Headers/libavutil/attributes.h</key>
		<dict>
			<key>hash</key>
			<data>
			70oWd8rEensjK/Wkt05TaouQt1g=
			</data>
			<key>hash2</key>
			<data>
			1pXFZhrfC1AYvZp+WffxuDkxi+7WVrCTDxDO5zYKUJo=
			</data>
		</dict>
		<key>Headers/libavutil/audio_fifo.h</key>
		<dict>
			<key>hash</key>
			<data>
			MyfFVDwuNy23nV+HCjf5LDUMlpI=
			</data>
			<key>hash2</key>
			<data>
			oURhdK6TVcamXvVFLvxlfuTUpJdIYh825JYN0UXocT4=
			</data>
		</dict>
		<key>Headers/libavutil/avassert.h</key>
		<dict>
			<key>hash</key>
			<data>
			RIdU9QoB0MlOcHg/FOLQh3ry0cw=
			</data>
			<key>hash2</key>
			<data>
			KmkD5uRJ/WBY/5y1e/ZL/7d0YJ8u2XXzm6DO+nsoawI=
			</data>
		</dict>
		<key>Headers/libavutil/avconfig.h</key>
		<dict>
			<key>hash</key>
			<data>
			JlEMbqYlqDb0kReMt9J40giJ9UA=
			</data>
			<key>hash2</key>
			<data>
			3PGQfEayxzaNWJa3cwVRS2GZ+/gTL8RqPDvvIdpgp6A=
			</data>
		</dict>
		<key>Headers/libavutil/avstring.h</key>
		<dict>
			<key>hash</key>
			<data>
			Dlf4bnL2Cv+OdiQ1G4A218IfKec=
			</data>
			<key>hash2</key>
			<data>
			eBqKChYmCPDcBOWRG6a3G/BAalA8DBTwkZmVLEX4FHs=
			</data>
		</dict>
		<key>Headers/libavutil/avutil.h</key>
		<dict>
			<key>hash</key>
			<data>
			WWLUOXyAUQWOv1gPxkpgjnqfhfw=
			</data>
			<key>hash2</key>
			<data>
			qKcKe4ntO4sMgLtTXuMgd/A7jEe0/EyPsqKvgdS7j+Y=
			</data>
		</dict>
		<key>Headers/libavutil/base64.h</key>
		<dict>
			<key>hash</key>
			<data>
			7ZmPk39bIx21Sw6INED2MTWlcfQ=
			</data>
			<key>hash2</key>
			<data>
			vzBdZRUuGW5bgsCzB0EdLlEhHRtzGkM5uRQBvFTS6dU=
			</data>
		</dict>
		<key>Headers/libavutil/blowfish.h</key>
		<dict>
			<key>hash</key>
			<data>
			Y9Zv6Z/88dPAZKNrrIMIT/+zOhE=
			</data>
			<key>hash2</key>
			<data>
			xVQS4VkIRvtQKVYBEeWT5bA7Ldyv9g498zYwqQa3OJU=
			</data>
		</dict>
		<key>Headers/libavutil/bprint.h</key>
		<dict>
			<key>hash</key>
			<data>
			YwSb7qayyAMzJlxrOilOD95cl8w=
			</data>
			<key>hash2</key>
			<data>
			e2IGBjaoNDZ8bRMv+xOB314X5ti7ltw2031Ywj3IOV4=
			</data>
		</dict>
		<key>Headers/libavutil/bswap.h</key>
		<dict>
			<key>hash</key>
			<data>
			SIsW8QQ0vwQjdxgf+YQA0/rZIvM=
			</data>
			<key>hash2</key>
			<data>
			bc+QaTEAsqhUF5K6VPn21P5Au8avxozfWuTBArHDv0s=
			</data>
		</dict>
		<key>Headers/libavutil/buffer.h</key>
		<dict>
			<key>hash</key>
			<data>
			b77YKIsgzgKANadOdnC7bdfnC8Q=
			</data>
			<key>hash2</key>
			<data>
			kq+WJPlCuaevnQ5csXQbtSVPxW3gIAFlRCSA+crkbf4=
			</data>
		</dict>
		<key>Headers/libavutil/camellia.h</key>
		<dict>
			<key>hash</key>
			<data>
			6Y1W2qlgJZNd/acjPXmHPOjYQcg=
			</data>
			<key>hash2</key>
			<data>
			00OJg8r7zbcj6WO43dZUIikGWKfn1vFIU2yfgo3SS20=
			</data>
		</dict>
		<key>Headers/libavutil/cast5.h</key>
		<dict>
			<key>hash</key>
			<data>
			Wr4N03V6Yg9e31D6enE579F6usY=
			</data>
			<key>hash2</key>
			<data>
			tnMzqrbe1Wo4YU2YWIicY111rDmzwaSIjSlRGc4ojD8=
			</data>
		</dict>
		<key>Headers/libavutil/channel_layout.h</key>
		<dict>
			<key>hash</key>
			<data>
			4lmWj6nibdRMM5yh2an699qGYJI=
			</data>
			<key>hash2</key>
			<data>
			NKgAMwUkj9JemdKejgZ2+7zDpL6Mr2Iut68AfrPFHkQ=
			</data>
		</dict>
		<key>Headers/libavutil/common.h</key>
		<dict>
			<key>hash</key>
			<data>
			o1DZkTDxdA2VoSa2sIonZwMN53Y=
			</data>
			<key>hash2</key>
			<data>
			wHVjXX66fSTScv1RxQX/WAIwV1N5tOC6sc1nR9NPnDE=
			</data>
		</dict>
		<key>Headers/libavutil/cpu.h</key>
		<dict>
			<key>hash</key>
			<data>
			poL0tWRXEqKqMMdSK8jvx+FxZuw=
			</data>
			<key>hash2</key>
			<data>
			4ojc+Z+Wyl5oyd9Mf5Di/vq6fTtAA20CQce+fZja2UM=
			</data>
		</dict>
		<key>Headers/libavutil/crc.h</key>
		<dict>
			<key>hash</key>
			<data>
			h6hOII6Y0QxM5gXexZSpMyQOsI8=
			</data>
			<key>hash2</key>
			<data>
			25/04kH6Y06aPL3xTXiy8nUFxVOzzip7Pi55dYOBiGE=
			</data>
		</dict>
		<key>Headers/libavutil/des.h</key>
		<dict>
			<key>hash</key>
			<data>
			h44uOKiIksv6CBdAzvIR4nEIM9c=
			</data>
			<key>hash2</key>
			<data>
			Yr/M1xrIxTHL51ULlXGSRYGy8wE6h4QRnA6FdJCkloo=
			</data>
		</dict>
		<key>Headers/libavutil/dict.h</key>
		<dict>
			<key>hash</key>
			<data>
			2RVUdxvQMqj7rYbS9pAcYLvpCo0=
			</data>
			<key>hash2</key>
			<data>
			aCOeGT7VooIj2TkBrIu/P7gXfbsBFmRkxNq8DHH7UKU=
			</data>
		</dict>
		<key>Headers/libavutil/display.h</key>
		<dict>
			<key>hash</key>
			<data>
			CjwGoQnsHBNtYcXSjpZ6VHD9ljo=
			</data>
			<key>hash2</key>
			<data>
			FClfqVfu5NAa92D7OoR7SOrL9znk+ua2Wpg9573Rj+Y=
			</data>
		</dict>
		<key>Headers/libavutil/dovi_meta.h</key>
		<dict>
			<key>hash</key>
			<data>
			jgL4vW+RU/SEhdm7/vMmVO/D2f0=
			</data>
			<key>hash2</key>
			<data>
			jgscfNk+SR/cFVQz7mso8EA8Y+FDUDQfoV4+TF/0E3c=
			</data>
		</dict>
		<key>Headers/libavutil/downmix_info.h</key>
		<dict>
			<key>hash</key>
			<data>
			Jn88Zz4VTRMH+QB/1RMfHfYULfk=
			</data>
			<key>hash2</key>
			<data>
			7V3LyEGaJwop8/9Dk5Sz8pvNLzVX/jMfnfQFrJJEpQw=
			</data>
		</dict>
		<key>Headers/libavutil/encryption_info.h</key>
		<dict>
			<key>hash</key>
			<data>
			mrmcZta06xj53KIp0Cm7FBcFklU=
			</data>
			<key>hash2</key>
			<data>
			uk9PwEtmnkWyK8ejDhCtzoGNFn2D3PgVzIpA6oFR8nY=
			</data>
		</dict>
		<key>Headers/libavutil/error.h</key>
		<dict>
			<key>hash</key>
			<data>
			/uaC22OgcgEwtZ/vsDnXUG4j5q4=
			</data>
			<key>hash2</key>
			<data>
			M05CVkDeJibaZyJysYOMuPS34Nm2IEQjCmJURpxUyP0=
			</data>
		</dict>
		<key>Headers/libavutil/eval.h</key>
		<dict>
			<key>hash</key>
			<data>
			dV5+ybxFt1K3b0qi7PO756+w8Kw=
			</data>
			<key>hash2</key>
			<data>
			TWNHDJknTgltGssdkfd6HEA4Uq3uRYeImCCEymobpnA=
			</data>
		</dict>
		<key>Headers/libavutil/ffversion.h</key>
		<dict>
			<key>hash</key>
			<data>
			nchDwlNd7heEk30DXx4RotQVW3A=
			</data>
			<key>hash2</key>
			<data>
			cd6Bz2NQIrjg5l0C0As8gt/tz1w4yhejpbl8fFjtCPs=
			</data>
		</dict>
		<key>Headers/libavutil/fifo.h</key>
		<dict>
			<key>hash</key>
			<data>
			RAsTjx+uSqDZZaQbQNtiDdZNMCk=
			</data>
			<key>hash2</key>
			<data>
			jOow9fSmcT1A8HvdSp2Lm+xDlqjLalDHNl3R79xW8gM=
			</data>
		</dict>
		<key>Headers/libavutil/file.h</key>
		<dict>
			<key>hash</key>
			<data>
			9t0Hlq7GMrH9412iqXeTpepgBjs=
			</data>
			<key>hash2</key>
			<data>
			mmzLvaTZFTgmcKV4tq0oSBt/GHqFIIe1lC6DiT8ITuQ=
			</data>
		</dict>
		<key>Headers/libavutil/frame.h</key>
		<dict>
			<key>hash</key>
			<data>
			Zxxxn408v4+FMna3byPWrtfjhIk=
			</data>
			<key>hash2</key>
			<data>
			r0Ae4nMPgKiPFvTcZ1IwaLtO2yjVZ6F1YnxnpYF9jrE=
			</data>
		</dict>
		<key>Headers/libavutil/hash.h</key>
		<dict>
			<key>hash</key>
			<data>
			KIoZivXG3iTI/z2P7AQdktvS9zA=
			</data>
			<key>hash2</key>
			<data>
			W+eY7hPz+9qEv9niqpoKKb+L0KrJaYmQDAyO73n2qjc=
			</data>
		</dict>
		<key>Headers/libavutil/hmac.h</key>
		<dict>
			<key>hash</key>
			<data>
			Ld+IIkmN2Fp4XDAE0kGfxjR5WYY=
			</data>
			<key>hash2</key>
			<data>
			SuxPfhICjHkb7XNdLXRIkcOaMfyNWkf1GkqmfJbCjKQ=
			</data>
		</dict>
		<key>Headers/libavutil/hwcontext.h</key>
		<dict>
			<key>hash</key>
			<data>
			ZoD0DamnTt2TLsKvCtIJZ10lrCY=
			</data>
			<key>hash2</key>
			<data>
			86SmUzuyrp3hi/lzDUVrk1XqU/gYrrovvHSZKKX7Eew=
			</data>
		</dict>
		<key>Headers/libavutil/hwcontext_cuda.h</key>
		<dict>
			<key>hash</key>
			<data>
			hGYrwQwmC2b+w7bV8o8y4KqqHYk=
			</data>
			<key>hash2</key>
			<data>
			eCVvH/FiLzcpuhxsRh4KDkrSKVColyh8TxNJfuoBjUQ=
			</data>
		</dict>
		<key>Headers/libavutil/hwcontext_d3d11va.h</key>
		<dict>
			<key>hash</key>
			<data>
			KT+5fMsTpn7n69UemyC1MF3h3Bo=
			</data>
			<key>hash2</key>
			<data>
			/Z/A/mtcoeoqNj5x4dM5Hq2T+7gwaOMtBdCmOFU366A=
			</data>
		</dict>
		<key>Headers/libavutil/hwcontext_drm.h</key>
		<dict>
			<key>hash</key>
			<data>
			FZVjI4QRwnf8TSm4+6IgBRuB+x8=
			</data>
			<key>hash2</key>
			<data>
			tZjzf0DPE0L5I8C5d4Sm8oMLVDho7M7gRjdeCW+9XyQ=
			</data>
		</dict>
		<key>Headers/libavutil/hwcontext_dxva2.h</key>
		<dict>
			<key>hash</key>
			<data>
			bW/uaU54JcOvFlGqZMT4UgLn6NY=
			</data>
			<key>hash2</key>
			<data>
			c6AzO2XplnWDTcsbY6XpM5Y4zMYZ8aL8uoXN0OF5reA=
			</data>
		</dict>
		<key>Headers/libavutil/hwcontext_mediacodec.h</key>
		<dict>
			<key>hash</key>
			<data>
			4/3jXjzvwoTyUm2ywD0lwsDAD0U=
			</data>
			<key>hash2</key>
			<data>
			eC/8SstUdIEpVBnOyY4DYpD1VUg+w3SXLbEpKZyezsM=
			</data>
		</dict>
		<key>Headers/libavutil/hwcontext_qsv.h</key>
		<dict>
			<key>hash</key>
			<data>
			oayjX4sqJViEwQ2+2FrA8Nv2hE8=
			</data>
			<key>hash2</key>
			<data>
			KMVMndtL2OjEexGx9E3/3kDqxgSytuxvFEj30ph4CHg=
			</data>
		</dict>
		<key>Headers/libavutil/hwcontext_vaapi.h</key>
		<dict>
			<key>hash</key>
			<data>
			uclVGx0FOk8ORJcgVEnjY4lmgYI=
			</data>
			<key>hash2</key>
			<data>
			5cHlcFIQKKXTgCnXjhhxEbgkTKGXl4BhwDlGaf/9sGw=
			</data>
		</dict>
		<key>Headers/libavutil/hwcontext_vdpau.h</key>
		<dict>
			<key>hash</key>
			<data>
			sowp4hhJVNNDKM7l460gbhytXdw=
			</data>
			<key>hash2</key>
			<data>
			bJY3PZ5d6yxQAATz9V7h0s6g92zfrquvWjrT5JOOglI=
			</data>
		</dict>
		<key>Headers/libavutil/hwcontext_videotoolbox.h</key>
		<dict>
			<key>hash</key>
			<data>
			oXfOysLb5pmt8YWLYNBxhP6NR2Y=
			</data>
			<key>hash2</key>
			<data>
			gVrYJah8LT0HaNVpqVim0woPttEnRhiLvzLY199istQ=
			</data>
		</dict>
		<key>Headers/libavutil/imgutils.h</key>
		<dict>
			<key>hash</key>
			<data>
			bX6wm+mdyZ/NTzkpBCeObqhdxMc=
			</data>
			<key>hash2</key>
			<data>
			yJ0pv2hqZc0WW2HXmH2ZQiZk6rpLfyh9m/lpWnOM7S4=
			</data>
		</dict>
		<key>Headers/libavutil/intfloat.h</key>
		<dict>
			<key>hash</key>
			<data>
			Do3338v8xnexknlgiCxBDd/Z7dI=
			</data>
			<key>hash2</key>
			<data>
			Oink7ryMJpz9hnuW3pHYIxdz05LBKogg5G6rqW0rTKE=
			</data>
		</dict>
		<key>Headers/libavutil/intreadwrite.h</key>
		<dict>
			<key>hash</key>
			<data>
			AORLNvPykErsDfgLQbLkXsXc37U=
			</data>
			<key>hash2</key>
			<data>
			1xHnehnTf50MI8H2RLlCbTebO0FMqW27mpI1aayr2s0=
			</data>
		</dict>
		<key>Headers/libavutil/lfg.h</key>
		<dict>
			<key>hash</key>
			<data>
			lEuoQAEo3HU2WPh14O8X5G5Okpw=
			</data>
			<key>hash2</key>
			<data>
			WSpko8VUamW6Ftyu2pYIkMxZZ7Eh9245Qgp1RzNTS/k=
			</data>
		</dict>
		<key>Headers/libavutil/log.h</key>
		<dict>
			<key>hash</key>
			<data>
			7hJ7VhfM2Ki4DJ7slFit4Sbfdr8=
			</data>
			<key>hash2</key>
			<data>
			MQDYjTKmFbGI+a64+ulUNqIlASH8W/h0YVB6epmPcBI=
			</data>
		</dict>
		<key>Headers/libavutil/lzo.h</key>
		<dict>
			<key>hash</key>
			<data>
			QDynH/VFfo/qe29xqd2gf9gQ8fw=
			</data>
			<key>hash2</key>
			<data>
			YeiZKN7p2DAwrezsrAaqbBriqtoGxWgv3lLFIBXFNVY=
			</data>
		</dict>
		<key>Headers/libavutil/macros.h</key>
		<dict>
			<key>hash</key>
			<data>
			yHjMvEMu8fWttm4Bhfq+/kC1Mp4=
			</data>
			<key>hash2</key>
			<data>
			f9hRTL5LbvAlsPXMO3UuLDlA4i9JRc9uY3JlO249K8o=
			</data>
		</dict>
		<key>Headers/libavutil/mastering_display_metadata.h</key>
		<dict>
			<key>hash</key>
			<data>
			v3q3czOkhWuQvw6WtKTTS7hQfrU=
			</data>
			<key>hash2</key>
			<data>
			f1Q9HzgP4bAHibAqBow94uBQBhHvbFApVr08IYhvYoU=
			</data>
		</dict>
		<key>Headers/libavutil/mathematics.h</key>
		<dict>
			<key>hash</key>
			<data>
			toiRItVKx8jXPvwDgAlHdDjabvA=
			</data>
			<key>hash2</key>
			<data>
			cS4NBlCSlkhdKKiDD1NLE/YOe7hzH1POmsuhbwijCYo=
			</data>
		</dict>
		<key>Headers/libavutil/md5.h</key>
		<dict>
			<key>hash</key>
			<data>
			oRQXNj26NE+wSB+GHGOlbA6eCkY=
			</data>
			<key>hash2</key>
			<data>
			3/4LyNCvRs12sxgsQeo5k+QfyREbvJtgyeIPrDyImA0=
			</data>
		</dict>
		<key>Headers/libavutil/mem.h</key>
		<dict>
			<key>hash</key>
			<data>
			Zuzi93CE5HtGcTKo9GPQKivJizc=
			</data>
			<key>hash2</key>
			<data>
			qzKv3J4qRNSSsKsh9LLTEyR4M2EWpp+Qpk/J9UXt6dk=
			</data>
		</dict>
		<key>Headers/libavutil/motion_vector.h</key>
		<dict>
			<key>hash</key>
			<data>
			hN3ZouLVNQGuyZv5XdiFbN/VTFE=
			</data>
			<key>hash2</key>
			<data>
			3AsKFaY4yLkd+VpBjFlR7l54fVGPIrbj1wCUkiU26Ls=
			</data>
		</dict>
		<key>Headers/libavutil/murmur3.h</key>
		<dict>
			<key>hash</key>
			<data>
			dPRY7Jz5EJmt/REAwNp19+XnEzc=
			</data>
			<key>hash2</key>
			<data>
			nGKLrPfHSSdC6varX1trXTPx9OIA/GJrWFx3fGFFybU=
			</data>
		</dict>
		<key>Headers/libavutil/opt.h</key>
		<dict>
			<key>hash</key>
			<data>
			9+HjzT2PEH960GklNbFsVq/am/4=
			</data>
			<key>hash2</key>
			<data>
			lqRd6JmcLODoo2bxSE0foB+Igr1dC3lDpajaAe6En3w=
			</data>
		</dict>
		<key>Headers/libavutil/parseutils.h</key>
		<dict>
			<key>hash</key>
			<data>
			AOixHu9pz71HplBrKcwrrEYhhUA=
			</data>
			<key>hash2</key>
			<data>
			O9mczBQHdEgtdJQQNKxW1F0CDL7cH+iddAPjs2gFV7M=
			</data>
		</dict>
		<key>Headers/libavutil/pixdesc.h</key>
		<dict>
			<key>hash</key>
			<data>
			fgA9UFV8u7gZjpqox9jqqJ0CMAY=
			</data>
			<key>hash2</key>
			<data>
			yAfEuZUyD5saYd1GUhU5odlNSSdHpUkjgwlD8p6Qn1U=
			</data>
		</dict>
		<key>Headers/libavutil/pixfmt.h</key>
		<dict>
			<key>hash</key>
			<data>
			IdsgzoPU0Oor21h1NXfT04ZLvrE=
			</data>
			<key>hash2</key>
			<data>
			LBF9L7IFlb6GS6tAYMMfu4JprpdzbNO169BmCeviyiA=
			</data>
		</dict>
		<key>Headers/libavutil/pthread_helper.h</key>
		<dict>
			<key>hash</key>
			<data>
			OnXVjSK6V9vdg+ASxPQXI7c5vVI=
			</data>
			<key>hash2</key>
			<data>
			9RIUJ/wj7IohUmuj9mMQhrK5K6eOs/087vSYmJLN35w=
			</data>
		</dict>
		<key>Headers/libavutil/random_seed.h</key>
		<dict>
			<key>hash</key>
			<data>
			AXgcmZ7VW7TmxR3Up9F6SQg19ic=
			</data>
			<key>hash2</key>
			<data>
			PcfVHLf++lYFg5t5LrCS1ue/8LDcMxr6BMLstvR886Q=
			</data>
		</dict>
		<key>Headers/libavutil/rational.h</key>
		<dict>
			<key>hash</key>
			<data>
			+rETPNnCzcBFt1hAa/5FJwDEPu8=
			</data>
			<key>hash2</key>
			<data>
			0FEc0EuKvwAgn3jgTP41hKBL01NsgJQnHRCcz5ocs2g=
			</data>
		</dict>
		<key>Headers/libavutil/rc4.h</key>
		<dict>
			<key>hash</key>
			<data>
			Xnq4U5iEWUcl8hIy5yUmbWEPzGw=
			</data>
			<key>hash2</key>
			<data>
			5SA36SAE/3zWkEpyvJi63Mj8U+pQcb40uJYsdxh901s=
			</data>
		</dict>
		<key>Headers/libavutil/replaygain.h</key>
		<dict>
			<key>hash</key>
			<data>
			8BBzMUHr8oeBDOkmHYWyvXV61sk=
			</data>
			<key>hash2</key>
			<data>
			Tsgu29xOVJP7o8rmonVm8PFdE5nM8W4lBz/9ULqBh+o=
			</data>
		</dict>
		<key>Headers/libavutil/ripemd.h</key>
		<dict>
			<key>hash</key>
			<data>
			2xdEI18L4oxGGt3xGjNrzeIu1iI=
			</data>
			<key>hash2</key>
			<data>
			gNs5OIRdtOr+tCVuJZwrDuB+5VYw2kG2bXeouLXNUj0=
			</data>
		</dict>
		<key>Headers/libavutil/samplefmt.h</key>
		<dict>
			<key>hash</key>
			<data>
			TVu8dhUgu/GEaZlUWSvd3g8iwM0=
			</data>
			<key>hash2</key>
			<data>
			75EI7Cz1MpaHeZKOuVdAA8aHQLQ8KjGtQRQz2bEnm6U=
			</data>
		</dict>
		<key>Headers/libavutil/sha.h</key>
		<dict>
			<key>hash</key>
			<data>
			6gq59PlmcHFbB2Aczp4PW4wcgVc=
			</data>
			<key>hash2</key>
			<data>
			yL3XKYVd6aMcmFWE3N+FVgzSS53g4ZSFXQ3BJzo55gs=
			</data>
		</dict>
		<key>Headers/libavutil/sha512.h</key>
		<dict>
			<key>hash</key>
			<data>
			Gc4KXKVfm0GpfoVPnywVzOuF3OY=
			</data>
			<key>hash2</key>
			<data>
			efZUgNc/WtOMyYRTPRu6ByiJdp6MDmSIow3Cx/iJJn0=
			</data>
		</dict>
		<key>Headers/libavutil/spherical.h</key>
		<dict>
			<key>hash</key>
			<data>
			DkHFpaWsxp7ZfEuRskLeitkItKs=
			</data>
			<key>hash2</key>
			<data>
			qMngnsXkg80Ypj/OvtbhIqdaONeSApbK+CCEk8uTYBo=
			</data>
		</dict>
		<key>Headers/libavutil/stereo3d.h</key>
		<dict>
			<key>hash</key>
			<data>
			tRa/S3QSmLa+ZTsnSvrsTbdQelY=
			</data>
			<key>hash2</key>
			<data>
			km9JD6e7G9br4siJc3X0oX1+FD2n8y8PX95/9PFHRc4=
			</data>
		</dict>
		<key>Headers/libavutil/tea.h</key>
		<dict>
			<key>hash</key>
			<data>
			KQ3wag+Qa3uvwOdCeUa1DxTsJVE=
			</data>
			<key>hash2</key>
			<data>
			Osv4KDPnr6gzXS3dsRpBOpzsZJ4ufcoj95kIe7dfP+Q=
			</data>
		</dict>
		<key>Headers/libavutil/threadmessage.h</key>
		<dict>
			<key>hash</key>
			<data>
			H63wn8WZm/rZKqZRlD4q27vHmIg=
			</data>
			<key>hash2</key>
			<data>
			YXtXsOUyhuBozcyDVLqF2FijCWGw7OYCw1crlJdcuBQ=
			</data>
		</dict>
		<key>Headers/libavutil/time.h</key>
		<dict>
			<key>hash</key>
			<data>
			Io486SJ0DbYyCxUQnFSK+/g2vRc=
			</data>
			<key>hash2</key>
			<data>
			oilI+FgTywXFt9+VZyXH6VilxGoTMfqEMYBb/93GDmc=
			</data>
		</dict>
		<key>Headers/libavutil/timecode.h</key>
		<dict>
			<key>hash</key>
			<data>
			ioljUwWFjXeR6c3AarVDmpT/h6I=
			</data>
			<key>hash2</key>
			<data>
			50dRGIUqfmwWPa2QHQy/TfIZVIw4ejcdjv7ztgciWfk=
			</data>
		</dict>
		<key>Headers/libavutil/timestamp.h</key>
		<dict>
			<key>hash</key>
			<data>
			0fF82f4iMTWJh2gCdO+3St2A8rc=
			</data>
			<key>hash2</key>
			<data>
			xHU983yLDmPpkEIAfVg2uVTOglkTZCxXzHxYtKUtAnM=
			</data>
		</dict>
		<key>Headers/libavutil/tree.h</key>
		<dict>
			<key>hash</key>
			<data>
			5npf5m5g/6xp69csIaG52vJLirw=
			</data>
			<key>hash2</key>
			<data>
			Crlm6saNj+kqX2y/DKMuHWqEwPvvP9gtCVemn4NzlE0=
			</data>
		</dict>
		<key>Headers/libavutil/twofish.h</key>
		<dict>
			<key>hash</key>
			<data>
			b5pahKBqay13rNwLG69tRnKyFSU=
			</data>
			<key>hash2</key>
			<data>
			GMHUrMP3XpiPI0+nYbdL9bn+NgP/leM2EBX6495taV4=
			</data>
		</dict>
		<key>Headers/libavutil/tx.h</key>
		<dict>
			<key>hash</key>
			<data>
			a3xvfwr6/J/NzBsBO/3LYKvhMK8=
			</data>
			<key>hash2</key>
			<data>
			3PSnW29WSqkHb/WNaffQCiFmgwdjxSGE72cOLx6OxT0=
			</data>
		</dict>
		<key>Headers/libavutil/version.h</key>
		<dict>
			<key>hash</key>
			<data>
			5coIxEGXTdwphKHiv0OASICUcaM=
			</data>
			<key>hash2</key>
			<data>
			72634wo4hqdwHhdIso92+VTMWgJAVYUKCQ92XazJ+Us=
			</data>
		</dict>
		<key>Headers/libavutil/xtea.h</key>
		<dict>
			<key>hash</key>
			<data>
			odwtaIhHtQUxydhq2vTu0p+kqeE=
			</data>
			<key>hash2</key>
			<data>
			Sj+bfDY2UX8rFR4YvKFe/KN+Ws8Eln5kpMe62Rx5gUw=
			</data>
		</dict>
		<key>Headers/libswresample/swresample.h</key>
		<dict>
			<key>hash</key>
			<data>
			ZGRGg9h9I33MieNCrbsPppMj+cQ=
			</data>
			<key>hash2</key>
			<data>
			3k2HSFvLM7fS+4uCHTJWqNjmVv4hMRArlUSeShd0GOw=
			</data>
		</dict>
		<key>Headers/libswresample/version.h</key>
		<dict>
			<key>hash</key>
			<data>
			tj8KzUO28Y8vJtdaoFhmFo0gkyc=
			</data>
			<key>hash2</key>
			<data>
			q/P6E6H3YuyiSqbHksQPjVpmpLcC/PeSZ7XRhd9ClOA=
			</data>
		</dict>
		<key>Headers/libswscale/swscale.h</key>
		<dict>
			<key>hash</key>
			<data>
			/+sRIbXfTFkVek+hgypoaRgVjJg=
			</data>
			<key>hash2</key>
			<data>
			O1rcE5kxVgiKTGBpc65Z48Wi25X56TNGM2Mk1w61A0g=
			</data>
		</dict>
		<key>Headers/libswscale/version.h</key>
		<dict>
			<key>hash</key>
			<data>
			q0RoUTVrBUCyybeKh2GGnpIUY/8=
			</data>
			<key>hash2</key>
			<data>
			C3MO1GBiFyIwEVdC0XWH1uKtMAnZlKVw5hHcExVbO2I=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			s/Mc0hdiuH7zOubtnON2Skw6z+k=
			</data>
			<key>hash2</key>
			<data>
			WIaDXyP0QCMaBdRzrIeOtldbJ8DYc9She/5FhtTUk98=
			</data>
		</dict>
		<key>TXFFmpeg_arm</key>
		<dict>
			<key>hash</key>
			<data>
			aFswECGIkhTz+/IwXeer+KbbJm4=
			</data>
			<key>hash2</key>
			<data>
			YjfYGNW8loub0ofCSWHfUwWskYMK3n/dxgxInphW5ds=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
