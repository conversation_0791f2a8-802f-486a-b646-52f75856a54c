{"project_info": {"project_number": "545427402667", "project_id": "pinim-849e4", "storage_bucket": "pinim-849e4.firebasestorage.app"}, "client": [{"client_info": {"mobilesdk_app_id": "1:545427402667:android:1b0b53bea08253fddd6cee", "android_client_info": {"package_name": "com.tencent.qcloud.tim.tuikit"}}, "oauth_client": [{"client_id": "545427402667-h8eaqnqjhjchu7mfet8hkph8qqds2b8d.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.tencent.qcloud.tim.tuikit", "certificate_hash": "8c3598da0419b01d4a6c6edc12965af6ad03fc02"}}, {"client_id": "545427402667-rq17et6698s1m62s6pcmika93qm0ug5d.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDALTJolJGzeax-sa-WXYftoy7lENppsgY"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "545427402667-rq17et6698s1m62s6pcmika93qm0ug5d.apps.googleusercontent.com", "client_type": 3}, {"client_id": "545427402667-v1r3qc8o9r69jm5f4oaoqebc79lo47u7.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.phichat.phichat"}}]}}}], "configuration_version": "1"}