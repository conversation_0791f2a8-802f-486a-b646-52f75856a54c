import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';
import 'package:tencent_cloud_chat_demo/utils/toast.dart';
import 'package:dio/dio.dart';
import 'dart:async';
import '../../http/dio_instance.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../apis/tuils_api.dart';

class RegisterPage extends StatefulWidget {
  const RegisterPage({Key? key}) : super(key: key);

  @override
  State<RegisterPage> createState() => _RegisterPageState();
}

class _RegisterPageState extends State<RegisterPage> {
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _verifyCodeController = TextEditingController();
  final TextEditingController _nicknameController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  bool _isAgreed = false;
  // 验证码时间 60秒
  int _verifyCodeTime = 60;
  bool _isVerifyCodeCounting = false;
  Timer? _timer;

  @override
  void dispose() {
    _phoneController.dispose();
    _verifyCodeController.dispose();
    _nicknameController.dispose();
    _timer?.cancel();
    super.dispose();
  }
  // 注册接口
  Future<void> handleRegister() async {
    final phone = _phoneController.text;
    final verifyCode = _verifyCodeController.text;
    final nickname = _nicknameController.text;
    final password = _passwordController.text;
    debugPrint(
        '[Register] 注册信息: 手机号: ${phone}, 验证码: ${verifyCode}, 昵称: ${nickname}');
    ToastUtils.showLoading();
    Api.instance.register({
      "loginName": phone,
      "nick": nickname,
      "password": password,
      "code": verifyCode,
      "loginDevice": 2
    }).then((res) {
      if(res.code == 0 && res.ok!){
        debugPrint('[Register] 注册成功');
        ToastUtils.toast(TIM_t("成功"));
        Navigator.pop(context);
      }else{
        debugPrint('[Register] 注册失败');
        ToastUtils.toast(res.msg ?? '');
      }
    }).whenComplete(()=>ToastUtils.hideLoading());
  }
  // 获取验证码
  getVerifyCode() {
    final phone = _phoneController.text;
    if(phone.isEmpty) {
      ToastUtils.toast(TIM_t("请输入手机号"));
      return;
    }

    // 点击获取验证码，开始倒计时
    debugPrint('[Register] 获取验证码');
    setState(() {
      _isVerifyCodeCounting = true;
      _verifyCodeTime = 60;
    });
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_verifyCodeTime > 0) {
        setState(() {
          _verifyCodeTime--;
        });
      } else {
        timer.cancel();
        setState(() {
          _isVerifyCodeCounting = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(
      context,
      designSize: const Size(375, 812),
      minTextAdapt: true,
    );
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: Stack(
        children: [
          // 背景图片
          Image.asset(
            'assets/login_bg.png',
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height,
            fit: BoxFit.cover,
          ),
          SafeArea(
            child: Stack(
              children: [
                // 密码登录按钮
                Positioned(
                  right: 16,
                  top: 16,
                  child: TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    style: TextButton.styleFrom(
                      foregroundColor: const Color(0xFF0072FC), // #0072FC
                      textStyle: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    child: Text(TIM_t('密码登录')),
                  ),
                ),
                // 主要内容
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 48),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const SizedBox(height: 64), // 32 + 32(标题到顶部的距离)
                      Text(
                        TIM_t('欢迎注册'),
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 48),
                      // 手机号输入框
                      Container(
                          height: 48,
                          decoration: BoxDecoration(
                            color: const Color(0xFFFFFFFF),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          child: Row(children: [
                            Image.asset(
                              'assets/login_phone.png',
                              width: 24,
                              height: 24,
                            ),
                            const SizedBox(width: 4),
                            Expanded(
                                child: TextField(
                              controller: _phoneController,
                              keyboardType: TextInputType.phone,
                              decoration: InputDecoration(
                                hintText: TIM_t('请输入手机号'),
                                border: InputBorder.none,
                                hintStyle: const TextStyle(
                                  color: Color(0xFF999999),
                                  fontSize: 14,
                                ),
                              ),
                              style: const TextStyle(
                                color: Color(0xFF333333),
                                fontSize: 14,
                              ),
                            ))
                          ])),
                      const SizedBox(height: 16),
                      // 密码输入框
                      Container(
                        height: 48,
                        decoration: BoxDecoration(
                          color: const Color(0xFFFFFFFF),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: Row(
                          children: [
                            Image.asset(
                              'assets/login_lock.png',
                              width: 24,
                              height: 24,
                            ),
                            const SizedBox(width: 4),
                            Expanded(
                              child: TextField(
                                controller: _passwordController,
                                keyboardType: TextInputType.visiblePassword,
                                decoration: InputDecoration(
                                  hintText: TIM_t('请输入密码'),
                                  border: InputBorder.none,
                                  hintStyle: const TextStyle(
                                    color: Color(0xFF999999),
                                    fontSize: 14,
                                  ),
                                ),
                                style: const TextStyle(
                                  color: Color(0xFF333333),
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 16),
                      // 验证码输入框
                      Container(
                        height: 48,
                        decoration: BoxDecoration(
                          color: const Color(0xFFFFFFFF),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: Row(
                          children: [
                            Image.asset(
                              'assets/login_code.png',
                              width: 24,
                              height: 24,
                            ),
                            const SizedBox(width: 4),
                            Expanded(
                              child: TextField(
                                controller: _verifyCodeController,
                                keyboardType: TextInputType.number,
                                decoration: InputDecoration(
                                  hintText: TIM_t('请输入验证码'),
                                  border: InputBorder.none,
                                  hintStyle: const TextStyle(
                                    color: Color(0xFF999999),
                                    fontSize: 14,
                                  ),
                                ),
                                style: const TextStyle(
                                  color: Color(0xFF333333),
                                  fontSize: 14,
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            TextButton(
                              onPressed: () {
                                // TODO: 实现获取验证码逻辑
                                getVerifyCode();
                              },
                              child: _isVerifyCodeCounting
                                  ? Text(
                                      '$_verifyCodeTime s',
                                      style: const TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    )
                                  : Text(
                                      TIM_t('获取验证码'),
                                      style: const TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                              style: TextButton.styleFrom(
                                foregroundColor:
                                    const Color(0xFF0072FC), // #0072FC
                                textStyle: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 16),
                      // 昵称输入框
                      Container(
                          height: 48,
                          decoration: BoxDecoration(
                            color: const Color(0xFFFFFFFF),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          child: Row(
                            children: [
                              Image.asset(
                                'assets/login_user.png',
                                width: 24,
                                height: 24,
                              ),
                              const SizedBox(width: 4),
                              Expanded(
                                child: TextField(
                                  controller: _nicknameController,
                                  decoration: InputDecoration(
                                    hintText: TIM_t('请输入昵称'),
                                    border: InputBorder.none,
                                    hintStyle: const TextStyle(
                                      color: Color(0xFF999999),
                                      fontSize: 14,
                                    ),
                                  ),
                                  style: const TextStyle(
                                    color: Color(0xFF333333),
                                    fontSize: 14,
                                  ),
                                ),
                              )
                            ],
                          )),
                      const SizedBox(height: 18),
                      // 协议勾选框
                      Row(
                        children: [
                          SizedBox(
                            width: 16,
                            height: 16,
                            child: Checkbox(
                              value: _isAgreed,
                              onChanged: (value) {
                                setState(() {
                                  _isAgreed = value ?? false;
                                });
                              },
                              checkColor: Colors.white,
                              fillColor:
                                  WidgetStateProperty.resolveWith<Color>(
                                (Set<WidgetState> states) {
                                  if (states.contains(WidgetState.selected)) {
                                    return const Color(0xFF0072FC);
                                  }
                                  return Colors.transparent;
                                },
                              ),
                              side: WidgetStateBorderSide.resolveWith((states) {
                                if (states.contains(WidgetState.selected)) {
                                  return const BorderSide(
                                    width: 1.5,
                                    color: Color(0xFF0072FC), // 设置未选择时的边框颜色
                                  );
                                }

                                return const BorderSide(
                                  width: 1.5,
                                  color: Color(0xFFD8D8D8), // 设置未选择时的边框颜色
                                  );
                                }
                              ),
                            ),
                          ),
                          Padding(
                            padding: EdgeInsets.only(left: 8.w),
                            child:
                                Text(TIM_t('阅读并同意'), style: TextStyle(fontSize: 8.sp)),
                          ),
                          TextButton(
                            onPressed: () {
                              // TODO: 跳转到用户服务协议
                            },
                            child: Text('《${TIM_t("用户服务协议")}》', style: TextStyle(fontSize: 10.sp)),
                            style: TextButton.styleFrom(
                              foregroundColor:
                                  const Color(0xFF0072FC), // #0072FC
                              textStyle: const TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                              padding: EdgeInsets.zero,
                              minimumSize: Size.zero,
                              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                            ),
                          ),
                          Text(TIM_t("和"), style: TextStyle(fontSize: 10.sp)),
                          TextButton(
                            onPressed: () {
                              // TODO: 跳转到隐私政策
                            },
                            child: Text('《${TIM_t("隐私政策")}》', style: TextStyle(fontSize: 10.sp)),
                            style: TextButton.styleFrom(
                              foregroundColor:
                                  const Color(0xFF0072FC), // #0072FC
                              textStyle: const TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                              padding: EdgeInsets.zero,
                              minimumSize: Size.zero,
                              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 32),
                      // 注册按钮
                      SizedBox(
                        width: double.infinity,
                        height: 40,
                        child: ElevatedButton(
                          onPressed: _isAgreed
                              ? () {
                                  // TODO: 实现注册逻辑
                                  handleRegister();
                                }
                              : null,
                          child: Text(TIM_t('注册'), style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500, color: Colors.white),),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF0072FC),
                            foregroundColor: const Color(0xFFCBDBFF),
                            disabledBackgroundColor: const Color(0xFF98C6FD), // 禁用时的背景颜色
                            disabledForegroundColor: const Color(0xFFFFFFFF), // 禁用时的前景颜色
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
