import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';
import 'package:tencent_cloud_chat_demo/config.dart';
import 'package:provider/provider.dart';
import './withdraw_page.dart';
import './bill_page.dart';
import '../../provider/wallet_provider.dart';
import './bank_add_page.dart';
import '../../widgets/confirm_dialog.dart';
class MoneyPage extends StatefulWidget {
  const MoneyPage({Key? key}) : super(key: key);

  @override
  State<MoneyPage> createState() => _MoneyPageState();
}

class _MoneyPageState extends State<MoneyPage> {
  late WalletProvider walletProvider;
  @override
  void initState() {
    super.initState();
    walletProvider = Provider.of<WalletProvider>(context, listen: false);
  }

  jumpPage(String type) async {
    if (type == 'add') {
      // 如果没有银行卡，跳转到添加银行卡页面
      if (walletProvider.bankList.isEmpty) {
        final bool? confirm = await ConfirmDialog.show(
          context: context,
          title: TIM_t('绑定银行卡'),
          content: TIM_t('您还没有绑定银行卡，是否绑定?'),
        );
        if (confirm == true) {
          Navigator.push(context, MaterialPageRoute(builder: (context) => const BankAddPage()));
        }
        return;
      }
      // 跳转充值页面
      Navigator.push(context, MaterialPageRoute(builder: (context) => const WithdrawPage(type: 'add')));
    }
    if (type == 'withdraw') {
      // 如果没有银行卡，跳转到添加银行卡页面
      if (walletProvider.bankList.isEmpty) {
        final bool? confirm = await ConfirmDialog.show(
          context: context,
          title: TIM_t('绑定银行卡'),
          content: TIM_t('您还没有绑定银行卡，是否绑定?'),
        );
        if (confirm == true) {
          Navigator.push(context, MaterialPageRoute(builder: (context) => const BankAddPage()));
        }
        return;
      }
      // 提现逻辑
      Navigator.push(context, MaterialPageRoute(builder: (context) => const WithdrawPage(type: 'withdraw')));
    }
    if (type == 'bill') {
      // 跳转零钱明细页面
      Navigator.push(context, MaterialPageRoute(builder: (context) => const BillPage()));
    }
  }

  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(
      context,
      designSize: const Size(375, 812),
      minTextAdapt: true,
    );
    return 
        Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          elevation: 1,
          title: Text(TIM_t("钱包"),style: const TextStyle(fontSize: IMDemoConfig.appBarTitleFontSize),),
          backgroundColor: const Color(0xFFFFFFFF),
          surfaceTintColor: Colors.white,
          actions: [
            InkWell(
              onTap: () {
                jumpPage('bill');
              },
              child: Padding(padding: EdgeInsets.only(right: 16.w), child: Text(TIM_t("零钱明细"), style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w400, color: const Color(0xFF333333)),),)
            ),
          ]
        ),
        body: Column(
          children: [
            Expanded(
              child: _moneyInfoView()
            ),
            // 添加银行卡按钮
            Padding(
              padding: EdgeInsets.only(left: 16.w, right: 16.w, bottom: 16.h),
              child: InkWell(
                  onTap: () {
                    jumpPage('add');
                  },
                  child: Container(
                    width: 140.w,
                    height: 40.h,
                    decoration: BoxDecoration(
                      color: const Color(0xFF0072FC),
                      borderRadius: BorderRadius.circular(8.w),
                    ),
                    child: Center(
                      child: Text(TIM_t("充值"),
                          style: TextStyle(
                              fontSize: 14.sp,
                              fontWeight: FontWeight.w500,
                              color: Colors.white)),
                    ),
                  )),
            ),
            Padding(
              padding: EdgeInsets.only(left: 16.w, right: 16.w, bottom: 120.h),
              child: InkWell(
                  onTap: () {
                    jumpPage('withdraw');
                  },
                  child: Container(
                    width: 140.w,
                    height: 40.h,
                    decoration: BoxDecoration(
                      color: const Color(0xFFE9E9E9),
                      borderRadius: BorderRadius.circular(8.w),
                    ),
                    child: Center(
                      child: Text(TIM_t("提现"),
                          style: TextStyle(
                              fontSize: 14.sp,
                              fontWeight: FontWeight.w500,
                              color: const Color(0xFF333333))),
                    ),
                  )),
            ),
          ],
        ));

  }

  Widget _moneyInfoView() {
    return Container(
      width: double.infinity,
      alignment: Alignment.center,
      margin: EdgeInsets.only(top: 40.h),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Image.asset('assets/serveice/default_icon.png', width: 68.w, height: 68.h,),
          SizedBox(height: 32.h,),
          Text(TIM_t("我的零钱"), style: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w500, color: const Color(0xFF333333))),
          SizedBox(height: 16.h,),
          moneyView(),
        ],
      ),
    );
  }

  Widget moneyView() {
    return Consumer<WalletProvider>(
      builder: (context, walletProvider, child){
        return  RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: '₱', 
                  style: TextStyle(
                    fontSize: 24.sp, 
                    fontWeight: FontWeight.w500, 
                    color: const Color(0xFF333333),
                    height: 1.5, // 调整行高使其与较大文本底部对齐
                  )
                ),
                TextSpan(
                  text: walletProvider.balance.toStringAsFixed(2), 
                  style: TextStyle(
                    fontSize: 36.sp, 
                    fontWeight: FontWeight.w500, 
                    color: const Color(0xFF333333)
                  )
                ),
              ],
            ),
            textAlign: TextAlign.start,
          );
      }
    );
  }
}
