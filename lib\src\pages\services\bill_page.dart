import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';
import 'package:tencent_cloud_chat_demo/config.dart';
import 'package:provider/provider.dart';
import './bank_vm.dart';
import './bank_add_page.dart';
class BillPage extends StatefulWidget {
  const BillPage({Key? key}) : super(key: key);

  @override
  State<BillPage> createState() => _BillPageState();
}

class _BillPageState extends State<BillPage> {
  BankVM viewModel = BankVM();
  @override
  void initState() {
    super.initState();
    viewModel.getBankList();
  }

  jumpPage(String type) {
    if (type == 'add') {
      // 添加银行卡逻辑
      final result = Navigator.push(context, MaterialPageRoute(builder: (context) => const BankAddPage()));
      if (result == true) {
        viewModel.getBankList();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(
      context,
      designSize: const Size(375, 812),
      minTextAdapt: true,
    );
    return ChangeNotifierProvider(
      create: (context) => viewModel,
      child: Scaffold(
        backgroundColor: const Color(0xFFF9F9F9),
        appBar: AppBar(
          elevation: 1,
          title: Text(TIM_t("账单"),style: const TextStyle(fontSize: IMDemoConfig.appBarTitleFontSize),),
          backgroundColor: const Color(0xFFFFFFFF),
          surfaceTintColor: Colors.white,
        ),
        body: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _billTopView(),
            _billDateView(),
            // 添加银行卡按钮
            Expanded(
              child: SingleChildScrollView(
                child: _listView(),
              ),
            ),
          ],
        )) 
    );

  }

  Widget _billTopView() {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFFE2E2E2),
        borderRadius: BorderRadius.circular(8.w),
      ),
      padding: EdgeInsets.only(left: 8.w, right: 8.w, top: 5.h, bottom: 5.h),
      margin: EdgeInsets.only(left: 16.w, top: 16.h, bottom: 16.h),
      width: 72.w,
      child: Row(
        children: [
          Text(TIM_t("全部账单"), style: TextStyle(fontSize: 10.sp, fontWeight: FontWeight.w500, color: const Color(0xFF333333))),
          SizedBox(width: 4.w,),
          Image.asset('assets/serveice/bottom_icon.png', width: 10.w, height: 10.h,),
        ],
      ),
    );
  }

  Widget _billDateView() {
    return Container(
      margin: EdgeInsets.only(left: 16.w, right: 16.w, bottom: 12.h, top: 12.h),
      child: Row(
        children: [
          Row(
            children: [
              Text('2025-6', style: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w500, color: const Color(0xFF333333))),
              SizedBox(width: 2.w,),
              Image.asset('assets/serveice/bottom_icon_big.png', width: 24.w, height: 24.h,),
            ],
         ),
         const Expanded(child: SizedBox()),
          Row(
            children: [
              Text(TIM_t('支出'), style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.w500, color: const Color(0xFF999999))),
              Text('₱', style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.w500, color: const Color(0xFF999999))),
              Text('100.00', style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.w500, color: const Color(0xFF999999))),
            ],
         ),
         SizedBox(width: 12.w,),
         Row(
            children: [
              Text(TIM_t('收入'), style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.w500, color: const Color(0xFF999999))),
              Text('₱', style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.w500, color: const Color(0xFF999999))),
              Text('100.00', style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.w500, color: const Color(0xFF999999))),
            ],
         ),
        ],
      )
    );
  }

  Widget _listView() {
     return Container(
      padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 2.h),
      color: Colors.white,
      child: ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) {
          return _listItemView();
        },
        itemCount: 10,
      ),
     );
  }

  Widget _listItemView() {
    return Container(
      padding: EdgeInsets.only(top: 12.h, bottom: 12.h),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: const Color(0xFFE9E9E9),
            width: 1.w,
          ),
        ),
      ),
      child: Row(
        children: [
          Column(
            children: [
              Text('Juanmao', style: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w500, color: const Color(0xFF333333))),
              Text('2025-6-12', style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.w500, color: const Color(0xFF999999))),
            ]
          ),
          const Expanded(child: SizedBox()),
          Row(
            children: [
              Text('-', style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.w500, color: const Color(0xFF999999))),
              Text('₱', style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.w500, color: const Color(0xFF999999))),
              Text('100.00', style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.w500, color: const Color(0xFF999999))),
            ]
          )
        ],
      ),
    );
  }
}
