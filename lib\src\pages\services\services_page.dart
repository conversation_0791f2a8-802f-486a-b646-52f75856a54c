import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';
import 'package:tencent_cloud_chat_demo/config.dart';
import 'package:tencent_cloud_chat_demo/utils/toast.dart';
import './wallet_page.dart';
import '../../provider/wallet_provider.dart';
class ServicesPage extends StatefulWidget {
  const ServicesPage({super.key});
  @override
  State<StatefulWidget> createState() => _ServicesPageState();
}

class _ServicesPageState extends State<ServicesPage> {
  late WalletProvider walletProvider;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    walletProvider = Provider.of<WalletProvider>(context, listen: false);
    walletProvider.queryAccount();
  }

  jumpPage(String type) {
    if (type == 'pay') {
      ToastUtils.toast(TIM_t('开发中'));
    }
    if (type == 'wallet') {
      Navigator.push(context, MaterialPageRoute(builder: (context) => const WalletPage()));
    }
  }


  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(
      context,
      designSize: const Size(375, 812),
      minTextAdapt: true,
    );
    return Scaffold(
      backgroundColor: const Color(0xFFF9F9F9),
      appBar: AppBar(
        elevation: 1,
        title: Text(
          TIM_t("服务"),
          style: const TextStyle(fontSize: IMDemoConfig.appBarTitleFontSize),
        ),
        backgroundColor: const Color(0xFFFFFFFF),
        surfaceTintColor: Colors.white,
      ),
      body: Container(
        height: 118.h,
        margin: EdgeInsets.only(left: 16.w, right: 16.w, top: 16.h, bottom: 16.h),
        padding: EdgeInsets.only(left: 65.w, right: 65.w, top: 22.h),
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
            colors: [Color(0xff6D97FD), Color(0xff97B8FF)],
          ),
          borderRadius: BorderRadius.circular(8.w),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
          InkWell(
            onTap: () {
              jumpPage('pay');
            },
            child: Column(
              children: [
                Image.asset('assets/serveice/pay_icon.png', width: 34.w, height: 34.h,),
                SizedBox(height: 2.h,),
                Text(TIM_t("收付款"), style: TextStyle(fontSize: 12.sp, color: const Color(0xFFFFFFFF)))
              ],
            ),
          ),
          walletView(),
        ],),
      )
    );
  }

  Widget walletView() {
    return Consumer<WalletProvider>(
       builder: (context, walletProvider, child){
        return InkWell(
            onTap: () {
              jumpPage('wallet');
            },
            child: Column(
              children: [
                Image.asset('assets/serveice/wallet_icon.png', width: 34.w, height: 34.h,),
                SizedBox(height: 2.h,),
                Text(TIM_t("钱包"), style: TextStyle(fontSize: 12.sp, color: const Color(0xFFFFFFFF))),
                SizedBox(height: 2.h,),
                Text('₱${walletProvider.balance.toStringAsFixed(2)}', style: TextStyle(fontSize: 12.sp, color: const Color(0xFFFFFFFF)))
              ],
            )
          );
       }
    );
  }
}
