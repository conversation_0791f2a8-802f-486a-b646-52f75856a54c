'use strict';const e={A2KEY_AND_TINYID_UPDATED:"_inner1",CLOUD_CONFIG_UPDATED:"_inner2",PROFILE_UPDATED:"_inner3",CONV_SYNC_COMPLETED:"_inner4",C2C_UNREAD_HANDLE_COMPLETED:"_inner5"};let t,s;t="undefined"!=typeof console?console:"undefined"!=typeof global&&global.console?global.console:"undefined"!=typeof window&&window.console?window.console:{};const r=function(){},i=["assert","clear","count","debug","dir","dirxml","error","group","groupCollapsed","groupEnd","info","log","profile","profileEnd","table","time","timeEnd","timeStamp","trace","warn"];let n=i.length;for(;n--;)s=i[n],console[s]||(t[s]=r);var o=t;class a{constructor(e=0,t=0){this.high=e,this.low=t}equal(e){return null!==e&&(this.low===e.low&&this.high===e.high)}toString(){const e=Number(this.high).toString(16);let t=Number(this.low).toString(16);if(t.length<8){let e=8-t.length;for(;e;)t="0"+t,e--}return e+t}}const d={TEST:{CHINA:{DEFAULT:"wss://wss-dev.tim.qq.com"},OVERSEA:{DEFAULT:"wss://wss-dev.tim.qq.com"},SINGAPORE:{DEFAULT:"wss://wsssgp-dev.im.qcloud.com"},KOREA:{DEFAULT:"wss://wsskr-dev.im.qcloud.com"},GERMANY:{DEFAULT:"wss://wssger-dev.im.qcloud.com"},IND:{DEFAULT:"wss://wssind-dev.im.qcloud.com"},JPN:{DEFAULT:"wss://wssjpn-dev.im.qcloud.com"},USA:{DEFAULT:"wss://wssusa-dev.im.qcloud.com"}},PRODUCTION:{CHINA:{DEFAULT:"wss://wss.im.qcloud.com",BACKUP:"wss://wss.tim.qq.com",STAT:"https://events.im.qcloud.com",ANYCAST:"wss://162.14.13.203"},OVERSEA:{DEFAULT:"wss://wss.im.qcloud.com",BACKUP:"wss://wss.my-imcloud.com",STAT:"https://api.my-imcloud.com"},SINGAPORE:{DEFAULT:"wss://wsssgp.im.qcloud.com",BACKUP:"wss://wsssgp.my-imcloud.com",STAT:"https://apisgp.my-imcloud.com"},KOREA:{DEFAULT:"wss://wsskr.im.qcloud.com",BACKUP:"wss://wsskr.my-imcloud.com",STAT:"https://apikr.my-imcloud.com"},GERMANY:{DEFAULT:"wss://wssger.im.qcloud.com",BACKUP:"wss://wssger.my-imcloud.com",STAT:"https://apiger.my-imcloud.com"},IND:{DEFAULT:"wss://wssind.my-imcloud.com",BACKUP:"wss://wssind.im.qcloud.com",STAT:"https://apiind.my-imcloud.com"},JPN:{DEFAULT:"wss://wssjpn.im.qcloud.com",BACKUP:"wss://wssjpn.my-imcloud.com",STAT:"https://apijpn.my-imcloud.com"},USA:{DEFAULT:"wss://wssusa.im.qcloud.com",BACKUP:"wss://wssusa.my-imcloud.com",STAT:"https://apiusa.my-imcloud.com"}}},u={ANDROID:2,IOS:3,MAC:4,WEB:7,WX_MP:8,QQ_MP:9,TT_MP:10,BAIDU_MP:11,ALI_MP:12,IPAD:13,UNI_NATIVE_APP:15},l="CHINA",p={HOST:{CURRENT:{DEFAULT:"wss://wss.im.qcloud.com",STAT:"https://events.im.qcloud.com"},setCurrent(e=l){this.CURRENT=d.PRODUCTION[e]}},NAME:{OPEN_IM:"openim",OPEN_IM_MSG_EXT:"openim_msg_ext_http_svc",GROUP:"group_open_http_svc",GROUP_AVCHATROOM:"group_open_avchatroom_http_svc",GROUP_COMMUNITY:"million_group_open_http_svc",GROUP_ATTR:"group_open_attr_http_svc",FRIEND:"sns",PROFILE:"profile",RECENT_CONTACT:"recentcontact",PIC:"openpic",BIG_GROUP_NO_AUTH:"group_open_http_noauth_svc",BIG_GROUP_LONG_POLLING:"group_open_long_polling_http_svc",BIG_GROUP_LONG_POLLING_NO_AUTH:"group_open_long_polling_http_noauth_svc",IM_OPEN_STAT:"imopenstat",WEB_IM:"webim",IM_COS_SIGN:"im_cos_sign_svr",CUSTOM_UPLOAD:"im_cos_msg",HEARTBEAT:"heartbeat",IM_OPEN_PUSH:"im_open_push",IM_OPEN_STATUS:"im_open_status",IM_LONG_MESSAGE:"im_long_msg",IM_CONFIG_MANAGER:"im_sdk_config_mgr",STAT_SERVICE:"StatSvc",OVERLOAD_PUSH:"OverLoadPush",IM_MSG_AUDIT_MGR:"im_msg_audit_mgr",TUIROOM_SVR:"tui_room_svr",IM_OPEN_TRANSLATE:"im_open_translate",IM_OPEN_SPEECH:"im_open_speech",MESSAGE_SEARCH:"message_search"},CHANNEL:{SOCKET:1,XHR:2,AUTO:0},NAME_VERSION:{openim:"v4",group_open_http_svc:"v4",sns:"v4",profile:"v4",recentcontact:"v4",openpic:"v4",group_open_http_noauth_svc:"v4",group_open_long_polling_http_svc:"v4",group_open_long_polling_http_noauth_svc:"v4",imopenstat:"v4",im_cos_sign_svr:"v4",im_cos_msg:"v4",webim:"v4",im_open_push:"v4",im_open_status:"v4"}};new a(0,Math.pow(2,0)).toString(),new a(0,Math.pow(2,1)).toString(),new a(0,Math.pow(2,2)).toString(),new a(0,Math.pow(2,3)).toString(),new a(0,Math.pow(2,4)).toString(),new a(0,Math.pow(2,6)).toString(),new a(0,Math.pow(2,7)).toString(),new a(0,Math.pow(2,9)).toString(),new a(0,Math.pow(2,10)).toString(),new a(0,Math.pow(2,11)).toString(),new a(0,Math.pow(2,13)).toString(),new a(0,Math.pow(2,15)).toString();const c="sns";p.HOST.setCurrent(l);const h="undefined"!=typeof wx&&"function"==typeof wx.getSystemInfoSync&&Boolean(wx.getSystemInfoSync().fontSizeSetting),_="undefined"!=typeof qq&&"function"==typeof qq.getSystemInfoSync&&Boolean(qq.getSystemInfoSync().fontSizeSetting),f="undefined"!=typeof tt&&"function"==typeof tt.getSystemInfoSync&&Boolean(tt.getSystemInfoSync().fontSizeSetting),m="undefined"!=typeof swan&&"function"==typeof swan.getSystemInfoSync&&Boolean(swan.getSystemInfoSync().fontSizeSetting),g="undefined"!=typeof my&&"function"==typeof my.getSystemInfoSync&&Boolean(my.getSystemInfoSync().fontSizeSetting),M="undefined"!=typeof jd&&"function"==typeof jd.getSystemInfoSync,y="undefined"!=typeof uni&&"undefined"==typeof window&&"function"==typeof uni.requireNativePlugin,w=h||_||f||m||g||y||M,F=("undefined"!=typeof uni||"undefined"!=typeof window)&&!w;_?qq:f?tt:m?swan:g?my:h?wx:y?uni:!M||jd;const I=F&&window&&window.navigator&&window.navigator.userAgent||"",T=/(micromessenger|webbrowser)/i.test(I),A=/AppleWebKit\/([\d.]+)/i.exec(I);A&&parseFloat(A.pop());const D=function(){let e="WEB";return T?e="WEB":_?e="QQ_MP":f?e="TT_MP":m?e="BAIDU_MP":g?e="ALI_MP":h?e="WX_MP":y&&(e="UNI_NATIVE_APP"),u[e]}();!function(){const e=I.match(/OS (\d+)_/i);e&&e[1]&&e[1]}(),function(){const e=I.match(/Android (\d+)(?:\.(\d+))?(?:\.(\d+))*/i);if(!e)return null;const t=e[1]&&parseFloat(e[1]),s=e[2]&&parseFloat(e[2]);t&&s&&parseFloat(e[1]+"."+e[2])}(),function(){const e=I.match(/Chrome\/(\d+)/);e&&e[1]&&parseFloat(e[1])}();const L=/MSIE/.test(I)||I.indexOf("Trident")>-1&&I.indexOf("rv:11.0")>-1;!function(){const e=/MSIE\s(\d+)\.\d/.exec(I);let t=e&&parseFloat(e[1]);!t&&/Trident\/7.0/i.test(I)&&/rv:11.0/.test(I)&&(t=11)}(),function(){const e=I.match(/TBS\/(\d+)/i);if(e&&e[1])e[1]}();const k="C2C",S="AllowType_Type_AllowAny",N="AdminForbid_Type_None",v="Add_Type_Single",b="Add_Type_Both",E="Delete_Type_Single",G="Delete_Type_Both",U="Pendency_Type_Both",C="Pendency_Type_ComeIn",P="Pendency_Type_SendOut",O="Response_Action_Agree",$="Response_Action_AgreeAndAdd",q="CheckResult_Type_Both",R="CheckResult_Type_Single";const x=function(){return(new Date).getTime()+0},H="Tag_Profile_IM_Nick",W="Tag_Profile_IM_Gender",B="Tag_Profile_IM_BirthDay",j="Tag_Profile_IM_Location",K="Tag_Profile_IM_SelfSignature",J="Tag_Profile_IM_AllowType",V="Tag_Profile_IM_Language",Y="Tag_Profile_IM_Image",z="Tag_Profile_IM_MsgSettings",Q="Tag_Profile_IM_AdminForbidType",X="Tag_Profile_IM_Level",Z="Tag_Profile_IM_Role",ee="Tag_SNS_IM_Group",te="Tag_SNS_IM_Remark",se="Tag_SNS_IM_AddSource",re="Tag_SNS_IM_AddWording",ie="Tag_SNS_IM_AddTime",ne={UNKNOWN:"Gender_Type_Unknown",FEMALE:"Gender_Type_Female",MALE:"Gender_Type_Male"},oe={NONE:"AdminForbid_Type_None",SEND_OUT:"AdminForbid_Type_SendOut"},ae={NEED_CONFIRM:"AllowType_Type_NeedConfirm",ALLOW_ANY:"AllowType_Type_AllowAny",DENY_ANY:"AllowType_Type_DenyAny"},de=function(e){return null!==e&&("number"==typeof e&&!isNaN(e-0)||"object"==typeof e&&e.constructor===Number)},ue=function(e){return"string"==typeof e},le=function(e){return"function"==typeof Array.isArray?Array.isArray(e):"array"===he(e)},pe=function(e){return void 0===e},ce=function(e){return e instanceof Error},he=function(e){return Object.prototype.toString.call(e).match(/^\[object (.*)\]$/)[1].toLowerCase()};Date.now||(Date.now=function(){return(new Date).getTime()});const _e=function(e){if(0===e.length)return 0;let t=0,s="",r=0,i=1;const n="undefined"!=typeof document&&void 0!==document.characterSet?document.characterSet:"UTF-8";for(;void 0!==e[t];)s=e[t++],i=s.charCodeAt[t]<=255?1:!1===n?3:2,r+=i;return r},fe=function(e,t){for(const s in e)if(e[s]===t)return!0;return!1};function me(){return!L&&!w}let ge=0;function Me(){return me()?"%c Chat %c":"Chat"}function ye(){const e=function(){const e=new Date;return e.setTime(x()),e}();return e.toLocaleTimeString("en-US",{hour12:!1})+"."+function(e){let t;switch(e.toString().length){case 1:t="00"+e;break;case 2:t="0"+e;break;default:t=e}return t}(e.getMilliseconds())}const we={arguments2String(e){let t="";if(1===e.length)t=e[0];else for(let i=0,n=e.length;i<n;i++)r=e[i],le(r)||function(e){return null!==e&&"object"==typeof e}(r)?ce(e[i])?t+=(s=e[i],JSON.stringify(s,["message","code"])):t+=JSON.stringify(e[i]):t+=e[i],t+=" ";var s,r;return t},_exec(e,t){me()?o[e](Me(),"background:#0abf5b; padding:1px; border-radius:3px; color: #fff","background:transparent",ye(),t):o[e](`${Me()} ${ye()} ${t}`)},d:function(){if(ge<=-1){const e=this.arguments2String(arguments);this._exec("debug",e)}},l:function(){if(ge<=0){const e=this.arguments2String(arguments);this._exec("log",e)}},log:function(){if(ge<=0){const e=this.arguments2String(arguments);this._exec("log",e)}},i:function(){if(ge<=1){const e=this.arguments2String(arguments);this._exec("info",e)}},w:function(){if(ge<=2){const e=this.arguments2String(arguments);this._exec("warn",e)}},e:function(){if(ge<=3){const e=this.arguments2String(arguments);this._exec("error",e)}},setLevel:function(e){e<4&&this._exec("log","set level from "+ge+" to "+e),ge=e},getLevel:function(){return ge}},Fe=Object.prototype.hasOwnProperty;function Ie(e){if(null==e)return!0;if("boolean"==typeof e)return!1;if("number"==typeof e)return 0===e;if("string"==typeof e)return 0===e.length;if("function"==typeof e)return 0===e.length;if(Array.isArray(e))return 0===e.length;if(e instanceof Error)return""===e.message;if(function(e){if("object"!=typeof e||null===e)return!1;const t=Object.getPrototypeOf(e);if(null===t)return!0;let s=t;for(;null!==Object.getPrototypeOf(s);)s=Object.getPrototypeOf(s);return t===s}(e)){for(const t in e)if(Fe.call(e,t))return!1;return!0}return!("map"!==he(e)&&!function(e){return"set"===he(e)}(e)&&!function(e){return"file"===he(e)}(e))&&0===e.size}class Te{constructor(e){Ie(e)||(this.userID=e.userID||"",this.nick=e.nick||"",this.avatar=e.avatar||"",this.time=e.time||0,this.source=e.source||"",this.wording=e.wording||"",this.type=e.type||"")}}const Ae=function(e){return{code:0,data:e||{}}};class De extends Error{constructor(e){super();const{code:t,message:s,data:r}=e;this.code=t,this.message=s||this._getErrorMessage(this.code),this.data=r||{}}}const Le=2700,ke=2701,Se=2710,Ne=2711,ve=2716,be=2805,Ee=2903,Ge=3122,Ue=3123,Ce="onMessageModified",Pe="onFriendListUpdated",Oe="onFriendGroupListUpdated",$e="onFriendApplicationListUpdated",qe="error";let Re=null;const xe=function(e){return Promise.resolve(Ae(e))},He=function(e,t=!1){if(e instanceof De)return t&&null!==Re&&Re.emit(qe,e),Promise.reject(e);if(e instanceof Error){const e=new De({code:Ee});return t&&null!==Re&&Re.emit(qe,e),Promise.reject(e)}if(pe(e)||pe(e.code))return Promise.reject(new De({code:Ee}));const s=new De(e);return t&&null!==Re&&Re.emit(qe,s),Promise.reject(s)},We="friend_get",Be="friend_get_specified",je="friend_check",Ke="friend_delete",Je="friend_add",Ve="friend_update",Ye="friend_response",ze="pendency_get",Qe="pendency_delete",Xe="pendency_report",Ze="group_get",et="group_add",st="group_delete",rt="group_update",it={info:4,warning:5,error:6},nt={wifi:1,"2g":2,"3g":3,"4g":4,"5g":5,unknown:6,none:7,online:8},ot={login:4};class at{constructor(e){this._n="SSOLogData",this.eventType=ot[e]||0,this.timestamp=0,this.networkType=8,this.code=0,this.message="",this.moreMessage="",this.extension=e,this.costTime=0,this.duplicate=!1,this.level=4,this.uiPlatform=void 0,this._sentFlag=!1,this._startts=x()}static bindEventStatModule(e){at.prototype._eventStatModule=e}updateTimeStamp(){this.timestamp=x()}start(e){return this._startts=e,this}end(e=!1){if(this._sentFlag)return;const t=x();0===this.costTime&&(this.costTime=t-this._startts),this.setMoreMessage(`startts:${this._startts} endts:${t}`),e?(this._sentFlag=!0,this._eventStatModule&&this._eventStatModule.pushIn(this)):setTimeout(()=>{this._sentFlag=!0,this._eventStatModule&&this._eventStatModule.pushIn(this)},0)}setError(e,t,s){if(!(e instanceof Error))return we.w(this._n+".setError value not instanceof Error, please check!"),this;if(this._sentFlag)return this;if(this.setNetworkType(s),t)e.code&&this.setCode(e.code),e.message&&this.setMoreMessage(e.message);else{const e=be;this.setCode(e)}return this.setLevel("error"),this}setCode(e){return pe(e)||this._sentFlag||("ECONNABORTED"===e&&(this.code=103),de(e)?this.code=e:we.w(this._n+".setCode value not a number, please check!",e,typeof e)),this}setMessage(e){return pe(e)||this._sentFlag||(de(e)&&(this.message=e.toString()),ue(e)&&(this.message=e)),this}setCostTime(e){return this.costTime=e,this}setLevel(e){return pe(e)||this._sentFlag||(this.level=it[e]),this}setMoreMessage(e){return Ie(this.moreMessage)?this.moreMessage=""+e:this.moreMessage+=" "+e,this}setNetworkType(e){if(pe(e))we.w(this._n+".setNetworkType value is undefined, please check!");else{const t=nt[e.toLowerCase()];pe(t)||(this.networkType=t)}return this}getStartTs(){return this._startts}setUIPlatform(e){this.uiPlatform=e}}class dt{constructor(e){this._snsModule=e,this._n="FriendApplicationHandler",this._startTime=0,this._maxLimited=100,this._currentSequence=0,this._friendApplicationMap=new Map,this._unreadCount=0}getLocalFriendApplicationList(){return{friendApplicationList:[...this._friendApplicationMap.values()],unreadCount:this._unreadCount}}_onFriendApplicationListUpdated(){this._snsModule.emitOuterEvent($e,{friendApplicationList:[...this._friendApplicationMap.values()],unreadCount:this._unreadCount})}onFriendApplicationRead(){this._unreadCount=0,this._onFriendApplicationListUpdated()}onFriendApplicationAdded(e,t){if(Ie(e))return;let s="";s=t===this._snsModule.getMyUserID()?P:C;let r=!1;e.forEach(e=>{const t=`${e.userID}_${s}`;s!==C||this._friendApplicationMap.has(t)||(this._unreadCount+=1),this._friendApplicationMap.set(t,new Te({...e,type:s})),r=!0}),r&&this._onFriendApplicationListUpdated()}onFriendApplicationDeleted(e){Ie(e)||this.getFriendApplicationList()}getFriendApplicationList(){const e=this._n+".getFriendApplicationList",t=new at("getFriendApplicationList");return this._snsModule.request({protocolName:ze,requestData:{applicationType:U,fromAccount:this._snsModule.getMyUserID(),maxLimited:this._maxLimited,startTime:this._startTime,lastSequence:this._currentSequence}}).then(s=>{const{resultList:r,unreadCount:i,startTime:n,currentSequence:o}=s.data;this._startTime=n,this._currentSequence=o,this._unreadCount=i;const a=le(r)?r.length:0;t.setNetworkType(this._snsModule.getNetworkType()).setMessage(`applicationCount:${a} unreadCount:${i}`).end(),we.i(`${e} ok. applicationCount:${a} unreadCount:${i}`),this._friendApplicationMap.clear(),le(r)&&r.forEach(e=>{const{userID:t,type:s}=e,r=new Te(e);this._friendApplicationMap.set(`${t}_${s}`,r)}),this._onFriendApplicationListUpdated()}).catch(s=>(this._snsModule.probeNetwork().then(([e,r])=>{t.setError(s,e,r).end()}),we.w(e+" failed. error:",s),He(s)))}deleteFriendApplication(e){const t=this._n+".deleteFriendApplication",{userID:s,type:r}=e;let i=r;if((!i||i!==P&&i!==C)&&(i=C),!this._friendApplicationMap.has(`${s}_${i}`))return He({code:ve});const n=new at("deleteFriendApplication");n.setMessage(`userID:${s} type:${i}`);return this._snsModule.request({protocolName:Qe,requestData:{fromAccount:this._snsModule.getMyUserID(),userIDList:[s],type:i}}).then(e=>{const{resultList:r}=e.data,{to:o,resultCode:a,resultInfo:d}=r[0];return n.setNetworkType(this._snsModule.getNetworkType()).setMoreMessage("resultList:"+JSON.stringify(r)).end(),we.i(`${t} ok. userID:${s} type:${i}`),0===a?Ae():He({userID:o,code:a,message:d})}).catch(e=>(this._snsModule.probeNetwork().then(([t,s])=>{n.setError(e,t,s).end()}),we.w(t+" failed. error:",e),He(e)))}acceptFriendApplication(e){const t=this._n+".acceptFriendApplication",{userID:s,remark:r,tag:i,type:n}=e;let o=n;(!o||o!==O&&o!==$)&&(o=$);const a=new at("acceptFriendApplication");a.setMessage(`userID:${s} type:${o}`);return this._snsModule.request({protocolName:Ye,requestData:{fromAccount:this._snsModule.getMyUserID(),responseFriendItem:[{userID:s,remark:r,tag:i,action:o}]}}).then(e=>{a.setNetworkType(this._snsModule.getNetworkType()).end();const{resultList:r}=e.data,{resultCode:i,resultInfo:n}=r[0];if(0!==i)return He({code:i,message:n});we.i(`${t} ok. userID:${s} type:${o}`)}).catch(e=>(this._snsModule.probeNetwork().then(([t,s])=>{a.setError(e,t,s).end()}),we.w(t+" failed. error:",e),He(e)))}refuseFriendApplication(e){const t=this._n+".refuseFriendApplication",{userID:s}=e,r=new at("refuseFriendApplication");r.setMessage("userID:"+s);return this._snsModule.request({protocolName:Ye,requestData:{fromAccount:this._snsModule.getMyUserID(),responseFriendItem:[{userID:s,action:"Response_Action_Reject"}]}}).then(e=>{r.setNetworkType(this._snsModule.getNetworkType()).end();const{resultList:i}=e.data,{resultCode:n,resultInfo:o}=i[0];if(0!==n)return He({code:n,message:o});we.i(`${t} ok. userID:${s}`)}).catch(e=>(this._snsModule.probeNetwork().then(([t,s])=>{r.setError(e,t,s).end()}),we.w(t+" failed. error:",e),He(e)))}_onFriendApplicationProcessed(e){if(0===e.length)return;let t=!1;e.forEach(e=>{const s=`${e.to}_${C}`;this._friendApplicationMap.has(s)&&(this._friendApplicationMap.delete(s),this._unreadCount-=1,t=!0)}),this._unreadCount<0&&(this._unreadCount=0),t&&this._onFriendApplicationListUpdated()}setFriendApplicationRead(){const e=this._n+".setFriendApplicationRead",t=new at("setFriendApplicationRead");var s,r;return this._snsModule.request({protocolName:Xe,requestData:{fromAccount:this._snsModule.getMyUserID(),latestTimeStamp:(s=x()/1e3,r=0,Math.round(Number(s)*Math.pow(10,r))/Math.pow(10,r))}}).then(s=>{t.setNetworkType(this._snsModule.getNetworkType()).end(),we.i(e+" ok"),this._unreadCount=0}).catch(s=>(this._snsModule.probeNetwork().then(([e,r])=>{t.setError(s,e,r).end()}),we.w(e+" failed. error:",s),He(s)))}reset(){this._startIndex=0,this._maxLimited=100,this._currentSequence=0,this._unreadCount=0,this._friendApplicationMap.clear()}}class ut{constructor(e){Ie(e)||(this.userID=e.userID||"",this.nick=e.nick||"",this.gender=e.gender||"",this.birthday=e.birthday||0,this.location=e.location||"",this.selfSignature=e.selfSignature||"",this.allowType=e.allowType||S,this.language=e.language||0,this.avatar=e.avatar||"",this.messageSettings=e.messageSettings||0,this.adminForbidType=e.adminForbidType||N,this.level=e.level||0,this.role=e.role||0,this.lastUpdatedTime=0,this.profileCustomField=[],Ie(e.profileCustomField)||e.profileCustomField.forEach(e=>{this.profileCustomField.push({key:e.key,value:e.value})}))}validate(e){let t=!0,s="";if(Ie(e))return{valid:!1,tips:"empty options"};if(e.profileCustomField){const t=e.profileCustomField.length;let s=null;for(let r=0;r<t;r++){if(s=e.profileCustomField[r],!ue(s.key)||-1===s.key.indexOf("Tag_Profile_Custom"))return{valid:!1,tips:"The prefix of keys of the custom profile key-value pairs (which is profileCustomField) must be Tag_Profile_Custom"};if(!ue(s.value))return{valid:!1,tips:"The type of values of the custom profile key-value pairs (which is profileCustomField) must be String"}}}for(const r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if("profileCustomField"===r)continue;if(Ie(e[r])&&!ue(e[r])&&!de(e[r])){s="key:"+r+", invalid value:"+e[r],t=!1;continue}switch(r){case"nick":ue(e[r])||(s="nick must be a string",t=!1),_e(e[r])>500&&(s=`nick name limited: must less than or equal to 500 bytes, current size: ${_e(e[r])} bytes`,t=!1);break;case"gender":fe(ne,e.gender)||(s="key:gender, invalid value:"+e.gender,t=!1);break;case"birthday":de(e.birthday)||(s="birthday must be a number",t=!1);break;case"location":ue(e.location)||(s="location must be a string",t=!1);break;case"selfSignature":ue(e.selfSignature)||(s="selfSignature must be a string",t=!1);break;case"allowType":fe(ae,e.allowType)||(s="key:allowType, invalid value:"+e.allowType,t=!1);break;case"language":de(e.language)||(s="language must be a number",t=!1);break;case"avatar":ue(e.avatar)||(s="avatar must be a string",t=!1);break;case"messageSettings":0!==e.messageSettings&&1!==e.messageSettings&&(s="messageSettings must be 0 or 1",t=!1);break;case"adminForbidType":fe(oe,e.adminForbidType)||(s="key:adminForbidType, invalid value:"+e.adminForbidType,t=!1);break;case"level":de(e.level)||(s="level must be a number",t=!1);break;case"role":de(e.role)||(s="role must be a number",t=!1);break;default:s="unknown key:"+r+"  "+e[r],t=!1}}return{valid:t,tips:s}}}class lt{constructor(e,t){this.userID=e,this.remark="",this.groupList=[],this.source="",this.addTime=0,this.friendCustomField=[],this.timestamp=0;const s={},r=[];if(s.userID=e,!Ie(t)){let e="",i="";for(let n=0,o=t.length;n<o;n++)if(e=t[n].tag,i=t[n].value,e.indexOf("Tag_SNS_Custom")>-1)this.friendCustomField.push({key:e,value:i});else if(e.indexOf("Tag_Profile_Custom")>-1)r.push({key:e,value:i});else switch(e){case H:s.nick=i;break;case W:s.gender=i;break;case B:s.birthday=i;break;case j:s.location=i;break;case K:s.selfSignature=i;break;case J:s.allowType=i;break;case V:s.language=i;break;case Y:s.avatar=i;break;case z:s.messageSettings=i;break;case Q:s.adminForbidType=i;break;case X:s.level=i;break;case Z:s.role=i;break;case te:this.remark=i;break;case ie:this.addTime=i;break;case ee:this.groupList=JSON.parse(JSON.stringify(i));break;case se:this.source=i;break;case re:break;default:we.l("snsProfileItem unknown tag->",t[n].tag)}}this.profile=new ut({...s,profileCustomField:r})}validate(e){let t=!0,s="";if(Ie(e))return{valid:!1,tips:"empty options"};if(e.profileCustomField){const t=e.profileCustomField.length;let s=null;for(let r=0;r<t;r++){if(s=e.profileCustomField[r],!ue(s.key)||-1===s.key.indexOf("Tag_Profile_Custom"))return{valid:!1,tips:"The prefix of keys of the custom profile key-value pairs (which is profileCustomField) must be Tag_Profile_Custom"};if(!ue(s.value))return{valid:!1,tips:"The type of values of the custom profile key-value pairs (which is profileCustomField) must be String"}}}for(const r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if("profileCustomField"===r)continue;if(Ie(e[r])&&!ue(e[r])&&!de(e[r])){s="key:"+r+", invalid value:"+e[r],t=!1;continue}switch(r){case"nick":ue(e[r])||(s="nick must be a string",t=!1),_e(e[r])>500&&(s=`nick name limited: must less than or equal to 500 bytes, current size: ${_e(e[r])} bytes`,t=!1);break;case"gender":fe(ne,e.gender)||(s="key:gender, invalid value:"+e.gender,t=!1);break;case"birthday":de(e.birthday)||(s="birthday must be a number",t=!1);break;case"location":ue(e.location)||(s="location must be a string",t=!1);break;case"selfSignature":ue(e.selfSignature)||(s="selfSignature must be a string",t=!1);break;case"allowType":fe(ae,e.allowType)||(s="key:allowType, invalid value:"+e.allowType,t=!1);break;case"language":de(e.language)||(s="language must be a number",t=!1);break;case"avatar":ue(e.avatar)||(s="avatar must be a string",t=!1);break;case"messageSettings":0!==e.messageSettings&&1!==e.messageSettings&&(s="messageSettings must be 0 or 1",t=!1);break;case"adminForbidType":fe(oe,e.adminForbidType)||(s="key:adminForbidType, invalid value:"+e.adminForbidType,t=!1);break;case"level":de(e.level)||(s="level must be a number",t=!1);break;case"role":de(e.role)||(s="role must be a number",t=!1);break;default:s="unknown key:"+r+"  "+e[r],t=!1}}return{valid:t,tips:s}}update(e){let t="",s="";const r=[];this.friendCustomField.forEach(e=>{r.push(e.key)});for(let i=0,n=e.length;i<n;i++)if(t=e[i].tag,s=e[i].value,t.indexOf("Tag_SNS_Custom")>-1)r.indexOf(t)>-1?this.friendCustomField.forEach(e=>{e.key===t&&(e.value=s)}):this.friendCustomField.push({key:t,value:s});else if(t.indexOf("Tag_Profile_Custom")>-1){let e=!1;this.profile.profileCustomField.forEach(r=>{r.key===t&&(r.value=s,e=!0)}),e||this.profile.profileCustomField.push({key:t,value:s})}else switch(t){case H:this.profile.nick=s;break;case W:this.profile.gender=s;break;case B:this.profile.birthday=s;break;case j:this.profile.location=s;break;case K:this.profile.selfSignature=s;break;case J:this.profile.allowType=s;break;case V:this.profile.language=s;break;case Y:this.profile.avatar=s;break;case z:this.profile.messageSettings=s;break;case Q:this.profile.adminForbidType=s;break;case X:this.profile.level=s;break;case Z:this.profile.role=s;break;case te:this.remark=s;break;case ie:this.addTime=s;break;case ee:this.groupList=JSON.parse(JSON.stringify(s));break;case se:this.source=s;break;case re:break;default:we.d("snsProfileItem unkown tag->",e[i].tag)}this.timestamp=Date.now(),r.length=0}updateProfile(e){this.profile=JSON.parse(JSON.stringify(e)),this.timestamp=Date.now()}addToGroupList(e){-1===this.groupList.indexOf(e)&&(this.groupList.push(e),this.count=this.groupList.length)}removeFromGroupList(e){const t=this.groupList.indexOf(e);t>-1&&(this.groupList.splice(t,1),this.count=this.groupList.length)}}const pt=4,ct=11,ht=12,_t=15,ft=20,mt=23,gt=27,Mt=29;class yt{constructor(e){this._snsModule=e,this._n="FriendHandler",this._friendMap=new Map,this._startIndex=0,this._standardSequence=0,this._customSequence=0,this._expirationTime=18e4}getLocalFriendList(){return[...this._friendMap.values()]}getFriendRemark(e){return this._friendMap.has(e)?this._friendMap.get(e).remark:""}onFriendProfileModified(e){const{dataList:t}=e;if(Ie(t))return;const s=this._snsModule.getModule(ct);t.forEach(e=>{const{userID:t,profileList:r}=e;if(this.isMyFriend(t)){we.l(`${this._n}.onFriendProfileModified. friend account:${t}, profileList:${JSON.stringify(r)}`);const e=this._friendMap.get(t);e.update(r),s.modifyMessageSentByPeer({conversationID:`${k}${t}`,latestNick:e.profile.nick,latestAvatar:e.profile.avatar})}}),this._onFriendListUpdated()}onFriendAdded(e){0!==e.length&&(we.l(`${this._n}.onFriendAdded userIDList:${e}`),e.forEach(e=>{this._friendMap.set(e,new lt(e))}),this.getFriendProfile({userIDList:e}).then(t=>{e.forEach(e=>{const t=this._friendMap.get(e);t.groupList.length>0&&this._snsModule.updateWhenFriendAdded({nameList:t.groupList,userID:e})}),this._onFriendListUpdated()}))}onFriendDeleted(e){0!==e.length&&(we.l(`${this._n}.onFriendDeleted userIDList:${e}`),e.forEach(e=>{const t=this._friendMap.get(e);t.groupList.length>0&&this._snsModule.updateWhenFriendDeleted({nameList:t.groupList,userID:e}),this._friendMap.delete(e)}),this._onFriendListUpdated())}_onFriendListUpdated(){this._snsModule.emitOuterEvent(Pe);this._snsModule.getModule(ct).checkAndPatchRemark()}getFriendProfile(e){const t=this._n+".getFriendProfile",{userIDList:s}=e,r=[],i=[],n=[];if(s.forEach(e=>{if(this._friendMap.has(e)){const t=this._friendMap.get(e);Date.now()-t.timestamp<this._expirationTime?i.push(t):n.push(e)}else r.push({userID:e,code:Le,message:this._snsModule.getErrorMessage(Le)})}),0===n.length)return we.i(t+" newUserIDList is empty"),xe({friendList:i,failureUserIDList:r});const o=new at("getFriendProfile");o.setMessage("userIDList:"+n),we.i(`${t} userIDList:${n}`);return this._snsModule.request({protocolName:Be,requestData:{fromAccount:this._snsModule.getMyUserID(),userIDList:n}}).then(e=>{o.setNetworkType(this._snsModule.getNetworkType()).end(),we.i(t+" ok");const{resultList:s}=e.data;return s.forEach(e=>{const{to:t,resultCode:s,resultInfo:n,tagValueList:o}=e;if(pe(s)||0===s){let e;this._friendMap.has(t)?(e=this._friendMap.get(t),e.update(o)):(e=new lt(t,o),this._friendMap.set(t,e)),i.push(e)}else r.push({userID:t,code:s,message:n})}),Ae({friendList:i,failureUserIDList:r})}).catch(e=>(this._snsModule.probeNetwork().then(([t,s])=>{o.setError(e,t,s).end()}),we.w(t+" failed. error:",e),He(e)))}isMyFriend(e){return this._friendMap.has(e)}pagingGetFriendList(){const e=this._n+".getFriendList",t=new at("getFriendList"),s=Date.now();this._snsModule.request({protocolName:We,requestData:{fromAccount:this._snsModule.getMyUserID(),startIndex:this._startIndex,standardSequence:this._standardSequence,customSequence:this._customSequence}}).then(r=>{const{friendCount:i,resultList:n,nextStartIndex:o,standardSequence:a,customSequence:d,completeFlag:u}=r.data;this._startIndex=o,this._standardSequence=a,this._customSequence=d;const l=`friendCount:${i} nextStartIndex:${o} standardSequence:${a} customSequence:${d} completeFlag:${u} cost ${Date.now()-s} ms`;t.setNetworkType(this._snsModule.getNetworkType()).setMessage(l).end(),we.i(e+" ok.",l),Ie(n)||n.forEach(e=>{const{to:t,tagValueList:s}=e;this._friendMap.set(t,new lt(t,s))}),0===u?this.pagingGetFriendList():(this._snsModule.emitOuterEvent(Pe),this._pagingGetFriendProfile())}).catch(s=>(this._snsModule.probeNetwork().then(([e,r])=>{t.setError(s,e,r).end()}),we.w(e+" failed. error:",s),He(s)))}_pagingGetFriendProfile(){const e=[...this._friendMap.keys()],t=this._snsModule.getModule(pt),s=e.length,r=s<=100?1:Math.ceil(s/100);we.l(`${this._n}._pagingGetFriendProfile friendCount:${s} pageCount:${r}`);for(let i=0;i<r;i++)t.getUserProfile({userIDList:e.slice(100*i,100*(i+1))}).then(e=>{e.data.forEach(e=>{const t=this._friendMap.get(e.userID);t&&t.updateProfile(e)}),this._onFriendListUpdated()})}addFriend(e){const t=this._n+".addFriend";if(this._friendMap.has(e.to))return He({code:ke});if(e.wording&&!1===this._snsModule.filterProfanity("wording",e))return He({code:Ue});const{to:s,source:r,type:i,wording:n,remark:o,groupName:a}=e;let d=i;(!d||d!==v&&d!==b)&&(d=b);const u=new at("addFriend");u.setMessage(`to:${s} source:${r} type:${d}`);return this._snsModule.request({protocolName:Je,requestData:{fromAccount:this._snsModule.getMyUserID(),addFriendItem:[{to:s,source:r,wording:n,remark:o,groupName:a}],type:d}}).then(e=>{const{resultList:s}=e.data;u.setNetworkType(this._snsModule.getNetworkType()).setMoreMessage("resultList:"+JSON.stringify(s)).end();const{to:r,resultCode:i,resultInfo:n}=s[0];return we.i(`${t} ok. to:${r} type:${d} code:${i}`),pe(i)||0===i?Ae({userID:r,code:0}):30539===i?Ae({userID:r,code:i,message:this._snsModule.getErrorMessage(i)}):He({userID:r,code:i,message:this._snsModule.getErrorMessage(i)||n})}).catch(e=>(this._snsModule.probeNetwork().then(([t,s])=>{u.setError(e,t,s).end()}),we.w(t+" failed. error:",e),He(e)))}deleteFriend(e){const t=this._n+".deleteFriend",{userIDList:s,type:r}=e;var i;s.length>1e3&&(we.w(`${t} ${i=1e3,"the length of userIDList cannot exceed "+i}`),s.length=1e3);const n=[],o=[],a=[];if(s.forEach(e=>{this._friendMap.has(e)?a.push(e):n.push({userID:e,code:Le,message:this._snsModule.getErrorMessage(Le)})}),0===a.length)return xe({successUserIDList:o,failureUserIDList:n});let d=r;(!d||d!==E&&d!==G)&&(d=G);const u=new at("deleteFriend");u.setMessage(`userIDList:${a} type:${d}`);return this._snsModule.request({protocolName:Ke,requestData:{fromAccount:this._snsModule.getMyUserID(),userIDList:a,type:d}}).then(e=>{u.setNetworkType(this._snsModule.getNetworkType()).end(),we.i(t+" ok");const{resultList:s}=e.data;return Ie(s)||s.forEach(e=>{const{to:t,resultCode:s,resultInfo:r}=e;pe(s)||0===s?o.push({userID:t}):n.push({userID:t,code:s,message:r})}),Ae({successUserIDList:o,failureUserIDList:n})}).catch(e=>(this._snsModule.probeNetwork().then(([t,s])=>{u.setError(e,t,s).end()}),we.w(t+" error:",e),He(e)))}updateFriend(e){const{userID:t,remark:s,friendCustomField:r}=e;if(!this._friendMap.has(t))return He({code:Le});const i=this._n+".updateFriend",n=new at("updateFriend");n.setMessage(`userID:${t} remark:${s} friendCustomField:${r}`);const o=[];pe(s)||o.push({tag:te,value:s}),le(r)&&r.length>0&&r.forEach(e=>{o.push({tag:e.key,value:e.value})});return this._snsModule.request({protocolName:Ve,requestData:{fromAccount:this._snsModule.getMyUserID(),updateItem:[{to:t,snsItem:o}]}}).then(e=>{n.setNetworkType(this._snsModule.getNetworkType()).end(),we.i(i+" ok");const{resultList:t}=e.data,{to:o,resultCode:a,resultInfo:d}=t[0];if(pe(a)||0===a){const e=this._friendMap.get(o);return e&&(pe(s)||(e.remark=s),le(r)&&r.length>0&&function(e,t){if(!le(e)||!le(t))return!1;let s=!1;t.forEach(({key:t,value:r})=>{const i=e.find(e=>e.key===t);i?i.value!==r&&(i.value=r,s=!0):(e.push({key:t,value:r}),s=!0)})}(e.friendCustomField,r),this._onFriendListUpdated()),Ae(e)}return He({code:a,message:d})}).catch(e=>(this._snsModule.probeNetwork().then(([t,s])=>{n.setError(e,t,s).end()}),we.w(i+" failed. error:",e),He(e)))}checkFriend(e){const t=this._n+".checkFriend",{userIDList:s,type:r}=e;let i=r;(!i||i!==R&&i!==q)&&(i=q);const n=new at("checkFriend");n.setMessage(`userIDList:${s} type:${i}`);return this._snsModule.request({protocolName:je,requestData:{fromAccount:this._snsModule.getMyUserID(),userIDList:s,type:i}}).then(e=>{n.setNetworkType(this._snsModule.getNetworkType()).end(),we.i(`${t} ok. userIDList:${s} type:${i}`);const r=[],o=[],{resultList:a}=e.data;return le(a)&&a.forEach(e=>{const{to:t,relation:s,resultCode:i,resultInfo:n}=e;pe(i)||0===i?r.push({userID:t,code:0,relation:s}):o.push({userID:t,code:i,message:n})}),Ae({successUserIDList:r,failureUserIDList:o})}).catch(e=>(this._snsModule.probeNetwork().then(([t,s])=>{n.setError(e,t,s).end()}),we.w(t+" failed. error:",e),He(e)))}updateWhenAddedToFriendGroup(e){const{name:t,userIDList:s}=e;we.l(`${this._n}.updateWhenAddedToFriendGroup groupName:${t} userIDList:${s}`),t&&!Ie(s)&&s.forEach(e=>{if(this._friendMap.has(e)){this._friendMap.get(e).addToGroupList(t)}})}updateWhenRemovedFromFriendGroup(e){const{name:t,userIDList:s}=e;we.l(`${this._n}.updateWhenRemovedFromFriendGroup groupName:${t} userIDList:${s}`),t&&!Ie(s)&&s.forEach(e=>{if(this._friendMap.has(e)){this._friendMap.get(e).removeFromGroupList(t)}})}reset(){this._friendMap.clear(),this._startIndex=0,this._standardSequence=0,this._customSequence=0}}class wt{constructor(e){Ie(e)||(this.name=e.name||"",this.userIDList=e.userIDList||[],this.count=this.userIDList.length||0)}addToUserIDList(e){-1===this.userIDList.indexOf(e)&&(this.userIDList.push(e),this.count=this.userIDList.length)}removeFromUserIDList(e){const t=this.userIDList.indexOf(e);t>-1&&(this.userIDList.splice(t,1),this.count=this.userIDList.length)}}class Ft{constructor(e){this._snsModule=e,this._n="FriendGroupHandler",this._friendGroupMap=new Map}getLocalFriendGroupList(){return[...this._friendGroupMap.values()]}_emitFriendGroupListUpdated(){const e=[...this._friendGroupMap.values()];this._snsModule.emitOuterEvent(Oe,e)}getFriendGroupList(){const e=this._n+".getFriendGroupList",t=new at("getFriendGroupList");return this._snsModule.request({protocolName:Ze,requestData:{fromAccount:this._snsModule.getMyUserID()}}).then(s=>{t.setNetworkType(this._snsModule.getNetworkType()).end();const{resultList:r}=s.data;Ie(r)?we.i(e+" ok. friend group count:0"):(we.i(`${e} ok. friend group count:${r.length}`),this._friendGroupMap.clear(),r.forEach(e=>{const t=new wt(e);this._friendGroupMap.set(e.name,t)}),this._emitFriendGroupListUpdated())}).catch(s=>(this._snsModule.probeNetwork().then(([e,r])=>{t.setError(s,e,r).end()}),we.w(e+" error:",s),He(s)))}createFriendGroup(e){const t=this._n+".createFriendGroup",{name:s,userIDList:r}=e;if(this._friendGroupMap.has(s))return He({code:Se});const i=new at("createFriendGroup");return i.setMessage(`name:${s} userIDList:${r}`),this._snsModule.request({protocolName:et,requestData:{fromAccount:this._snsModule.getMyUserID(),groupName:[s],userIDList:le(r)?r:void 0}}).then(e=>{i.setNetworkType(this._snsModule.getNetworkType()).end(),we.l(`${t} ok. name:${s} userIDList:${r}`);const{resultList:n}=e.data,o=[],a=[];n&&n.forEach(e=>{const{to:t,resultCode:s,resultInfo:r}=e;if(pe(s)||0===s)o.push(t);else{const t={userID:e.to,code:s,message:r};a.push(t)}});const d=new wt({name:s,userIDList:o});return this._friendGroupMap.set(s,d),this._snsModule.updateWhenAddedToFriendGroup({name:s,userIDList:o}),this._emitFriendGroupListUpdated(),Ae({friendGroup:d,failureUserIDList:a})}).catch(e=>(this._snsModule.probeNetwork().then(([t,s])=>{i.setError(e,t,s).end()}),we.w(t+" failed. error:",e),He(e)))}deleteFriendGroup(e){const t=this._n+".deleteFriendGroup",{name:s}=e;if(!this._friendGroupMap.has(s))return He({code:Ne});const r=new at("deleteFriendGroup");r.setMessage("name:"+s);return this._snsModule.request({protocolName:st,requestData:{fromAccount:this._snsModule.getMyUserID(),nameList:[s]}}).then(e=>{r.setNetworkType(this._snsModule.getNetworkType()).end(),we.l(`${t} ok. name:${s}`);const i=this._friendGroupMap.get(s);return i&&(this._snsModule.updateWhenRemovedFromFriendGroup({name:s,userIDList:i.userIDList}),this._friendGroupMap.delete(s),i.userIDList.length=0),this._emitFriendGroupListUpdated(),Ae(i)}).catch(e=>(this._snsModule.probeNetwork().then(([t,s])=>{r.setError(e,t,s).end()}),we.w(t+" failed. error:",e),He(e)))}renameFriendGroup(e){const t=this._n+".renameFriendGroup",{oldName:s,newName:r}=e;if(!this._friendGroupMap.has(s))return He({code:Ne});const i=new at("renameFriendGroup");i.setMessage(`oldName:${s} newName:${r}`);return this._snsModule.request({protocolName:rt,requestData:{fromAccount:this._snsModule.getMyUserID(),oldName:s,newName:r}}).then(()=>{if(i.setNetworkType(this._snsModule.getNetworkType()).end(),we.l(`${t} ok. oldName:${s} newName:${r}`),this._friendGroupMap.has(s)){const e=this._friendGroupMap.get(s);return e.name=r,this._friendGroupMap.delete(s),this._friendGroupMap.set(r,e),this._snsModule.updateWhenRemovedFromFriendGroup({name:s,userIDList:e.userIDList}),this._snsModule.updateWhenAddedToFriendGroup({name:r,userIDList:e.userIDList}),this._emitFriendGroupListUpdated(),Ae(e)}return Ae()}).catch(e=>(this._snsModule.probeNetwork().then(([t,s])=>{i.setError(e,t,s).end()}),we.w(t+" failed. error:",e),He(e)))}addToFriendGroup(e){const t=this._n+".addToFriendGroup",{name:s,userIDList:r}=e;if(!this._friendGroupMap.has(s))return this._onFriendGroupNotExist(s);const i=new at("addToFriendGroup");i.setMessage(`name:${s} userIDList:${r}`),we.l(`${t} name:${s} userIDList:${r}`);return this._snsModule.request({protocolName:rt,requestData:{fromAccount:this._snsModule.getMyUserID(),oldName:s,updateGroupItem:r.filter(e=>this._snsModule.isMyFriend(e)).map(e=>({to:e,updateType:"Update_Type_Add"}))}}).then(e=>(i.setNetworkType(this._snsModule.getNetworkType()).end(),this._onFriendGroupUpdated(s,e))).catch(e=>(this._snsModule.probeNetwork().then(([t,s])=>{i.setError(e,t,s).end()}),we.w(t+" failed. error:",e),He(e)))}removeFromFriendGroup(e){const t=this._n+".removeFromFriendGroup",{name:s,userIDList:r}=e;if(!this._friendGroupMap.has(s))return this._onFriendGroupNotExist(s);const i=new at("removeFromFriendGroup");i.setMessage(`name:${s} userIDList:${r}`),we.l(`${t} name:${s} userIDList:${r}`);return this._snsModule.request({protocolName:rt,requestData:{fromAccount:this._snsModule.getMyUserID(),oldName:s,updateGroupItem:r.filter(e=>this._snsModule.isMyFriend(e)).map(e=>({to:e,updateType:"Update_Type_Delete"}))}}).then(e=>(i.setNetworkType(this._snsModule.getNetworkType()).end(),this._onFriendGroupUpdated(s,e))).catch(e=>(this._snsModule.probeNetwork().then(([t,s])=>{i.setError(e,t,s).end()}),we.w(t+" failed. error:",e),He(e)))}_onFriendGroupUpdated(e,t){const{resultList:s}=t.data,r=this._friendGroupMap.get(e),i=[],n=[],o=[];return le(s)&&s.forEach(e=>{const{to:t,resultCode:s,resultInfo:a,type:d}=e;0===s?"Update_Type_Add"===d?r&&(r.addToUserIDList(t),n.push(t)):"Update_Type_Delete"===d&&r&&(r.removeFromUserIDList(t),o.push(t)):i.push({to:e.to,code:s,message:a})}),we.l(`${this._n}._onFriendGroupUpdated name:${e} userIDList:${r.userIDList}`),n.length>0&&this._snsModule.updateWhenAddedToFriendGroup({name:e,userIDList:n}),o.length>0&&this._snsModule.updateWhenRemovedFromFriendGroup({name:e,userIDList:o}),Ae({friendGroup:r,failureUserIDList:i})}updateWhenFriendAdded(e){const{nameList:t,userID:s}=e;we.l(`${this._n}.updateWhenFriendAdded userID:${s} nameList:${t}`),Ie(t)||t.forEach(e=>{if(this._friendGroupMap.has(e)){this._friendGroupMap.get(e).addToUserIDList(s)}})}updateWhenFriendDeleted(e){const{nameList:t,userID:s}=e;we.l(`${this._n}.updateWhenFriendDeleted userID:${s} nameList:${t}`),Ie(t)||t.forEach(e=>{if(this._friendGroupMap.has(e)){this._friendGroupMap.get(e).removeFromUserIDList(s)}})}reset(){this._friendGroupMap.clear()}}class It extends class{constructor(e){this._m=e,this._n=""}isLoggedIn(){return this._m.getModule(ht).isLoggedIn()}isOversea(){return this._m.getModule(ht).isOversea()}isPrivateNetWork(){return this._m.getModule(ht).isPrivateNetWork()}getFileDownloadProxy(){return this._m.getModule(ht).getFileDownloadProxy()}getMyUserID(){return this._m.getModule(ht).getUserID()}getMyTinyID(){return this._m.getModule(ht).getTinyID()}getSDKAppID(){return this._m.getModule(ht).getSDKAppID()}isIntl(){return this._m.getModule(ht).isIntl()}isDevMode(){return this._m.getModule(ht).isDevMode()}getModule(e){return this._m.getModule(e)}getPlatform(){return D}getNetworkType(){return this._m.getModule(_t).getNetworkType()}probeNetwork(e){return this._m.getModule(_t).probe(e)}getCloudConfig(e){return this._m.getModule(mt).getCloudConfig(e)}emitOuterEvent(e,t){this._m.getOuterEmitterInstance().emit(e,t)}emitInnerEvent(e,t){this._m.getInnerEmitterInstance().emit(e,t)}getInnerEmitterInstance(){return this._m.getInnerEmitterInstance()}generateTjgID(e){return this._m.getModule(ht).getTinyID()+"-"+e.random}filterModifiedMessage(e){if(Ie(e))return;const t=e.filter(e=>!0===e.isModified);t.length>0&&this.emitOuterEvent(Ce,t)}filterUnmodifiedMessage(e){if(Ie(e))return[];return e.filter(e=>!1===e.isModified)}request(e){return this._m.getModule(ft).request(e)}canIUse(e){return this._m.getModule(gt).canIUse(e)}getErrorMessage(e,t,s){return this._m.getErrorMessage(e,t,s)}outputWarning(e,t,s){const r=this.getErrorMessage(e,t,s);r&&we.w(r)}cannotUseCommercialAbility(e){const t=Ge;return He({code:t,message:this.getErrorMessage(t,e)})}}{constructor(t){super(t),this._n="SnsModule",this._friendHandler=new yt(this),this._friendApplicationHandler=new dt(this),this._friendGroupHandler=new Ft(this);this.getInnerEmitterInstance().on(e.A2KEY_AND_TINYID_UPDATED,this.onContextUpdated,this)}onContextUpdated(e){this._friendHandler.pagingGetFriendList(),this._friendGroupHandler.getFriendGroupList(),this._friendApplicationHandler.getFriendApplicationList()}onRelationChainModified(e){const{dataList:t}=e;if(Ie(t))return;const s=[],r=[],i=[],n=[];let o=!1,a="";t.forEach(e=>{if(3!==e.pushType&&4!==e.pushType||!e.from||(a=e.from),e.friendAddAccount&&(s.push(...e.friendAddAccount),n.push(...e.friendAddAccount)),e.friendDelAccount&&r.push(...e.friendDelAccount),e.friendApplicationAdded&&i.push(...e.friendApplicationAdded),e.friendApplicationDeletedUserIDList&&n.push(...e.friendApplicationDeletedUserIDList),e.reportTime&&7===e.pushType&&(o=!0),e.friendUpInfo){const t={dataList:[]};e.friendUpInfo.forEach(e=>{t.dataList.push({userID:e.friendAccount,profileList:[...e.sns]})}),this.onFriendProfileModified(t)}}),o&&this._friendApplicationHandler.onFriendApplicationRead(),this._friendApplicationHandler.onFriendApplicationAdded(i,a),this._friendApplicationHandler.onFriendApplicationDeleted(n),this._friendHandler.onFriendAdded(s),this._friendHandler.onFriendDeleted(r)}isMyFriend(e){return this._friendHandler.isMyFriend(e)}filterProfanity(e,t){const s=this.getModule(Mt);if(!s)return!0;const{isAllowedToSend:r,modifiedText:i}=s.filterText(t[e],c);return!0===r&&(t[e]=i,!0)}onFriendProfileModified(e){this._friendHandler.onFriendProfileModified(e)}getLocalFriendList(e=!0){const t=this._friendHandler.getLocalFriendList();return e?xe(t):t}getFriendRemark(e){return this._friendHandler.getFriendRemark(e)}getFriendList(){return this._friendHandler.pagingGetFriendList()}addFriend(e){return this._friendHandler.addFriend(e)}deleteFriend(e){return this._friendHandler.deleteFriend(e)}checkFriend(e){return this._friendHandler.checkFriend(e)}getFriendProfile(e){return this._friendHandler.getFriendProfile(e)}updateFriend(e){return this._friendHandler.updateFriend(e)}updateWhenAddedToFriendGroup(e){this._friendHandler.updateWhenAddedToFriendGroup(e)}updateWhenRemovedFromFriendGroup(e){this._friendHandler.updateWhenRemovedFromFriendGroup(e)}getLocalFriendApplicationList(){const e=this._friendApplicationHandler.getLocalFriendApplicationList();return xe(e)}deleteFriendApplication(e){return this._friendApplicationHandler.deleteFriendApplication(e)}refuseFriendApplication(e){return this._friendApplicationHandler.refuseFriendApplication(e)}acceptFriendApplication(e){return this._friendApplicationHandler.acceptFriendApplication(e)}setFriendApplicationRead(e){return this._friendApplicationHandler.setFriendApplicationRead(e)}getLocalFriendGroupList(){const e=this._friendGroupHandler.getLocalFriendGroupList();return xe(e)}createFriendGroup(e){return this._friendGroupHandler.createFriendGroup(e)}deleteFriendGroup(e){return this._friendGroupHandler.deleteFriendGroup(e)}addToFriendGroup(e){return this._friendGroupHandler.addToFriendGroup(e)}removeFromFriendGroup(e){return this._friendGroupHandler.removeFromFriendGroup(e)}renameFriendGroup(e){return this._friendGroupHandler.renameFriendGroup(e)}onAddToFriendGroup(e){return this._friendGroupHandler.onAddToFriendGroup(e)}updateWhenFriendAdded(e){this._friendGroupHandler.updateWhenFriendAdded(e)}updateWhenFriendDeleted(e){this._friendGroupHandler.updateWhenFriendDeleted(e)}reset(){we.l(this._n+".reset"),this._friendHandler.reset(),this._friendGroupHandler.reset(),this._friendApplicationHandler.reset()}}export{It as default};
