import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';

class MomentsLocationPage extends StatefulWidget {
  const MomentsLocationPage({super.key});

  @override
  _MomentsLocationPageState createState() => _MomentsLocationPageState();
}

class _MomentsLocationPageState extends State<MomentsLocationPage> {
  // 模拟的地点数据（可以替换成从 API 获取的数据）
  final List<Map<String, String>> locations = [
    {'name': '五五商务中心', 'distance': '100米'},
    {'name': '厦门高崎国际机场', 'distance': '2.1公里'},
    {'name': '厦门市中心幼儿园', 'distance': '100米'},
    {'name': '联动优客厦门运营中心', 'distance': '100米'},
    {'name': '巨岸集团', 'distance': '100米'},
    {'name': '福建筑工人第二中心（五缘路校区）', 'distance': '508米'},
    {'name': '新屿国际酒店', 'distance': '98米'},
    {'name': '2030·U-WORK', 'distance': '313米'},
    {'name': '中央美术学院厦门学院', 'distance': '477米'},
    {'name': '红星美凯龙（厦门全球家居1店）', 'distance': '997米'},
  ];

  // 用于存储搜索框输入的关键词
  final TextEditingController _searchController = TextEditingController();
// 用于存储过滤后的地点列表
  List<Map<String, String>> filteredLocations = [];

  @override
  void initState() {
    super.initState();
    // 初始时显示所有地点
    filteredLocations = locations;

    // 监听搜索框的变化
    _searchController.addListener(_filterLocations);

  }

  // 根据搜索框内容过滤地点
  void _filterLocations() {
    setState(() {
      filteredLocations = locations.where((location) {
        return location['name']!.toLowerCase().contains(_searchController.text.toLowerCase());
      }).toList();
    });
  }

  Widget _buildInput(){

    return Container(
      height: 32,
      child: TextField(
        controller: _searchController,

        textAlign: TextAlign.center, // 文本居中
        decoration: InputDecoration(

          fillColor: Color(0xffE9E9E9),  // 设置背景颜色
          filled: true,  // 启用背景颜色
          hintText: '搜索附近位置',  // 提示文字
          contentPadding: EdgeInsets.symmetric(vertical: 0, horizontal:   0),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(30), // 圆角
            borderSide: BorderSide.none,  // 去除边框
          ),
          hintStyle: const TextStyle(color: Colors.grey),  // 提示文字样式
          prefixIcon: const Icon(Icons.search),
        ),

      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        automaticallyImplyLeading: false,
        // 禁用自动生成的返回按钮
        leading: null,
        // 移除leading
        leadingWidth: 0,
        // 设置leading宽度为0
        actions: [
          Padding(
            padding: const EdgeInsets.only(left: 16),
            child: GestureDetector(
              onTap: () {
                Navigator.pop(context);
              },
              child: Container(
                padding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                margin: const EdgeInsets.only(right: 16),
                height: 32,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: const Color(0xFFE9E9E9),
                ),
                child: Center(
                  child: Text(TIM_t('取消'),
                      style: const TextStyle(color: Color(0XFF666666))),
                ),
              ),
            ),
          ),
          const Spacer(), // 添加一个Spacer将发布按钮推到右边

          Text(TIM_t("所在位置")),
          const Spacer(), // 添加一个Spacer将发布按钮推到右边
          Padding(
            padding: const EdgeInsets.only(right: 16),
            child: GestureDetector(
              onTap: () {

              },
              child: Container(
                padding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                height: 32,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: const Color(0xFF0072FC),
                ),
                child: Center(
                  child: Text(TIM_t('确定'),
                      style: const TextStyle(color: Colors.white)),
                ),
              ),
            ),
          ),

        ],
      ),
      body: SingleChildScrollView(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            children: [
              SizedBox(height: 9,),
              _buildInput()
            ],
          ),
        ),
      ),
    );
  }
}