import 'package:flutter/material.dart';
import '../../../apis/account_api.dart';

class BankVM with ChangeNotifier {
  List<Map<String, String>> bankList = [
      {'name': '兴业银行1', 'cardNumber': '**** **** **** 1234', 'type': '储蓄卡'},
      {'name': '兴业银行2', 'cardNumber': '**** **** **** 1234', 'type': '储蓄卡'},
      {'name': '兴业银行3', 'cardNumber': '**** **** **** 1234', 'type': '储蓄卡'},
      {'name': '兴业银行4', 'cardNumber': '**** **** **** 1234', 'type': '储蓄卡'},
    ];
  // 总余额
  double totalBalance = 100.00;
  // 账户状态
  int accountStatus = 1;
  // 用户id
  String userId = '';
  // 选中的银行卡
  Map<String, String>? selectedBank;



  // 查询账户信息
  Future<void> queryAccount() async {
    AccountApi.instance.queryAccount().then((value) {
     
      if(value.code == 0 && value.ok == true) {
         debugPrint('账户信息0: ${value.toJson()}');
        if(value.data == null) {
          // 如果是null，说明没有开通
          // 自动开通账户
          AccountApi.instance.openAccount();
        }else {
           debugPrint('账户信息2: ${value.toJson()}');
          // 记录账户信息
          debugPrint('账户余额: ${value.data!.balance!.toDouble()}');
          totalBalance = value.data!.balance!.toDouble();
          accountStatus = value.data!.status!;
          userId = value.data!.userId!.toString();
          notifyListeners();
        }
      }
    });
  }
  // 获取银行卡列表
  Future<void> getBankList() async {
    bankList = [
      {'name': '兴业银行1', 'cardNumber': '**** **** **** 1234', 'type': '储蓄卡'},
      {'name': '兴业银行2', 'cardNumber': '**** **** **** 1234', 'type': '储蓄卡'},
      {'name': '兴业银行3', 'cardNumber': '**** **** **** 1234', 'type': '储蓄卡'},
      {'name': '兴业银行4', 'cardNumber': '**** **** **** 1234', 'type': '储蓄卡'},
    ];
    notifyListeners();
  }

  // 添加银行卡
  Future<void> addBank() async {
    bankList.add({'name': '兴业银行', 'cardNumber': '**** **** **** 1234', 'type': '储蓄卡'});
    notifyListeners();
  }

  // 模拟提现
  Future<void> withdraw(double amount) async {
    totalBalance -= amount;
    notifyListeners();
  }

  // 模拟充值
  Future<void> recharge(double amount) async {
    totalBalance += amount;
    notifyListeners();
  }

  // 修改默认选中的银行卡
  Future<void> changeDefaultBank(Map<String, String> bank) async {
    selectedBank = bank;
    notifyListeners();
  }
}
