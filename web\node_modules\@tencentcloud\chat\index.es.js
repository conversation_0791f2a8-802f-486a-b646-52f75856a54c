'use strict';const e={SDK_READY:"sdkStateReady",SDK_NOT_READY:"sdkStateNotReady",SDK_DESTROY:"sdkD<PERSON>roy",MESSAGE_RECEIVED:"onMessageReceived",MESSAGE_MODIFIED:"onMessageModified",MESSAGE_REVOKED:"onMessageRevoked",MESSAGE_READ_BY_PEER:"onMessageReadByPeer",MESSAGE_READ_RECEIPT_RECEIVED:"onMessageReadReceiptReceived",MESSAGE_EXTENSIONS_UPDATED:"onMessageExtensionsUpdated",MESSAGE_EXTENSIONS_DELETED:"onMessageExtensionsDeleted",CONVERSATION_LIST_UPDATED:"onConversationListUpdated",TOTAL_UNREAD_MESSAGE_COUNT_UPDATED:"onTotalUnreadMessageCountUpdated",CONVERSATION_GROUP_LIST_UPDATED:"onConversationGroupListUpdated",CONVERSATION_IN_GROUP_UPDATED:"onConversationInGroupUpdated",GROUP_LIST_UPDATED:"onGroupListUpdated",GROUP_ATTRIBUTES_UPDATED:"groupAttributesUpdated",GROUP_COUNTER_UPDATED:"onGroupCounterUpdated",TOPIC_CREATED:"onTopicCreated",TOPIC_DELETED:"onTopicDeleted",TOPIC_UPDATED:"onTopicUpdated",PROFILE_UPDATED:"onProfileUpdated",USER_STATUS_UPDATED:"onUserStatusUpdated",BLACKLIST_UPDATED:"blacklistUpdated",FRIEND_LIST_UPDATED:"onFriendListUpdated",FRIEND_GROUP_LIST_UPDATED:"onFriendGroupListUpdated",FRIEND_APPLICATION_LIST_UPDATED:"onFriendApplicationListUpdated",KICKED_OUT:"kickedOut",ERROR:"error",NET_STATE_CHANGE:"netStateChange"},t={MSG_TEXT:"TIMTextElem",MSG_IMAGE:"TIMImageElem",MSG_SOUND:"TIMSoundElem",MSG_AUDIO:"TIMSoundElem",MSG_FILE:"TIMFileElem",MSG_FACE:"TIMFaceElem",MSG_VIDEO:"TIMVideoFileElem",MSG_GEO:"TIMLocationElem",MSG_LOCATION:"TIMLocationElem",MSG_GRP_TIP:"TIMGroupTipElem",MSG_GRP_SYS_NOTICE:"TIMGroupSystemNoticeElem",MSG_CUSTOM:"TIMCustomElem",MSG_MERGER:"TIMRelayElem",MSG_PRIORITY_HIGH:"High",MSG_PRIORITY_NORMAL:"Normal",MSG_PRIORITY_LOW:"Low",MSG_PRIORITY_LOWEST:"Lowest",CONV_C2C:"C2C",CONV_GROUP:"GROUP",CONV_TOPIC:"TOPIC",CONV_SYSTEM:"@TIM#SYSTEM",CONV_AT_ME:1,CONV_AT_ALL:2,CONV_AT_ALL_AT_ME:3,CONV_MARK_TYPE_STAR:1,CONV_MARK_TYPE_UNREAD:2,CONV_MARK_TYPE_FOLD:4,CONV_MARK_TYPE_HIDE:8,GRP_PRIVATE:"Private",GRP_WORK:"Private",GRP_PUBLIC:"Public",GRP_CHATROOM:"ChatRoom",GRP_MEETING:"ChatRoom",GRP_AVCHATROOM:"AVChatRoom",GRP_COMMUNITY:"Community",GRP_MBR_ROLE_OWNER:"Owner",GRP_MBR_ROLE_ADMIN:"Admin",GRP_MBR_ROLE_MEMBER:"Member",GRP_MBR_ROLE_CUSTOM:"Custom",GRP_TIP_MBR_JOIN:1,GRP_TIP_MBR_QUIT:2,GRP_TIP_MBR_KICKED_OUT:3,GRP_TIP_MBR_SET_ADMIN:4,GRP_TIP_MBR_CANCELED_ADMIN:5,GRP_TIP_GRP_PROFILE_UPDATED:6,GRP_TIP_MBR_PROFILE_UPDATED:7,GRP_TIP_BAN_AVCHATROOM_MEMBER:10,GRP_TIP_UNBAN_AVCHATROOM_MEMBER:11,MSG_REMIND_ACPT_AND_NOTE:"AcceptAndNotify",MSG_REMIND_ACPT_NOT_NOTE:"AcceptNotNotify",MSG_REMIND_DISCARD:"Discard",GENDER_UNKNOWN:"Gender_Type_Unknown",GENDER_FEMALE:"Gender_Type_Female",GENDER_MALE:"Gender_Type_Male",KICKED_OUT_MULT_ACCOUNT:"multipleAccount",KICKED_OUT_MULT_DEVICE:"multipleDevice",KICKED_OUT_USERSIG_EXPIRED:"userSigExpired",KICKED_OUT_REST_API:"REST_API_Kick",ALLOW_TYPE_ALLOW_ANY:"AllowType_Type_AllowAny",ALLOW_TYPE_NEED_CONFIRM:"AllowType_Type_NeedConfirm",ALLOW_TYPE_DENY_ANY:"AllowType_Type_DenyAny",FORBID_TYPE_NONE:"AdminForbid_Type_None",FORBID_TYPE_SEND_OUT:"AdminForbid_Type_SendOut",JOIN_OPTIONS_FREE_ACCESS:"FreeAccess",JOIN_OPTIONS_NEED_PERMISSION:"NeedPermission",JOIN_OPTIONS_DISABLE_APPLY:"DisableApply",JOIN_STATUS_SUCCESS:"JoinedSuccess",JOIN_STATUS_ALREADY_IN_GROUP:"AlreadyInGroup",JOIN_STATUS_WAIT_APPROVAL:"WaitAdminApproval",INVITE_OPTIONS_DISABLE_INVITE:"DisableInvite",INVITE_OPTIONS_NEED_PERMISSION:"NeedPermission",INVITE_OPTIONS_FREE_ACCESS:"FreeAccess",GRP_PROFILE_OWNER_ID:"ownerID",GRP_PROFILE_CREATE_TIME:"createTime",GRP_PROFILE_LAST_INFO_TIME:"lastInfoTime",GRP_PROFILE_MEMBER_NUM:"memberNum",GRP_PROFILE_MAX_MEMBER_NUM:"maxMemberNum",GRP_PROFILE_JOIN_OPTION:"joinOption",GRP_PROFILE_INVITE_OPTION:"inviteOption",GRP_PROFILE_INTRODUCTION:"introduction",GRP_PROFILE_NOTIFICATION:"notification",GRP_PROFILE_MUTE_ALL_MBRS:"muteAllMembers",SNS_ADD_TYPE_SINGLE:"Add_Type_Single",SNS_ADD_TYPE_BOTH:"Add_Type_Both",SNS_DELETE_TYPE_SINGLE:"Delete_Type_Single",SNS_DELETE_TYPE_BOTH:"Delete_Type_Both",SNS_APPLICATION_TYPE_BOTH:"Pendency_Type_Both",SNS_APPLICATION_SENT_TO_ME:"Pendency_Type_ComeIn",SNS_APPLICATION_SENT_BY_ME:"Pendency_Type_SendOut",SNS_APPLICATION_AGREE:"Response_Action_Agree",SNS_APPLICATION_AGREE_AND_ADD:"Response_Action_AgreeAndAdd",SNS_CHECK_TYPE_BOTH:"CheckResult_Type_Both",SNS_CHECK_TYPE_SINGLE:"CheckResult_Type_Single",SNS_TYPE_NO_RELATION:"CheckResult_Type_NoRelation",SNS_TYPE_A_WITH_B:"CheckResult_Type_AWithB",SNS_TYPE_B_WITH_A:"CheckResult_Type_BWithA",SNS_TYPE_BOTH_WAY:"CheckResult_Type_BothWay",NET_STATE_CONNECTED:"connected",NET_STATE_CONNECTING:"connecting",NET_STATE_DISCONNECTED:"disconnected",MSG_AT_ALL:"__kImSDK_MesssageAtALL__",READ_ALL_C2C_MSG:"readAllC2CMessage",READ_ALL_GROUP_MSG:"readAllGroupMessage",READ_ALL_MSG:"readAllMessage",USER_STATUS_UNKNOWN:0,USER_STATUS_ONLINE:1,USER_STATUS_OFFLINE:2,USER_STATUS_UNLOGINED:3};class s{constructor(){this.cache=[],this.options=null}use(e){if("function"!=typeof e)throw"middleware must be a function";return this.cache.push(e),this}next(e){if(this.middlewares&&this.middlewares.length>0){return this.middlewares.shift().call(this,this.options,this.next.bind(this))}}run(e){return this.middlewares=this.cache.map((function(e){return e})),this.options=e,this.next()}}class o{constructor(e=0,t=0){this.high=e,this.low=t}equal(e){return null!==e&&(this.low===e.low&&this.high===e.high)}toString(){const e=Number(this.high).toString(16);let t=Number(this.low).toString(16);if(t.length<8){let e=8-t.length;for(;e;)t="0"+t,e--}return e+t}}const i={TEST:{CHINA:{DEFAULT:"wss://wss-dev.tim.qq.com"},OVERSEA:{DEFAULT:"wss://wss-dev.tim.qq.com"},SINGAPORE:{DEFAULT:"wss://wsssgp-dev.im.qcloud.com"},KOREA:{DEFAULT:"wss://wsskr-dev.im.qcloud.com"},GERMANY:{DEFAULT:"wss://wssger-dev.im.qcloud.com"},IND:{DEFAULT:"wss://wssind-dev.im.qcloud.com"},JPN:{DEFAULT:"wss://wssjpn-dev.im.qcloud.com"},USA:{DEFAULT:"wss://wssusa-dev.im.qcloud.com"}},PRODUCTION:{CHINA:{DEFAULT:"wss://wss.im.qcloud.com",BACKUP:"wss://wss.tim.qq.com",STAT:"https://events.im.qcloud.com",ANYCAST:"wss://162.14.13.203"},OVERSEA:{DEFAULT:"wss://wss.im.qcloud.com",BACKUP:"wss://wss.my-imcloud.com",STAT:"https://api.my-imcloud.com"},SINGAPORE:{DEFAULT:"wss://wsssgp.im.qcloud.com",BACKUP:"wss://wsssgp.my-imcloud.com",STAT:"https://apisgp.my-imcloud.com"},KOREA:{DEFAULT:"wss://wsskr.im.qcloud.com",BACKUP:"wss://wsskr.my-imcloud.com",STAT:"https://apikr.my-imcloud.com"},GERMANY:{DEFAULT:"wss://wssger.im.qcloud.com",BACKUP:"wss://wssger.my-imcloud.com",STAT:"https://apiger.my-imcloud.com"},IND:{DEFAULT:"wss://wssind.my-imcloud.com",BACKUP:"wss://wssind.im.qcloud.com",STAT:"https://apiind.my-imcloud.com"},JPN:{DEFAULT:"wss://wssjpn.im.qcloud.com",BACKUP:"wss://wssjpn.my-imcloud.com",STAT:"https://apijpn.my-imcloud.com"},USA:{DEFAULT:"wss://wssusa.im.qcloud.com",BACKUP:"wss://wssusa.my-imcloud.com",STAT:"https://apiusa.my-imcloud.com"}}},n={ANDROID:2,IOS:3,MAC:4,WEB:7,WX_MP:8,QQ_MP:9,TT_MP:10,BAIDU_MP:11,ALI_MP:12,IPAD:13,UNI_NATIVE_APP:15},r="1.7.3",a=537048168,c="CHINA",u="OVERSEA",l="SINGAPORE",d="KOREA",p="GERMANY",h="IND",g="JPN",_="USA",m={HOST:{CURRENT:{DEFAULT:"wss://wss.im.qcloud.com",STAT:"https://events.im.qcloud.com"},setCurrent(e=c){this.CURRENT=i.PRODUCTION[e]}},NAME:{OPEN_IM:"openim",OPEN_IM_MSG_EXT:"openim_msg_ext_http_svc",GROUP:"group_open_http_svc",GROUP_AVCHATROOM:"group_open_avchatroom_http_svc",GROUP_COMMUNITY:"million_group_open_http_svc",GROUP_ATTR:"group_open_attr_http_svc",FRIEND:"sns",PROFILE:"profile",RECENT_CONTACT:"recentcontact",PIC:"openpic",BIG_GROUP_NO_AUTH:"group_open_http_noauth_svc",BIG_GROUP_LONG_POLLING:"group_open_long_polling_http_svc",BIG_GROUP_LONG_POLLING_NO_AUTH:"group_open_long_polling_http_noauth_svc",IM_OPEN_STAT:"imopenstat",WEB_IM:"webim",IM_COS_SIGN:"im_cos_sign_svr",CUSTOM_UPLOAD:"im_cos_msg",HEARTBEAT:"heartbeat",IM_OPEN_PUSH:"im_open_push",IM_OPEN_STATUS:"im_open_status",IM_LONG_MESSAGE:"im_long_msg",IM_CONFIG_MANAGER:"im_sdk_config_mgr",STAT_SERVICE:"StatSvc",OVERLOAD_PUSH:"OverLoadPush",IM_MSG_AUDIT_MGR:"im_msg_audit_mgr",TUIROOM_SVR:"tui_room_svr",IM_OPEN_TRANSLATE:"im_open_translate",IM_OPEN_SPEECH:"im_open_speech",MESSAGE_SEARCH:"message_search"},CHANNEL:{SOCKET:1,XHR:2,AUTO:0},NAME_VERSION:{openim:"v4",group_open_http_svc:"v4",sns:"v4",profile:"v4",recentcontact:"v4",openpic:"v4",group_open_http_noauth_svc:"v4",group_open_long_polling_http_svc:"v4",group_open_long_polling_http_noauth_svc:"v4",imopenstat:"v4",im_cos_sign_svr:"v4",im_cos_msg:"v4",webim:"v4",im_open_push:"v4",im_open_status:"v4"}},M={SEARCH_MSG:new o(0,Math.pow(2,0)).toString(),SEARCH_GRP_SNS:new o(0,Math.pow(2,1)).toString(),AVCHATROOM_HISTORY_MSG:new o(0,Math.pow(2,2)).toString(),GRP_COMMUNITY:new o(0,Math.pow(2,3)).toString(),MSG_TO_SPECIFIED_GRP_MBR:new o(0,Math.pow(2,4)).toString(),AVCHATROOM_MBR_LIST:new o(0,Math.pow(2,6)).toString(),USER_STATUS:new o(0,Math.pow(2,7)).toString(),CONV_MARK:new o(0,Math.pow(2,9)).toString(),CONV_GROUP:new o(0,Math.pow(2,10)).toString(),AVCHATROOM_BAN_MBR:new o(0,Math.pow(2,11)).toString(),MSG_EXT:new o(0,Math.pow(2,13)).toString(),GRP_COUNTER:new o(0,Math.pow(2,15)).toString()},f="c2c_text_message",I="c2c_custom_message",C="group_text_message",y="group_custom_message",T="user_profile",v="group_profile";m.HOST.setCurrent(c);const D="undefined"!=typeof wx&&"function"==typeof wx.getSystemInfoSync&&Boolean(wx.getSystemInfoSync().fontSizeSetting),S="undefined"!=typeof qq&&"function"==typeof qq.getSystemInfoSync&&Boolean(qq.getSystemInfoSync().fontSizeSetting),N="undefined"!=typeof tt&&"function"==typeof tt.getSystemInfoSync&&Boolean(tt.getSystemInfoSync().fontSizeSetting),E="undefined"!=typeof swan&&"function"==typeof swan.getSystemInfoSync&&Boolean(swan.getSystemInfoSync().fontSizeSetting),A="undefined"!=typeof my&&"function"==typeof my.getSystemInfoSync&&Boolean(my.getSystemInfoSync().fontSizeSetting),L="undefined"!=typeof jd&&"function"==typeof jd.getSystemInfoSync,O="undefined"!=typeof uni&&"undefined"==typeof window&&"function"==typeof uni.requireNativePlugin,R="undefined"!=typeof uni,U=D||S||N||E||A||O||L,k=("undefined"!=typeof uni||"undefined"!=typeof window)&&!U,P=S?qq:N?tt:E?swan:A?my:D?wx:O?uni:L?jd:{},G=k&&window&&window.navigator&&window.navigator.userAgent||"",w=/(micromessenger|webbrowser)/i.test(G),b=/AppleWebKit\/([\d.]+)/i.exec(G);b&&parseFloat(b.pop());const F=function(){let e="WEB";return w?e="WEB":S?e="QQ_MP":N?e="TT_MP":E?e="BAIDU_MP":A?e="ALI_MP":D?e="WX_MP":O&&(e="UNI_NATIVE_APP"),n[e]}(),$=/iPad/i.test(G),q=/iPhone/i.test(G)&&!$,x=/iPod/i.test(G),V=q||$||x,K=function(){const e=G.match(/OS (\d+)_/i);return e&&e[1]?e[1]:null}(),B=/Android/i.test(G),H=function(){const e=G.match(/Android (\d+)(?:\.(\d+))?(?:\.(\d+))*/i);if(!e)return null;const t=e[1]&&parseFloat(e[1]),s=e[2]&&parseFloat(e[2]);return t&&s?parseFloat(e[1]+"."+e[2]):t||null}(),W=/Edge/i.test(G),Y=!W&&/Chrome/i.test(G);!function(){const e=G.match(/Chrome\/(\d+)/);e&&e[1]&&parseFloat(e[1])}();const z=/MSIE/.test(G)||G.indexOf("Trident")>-1&&G.indexOf("rv:11.0")>-1,j=function(){const e=/MSIE\s(\d+)\.\d/.exec(G);let t=e&&parseFloat(e[1]);return!t&&/Trident\/7.0/i.test(G)&&/rv:11.0/.test(G)&&(t=11),t}(),J=/Safari/i.test(G)&&!Y&&!B&&!W;!function(){const e=G.match(/TBS\/(\d+)/i);if(e&&e[1])e[1]}();const X=/Windows/i.test(G),Q=/MAC OS X/i.test(G),Z=k&&"undefined"!=typeof Worker&&!z,ee=B||V,te=k&&void 0!==window.tencent_cloud_im_csig_flutter_for_web_25F_cy,se=function(){if("undefined"==typeof window)return!1;const e=window.navigator.standalone;return!(!V||e||J)}();let oe,ie;oe="undefined"!=typeof console?console:"undefined"!=typeof global&&global.console?global.console:"undefined"!=typeof window&&window.console?window.console:{};const ne=function(){},re=["assert","clear","count","debug","dir","dirxml","error","group","groupCollapsed","groupEnd","info","log","profile","profileEnd","table","time","timeEnd","timeStamp","trace","warn"];let ae=re.length;for(;ae--;)ie=re[ae],console[ie]||(oe[ie]=ne);var ce=oe;let ue=0;const le=function(){return(new Date).getTime()+ue},de=function(){ue=0},pe=function(){return Math.floor(le()/1e3)};let he=0;function ge(){return _t()?"%c Chat %c":"Chat"}function _e(){const e=function(){const e=new Date;return e.setTime(le()),e}();return e.toLocaleTimeString("en-US",{hour12:!1})+"."+function(e){let t;switch(e.toString().length){case 1:t="00"+e;break;case 2:t="0"+e;break;default:t=e}return t}(e.getMilliseconds())}const me={arguments2String(e){let t="";if(1===e.length)t=e[0];else for(let s=0,o=e.length;s<o;s++)Ue(e[s])?Pe(e[s])?t+=$e(e[s]):t+=JSON.stringify(e[s]):t+=e[s],t+=" ";return t},_exec(e,t){_t()?ce[e](ge(),"background:#0abf5b; padding:1px; border-radius:3px; color: #fff","background:transparent",_e(),t):ce[e](`${ge()} ${_e()} ${t}`)},d:function(){if(he<=-1){const e=this.arguments2String(arguments);this._exec("debug",e)}},l:function(){if(he<=0){const e=this.arguments2String(arguments);this._exec("log",e)}},log:function(){if(he<=0){const e=this.arguments2String(arguments);this._exec("log",e)}},i:function(){if(he<=1){const e=this.arguments2String(arguments);this._exec("info",e)}},w:function(){if(he<=2){const e=this.arguments2String(arguments);this._exec("warn",e)}},e:function(){if(he<=3){const e=this.arguments2String(arguments);this._exec("error",e)}},setLevel:function(e){e<4&&this._exec("log","set level from "+he+" to "+e),he=e},getLevel:function(){return he}},Me={JPG:1,JPEG:1,GIF:2,PNG:3,BMP:4,UNKNOWN:255},fe={NICK:"Tag_Profile_IM_Nick",GENDER:"Tag_Profile_IM_Gender",BIRTHDAY:"Tag_Profile_IM_BirthDay",LOCATION:"Tag_Profile_IM_Location",SELFSIGNATURE:"Tag_Profile_IM_SelfSignature",ALLOWTYPE:"Tag_Profile_IM_AllowType",LANGUAGE:"Tag_Profile_IM_Language",AVATAR:"Tag_Profile_IM_Image",MESSAGESETTINGS:"Tag_Profile_IM_MsgSettings",ADMINFORBIDTYPE:"Tag_Profile_IM_AdminForbidType",LEVEL:"Tag_Profile_IM_Level",ROLE:"Tag_Profile_IM_Role"},Ie={UNKNOWN:"Gender_Type_Unknown",FEMALE:"Gender_Type_Female",MALE:"Gender_Type_Male"},Ce={NONE:"AdminForbid_Type_None",SEND_OUT:"AdminForbid_Type_SendOut"},ye={NEED_CONFIRM:"AllowType_Type_NeedConfirm",ALLOW_ANY:"AllowType_Type_AllowAny",DENY_ANY:"AllowType_Type_DenyAny"},Te="@TGS#_",ve="@TOPIC#_",De=function(e){return"map"===we(e)},Se=function(e){return"file"===we(e)},Ne=function(e){return null!==e&&("number"==typeof e&&!isNaN(e-0)||"object"==typeof e&&e.constructor===Number)},Ee=function(e){return"string"==typeof e},Ae=function(e){return null!==e&&"object"==typeof e},Le=function(e){if("object"!=typeof e||null===e)return!1;const t=Object.getPrototypeOf(e);if(null===t)return!0;let s=t;for(;null!==Object.getPrototypeOf(s);)s=Object.getPrototypeOf(s);return t===s},Oe=function(e){return"function"==typeof Array.isArray?Array.isArray(e):"array"===we(e)},Re=function(e){return void 0===e},Ue=function(e){return Oe(e)||Ae(e)},ke=function(e){return"function"==typeof e},Pe=function(e){return e instanceof Error},Ge=function(e){return"filelist"===we(e)},we=function(e){return Object.prototype.toString.call(e).match(/^\[object (.*)\]$/)[1].toLowerCase()},be=function(e){if("string"!=typeof e)return!1;const t=e[0];return!/[^a-zA-Z0-9]/.test(t)};Date.now||(Date.now=function(){return(new Date).getTime()});const Fe=function(e,t,s,o){if(!Ue(e)||!Ue(t))return 0;let i=0;const n=Object.keys(t);let r;for(let a=0,c=n.length;a<c;a++)if(r=n[a],!(Re(t[r])||s&&s.includes(r)))if(Ue(e[r])&&Ue(t[r]))i+=Fe(e[r],t[r],s,o);else{if(o&&o.includes(t[r]))continue;e[r]!==t[r]&&(e[r]=t[r],i+=1)}return i},$e=function(e){return JSON.stringify(e,["message","code"])},qe=function(e){if(0===e.length)return 0;let t=0,s="",o=0,i=1;const n="undefined"!=typeof document&&void 0!==document.characterSet?document.characterSet:"UTF-8";for(;void 0!==e[t];)s=e[t++],i=s.charCodeAt[t]<=255?1:!1===n?3:2,o+=i;return o},xe=function(e){const t=e||99999999;return Math.round(Math.random()*t)},Ve="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ",Ke=Ve.length,Be=function(e,t){for(const s in e)if(e[s]===t)return!0;return!1},He={},We=function(e){return-1===e.indexOf("http://")||-1===e.indexOf("https://")?"https://"+e:e.replace(/https|http/,"https")};function Ye(e,t){if(!Oe(e)||!Oe(t))return!1;let s=!1;return t.forEach(({key:t,value:o})=>{const i=e.find(e=>e.key===t);i?i.value!==o&&(i.value=o,s=!0):(e.push({key:t,value:o}),s=!0)}),s}const ze=e=>e===t.GRP_AVCHATROOM,je=({type:e,groupID:s})=>e===t.GRP_COMMUNITY||(""+s).startsWith(Te)&&!(""+s).includes(ve),Je=e=>(""+e).startsWith(Te)&&(""+e).includes(ve),Xe=e=>Ee(e)&&e.slice(0,3)===t.CONV_C2C,Qe=e=>Ee(e)&&e.slice(0,5)===t.CONV_GROUP,Ze=e=>Ee(e)&&e===t.CONV_SYSTEM;function et(e,t){const s={};return Object.keys(e).forEach(o=>{s[o]=t(e[o],o)}),s}function st(e){return U?new Promise((t,s)=>{P.getImageInfo({src:e,success(e){t({width:e.width,height:e.height})},fail(){t({width:0,height:0})}})}):z&&9===j?Promise.resolve({width:0,height:0}):new Promise((t,s)=>{let o=new Image;o.onload=function(){t({width:this.width,height:this.height}),o=null},o.onerror=function(){t({width:0,height:0}),o=null},o.src=e})}function ot(){function e(){return(65536*(1+Math.random())|0).toString(16).substring(1)}return`${e()+e()}${e()}${e()}${e()}${e()}${e()}${e()}`}function it(){let e="unknown";if(Q&&(e="mac"),X&&(e="windows"),V&&(e="ios"),B&&(e="android"),U)try{const t=P.getSystemInfoSync().platform;void 0!==t&&(e=t)}catch(t){}return e}function nt(e,t){e=e.split("."),t=t.split(".");const s=Math.max(e.length,t.length);for(;e.length<s;)e.push("0");for(;t.length<s;)t.push("0");for(let o=0;o<s;o++){const s=parseInt(e[o]),i=parseInt(t[o]);if(s>i)return 1;if(s<i)return-1}return 0}function rt(e){const{originUrl:t,originWidth:s,originHeight:o,min:i=198}=e,n=parseInt(s),r=parseInt(o),a={url:void 0,width:0,height:0};if((n<=r?n:r)<=i)a.url=t,a.width=n,a.height=r;else{r<=n?(a.width=Math.ceil(n*i/r),a.height=i):(a.width=i,a.height=Math.ceil(r*i/n));const e=t&&t.indexOf("?")>-1?t+"&":t+"?";a.url=198===i?e+"imageView2/3/w/198/h/198":e+"imageView2/3/w/720/h/720"}if(Re(t)){const{url:e,...t}=a;return t}return a}function at(e){const t=e[2];e[2]=e[1],e[1]=t;for(let s=0;s<e.length;s++)e[s].setType(s)}function ct(e){const{servcmd:t}=e;return t.slice(t.indexOf(".")+1)}function ut(e,t){return Math.round(Number(e)*Math.pow(10,t))/Math.pow(10,t)}function lt(e,t){return e.includes(t)}function dt(e,t){return e.includes(t)}function pt(e){return e.split(ve)[0]}const ht=function(e,s,o){if(Re(s))return"";switch(e){case t.MSG_TEXT:return s.text;case t.MSG_IMAGE:return o?"[Image]":"[图片]";case t.MSG_LOCATION:return o?"[Location]":"[位置]";case t.MSG_AUDIO:return o?"[Voice]":"[语音]";case t.MSG_VIDEO:return o?"[Video]":"[视频]";case t.MSG_FILE:return o?"[File]":"[文件]";case t.MSG_CUSTOM:return o?"[Custom Messages]":"[自定义消息]";case t.MSG_GRP_TIP:return o?"[Group Notification]":"[群提示消息]";case t.MSG_GRP_SYS_NOTICE:return o?"[Group System Message]":"[群系统通知]";case t.MSG_FACE:return o?"[Animated Sticker]":"[动画表情]";case t.MSG_MERGER:return o?"[Chat Record]":"[聊天记录]";default:return""}};function gt(e){const t=[];if(!Ee(e))return t;const s=e.length;if(0===s)return t;for(let o=s-1;o>=0;o--)"1"===e[o]&&t.push(Math.pow(2,s-o-1));return t}function _t(){return!z&&!U}function mt(e){return"the length of userIDList cannot exceed "+e}function Mt(e,t){if(!e)return;let s=e;return t&&(e.startsWith("http://")?s=e.replace(/^http:\/\/[^/]+/,t):e.startsWith("https://")&&(s=e.replace(/^https:\/\/[^/]+/,t))),s}function ft(e){let t=!1;return e&&e>1&&(t=!0),t}const It=Object.prototype.hasOwnProperty;function Ct(e){if(null==e)return!0;if("boolean"==typeof e)return!1;if("number"==typeof e)return 0===e;if("string"==typeof e)return 0===e.length;if("function"==typeof e)return 0===e.length;if(Array.isArray(e))return 0===e.length;if(e instanceof Error)return""===e.message;if(Le(e)){for(const t in e)if(It.call(e,t))return!1;return!0}return!(!De(e)&&(t=e,"set"!==we(t))&&!Se(e))&&0===e.size;var t}function yt(e,t,s){if(void 0===t)return!0;let o=!0;if(Le(t))Object.keys(t).forEach(i=>{const n=1===e.length?e[0][i]:void 0;o=!!Tt(n,t[i],s,i)&&o});else if(Oe(t))for(let i=0;i<t.length;i++)o=!!Tt(e[i],t[i],s,t[i].name)&&o;if(o)return o;throw new Error("Params validate failed.")}function Tt(e,t,s,o){if(void 0===t)return!0;let i=!0;if(t.required&&Ct(e)&&(me.e(`[${s}] Missing required params: "${o}".`),i=!1),!Ct(e)){const n=we(e),r=t.type.toLowerCase();n!==r&&("asyncfunction"===n&&"function"===r||(me.e(`[${s}] Invalid params: type check failed for "${o}". Expected ${t.type}.`),i=!1))}return t.validator&&!t.validator(e,s,o)&&(me.e(`[${s}] Invalid params: custom validator check failed for "${o}".`),i=!1),i}const vt={UNSEND:"unSend",SUCCESS:"success",FAIL:"fail"},Dt={NOT_START:"notStart",PENDING:"pending",RESOLVED:"resolved",REJECTED:"rejected"},St=function(e){if(!e)return!1;if(Xe(e)||Qe(e)||Ze(e))return!0;const t=Qt("InvalidConversationID",e);return t&&me.w(t),!1},Nt=function(e){""!==e.desc&&""!==Qt("API_REFER")&&me.w(`[${e.api}] | ${e.paramName} | ${e.desc}, ${Qt("API_REFER")}${e.api}`)},Et=function(){return Qt("StringRequiredLog")},At=function(e){return Qt("NonEmptyStringRequiredLog",e)},Lt=function(){return Qt("NumberRequiredLog")},Ot=function(){return Qt("UndefinedNotAllowedLog")},Rt=function(){return Qt("FileRequiredLog")},Ut=function(){return Qt("FunctionRequiredLog")},kt=function(){return Qt("ArrayRequiredLog")},Pt=function(){return Qt("NonEmptyArrayLog")},Gt=function(){return Qt("CallbackMissingLog")},wt=function(){return Qt("PositiveIntegerRequiredLog")},bt=function(e,t){return Qt("StringNotLongerThanLog",e,t)},Ft=function(e,t){return Qt("NumberLessThanLog",e,t)},$t=function(e){return Qt("KeyValueStringRequiredLog",e)},qt=function(){return Qt("PlainObjectRequiredLog")},xt=function(){return Qt("NonEmptyContentRequiredLog")},Vt=function(){return Qt("FileNotSelectedLog")},Kt=function(){return Qt("MessageInstanceRequiredLog")},Bt=function(){return Qt("NonAnonymousFunctionLog")},Ht=function(){return Qt("MessageExtensionNotAvailableLog")},Wt=function(e,t){return Qt("MaximumArrayLengthLog",e,t)},Yt={type:"String",required:!0},zt={type:"Array",required:!0},jt={type:"Object",required:!0},Jt={type:"Boolean",required:!0},Xt={type:"number",required:!0};let Qt=null;const Zt={hookGetAPITips:function(e){Qt=e},login:{userID:Yt,userSig:Yt},addToBlacklist:{userIDList:zt},removeFromBlacklist:{userIDList:zt},on:[{name:"eventName",type:"String",validator:(e,t,s)=>"string"==typeof e&&0!==e.length||(Nt({api:t,paramName:s,desc:At(s)}),!1)},{name:"handler",type:"Function",validator:(e,t,s)=>"function"!=typeof e?(Nt({api:t,paramName:s,desc:Ut()}),!1):(""===e.name&&Nt({api:t,paramName:s,desc:Bt()}),!0)}],once:[{name:"eventName",type:"String",validator:(e,t,s)=>"string"==typeof e&&0!==e.length||(Nt({api:t,paramName:s,desc:At(s)}),!1)},{name:"handler",type:"Function",validator:(e,t,s)=>"function"!=typeof e?(Nt({api:t,paramName:s,desc:Ut()}),!1):(""===e.name&&Nt({api:t,paramName:s,desc:Bt()}),!0)}],off:[{name:"eventName",type:"String",validator:(e,t,s)=>"string"==typeof e&&0!==e.length||(Nt({api:t,paramName:s,desc:At(s)}),!1)},{name:"handler",type:"Function",validator:(e,t,s)=>"function"!=typeof e?(Nt({api:t,paramName:s,desc:Ut()}),!1):(""===e.name&&Nt({api:t,paramName:s,desc:Bt()}),!0)}],sendMessage:[{name:"message",...jt}],setMessageExtensions:[{name:"message",...jt,validator:(e,t,s)=>e.status===vt.SUCCESS&&!0===e.isSupportExtension||(Nt({api:t,paramName:s,desc:Ht()}),!1)},{name:"extensions",...zt}],getMessageExtensions:[{name:"message",...jt,validator:(e,t,s)=>e.status===vt.SUCCESS&&!0===e.isSupportExtension||(Nt({api:t,paramName:s,desc:Ht()}),!1)}],deleteMessageExtensions:[{name:"message",...jt,validator:(e,t,s)=>e.status===vt.SUCCESS&&!0===e.isSupportExtension||(Nt({api:t,paramName:s,desc:Ht()}),!1)}],getMessageList:{conversationID:{...Yt,validator:e=>St(e)},nextReqMessageID:{type:"String"},count:{type:"Number",validator:(e,t,s)=>!(!Re(e)&&!/^[1-9][0-9]*$/.test(e))||(Nt({api:t,paramName:s,desc:wt()}),!1)}},getMessageListHopping:{conversationID:{...Yt,validator:e=>St(e)},sequence:{type:"Number"},time:{type:"Number"},direction:{type:"Number",validator:(e,t,s)=>!(!Re(e)&&0!==e&&1!==e)||(Nt({api:t,paramName:s,desc:Qt("0Or1RequiredLog")}),!1)},count:{type:"Number",validator:(e,t,s)=>!(!Re(e)&&!/^[1-9][0-9]*$/.test(e))||(Nt({api:t,paramName:s,desc:wt}),!1)}},setMessageRead:{conversationID:{...Yt,validator:e=>St(e)}},setAllMessageRead:{scope:{type:"String",required:!1,validator:(e,s,o)=>!e||-1!==[t.READ_ALL_C2C_MSG,t.READ_ALL_GROUP_MSG,t.READ_ALL_MSG].indexOf(e)||(Nt({api:s,paramName:o,desc:Qt("ValidScopeRequired")}),!1)}},getConversationProfile:[{name:"conversationID",...Yt,validator:e=>St(e)}],clearHistoryMessage:[{name:"conversationID",...Yt,validator:e=>St(e)}],pinConversation:{conversationID:{...Yt,validator:e=>St(e)},isPinned:{...Jt}},setConversationDraft:{conversationID:{...Yt,validator:e=>St(e)},draftText:{type:"String",validator:(e,t,s)=>!!Ee(e)||(Nt({api:t,paramName:s,desc:Et()}),!1)}},setConversationCustomData:{conversationIDList:{...zt},customData:{type:"String",validator:(e,t,s)=>Ee(e)?!(e.length>256)||(Nt({api:t,paramName:s,desc:bt(s,256)}),!1):(Nt({api:t,paramName:s,desc:Et()}),!1)}},markConversation:{conversationIDList:{...zt},markType:{type:"number",validator:(e,t,s)=>{return Ne(e)?e<=0?(Nt({api:t,paramName:s,desc:(o=s,i=0,Qt("NumberGreaterThanLog",o,i))}),!1):!(e>=Math.pow(2,64))||(Nt({api:t,paramName:s,desc:Ft(s,"Math.pow(2,64)")}),!1):(Nt({api:t,paramName:s,desc:Lt()}),!1);var o,i}},enableMark:{...Jt}},createConversationGroup:{conversationIDList:{...zt},groupName:{...Yt,validator:(e,t,s)=>!!e&&(!(e.length>32)||(Nt({api:t,paramName:s,desc:bt(s,32)}),!1))}},deleteConversationGroup:[{name:"groupName",...Yt}],renameConversationGroup:{oldName:{...Yt},newName:{...Yt,validator:(e,t,s)=>!!e&&(!(e.length>32)||(Nt({api:t,paramName:s,desc:bt(s,32)}),!1))}},addConversationsToGroup:{conversationIDList:{...zt},groupName:{...Yt}},deleteConversationsFromGroup:{conversationIDList:{...zt},groupName:{...Yt}},getGroupList:{groupProfileFilter:{type:"Array"}},getGroupProfile:{groupID:Yt,groupCustomFieldFilter:{type:"Array"},memberCustomFieldFilter:{type:"Array"}},getGroupProfileAdvance:{groupIDList:zt},createGroup:{name:Yt},joinGroup:{groupID:Yt,type:{type:"String"},applyMessage:{type:"String"}},quitGroup:[{name:"groupID",...Yt}],handleApplication:{message:jt,handleAction:Yt,handleMessage:{type:"String"}},changeGroupOwner:{groupID:Yt,newOwnerID:Yt},updateGroupProfile:{groupID:Yt,muteAllMembers:{type:"Boolean"}},dismissGroup:[{name:"groupID",...Yt}],searchGroupByID:[{name:"groupID",...Yt}],initGroupAttributes:{groupID:Yt,groupAttributes:{...jt,validator:(e,t,s)=>{let o=!0;return Object.keys(e).forEach(i=>{if(!Ee(e[i]))return Nt({api:t,paramName:s,desc:$t("value")}),o=!1,o}),o}}},setGroupAttributes:{groupID:Yt,groupAttributes:{...jt,validator:(e,t,s)=>{let o=!0;return Object.keys(e).forEach(i=>{if(!Ee(e[i]))return Nt({api:t,paramName:s,desc:$t("value")}),o=!1,o}),o}}},deleteGroupAttributes:{groupID:Yt,keyList:{type:"Array",validator:(e,t,s)=>{if(Re(e)||!Oe(e))return Nt({api:t,paramName:s,desc:kt()}),!1;if(!Ct(e)){let o=!0;return e.forEach(e=>{if(!Ee(e))return Nt({api:t,paramName:s,desc:Qt("StringArrayRequiredLog")}),o=!1,o}),o}return!0}}},getGroupAttributes:{groupID:Yt,keyList:{type:"Array",validator:(e,t,s)=>{if(Re(e)||!Oe(e))return Nt({api:t,paramName:s,desc:kt()}),!1;if(!Ct(e)){let o=!0;return e.forEach(e=>{if(!Ee(e))return Nt({api:t,paramName:s,desc:$t("key")}),o=!1,o}),o}return!0}}},setGroupCounters:{groupID:Yt,counters:jt},increaseGroupCounter:{groupID:Yt,key:Yt,value:Xt},decreaseGroupCounter:{groupID:Yt,key:Yt,value:Xt},getGroupCounters:{groupID:Yt},getGroupMemberList:{groupID:Yt,count:{type:"Number"}},getGroupMemberProfile:{groupID:Yt,userIDList:zt,memberCustomFieldFilter:{type:"Array"}},addGroupMember:{groupID:Yt,userIDList:zt},setGroupMemberRole:{groupID:Yt,userID:Yt,role:Yt},setGroupMemberMuteTime:{groupID:Yt,userID:Yt,muteTime:{type:"Number",validator:e=>e>=0}},setGroupMemberNameCard:{groupID:Yt,userID:{type:"String"},nameCard:{type:"String",validator:(e,t,s)=>Ee(e)?(e.length,!0):(Nt({api:t,paramName:s,desc:Et()}),!1)}},setGroupMemberCustomField:{groupID:Yt,userID:{type:"String"},memberCustomField:zt},deleteGroupMember:{groupID:Yt},markGroupMemberList:{groupID:Yt,markType:{type:"number",validator:(e,t,s)=>{return Ne(e)?!(e<1e3)||(Nt({api:t,paramName:s,desc:(o=s,i=1e3,Qt("NumberGreaterOrEqualLog",o,i))}),!1):(Nt({api:t,paramName:s,desc:Lt()}),!1);var o,i}},userIDList:{...zt},enableMark:{...Jt}},createTextMessage:{to:Yt,conversationType:Yt,payload:{...jt,validator:(e,t,s)=>Le(e)?Ee(e.text)?0!==e.text.length||(Nt({api:t,paramName:"payload.text",desc:xt()}),!1):(Nt({api:t,paramName:"payload.text",desc:Et()}),!1):(Nt({api:t,paramName:s,desc:qt()}),!1)}},createTextAtMessage:{to:Yt,conversationType:Yt,payload:{...jt,validator:(e,t,s)=>Le(e)?Ee(e.text)?0===e.text.length?(Nt({api:t,paramName:"payload.text",desc:xt()}),!1):!(e.atUserList&&!Oe(e.atUserList))||(Nt({api:t,paramName:"payload.atUserList",desc:kt()}),!1):(Nt({api:t,paramName:"payload.text",desc:Et()}),!1):(Nt({api:t,paramName:s,desc:qt()}),!1)}},createCustomMessage:{to:Yt,conversationType:Yt,payload:{...jt,validator:(e,t,s)=>Le(e)?e.data&&!Ee(e.data)?(Nt({api:t,paramName:"payload.data",desc:Et()}),!1):e.description&&!Ee(e.description)?(Nt({api:t,paramName:"payload.description",desc:Et()}),!1):!(e.extension&&!Ee(e.extension))||(Nt({api:t,paramName:"payload.extension",desc:Et()}),!1):(Nt({api:t,paramName:"payload",desc:qt()}),!1)}},createImageMessage:{to:Yt,conversationType:Yt,payload:{...jt,validator:(e,t,s)=>{if(!Le(e))return Nt({api:t,paramName:s,desc:qt()}),!1;if(Re(e.file))return Nt({api:t,paramName:"payload.file",desc:Ot()}),!1;if(k){if(!(e.file instanceof HTMLInputElement||Se(e.file)))return Le(e.file)&&"undefined"!=typeof uni?0!==e.file.tempFilePaths.length&&0!==e.file.tempFiles.length||(Nt({api:t,paramName:"payload.file",desc:Vt()}),!1):(Nt({api:t,paramName:"payload.file",desc:Rt()}),!1);if(e.file instanceof HTMLInputElement&&0===e.file.files.length)return Nt({api:t,paramName:"payload.file",desc:Vt()}),!1}return!0},onProgress:{type:"Function",required:!1,validator:(e,t,s)=>(Re(e)&&Nt({api:t,paramName:s,desc:Gt()}),!0)}}},createAudioMessage:{to:Yt,conversationType:Yt,payload:{...jt,validator:(e,t,s)=>!!Le(e)||(Nt({api:t,paramName:s,desc:qt()}),!1)},onProgress:{type:"Function",required:!1,validator:(e,t,s)=>(Re(e)&&Nt({api:t,paramName:s,desc:Gt()}),!0)}},createVideoMessage:{to:Yt,conversationType:Yt,payload:{...jt,validator:(e,t,s)=>{if(!Le(e))return Nt({api:t,paramName:s,desc:qt()}),!1;if(Re(e.file))return Nt({api:t,paramName:"payload.file",desc:Ot()}),!1;if(k){if(!(e.file instanceof HTMLInputElement||Se(e.file)))return Le(e.file)&&"undefined"!=typeof uni?!!Se(e.file.tempFile)||(Nt({api:t,paramName:"payload.file",desc:Vt()}),!1):(Nt({api:t,paramName:"payload.file",desc:Rt()}),!1);if(e.file instanceof HTMLInputElement&&0===e.file.files.length)return Nt({api:t,paramName:"payload.file",desc:Vt()}),!1}return!0}},onProgress:{type:"Function",required:!1,validator:(e,t,s)=>(Re(e)&&Nt({api:t,paramName:s,desc:Gt()}),!0)}},createFaceMessage:{to:Yt,conversationType:Yt,payload:{...jt,validator:(e,t,s)=>Le(e)?Ne(e.index)?!!Ee(e.data)||(Nt({api:t,paramName:"payload.data",desc:Et()}),!1):(Nt({api:t,paramName:"payload.index",desc:Lt()}),!1):(Nt({api:t,paramName:s,desc:qt()}),!1)}},createFileMessage:{to:Yt,conversationType:Yt,payload:{...jt,validator:(e,t,s)=>{if(!Le(e))return Nt({api:t,paramName:s,desc:qt()}),!1;if(Re(e.file))return Nt({api:t,paramName:"payload.file",desc:Ot()}),!1;if(k){if(!(e.file instanceof HTMLInputElement||Se(e.file)))return Le(e.file)&&"undefined"!=typeof uni?0!==e.file.tempFilePaths.length&&0!==e.file.tempFiles.length||(Nt({api:t,paramName:"payload.file",desc:Vt()}),!1):(Nt({api:t,paramName:"payload.file",desc:Rt()}),!1);if(e.file instanceof HTMLInputElement&&0===e.file.files.length)return Nt({api:t,paramName:"payload.file",desc:Vt()}),!1}return!0}},onProgress:{type:"Function",required:!1,validator:(e,t,s)=>(Re(e)&&Nt({api:t,paramName:s,desc:Gt()}),!0)}},createLocationMessage:{to:Yt,conversationType:Yt,payload:{...jt,validator:(e,t,s)=>Le(e)?Ee(e.description)?Ne(e.longitude)?!!Ne(e.latitude)||(Nt({api:t,paramName:"payload.latitude",desc:Lt()}),!1):(Nt({api:t,paramName:"payload.longitude",desc:Lt()}),!1):(Nt({api:t,paramName:"payload.description",desc:Et()}),!1):(Nt({api:t,paramName:s,desc:qt()}),!1)}},createMergerMessage:{to:Yt,conversationType:Yt,payload:{...jt,validator:(e,t,s)=>{if(Ct(e.messageList))return Nt({api:t,paramName:"payload.messageList",desc:Pt()}),!1;if(Ct(e.compatibleText))return Nt({api:t,paramName:"payload.compatibleText",desc:At("compatibleText")}),!1;let o=!1;return e.messageList.forEach(e=>{e.status===vt.FAIL&&(o=!0)}),!o||(Nt({api:t,paramName:"payload.messageList",desc:Qt("MergeFailedMessageLog")}),!1)}}},revokeMessage:[{name:"message",...jt,validator:(e,s,o)=>Ct(e)?(Nt({api:s,paramName:o,desc:Kt()}),!1):e.conversationType===t.CONV_SYSTEM?(Nt({api:s,paramName:o,desc:Qt("MessageCanBeRevokedDesc")}),!1):!0!==e.isRevoked||(Nt({api:s,paramName:o,desc:Qt("MessageRevokedLog")}),!1)}],deleteMessage:[{name:"messageList",...zt,validator:(e,t,s)=>!Ct(e)||(Nt({api:t,paramName:s,desc:Pt()}),!1)}],translateText:{sourceTextList:zt,sourceLanguage:Yt,targetLanguage:Yt},convertVoiceToText:{message:{...jt,validator:(e,s,o)=>Ct(e)?(Nt({api:s,paramName:o,desc:Kt()}),!1):e.type===t.MSG_AUDIO&&"success"===e.status||(Nt({api:s,paramName:o,desc:Qt("AudioMessageRequiredLog")}),!1)}},modifyMessage:[{name:"message",...jt,validator:(e,s,o)=>Ct(e)?(Nt({api:s,paramName:o,desc:Kt()}),!1):e.conversationType===t.CONV_SYSTEM?(Nt({api:s,paramName:o,desc:Qt("MessageCanBeModifiedLog")}),!1):!0!==e._onlineOnlyFlag||(Nt({api:s,paramName:o,desc:Qt("OnlineMessageNotSupportLog")}),!1)}],searchCloudMessages:{keywordList:{type:"Array",required:!1,validator:(e,t,s)=>!e||(Oe(e)?0===e.length?(Nt({api:t,paramName:s,desc:Pt()}),!1):!(e.length>5)||(Nt({api:t,paramName:s,desc:Wt(s,5)}),!1):(Nt({api:t,paramName:s,desc:kt()}),!1))},keywordListMatchType:{type:"String",required:!1,validator:(e,t,s)=>!e||("or"===e||"and"===e||Nt({api:t,paramName:s,desc:e+" is invalid match type"}))},senderUserIDList:{type:"Array",required:!1,validator:(e,t,s)=>!e||(Oe(e)?(0===e.length&&Nt({api:t,paramName:s,desc:Pt()}),!(e.length>5)||(Nt({api:t,paramName:s,desc:Wt(s,5)}),!1)):(Nt({api:t,paramName:s,desc:kt()}),!1))},messageTypeList:{type:"Array",required:!1,validator:(e,s,o)=>{if(!e)return!0;if(!Oe(e))return Nt({api:s,paramName:o,desc:kt()}),!1;0===e.length&&Nt({api:s,paramName:o,desc:Pt()});const i=[t.MSG_TEXT,t.MSG_IMAGE,t.MSG_AUDIO,t.MSG_FILE,t.MSG_VIDEO,t.MSG_LOCATION,t.MSG_CUSTOM,t.MSG_MERGER];return!(e.filter(e=>-1===i.indexOf(e)).length>0)||(Nt({api:s,paramName:o,desc:(n=o,Qt("ContainsUnsupportedMessageTypeLog",n))}),!1);var n}},conversationID:{type:"String",required:!1,validator:e=>!e||St(e)},timePosition:{type:"number",required:!1},timePeriod:{type:"number",required:!1},count:{type:"number",required:!1,validator:(e,t,s)=>{return!e||(e>100&&Nt({api:t,paramName:s,desc:(o=s,i=100,Qt("MaximumNumberLog",o,i))}),!0);var o,i}},cursor:{type:"String",required:!1}},getUserProfile:{userIDList:{type:"Array",validator:(e,t,s)=>Oe(e)?(0===e.length&&Nt({api:t,paramName:s,desc:Pt()}),!0):(Nt({api:t,paramName:s,desc:kt()}),!1)}},updateMyProfile:{profileCustomField:{type:"Array",validator:(e,t,s)=>!!Re(e)||(!!Oe(e)||(Nt({api:t,paramName:s,desc:kt()}),!1))}},setSelfStatus:{customStatus:{type:"String",validator:(e,t,s)=>!!Ee(e)||(Nt({api:t,paramName:s,desc:Et()}),!1)}},getUserStatus:{userIDList:{type:"Array",validator:(e,t,s)=>Oe(e)?0!==e.length||(Nt({api:t,paramName:s,desc:Pt()}),!1):(Nt({api:t,paramName:s,desc:kt()}),!1)}},subscribeUserStatus:{userIDList:{type:"Array",validator:(e,t,s)=>Oe(e)?0!==e.length||(Nt({api:t,paramName:s,desc:Pt()}),!1):(Nt({api:t,paramName:s,desc:kt()}),!1)}},unsubscribeUserStatus:{userIDList:{type:"Array",validator:(e,t,s)=>!e||(!!Oe(e)||(Nt({api:t,paramName:s,desc:kt()}),!1))}},addFriend:{to:Yt,source:{type:"String",required:!0,validator:(e,t,s)=>{if(!e)return!1;if(!e.startsWith("AddSource_Type_"))return Nt({api:t,paramName:s,desc:Qt("SourcePrefixLog")}),!1;return!(e.replace("AddSource_Type_","").length>8)||(Nt({api:t,paramName:s,desc:bt("keyword",8)}),!1)}},remark:{type:"String",required:!1,validator:(e,t,s)=>!(Ee(e)&&e.length>96)||(Nt({api:t,paramName:s,desc:bt(s,96)}),!1)}},deleteFriend:{userIDList:zt},checkFriend:{userIDList:zt},getFriendProfile:{userIDList:zt},updateFriend:{userID:Yt,remark:{type:"String",required:!1,validator:(e,t,s)=>!(Ee(e)&&e.length>96)||(Nt({api:t,paramName:s,desc:bt(s,96)}),!1)},friendCustomField:{type:"Array",required:!1,validator:(e,t,s)=>{if(e){if(!Oe(e))return Nt({api:t,paramName:s,desc:kt()}),!1;let o=!0;return e.forEach(e=>{if(!Ee(e.key)||-1===e.key.indexOf("Tag_SNS_Custom"))return Nt({api:t,paramName:s,desc:Qt("FriendCustomFieldPrefixLog")}),o=!1,o;if(!Ee(e.value))return Nt({api:t,paramName:s,desc:$t("value")}),o=!1,o;return e.key.replace("Tag_SNS_Custom_","").length>8?(Nt({api:t,paramName:s,desc:bt("keyword",8)}),o=!1,o):void 0}),o}return!0}}},acceptFriendApplication:{userID:Yt},refuseFriendApplication:{userID:Yt},deleteFriendApplication:{userID:Yt},createFriendGroup:{name:Yt},deleteFriendGroup:{name:Yt},addToFriendGroup:{name:Yt,userIDList:zt},removeFromFriendGroup:{name:Yt,userIDList:zt},renameFriendGroup:{oldName:Yt,newName:Yt},sendMessageReadReceipt:[{name:"messageList",type:"Array",validator:(e,t,s)=>Oe(e)?0!==e.length||(Nt({api:t,paramName:s,desc:Pt()}),!1):(Nt({api:t,paramName:s,desc:kt()}),!1)}],getMessageReadReceiptList:[{name:"messageList",type:"Array",validator:(e,t,s)=>Oe(e)?0!==e.length||(Nt({api:t,paramName:s,desc:Pt()}),!1):(Nt({api:t,paramName:s,desc:kt()}),!1)}],createTopicInCommunity:{groupID:Yt,topicName:Yt},deleteTopicFromCommunity:{groupID:Yt,topicIDList:{type:"Array",validator:(e,t,s)=>!e||(!!Oe(e)||(Nt({api:t,paramName:s,desc:kt()}),!1))}},updateTopicProfile:{groupID:Yt,topicID:Yt},getTopicList:{groupID:Yt,topicIDList:{type:"Array",validator:(e,t,s)=>!e||(!!Oe(e)||(Nt({api:t,paramName:s,desc:kt()}),!1))}},addSignalingListener:[{name:"eventName",type:"String",validator:(e,t,s)=>"string"==typeof e&&0!==e.length||(Nt({api:t,paramName:s,desc:At(s)}),!1)},{name:"handler",type:"Function",validator:(e,t,s)=>"function"!=typeof e?(Nt({api:t,paramName:s,desc:Ut()}),!1):(""===e.name&&Nt({api:t,paramName:s,desc:Bt()}),!0)}],removeSignalingListener:[{name:"eventName",type:"String",validator:(e,t,s)=>"string"==typeof e&&0!==e.length||(Nt({api:t,paramName:s,desc:At(s)}),!1)},{name:"handler",type:"Function",validator:(e,t,s)=>"function"!=typeof e?(Nt({api:t,paramName:s,desc:Ut()}),!1):(""===e.name&&Nt({api:t,paramName:s,desc:Bt()}),!0)}],invite:{userID:Yt},inviteSync:[{...jt,validator:(e,t,s)=>Le(e)?!!Ee(e.userID)||(Nt({api:t,paramName:"options.userID",desc:Et()}),!1):(Nt({api:t,paramName:"options",desc:qt()}),!1)},{name:"successCb",type:"Function",required:!1,validator:(e,t,s)=>(Re(e)&&Nt({api:t,paramName:s,desc:Ut()}),!0)},{name:"errorCb",type:"Function",required:!1,validator:(e,t,s)=>(Re(e)&&Nt({api:t,paramName:s,desc:Ut()}),!0)}],inviteInGroup:{groupID:Yt,inviteeList:zt},inviteInGroupSync:[{...jt,validator:(e,t,s)=>Le(e)?Ee(e.groupID)?!!Oe(e.inviteeList)||(Nt({api:t,paramName:"options.inviteeList",desc:kt()}),!1):(Nt({api:t,paramName:"options.groupID",desc:Et()}),!1):(Nt({api:t,paramName:"options",desc:qt()}),!1)},{name:"successCb",type:"Function",required:!1,validator:(e,t,s)=>(Re(e)&&Nt({api:t,paramName:s,desc:Ut()}),!0)},{name:"errorCb",type:"Function",required:!1,validator:(e,t,s)=>(Re(e)&&Nt({api:t,paramName:s,desc:Ut()}),!0)}],accept:{inviteID:Yt},reject:{inviteID:Yt},getSignalingInfo:[{name:"message",...jt,validator:(e,t,s)=>!Ct(e)||(Nt({api:t,paramName:s,desc:Kt()}),!1)}],modifyInvitation:{inviteID:Yt,data:Yt}},es={login:1,logout:1,on:1,once:1,off:1,setLogLevel:1,registerPlugin:1,destroy:1,isReady:1,createTextMessage:1,createTextAtMessage:1,createImageMessage:1,createAudioMessage:1,createVideoMessage:1,createCustomMessage:1,createFaceMessage:1,createFileMessage:1,createLocationMessage:1,createMergerMessage:1,downloadMergerMessage:1,createForwardMessage:1,sendMessage:1,resendMessage:1,revokeMessage:1,deleteMessage:1,translateText:1,convertVoiceToText:1,modifyMessage:1,searchCloudMessages:1,sendMessageReadReceipt:1,getGroupMessageReadMemberList:1,getMessageReadReceiptList:1,setMessageExtensions:1,getMessageExtensions:1,deleteMessageExtensions:1,getMessageList:1,findMessage:1,getMessageListHopping:1,setMessageRead:1,setAllMessageRead:1,getConversationList:1,getConversationProfile:1,deleteConversation:1,setConversationDraft:1,pinConversation:1,getTotalUnreadMessageCount:1,setConversationCustomData:1,markConversation:1,createConversationGroup:1,getConversationGroupList:1,deleteConversationGroup:1,renameConversationGroup:1,addConversationsToGroup:1,deleteConversationsFromGroup:1,clearHistoryMessage:1,getGroupList:1,getGroupProfile:1,createGroup:1,joinGroup:1,updateGroupProfile:1,quitGroup:1,dismissGroup:1,changeGroupOwner:1,searchGroupByID:1,setMessageRemindType:1,getGroupApplicationList:1,handleGroupApplication:1,initGroupAttributes:1,setGroupAttributes:1,deleteGroupAttributes:1,getGroupAttributes:1,setGroupCounters:1,increaseGroupCounter:1,decreaseGroupCounter:1,getGroupCounters:1,getJoinedCommunityList:1,createTopicInCommunity:1,deleteTopicFromCommunity:1,updateTopicProfile:1,getTopicList:1,getGroupMemberProfile:1,getGroupMemberList:1,addGroupMember:1,deleteGroupMember:1,setGroupMemberNameCard:1,setGroupMemberMuteTime:1,setGroupMemberRole:1,setGroupMemberCustomField:1,getGroupOnlineMemberCount:1,markGroupMemberList:1,getMyProfile:1,getUserProfile:1,updateMyProfile:1,setSelfStatus:1,getUserStatus:1,subscribeUserStatus:1,unsubscribeUserStatus:1,getBlacklist:1,addToBlacklist:1,removeFromBlacklist:1,getFriendList:1,addFriend:1,deleteFriend:1,checkFriend:1,updateFriend:1,getFriendProfile:1,getFriendApplicationList:1,refuseFriendApplication:1,deleteFriendApplication:1,acceptFriendApplication:1,setFriendApplicationRead:1,getFriendGroupList:1,createFriendGroup:1,renameFriendGroup:1,deleteFriendGroup:1,addToFriendGroup:1,removeFromFriendGroup:1,callExperimentalAPI:1,addSignalingListener:1,removeSignalingListener:1,invite:1,inviteSync:1,inviteInGroup:1,inviteInGroupSync:1,cancel:1,accept:1,reject:1,getSignalingInfo:1,modifyInvitation:1},ts=1,ss=2,os=3,is=4,ns=6,rs=7,as=8,cs=10,us=11,ls=12,ds=13,ps=14,hs=15,gs=17,_s=18,ms=19,Ms=20,fs=21,Is=22,Cs=23,ys=24,Ts=25,vs=26,Ds=27,Ss=28,Ns=29,Es=30,As=31,Ls=32,Os=33,Rs=function(e){return{code:0,data:e||{}}};class Us extends Error{constructor(e){super();const{code:t,message:s,data:o}=e;this.code=t,this.message=s||this._getErrorMessage(this.code),this.data=o||{}}}const ks={NO_SDKAPPID:2e3,NO_ACCOUNT_TYPE:2001,NO_IDENTIFIER:2002,NO_USERSIG:2003,NO_TINYID:2022,NO_A2KEY:2023,USER_NOT_LOGGED_IN:2024,REPEAT_LOGIN:2025,COS_UNDETECTED:2040,COS_GET_SIG_FAIL:2041,MSG_SEND_FAIL:2100,MSG_SEND_FAIL_NOT_IN_AVCHATROOM:2101,MSG_INSTANCE_REQUIRED:2105,MSG_INVALID_CONV_TYPE:2106,MSG_F_IS_EMPTY:2108,MSG_ONPROGRESS_FUNCTION_ERROR:2109,MSG_REVOKE_FAIL:2110,MSG_DELETE_FAIL:2111,MSG_UNREAD_ALL_FAIL:2112,READ_RECEIPT_MSG_LIST_EMPTY:2114,MSG_SEND_GRP_WITH_TOPIC_FAIL:2115,CANNOT_DELETE_GRP_SYSTEM_NOTICE:2116,TRANSLATE_TEXT_FAIL:2117,VOICE_TO_TEXT_FAIL:2118,UNSUPPORTED_VOICE_FORMAT:2119,MSG_I_SELECT_F_FIRST:2251,MSG_I_TYPES_LIMIT:2252,MSG_I_SIZE_LIMIT:2253,MSG_A_UPLOAD_FAIL:2300,MSG_A_SIZE_LIMIT:2301,MSG_V_UPLOAD_FAIL:2350,MSG_V_SIZE_LIMIT:2351,MSG_V_TYPES_LIMIT:2352,MSG_F_UPLOAD_FAIL:2400,MSG_F_SELECT_F_FIRST:2401,MSG_F_SIZE_LIMIT:2402,MSG_F_URL_IS_EMPTY:2403,MSG_MERGER_TYPE_INVALID:2450,MSG_MERGER_KEY_INVALID:2451,MSG_MERGER_DOWNLOAD_FAIL:2452,MSG_FORWARD_TYPE_INVALID:2453,MSG_MODIFY_CONFLICT:2480,MSG_MODIFY_DISABLED_IN_AVCHATROOM:2481,CONV_NOT_FOUND:2500,USER_OR_GRP_NOT_FOUND:2501,CONV_UN_RECORDED_TYPE:2502,INVALID_CONV_ID:2503,ILLEGAL_GRP_TYPE:2600,CANNOT_JOIN_WORK:2601,ILLEGAL_GRP_ID:2602,CANNOT_FIND_GRP:2603,CANNOT_CHANGE_OWNER_IN_AVCHATROOM:2620,CANNOT_CHANGE_OWNER_TO_SELF:2621,CANNOT_DISMISS_WORK:2622,MEMBER_NOT_IN_GRP:2623,JOIN_GRP_FAIL:2660,CANNOT_ADD_MEMBER_IN_AVCHATROOM:2661,CANNOT_JOIN_NON_AVCHATROOM_WITHOUT_LOGIN:2662,NOT_OWNER:2681,CANNOT_SET_MEMBER_ROLE_IN_WORK_AND_AVCHATROOM:2682,INVALID_MEMBER_ROLE:2683,CANNOT_SET_SELF_MEMBER_ROLE:2684,CANNOT_MUTE_SELF:2685,BAN_DURATION_INVALID:2686,OPERATION_NOT_SUPPORTED_IN_AVCHATROOM:2687,NOT_MY_FRIEND:2700,ALREADY_MY_FRIEND:2701,FRIEND_GRP_EXISTED:2710,FRIEND_GRP_NOT_EXIST:2711,FRIEND_APPLICATION_NOT_EXIST:2716,UPDATE_PROFILE_INVALID_PARAM:2721,UPDATE_PROFILE_NO_KEY:2722,CANNOT_ADD_SELF_TO_BLACKLIST:2742,NETWORK_ERROR:2800,NETWORK_TIMEOUT:2801,NO_NETWORK:2805,UNCAUGHT_ERROR:2903,INVALID_OPERATION:2905,INVALID_TRTC_CMD:2995,OVER_FREQUENCY_LIMIT:2996,CANNOT_FIND_PROTOCOL:2997,CANNOT_FIND_MODULE:2998,SDK_IS_NOT_READY:2999,LOGGING_IN:3e3,LOGIN_FAILED:3001,KICKED_OUT_MULT_DEVICE:3002,KICKED_OUT_MULT_ACCOUNT:3003,KICKED_OUT_USERSIG_EXPIRED:3004,LOGGED_OUT:3005,KICKED_OUT_REST_API:3006,ILLEGAL_TOPIC_ID:3021,CANNOT_USE_COMMERCIAL_ABILITY:3122,PROFANITY_FOUND:3123,OPTIONS_IS_EMPTY:3153,MSG_A2KEY_EXPIRED:20002,ACCOUNT_A2KEY_EXPIRED:70001,HELLO_ANSWER_KICKED_OUT:1002,OPEN_SERVICE_OVERLOAD_ERROR:60022,SIGNALING_INVALID_INVITE_ID:8010,SIGNALING_NO_PERMISSION:8011,SIGNALING_ALREADY_EXISTS:8012,INVALID_CANCEL_MESSAGE:8020,SEND_MESSAGE_FAILED_WITH_CANCEL:8021,ERR_SVR_COMM_INVALID_SERVICE:60020,MSG_SEARCH_CURSOR_INVALID:27002,MSG_SEARCH_CURSOR_EXPIRED:27003};let Ps=null;const Gs=function(e){Ps=e},ws=function(e){return Promise.resolve(Rs(e))},bs=function(t,s=!1){if(t instanceof Us)return s&&null!==Ps&&Ps.emit(e.ERROR,t),Promise.reject(t);if(t instanceof Error){const t=new Us({code:ks.UNCAUGHT_ERROR});return s&&null!==Ps&&Ps.emit(e.ERROR,t),Promise.reject(t)}if(Re(t)||Re(t.code))return Promise.reject(new Us({code:ks.UNCAUGHT_ERROR}));const o=new Us(t);return s&&null!==Ps&&Ps.emit(e.ERROR,o),Promise.reject(o)};class Fs{constructor(e){this._m=e,this._n=""}isLoggedIn(){return this._m.getModule(ls).isLoggedIn()}isOversea(){return this._m.getModule(ls).isOversea()}isPrivateNetWork(){return this._m.getModule(ls).isPrivateNetWork()}getFileDownloadProxy(){return this._m.getModule(ls).getFileDownloadProxy()}getMyUserID(){return this._m.getModule(ls).getUserID()}getMyTinyID(){return this._m.getModule(ls).getTinyID()}getSDKAppID(){return this._m.getModule(ls).getSDKAppID()}isIntl(){return this._m.getModule(ls).isIntl()}isDevMode(){return this._m.getModule(ls).isDevMode()}getModule(e){return this._m.getModule(e)}getPlatform(){return F}getNetworkType(){return this._m.getModule(hs).getNetworkType()}probeNetwork(e){return this._m.getModule(hs).probe(e)}getCloudConfig(e){return this._m.getModule(Cs).getCloudConfig(e)}emitOuterEvent(e,t){this._m.getOuterEmitterInstance().emit(e,t)}emitInnerEvent(e,t){this._m.getInnerEmitterInstance().emit(e,t)}getInnerEmitterInstance(){return this._m.getInnerEmitterInstance()}generateTjgID(e){return this._m.getModule(ls).getTinyID()+"-"+e.random}filterModifiedMessage(t){if(Ct(t))return;const s=t.filter(e=>!0===e.isModified);s.length>0&&this.emitOuterEvent(e.MESSAGE_MODIFIED,s)}filterUnmodifiedMessage(e){if(Ct(e))return[];return e.filter(e=>!1===e.isModified)}request(e){return this._m.getModule(Ms).request(e)}canIUse(e){return this._m.getModule(Ds).canIUse(e)}getErrorMessage(e,t,s){return this._m.getErrorMessage(e,t,s)}outputWarning(e,t,s){const o=this.getErrorMessage(e,t,s);o&&me.w(o)}cannotUseCommercialAbility(e){const t=ks.CANNOT_USE_COMMERCIAL_ABILITY;return bs({code:t,message:this.getErrorMessage(t,e)})}}const $s="wslogin",qs="wslogout",xs="wshello",Vs="KickOther",Ks="getmsg",Bs="sendmsg",Hs="send_group_msg",Ws="portrait_get_all",Ys="portrait_set",zs="black_list_get",js="black_list_add",Js="black_list_delete",Xs="friend_get",Qs="friend_get_specified",Zs="friend_check",eo="friend_delete",to="friend_add",so="friend_update",oo="friend_response",io="pendency_get",no="pendency_delete",ro="pendency_report",ao="group_get",co="group_add",uo="group_delete",lo="group_update",po="msgwithdraw",ho="msgreaded",go="set_c2c_peer_mute_notifications",_o="get_c2c_peer_mute_notifications",mo="getroammsg",Mo="get_peer_read_time",fo="delete_c2c_msg_ramble",Io="modify_c2c_msg",Co="set_key_values",yo="get_key_values",To="page_get",vo="batch_delete",Do="clear_msg",So="top",No="deletemsg",Eo="set_conv_custom_data",Ao="mark_contact",Lo="create_contact_group",Oo="del_contact_group",Ro="update_contact_group",Uo="add_conv_to_group",ko="del_conv_from_group",Po="get_contact_group",Go="get_joined_group_list",wo="get_group_self_member_info",bo="create_group",Fo="destroy_group",$o="modify_group_base_info",qo="apply_join_group",xo="apply_join_group_noauth",Vo="quit_group",Ko="get_group_public_info",Bo="change_group_owner",Ho="handle_apply_join_group",Wo="handle_invite_join_permission_group",Yo="handle_invite_join_group",zo="group_msg_recall",jo="msg_read_report",Jo="read_all_unread_msg",Xo="group_msg_get",Qo="get_group_msg_receipt",Zo="group_msg_receipt",ei="c2c_msg_read_receipt",ti="get_group_msg_receipt_detail",si="get_pendency",oi="deletemsg",ii="get_msg",ni="get_msg_noauth",ri="get_online_member_num",ai="delete_group_ramble_msg_by_seq",ci="modify_group_msg",ui="set_group_attr",li="modify_group_attr",di="delete_group_attr",pi="clear_group_attr",hi="get_group_attr",gi="group_set_key_values",_i="group_get_key_values",mi="batch_get_group_notify",Mi="update_group_counter",fi="get_group_counter",Ii="get_group_member_info",Ci="get_members",yi="get_specified_group_member_info",Ti="add_group_member",vi="delete_group_member",Di="ban_group_member",Si="modify_group_member_info",Ni="modify_user_info",Ei="cos",Ai="pre_sig",Li="video_cover",Oi="tim_web_report_v2",Ri="alive",Ui="msg_push",ki="query",Pi="multi_msg_push_ws",Gi="ws_msg_push_ack",wi="stat_forceoffline",bi="save_relay_json_msg",Fi="get_relay_json_msg",$i="fetch_config",qi="push_configv2",xi="fetch_imsdk_purchase_bitsv2",Vi="push_imsdk_purchase_bitsv2",Ki="notify2",Bi="create_topic",Hi="destroy_topic",Wi="modify_topic",Yi="get_topic",zi="ws_set_custom_status",ji="ws_get_user_status",Ji="ws_status_subscribe",Xi="ws_status_unsubscribe",Qi="ws_stat_background",Zi="ws_stat_foreground",en="ws_stat_settoken",tn="get_local_words",sn="ws_batch_trans_text",on="ws_sentence_recognition",nn="networkRTT",rn="messageE2EDelay",an="sendMessageC2C",cn="sendMessageGroup",un="sendMessageGroupAV",ln="sendMessageRichMedia",dn="cosUpload",pn="messageReceivedGroup",hn="messageReceivedGroupAVPush",gn="messageReceivedGroupAVPull",_n={[nn]:2,[rn]:3,[an]:4,[cn]:5,[un]:6,[ln]:7,[pn]:8,[hn]:9,[gn]:10,[dn]:11},mn={info:4,warning:5,error:6},Mn={wifi:1,"2g":2,"3g":3,"4g":4,"5g":5,unknown:6,none:7,online:8},fn={login:4};class In{constructor(e){this._n="SSOLogData",this.eventType=fn[e]||0,this.timestamp=0,this.networkType=8,this.code=0,this.message="",this.moreMessage="",this.extension=e,this.costTime=0,this.duplicate=!1,this.level=4,this.uiPlatform=void 0,this._sentFlag=!1,this._startts=le()}static bindEventStatModule(e){In.prototype._eventStatModule=e}updateTimeStamp(){this.timestamp=le()}start(e){return this._startts=e,this}end(e=!1){if(this._sentFlag)return;const t=le();0===this.costTime&&(this.costTime=t-this._startts),this.setMoreMessage(`startts:${this._startts} endts:${t}`),e?(this._sentFlag=!0,this._eventStatModule&&this._eventStatModule.pushIn(this)):setTimeout(()=>{this._sentFlag=!0,this._eventStatModule&&this._eventStatModule.pushIn(this)},0)}setError(e,t,s){if(!(e instanceof Error))return me.w(this._n+".setError value not instanceof Error, please check!"),this;if(this._sentFlag)return this;if(this.setNetworkType(s),t)e.code&&this.setCode(e.code),e.message&&this.setMoreMessage(e.message);else{const e=ks.NO_NETWORK;this.setCode(e)}return this.setLevel("error"),this}setCode(e){return Re(e)||this._sentFlag||("ECONNABORTED"===e&&(this.code=103),Ne(e)?this.code=e:me.w(this._n+".setCode value not a number, please check!",e,typeof e)),this}setMessage(e){return Re(e)||this._sentFlag||(Ne(e)&&(this.message=e.toString()),Ee(e)&&(this.message=e)),this}setCostTime(e){return this.costTime=e,this}setLevel(e){return Re(e)||this._sentFlag||(this.level=mn[e]),this}setMoreMessage(e){return Ct(this.moreMessage)?this.moreMessage=""+e:this.moreMessage+=" "+e,this}setNetworkType(e){if(Re(e))me.w(this._n+".setNetworkType value is undefined, please check!");else{const t=Mn[e.toLowerCase()];Re(t)||(this.networkType=t)}return this}getStartTs(){return this._startts}setUIPlatform(e){this.uiPlatform=e}}class Cn{constructor(e){this.type=t.MSG_TEXT,this.content={text:e.text||""}}setText(e){this.content.text=e}sendable(){return 0!==this.content.text.length}}class yn{constructor(e,s){this._imageMemoryURL="",this._fileDownloadProxy=s,U?this.createImageDataASURLInWXMiniApp(e.file):this.createImageDataASURLInWeb(e.file),this._initImageInfoModel(),this.type=t.MSG_IMAGE,this._percent=0,this.content={imageFormat:e.imageFormat||Me.UNKNOWN,uuid:e.uuid,imageInfoArray:[]},this.initImageInfoArray(e.imageInfoArray),this._autoFixUrl()}_initImageInfoModel(){const e=this;this._ImageInfoModel=function(t){this.instanceID=xe(9999999),this.sizeType=t.type||0,this.type=0,this.size=t.size||0,this.width=t.width||0,this.height=t.height||0,this.imageUrl=t.url||"",this.url=Mt(t.url||e._imageMemoryURL,e._fileDownloadProxy)},this._ImageInfoModel.prototype={setSizeType(e){this.sizeType=e},setType(e){this.type=e},setImageUrl(e){e&&(this.imageUrl=e)},getImageUrl(){return this.imageUrl}}}initImageInfoArray(e){let t=0,s=null,o=null;for(;t<=2;)o=Re(e)||Re(e[t])?{type:0,size:0,width:0,height:0,url:""}:e[t],s=new this._ImageInfoModel(o),s.setSizeType(t+1),s.setType(t),this.addImageInfo(s),t++;this.updateAccessSideImageInfoArray()}updateImageInfoArray(e){const t=this.content.imageInfoArray.length;let s;for(let o=0;o<t;o++)s=this.content.imageInfoArray[o],e[o].size&&(s.size=e[o].size),e[o].url&&s.setImageUrl(e[o].url),e[o].width&&(s.width=e[o].width),e[o].height&&(s.height=e[o].height)}_autoFixUrl(){const e=this.content.imageInfoArray.length;let t="",s="";const o=["http","https"];let i=null;for(let n=0;n<e;n++)this.content.imageInfoArray[n].url&&(i=this.content.imageInfoArray[n],""!==i.imageUrl&&(s=i.imageUrl.slice(0,i.imageUrl.indexOf("://")+1),t=i.imageUrl.slice(i.imageUrl.indexOf("://")+1),o.indexOf(s)<0&&(s="https:"),this.content.imageInfoArray[n].setImageUrl([s,t].join(""))))}updatePercent(e){this._percent=e,this._percent>1&&(this._percent=1)}updateImageFormat(e){this.content.imageFormat=Me[e.toUpperCase()]||Me.UNKNOWN}createImageDataASURLInWeb(e){void 0!==e&&e.files.length>0&&(this._imageMemoryURL=window.URL.createObjectURL(e.files[0]))}createImageDataASURLInWXMiniApp(e){e&&e.url&&(this._imageMemoryURL=e.url)}replaceImageInfo(e,t){this.content.imageInfoArray[t]instanceof this._ImageInfoModel||(this.content.imageInfoArray[t]=e)}addImageInfo(e){this.content.imageInfoArray.length>=3||this.content.imageInfoArray.push(e)}updateAccessSideImageInfoArray(){const e=this.content.imageInfoArray,{width:t=0,height:s=0}=e[0];0!==t&&0!==s&&(at(e),Object.assign(e[2],rt({originWidth:t,originHeight:s,min:720})))}sendable(){return 0!==this.content.imageInfoArray.length&&(""!==this.content.imageInfoArray[0].imageUrl&&0!==this.content.imageInfoArray[0].size)}}class Tn{constructor(e){this.type=t.MSG_FACE,this.content=e||null}sendable(){return null!==this.content}}class vn{constructor(e,s){this.type=t.MSG_AUDIO,this._percent=0,this.content={downloadFlag:2,second:e.second,size:e.size,url:Mt(e.url,s),remoteAudioUrl:e.url||"",uuid:e.uuid}}updatePercent(e){this._percent=e,this._percent>1&&(this._percent=1)}updateAudioUrl(e){this.content.remoteAudioUrl=e}sendable(){return""!==this.content.remoteAudioUrl}}const Dn={from:!0,groupID:!0,groupName:!0,to:!0};class Sn{constructor(e){this.type=t.MSG_GRP_TIP,this.content={},this._initContent(e)}_initContent(e){Object.keys(e).forEach(t=>{switch(t){case"remarkInfo":break;case"groupProfile":this.content.groupProfile={},this._initGroupProfile(e[t]);break;case"operatorInfo":break;case"memberInfoList":case"msgMemberInfo":this._updateMemberList(e[t]);break;case"memberExtraInfo":case"onlineMemberInfo":break;case"memberNum":this.content[t]=e[t],this.content.memberCount=e[t];break;case"newGroupProfile":this.content.newGroupProfile={},this._initNewGroupProfile(e[t]);break;default:this.content[t]=e[t]}}),this.content.userIDList||(this.content.userIDList=[this.content.operatorID])}_initGroupProfile(e){const t=Object.keys(e);for(let s=0;s<t.length;s++){const o=t[s];Dn[o]&&(this.content.groupProfile[o]=e[o])}}_updateMemberList(e){Ct(this.content.memberList)?this.content.memberList=e:this.content.memberList.forEach(t=>{e.forEach(e=>{t.userID===e.userID&&Object.assign(t,e)})})}_initNewGroupProfile(e){const t=Object.keys(e);for(let s=0;s<t.length;s++){const o=t[s];"muteAllMembers"!==o?this.content.newGroupProfile[o]=e[o]:this.content.newGroupProfile[o]=1===e[o]}}}const Nn={from:!0,groupID:!0,groupName:!0,to:!0};class En{constructor(e){this.type=t.MSG_GRP_SYS_NOTICE,this.content={},this._initContent(e)}_initContent(e){Object.keys(e).forEach(t=>{switch(t){case"memberInfoList":break;case"remarkInfo":this.content.handleMessage=e[t];break;case"groupProfile":this.content.groupProfile={},this._initGroupProfile(e[t]);break;default:this.content[t]=e[t]}})}_initGroupProfile(e){const t=Object.keys(e);for(let s=0;s<t.length;s++){const o=t[s];Nn[o]&&("groupName"===o?this.content.groupProfile.name=e[o]:this.content.groupProfile[o]=e[o])}}}class An{constructor(e,s){this.type=t.MSG_FILE,this._percent=0;const o=this._getFileInfo(e);this.content={downloadFlag:2,fileUrl:Mt(e.url,s)||"",uuid:e.uuid,fileName:o.name||"",fileSize:o.size||0}}_getFileInfo(e){if(!Re(e.fileName)&&!Re(e.fileSize))return{size:e.fileSize,name:e.fileName};const t=e.file.files[0];if(O){if(t.path&&-1!==t.path.indexOf(".")){const e=t.path.slice(t.path.lastIndexOf(".")+1).toLowerCase();t.type=e,t.name||(t.name=`${xe(999999)}.${e}`)}t.name||(t.type="",t.name=t.path.slice(t.path.lastIndexOf("/")+1).toLowerCase()),t.suffix&&(t.type=t.suffix),t.url||(t.url=t.path)}return{size:t.size,name:t.name}}updatePercent(e){this._percent=e,this._percent>1&&(this._percent=1)}updateFileUrl(e){this.content.fileUrl=e}sendable(){return""!==this.content.fileUrl&&(""!==this.content.fileName&&0!==this.content.fileSize)}}class Ln{constructor(e){this.type=t.MSG_CUSTOM,this.content={data:e.data||"",description:e.description||"",extension:e.extension||""}}setData(e){return this.content.data=e,this}setDescription(e){return this.content.description=e,this}setExtension(e){return this.content.extension=e,this}sendable(){return 0!==this.content.data.length||0!==this.content.description.length||0!==this.content.extension.length}}class On{constructor(e,s){this.type=t.MSG_VIDEO,this._percent=0,this.content={remoteVideoUrl:e.remoteVideoUrl||e.videoUrl||"",videoFormat:e.videoFormat,videoSecond:parseInt(e.videoSecond,10),videoSize:e.videoSize,videoUrl:Mt(e.videoUrl,s),videoDownloadFlag:2,videoUUID:e.videoUUID,thumbUUID:e.thumbUUID,thumbFormat:e.thumbFormat,thumbWidth:e.thumbWidth,snapshotWidth:e.thumbWidth,thumbHeight:e.thumbHeight,snapshotHeight:e.thumbHeight,thumbSize:e.thumbSize,snapshotSize:e.thumbSize,thumbDownloadFlag:2,thumbUrl:Mt(e.thumbUrl,s),snapshotUrl:Mt(e.thumbUrl,s)}}updatePercent(e){this._percent=e,this._percent>1&&(this._percent=1)}updateVideoUrl(e){e&&(this.content.remoteVideoUrl=e)}updateSnapshotInfo(e){const{snapshotUrl:t,snapshotWidth:s,snapshotHeight:o}=e;Ct(t)||(this.content.thumbUrl=this.content.snapshotUrl=t),Ct(s)||(this.content.thumbWidth=this.content.snapshotWidth=Number(s)),Ct(o)||(this.content.thumbHeight=this.content.snapshotHeight=Number(o))}sendable(){return""!==this.content.remoteVideoUrl}}class Rn{constructor(e){this.type=t.MSG_LOCATION;const{description:s,longitude:o,latitude:i}=e;this.content={description:s,longitude:o,latitude:i}}sendable(){return!0}}class Un{constructor(e,s){if(this.from=e.from,this.messageSender=e.from,this.time=e.time,this.messageSequence=e.sequence,this.clientSequence=e.clientSequence||e.sequence,this.messageRandom=e.random,this.cloudCustomData=e.cloudCustomData||"",this.clientTime=e.clientTime||void 0,e.ID)this.ID=e.ID||"",this.nick=e.nick||"",this.avatar=e.avatar||"",this.messageBody=[{type:e.type,payload:e.payload}],e.conversationType.startsWith(t.CONV_C2C)?this.receiverUserID=e.to:e.conversationType.startsWith(t.CONV_GROUP)&&(this.receiverGroupID=e.to),this.messageReceiver=e.to;else{this.nick=e.nick||"",this.avatar=e.avatar||"",this.messageBody=[];const o=e.elements[0].type,i=e.elements[0].content;this._patchRichMediaPayload(o,i),this._updateRichMediaDownloadUrl(o,i,s),o===t.MSG_MERGER?this.messageBody.push({type:o,payload:new kn(i).content}):this.messageBody.push({type:o,payload:i}),e.groupID&&(this.receiverGroupID=e.groupID,this.messageReceiver=e.groupID),e.to&&(this.receiverUserID=e.to,this.messageReceiver=e.to),this.ID=`${e.tinyID}-${e.clientTime}-${e.random}`}}_patchRichMediaPayload(e,s){e===t.MSG_IMAGE?s.imageInfoArray.forEach(e=>{!e.imageUrl&&e.url&&(e.imageUrl=e.url,e.sizeType=e.type,1===e.type?e.type=0:3===e.type&&(e.type=1))}):e===t.MSG_VIDEO?!s.remoteVideoUrl&&s.videoUrl&&(s.remoteVideoUrl=s.videoUrl):e===t.MSG_AUDIO?!s.remoteAudioUrl&&s.url&&(s.remoteAudioUrl=s.url):e===t.MSG_FILE&&!s.fileUrl&&s.url&&(s.fileUrl=s.url,s.url=void 0)}_updateRichMediaDownloadUrl(e,s,o){o&&(e===t.MSG_IMAGE?s.imageInfoArray.forEach(e=>{e.url=Mt(e.url,o)}):e===t.MSG_VIDEO?(s.videoUrl=Mt(s.videoUrl,o),s.snapshotUrl=Mt(s.thumbUrl,o),s.snapshotHeight=s.thumbHeight,s.snapshotWidth=s.thumbWidth):e===t.MSG_AUDIO?s.url=Mt(s.url,o):e===t.MSG_FILE&&(s.fileUrl=Mt(s.fileUrl,o)))}}var kn=class{constructor(e,s){if(this.type=t.MSG_MERGER,this.content={downloadKey:"",pbDownloadKey:"",messageList:[],title:"",abstractList:[],compatibleText:"",version:0,layersOverLimit:!1},e.downloadKey){const{downloadKey:t,pbDownloadKey:s,title:o,abstractList:i,compatibleText:n,version:r}=e;this.content.downloadKey=t,this.content.pbDownloadKey=s,this.content.title=o,this.content.abstractList=i,this.content.compatibleText=n,this.content.version=r||0}else if(Ct(e.messageList))1===e.layersOverLimit&&(this.content.layersOverLimit=!0);else{const{messageList:t,title:o,abstractList:i,compatibleText:n,version:r}=e,a=[];t.forEach(e=>{if(!Ct(e)){const t=new Un(e,s);a.push(t)}}),this.content.messageList=a,this.content.title=o,this.content.abstractList=i,this.content.compatibleText=n,this.content.version=r||0}}sendable(){return!Ct(this.content.messageList)||!Ct(this.content.downloadKey)}};const Pn={1:t.MSG_PRIORITY_HIGH,2:t.MSG_PRIORITY_NORMAL,3:t.MSG_PRIORITY_LOW,4:t.MSG_PRIORITY_LOWEST};class Gn{constructor(e){this.ID="",this.conversationID=e.conversationID||null,this.conversationType=e.conversationType||t.CONV_C2C,this.conversationSubType=e.conversationSubType,this.time=e.time||Math.ceil(Date.now()/1e3),this.sequence=e.sequence||0,this.clientSequence=e.clientSequence||e.sequence||0,this.random=e.random||0===e.random?e.random:xe(),this.priority=this._computePriority(e.priority),this.nick=e.nick||"",this.avatar=e.avatar||"",this.isPeerRead=!1,this.nameCard="",this.hasRiskContent=ft(e.checkResult),this._elements=[],this.isPlaceMessage=e.isPlaceMessage||0,this.isRevoked=2===e.isPlaceMessage||8===e.msgFlagBits,this.from=e.from||null,this.to=e.to||null,this.flow="",this.isSystemMessage=e.isSystemMessage||!1,this.protocol=e.protocol||"JSON",this.isResend=!1,this.isRead=!1,this.status=e.status||vt.SUCCESS,this._onlineOnlyFlag=!1,this._groupAtInfoList=[],this._relayFlag=!1,this.atUserList=[],this.cloudCustomData=e.cloudCustomData||"",this.isDeleted=!1,this.isModified=!1,this._isExcludedFromUnreadCount=!(!e.messageControlInfo||1!==e.messageControlInfo.excludedFromUnreadCount),this._isExcludedFromLastMessage=!(!e.messageControlInfo||1!==e.messageControlInfo.excludedFromLastMessage),this.clientTime=e.clientTime||pe()||0,this.senderTinyID=e.senderTinyID||e.tinyID||"",this.readReceiptInfo=e.readReceiptInfo||{readCount:void 0,unreadCount:void 0,isPeerRead:void 0},this.needReadReceipt=!0===e.needReadReceipt||1===e.needReadReceipt,this.version=e.messageVersion||0,this.isBroadcastMessage=e.isBroadcastMessage||!1,this._receiverList=e.receiverList||void 0,this.isSupportExtension=!0===e.isSupportExtension||1===e.isSupportExtension,this.revoker=e.revokerInfo&&e.revokerInfo.revoker||"",this.revokerInfo=e.revokerInfo||{userID:"",nick:"",avatar:""},this.revokeReason=e.revokeReason||"",this.reInitialize(e.currentUser),this.extractGroupInfo(e.groupProfile||null),this.handleGroupAtInfo(e),this.initC2CReadReceiptInfo(e.readReceiptSentByPeer)}get elements(){return this._elements}getElements(){return this._elements}extractGroupInfo(e){if(null===e)return;Ee(e.nick)&&(this.nick=e.nick),Ee(e.avatar)&&(this.avatar=e.avatar);const{messageFromAccountExtraInformation:t}=e;Le(t)&&Ee(t.nameCard)&&(this.nameCard=t.nameCard)}handleGroupAtInfo(e){e.payload&&e.payload.atUserList&&e.payload.atUserList.forEach(e=>{e!==t.MSG_AT_ALL?(this._groupAtInfoList.push({groupAtAllFlag:0,groupAtUserID:e}),this.atUserList.push(e)):(this._groupAtInfoList.push({groupAtAllFlag:1}),this.atUserList.push(t.MSG_AT_ALL))}),Oe(e.groupAtInfo)&&e.groupAtInfo.forEach(e=>{0===e.groupAtAllFlag?this.atUserList.push(e.groupAtUserID):1===e.groupAtAllFlag&&this.atUserList.push(t.MSG_AT_ALL)})}getGroupAtInfoList(){return this._groupAtInfoList}_initProxy(){this._elements[0]&&(this.payload=this._elements[0].content,this.type=this._elements[0].type)}reInitialize(e){e&&(this.status=this.from?vt.SUCCESS:vt.UNSEND,!this.from&&(this.from=e)),this._initFlow(e),this._initSequence(e),this._concatConversationID(e),this.generateMessageID()}isSendable(){return 0!==this._elements.length&&("function"==typeof this._elements[0].sendable&&this._elements[0].sendable())}_initTo(e){this.conversationType===t.CONV_GROUP&&(this.to=e.groupID)}_initSequence(e){0===this.clientSequence&&e&&(this.clientSequence=function(e){if(!e)return!1;if(void 0===He[e]){const t=new Date;let s=("3"+t.getHours()).slice(-2),o=("0"+t.getMinutes()).slice(-2),i=("0"+t.getSeconds()).slice(-2);He[e]=parseInt([s,o,i,"0001"].join("")),s=null,o=null,i=null,me.l("autoIncrementIndex start index:"+He[e])}return He[e]++}(e)),0===this.sequence&&this.conversationType===t.CONV_C2C&&(this.sequence=this.clientSequence)}generateMessageID(){this.from===t.CONV_SYSTEM&&(this.senderTinyID="144115198244471703"),this.ID=`${this.senderTinyID}-${this.clientTime}-${this.random}`}_initFlow(e){""!==e&&(e===this.from?(this.flow="out",this.isRead=!0):this.flow="in")}_concatConversationID(e){const{to:s}=this;let o="";const i=this.conversationType;i!==t.CONV_SYSTEM?(o=i===t.CONV_C2C?e===this.from?s:this.from:this.to,this.conversationID=o?`${i}${o}`:null):this.conversationID=t.CONV_SYSTEM}isElement(e){return e instanceof Cn||e instanceof yn||e instanceof Tn||e instanceof vn||e instanceof An||e instanceof On||e instanceof Sn||e instanceof En||e instanceof Ln||e instanceof Rn||e instanceof kn}setElement(e,s){if(this.isElement(e))return this._elements=[e],void this._initProxy();const o=e=>{if(e.type&&e.content)switch(e.type){case t.MSG_TEXT:this.setTextElement(e.content);break;case t.MSG_IMAGE:this.setImageElement(e.content,s);break;case t.MSG_AUDIO:this.setAudioElement(e.content,s);break;case t.MSG_FILE:this.setFileElement(e.content,s);break;case t.MSG_VIDEO:this.setVideoElement(e.content,s);break;case t.MSG_CUSTOM:this.setCustomElement(e.content);break;case t.MSG_LOCATION:this.setLocationElement(e.content);break;case t.MSG_GRP_TIP:this.setGroupTipElement(e.content);break;case t.MSG_GRP_SYS_NOTICE:this.setGroupSystemNoticeElement(e.content);break;case t.MSG_FACE:this.setFaceElement(e.content);break;case t.MSG_MERGER:this.setMergerElement(e.content,s)}};if(Oe(e))for(let t=0;t<e.length;t++)o(e[t]);else o(e);this._initProxy()}clearElement(){this._elements.length=0}setTextElement(e){const t="string"==typeof e?e:e.text,s=new Cn({text:t});this._elements.push(s)}setImageElement(e,t){const s=new yn(e,t);this._elements.push(s)}setAudioElement(e,t){const s=new vn(e,t);this._elements.push(s)}setFileElement(e,t){const s=new An(e,t);this._elements.push(s)}setVideoElement(e,t){const s=new On(e,t);this._elements.push(s)}setLocationElement(e){const t=new Rn(e);this._elements.push(t)}setCustomElement(e){const t=new Ln(e);this._elements.push(t)}setGroupTipElement(e){let s={};const o=e.operationType;if(Ct(e.memberInfoList)?e.operatorInfo&&(s=e.operatorInfo):o!==t.GRP_TIP_MBR_JOIN&&o!==t.GRP_TIP_MBR_KICKED_OUT&&o!==t.GRP_TIP_MBR_SET_ADMIN&&o!==t.GRP_TIP_MBR_CANCELED_ADMIN||(s=e.memberInfoList[0]),!Ct(e.memberExtraInfo)){const{reason:t}=e.memberExtraInfo;e.msgMemberInfo.forEach(e=>{e.reason=t})}const{nick:i,avatar:n}=s;Ee(i)&&(this.nick=i),Ee(n)&&(this.avatar=n);const r=new Sn(e);this._elements.push(r)}setGroupSystemNoticeElement(e){const t=new En(e);this._elements.push(t)}setFaceElement(e){const t=new Tn(e);this._elements.push(t)}setMergerElement(e,t){const s=new kn(e,t);this._elements.push(s)}setIsRead(e){this.isRead=e}setRelayFlag(e){this._relayFlag=e}getRelayFlag(){return this._relayFlag}_computePriority(e){if(Re(e))return t.MSG_PRIORITY_NORMAL;if(Ee(e)&&-1!==Object.values(Pn).indexOf(e))return e;if(Ne(e)){const t=""+e;if(-1!==Object.keys(Pn).indexOf(t))return Pn[t]}return t.MSG_PRIORITY_NORMAL}setNickAndAvatar(e){const{nick:t,avatar:s}=e;Ee(t)&&(this.nick=t),Ee(s)&&(this.avatar=s)}setNameCard(e){Ee(e)&&(this.nameCard=e)}initC2CReadReceiptInfo(e){this.conversationType===t.CONV_C2C&&!0===this.needReadReceipt&&(this.readReceiptInfo.isPeerRead=1===e)}}class wn extends Fs{constructor(e){super(e),this._n="C2CModule",this._messageFromUnreadDBMap=new Map,this._noticeFromUnreadDBList=[]}onNewC2CMessage(t){const{dataList:s,isInstantMessage:o,C2CRemainingUnreadList:i,C2CPairUnreadList:n,isSyncingEnded:r}=t;me.d(`${this._n}.onNewC2CMessage count:${s.length} isInstantMessage:${o}`);const{conversationOptionsList:a,messageList:c,isUnreadC2CMessage:u}=this._newC2CMessageStoredAndSummary({dataList:s,C2CRemainingUnreadList:i,C2CPairUnreadList:n,isInstantMessage:o});this.filterModifiedMessage(c);this.getModule(us).onNewMessage({conversationOptionsList:a,isInstantMessage:o,isUnreadC2CMessage:u,isSyncingEnded:r});const l=this.filterUnmodifiedMessage(c);o&&l.length>0&&this.emitOuterEvent(e.MESSAGE_RECEIVED,l),c.length=0}_newC2CMessageStoredAndSummary({dataList:e,C2CRemainingUnreadList:s,C2CPairUnreadList:o,isInstantMessage:i}){let n=null;const r=[],a=[],c={},u=this.getModule(vs);let l=!1;const d=this.getModule(us),p=this.getModule(is),h=this.getFileDownloadProxy();for(let _=0,m=e.length;_<m;_++){if(this._isC2CNotice(e[_])){this._noticeFromUnreadDBList.push(e[_].eventArray[0].c2CNotifyMsgArray[0]);continue}const s=e[_];s.currentUser=this.getMyUserID(),s.conversationType=t.CONV_C2C,s.isSystemMessage=!!s.isSystemMessage,(Re(s.nick)||Re(s.avatar))&&(l=!0,me.d(this._n+"._newC2CMessageStoredAndSummary nick or avatar missing!")),n=new Gn(s),n.setElement(s.elements,h),n.setNickAndAvatar({nick:s.nick,avatar:s.avatar});const{conversationID:o}=n;if(i){if(1===this._messageFromUnreadDBMap.get(n.ID))continue;let t=!1;if(n.from!==this.getMyUserID()){const e=d.getLatestMessageSentByPeer(o);if(e){const{nick:s,avatar:o}=e;l?n.setNickAndAvatar({nick:s,avatar:o}):s===n.nick&&o===n.avatar||(t=!0)}}else{const e=d.getLatestMessageSentByMe(o);if(e){const{nick:t,avatar:s}=e;t===n.nick&&s===n.avatar||(d.modifyMessageSentByMe({conversationID:o,latestNick:n.nick,latestAvatar:n.avatar}),p.mockOnNickAvatarModified(n.nick,n.avatar))}}let r=1===e[_].isModified;if(d.isMessageSentByCurrentInstance(n)?n.isModified=r:r=!1,0===s.msgLifeTime)n._onlineOnlyFlag=!0,d.isMessageSentByCurrentInstance(n)||a.push(n);else{if(!d.pushIntoMessageList(a,n,r))continue;t&&(d.modifyMessageSentByPeer({conversationID:o,latestNick:n.nick,latestAvatar:n.avatar}),d.updateUserProfileSpecifiedKey({conversationID:o,nick:n.nick,avatar:n.avatar}))}i&&n.clientTime>0&&u.addMessageDelay(n.clientTime)}else this._messageFromUnreadDBMap.set(n.ID,1);if(0!==s.msgLifeTime){if(!1===n._onlineOnlyFlag){const e=d.getLastMessageTime(o);if(Ne(e)&&n.time<e)continue;if(Re(c[o])){let e=0;"in"===n.flow&&(n._isExcludedFromUnreadCount||(e=1)),c[o]=r.push({conversationID:o,unreadCount:e,type:n.conversationType,subType:n.conversationSubType,lastMessage:n._isExcludedFromLastMessage?"":n})-1}else{const e=c[o];r[e].type=n.conversationType,r[e].subType=n.conversationSubType,r[e].lastMessage=n._isExcludedFromLastMessage?"":n,"in"===n.flow&&(n._isExcludedFromUnreadCount||r[e].unreadCount++)}}}else n._onlineOnlyFlag=!0}this._handleRevokedNoticeFromUnreadDB();let g=!1;if(Oe(o))for(let _=0,m=o.length;_<m;_++)if(o[_].unreadCount>0){g=!0;const e=r.find(({conversationID:e})=>e==="C2C"+o[_].from);e?e.unreadCount=o[_].unreadCount:r.push({conversationID:"C2C"+o[_].from,unreadCount:o[_].unreadCount,type:t.CONV_C2C})}if(Oe(s))for(let _=0,m=s.length;_<m;_++){r.find(({conversationID:e})=>e==="C2C"+s[_].from)||r.push({conversationID:"C2C"+s[_].from,type:t.CONV_C2C,lastMsgTime:s[_].lastMsgTime})}return{conversationOptionsList:r,messageList:a,isUnreadC2CMessage:g}}_isC2CNotice(e){const{eventArray:t}=e;return!(!Oe(t)||10!==t[0].event)}_handleRevokedNoticeFromUnreadDB(){const e=this._noticeFromUnreadDBList.length;if(0===e)return;me.l(`${this._n}._handleRevokedNoticeFromUnreadDB count:${e}`);const t=[];this._noticeFromUnreadDBList.forEach(e=>{e.hasOwnProperty("c2cMessageRevokedNotify")&&t.push(e)}),this.onC2CMessageRevoked({dataList:t}),this._noticeFromUnreadDBList.length=0,t.length=0}onC2CMessageRevoked(e){const s=this.getModule(us),o=[];let i=null,n=!0;e.dataList.forEach(e=>{if(e.c2cMessageRevokedNotify){const{revokedInfos:r}=e.c2cMessageRevokedNotify;Re(r)||r.forEach(e=>{const r=this.getMyUserID()===e.from?`${t.CONV_C2C}${e.to}`:`${t.CONV_C2C}${e.from}`;i=s.revoke(r,e.sequence,e.random);const a=e.revokerInfo&&e.revokerInfo.revoker,c=e.revokerInfo&&e.revokerInfo.reason;if(i)i.revokeReason=c||"",i.revoker||(i.revoker=a,o.push(i));else{const t={conversationID:r,sequence:e.sequence,time:e.time,revoker:a};s.isLastMessageRevoked(t)&&(o.push(t),n=!1)}})}}),0!==o.length&&(s.onMessageRevoked(o),!0===n&&(me.l(`${this._n}.onC2CMessageRevoked count:${o.length}`),s.emitMessageRevokedEvent(o)))}onC2CMessageReadReceipt(e){e.dataList.forEach(e=>{if(!Ct(e.c2cMessageReadReceipt)){const{to:s}=e.c2cMessageReadReceipt;e.c2cMessageReadReceipt.uinPairReadArray.forEach(e=>{const{peerReadTime:o}=e;me.d(`${this._n}._onC2CMessageReadReceipt to:${s} peerReadTime:${o}`);const i=`${t.CONV_C2C}${s}`,n=this.getModule(us);n.recordPeerReadTime(i,o),n.updateMessageIsPeerReadProperty(i,o)})}})}onC2CMessageReadNotice(e){e.dataList.forEach(e=>{if(!Ct(e.c2cMessageReadNotice)){const s=this.getModule(us);e.c2cMessageReadNotice.uinPairReadArray.forEach(e=>{const{from:o,peerReadTime:i}=e;me.d(`${this._n}.onC2CMessageReadNotice from:${o} lastReadTime:${i}`);const n=`${t.CONV_C2C}${o}`;s.updateIsReadAfterReadReport({conversationID:n,lastMessageTime:i}),s.updateUnreadCount(n)})}})}onC2CMessageModified(e){me.d(this._n+".onC2CMessageModified options:",JSON.stringify(e));const s=this.getModule(us);e.dataList.forEach(e=>{s.onMessageModified({...e,conversationType:t.CONV_C2C})})}onReadReceiptList(e){me.d(this._n+".onReadReceiptList options:",JSON.stringify(e));const{userID:t,readReceiptList:s}=e.dataList;this.getModule(us).updateReadReceiptInfo({userID:t,readReceiptList:s})}sendMessage(e,t){const s=this._createC2CMessagePack(e,t);return this.request(s)}_createC2CMessagePack(e,t){let s=null;t&&(t.offlinePushInfo&&(s=t.offlinePushInfo),!0===t.onlineUserOnly&&(s?s.disablePush=!0:s={disablePush:!0}));let o="";Ee(e.cloudCustomData)&&e.cloudCustomData.length>0&&(o=e.cloudCustomData);const i=[];if(Le(t)&&Le(t.messageControlInfo)){const{excludedFromUnreadCount:e,excludedFromLastMessage:s,excludedFromContentModeration:o}=t.messageControlInfo;!0===e&&i.push("NoUnread"),!0===s&&i.push("NoLastMsg"),!0===o&&i.push("NoMsgCheck")}const n=this.isOnlineMessage(e,t)?0:void 0;return{protocolName:Bs,tjgID:this.generateTjgID(e),requestData:{fromAccount:this.getMyUserID(),toAccount:e.to,msgBody:e.getElements(),cloudCustomData:o,msgSeq:e.sequence,msgRandom:e.random,msgLifeTime:n,nick:e.nick,avatar:e.avatar,offlinePushInfo:s?{pushFlag:!0===s.disablePush?1:0,title:s.title||"",desc:s.description||"",ext:s.extension||"",apnsInfo:{badgeMode:!0===s.ignoreIOSBadge?1:0,isVoipPush:this._isVoipPush(s)},androidInfo:{OPPOChannelID:s.androidOPPOChannelID||""}}:void 0,messageControlInfo:0!==n?i:void 0,clientTime:e.clientTime,needReadReceipt:!0===e.needReadReceipt?1:0,isSupportExtension:!0===e.isSupportExtension?1:0,isRelayMessage:!0===e._relayFlag?1:0}}}_isVoipPush(e){let t=void 0;return Re(e.disableVoipPush)||(t=!1===e.disableVoipPush?1:0),t}isOnlineMessage(e,t){return!(!t||!0!==t.onlineUserOnly)}revokeMessage(e){return this.request({protocolName:po,requestData:{msgInfo:{fromAccount:e.from,toAccount:e.to,msgSeq:e.sequence,msgRandom:e.random,msgTimeStamp:e.time}}})}deleteMessage(e){const{to:t,keyList:s}=e;return me.l(`${this._n}.deleteMessage toAccount:${t} count:${s.length}`),this.request({protocolName:fo,requestData:{fromAccount:this.getMyUserID(),to:t,keyList:s}})}modifyRemoteMessage(e){const{from:s,to:o,version:i=0,sequence:n,random:r,time:a,payload:c,type:u,cloudCustomData:l}=e;let d=void 0;return function(e){return e===t.MSG_TEXT||e===t.MSG_CUSTOM||e===t.MSG_LOCATION||e===t.MSG_FACE}(u)&&(d=[],d.push({type:u,content:c})),this.request({protocolName:Io,requestData:{from:s,to:o,version:i,sequence:n,random:r,time:a,elements:d,cloudCustomData:l}})}setMessageRead({conversationID:e,lastMessageTime:t}){const s=this._n+".setMessageRead";me.l(`${s} conversationID:${e} lastMessageTime:${t}`),Ne(t)||this.outputWarning("DoNotModifyLastTime");const o=new In("setC2CMessageRead");return o.setMessage(`conversationID:${e} lastMessageTime:${t}`),this.request({protocolName:ho,requestData:{C2CMsgReaded:{cookie:"",C2CMsgReadedItem:[{toAccount:e.replace("C2C",""),lastMessageTime:t,receipt:1}]}}}).then(()=>{o.setNetworkType(this.getNetworkType()).end(),me.l(s+" ok");const i=this.getModule(us);return i.updateIsReadAfterReadReport({conversationID:e,lastMessageTime:t}),i.updateUnreadCount(e),Rs()}).catch(e=>(this.probeNetwork().then(([t,s])=>{o.setError(e,t,s).end()}),me.l(s+" failed. error:",e),bs(e)))}getRoamingMessage(e){const s=this._n+".getRoamingMessage",{peerAccount:o,conversationID:i,count:n,lastMessageTime:r,messageKey:a}=e,c=`peerAccount:${o} count:${n||15} lastMessageTime:${r||0} messageKey:${a}`;me.l(`${s} ${c}`);const u=new In("getC2CRoamingMessages");return this.request({protocolName:mo,requestData:{peerAccount:o,count:n||15,lastMessageTime:r||0,messageKey:a}}).then(e=>{const{complete:o,messageList:n,messageKey:r,lastMessageTime:a}=e.data;Re(n)?me.l(`${s} ok. complete:${o} but messageList is undefined!`):me.l(`${s} ok. complete:${o} count:${n.length}`),u.setNetworkType(this.getNetworkType()).setMessage(`${c} complete:${o} length:${n.length}`).end();const l=this.getModule(us);1===o&&l.setCompleted(i);const d=l.onRoamingMessage(n,i);l.modifyMessageList(i),l.updateIsRead(i),l.updateRoamingMessageKeyAndTime(i,r,a);const p=l.getPeerReadTime(i);if(me.l(`${s} update isPeerRead property. conversationID:${i} peerReadTime:${p}`),p)l.updateMessageIsPeerReadProperty(i,p);else{const e=i.replace(t.CONV_C2C,"");this.getRemotePeerReadTime([e]).then(()=>{l.updateMessageIsPeerReadProperty(i,l.getPeerReadTime(i))})}let h="";if(d.length>0)h=d[0].ID;else{const e=l.getLocalOldestMessage(i);e&&(h=e.ID)}return me.l(`${s} nextReqID:${h} stored message count:${d.length}`),{nextReqID:h,storedMessageList:d}}).catch(e=>(this.probeNetwork().then(([t,s])=>{u.setMessage(c).setError(e,t,s).end()}),me.w(s+" failed. error:",e),bs(e)))}getRoamingMessagesHopping(e){const s=this._n+".getRoamingMessagesHopping",{peerAccount:o,time:i=0,count:n,direction:r}=e,a=`${t.CONV_C2C}${o}`,c=`peerAccount:${o} count:${n} time:${i} direction:${r}`;me.l(`${s} ${c}`);const u=new In("getC2CRoamingMessagesHopping");return this.request({protocolName:mo,requestData:{peerAccount:o,count:n+1,lastMessageTime:i,direction:r}}).then(e=>{const{complete:t,messageList:o=[],lastMessageTime:i}=e.data;me.l(`${s} ok. complete:${t} count:${o.length}`),u.setNetworkType(this.getNetworkType()).setMessage(`${c} complete:${t} length:${o.length}`).end(),1!==t&&(1===r?o.pop():o.shift());const n=this.getModule(us).onRoamingMessage(o,a,!1);this._modifyMessageList(a,n);const l=this._computeResult({complete:t,lastMessageTime:i,resultList:n});return Rs(l)}).catch(e=>(this.probeNetwork().then(([t,s])=>{u.setMessage(c).setError(e,t,s).end()}),me.w(s+" failed. error:",e),bs(e)))}_computeResult(e){const{complete:t=0,lastMessageTime:s,resultList:o=[]}=e,i={messageList:[...o],isCompleted:!1,nextMessageTime:""};return 1===t?(i.isCompleted=!0,i):(i.nextMessageTime=s,i)}_modifyMessageList(e,t){const s=this.getModule(us).getLocalConversation(e);if(!s)return;const o=s.userProfile.nick,i=s.userProfile.avatar,n=this.getModule(is).getNickAndAvatarByUserID(this.getMyUserID()),r=n.nick,a=n.avatar;for(let c=t.length-1;c>=0;c--){const e=t[c];"in"===e.flow&&(e.nick!==o&&e.setNickAndAvatar({nick:o}),e.avatar!==i&&e.setNickAndAvatar({avatar:i})),"out"===e.flow&&(e.nick!==r&&e.setNickAndAvatar({nick:r}),e.avatar!==a&&e.setNickAndAvatar({avatar:a}))}}getRemotePeerReadTime(e){const t=this._n+".getRemotePeerReadTime";if(Ct(e))return me.w(t+" userIDList is empty!"),Promise.resolve();const s=new In("getPeerReadTime");return me.l(`${t} userIDList:${e}`),this.request({protocolName:Mo,requestData:{userIDList:e}}).then(o=>{const{peerReadTimeList:i}=o.data;me.l(`${t} ok. peerReadTimeList:${i}`);let n="";const r=this.getModule(us);for(let t=0;t<e.length;t++)n+=`${e[t]}-${i[t]} `,i[t]>0&&r.recordPeerReadTime("C2C"+e[t],i[t]);s.setNetworkType(this.getNetworkType()).setMessage(n).end()}).catch(e=>{this.probeNetwork().then(([t,o])=>{s.setError(e,t,o).end()}),me.w(t+" failed. error:",e)})}sendReadReceipt(e){const s=e[0].conversationID.replace(t.CONV_C2C,""),o=new In("sendC2CReadReceipt");o.setMessage("peerAccount:"+s);const i=this.getMyUserID(),n=e.filter(e=>e.from!==i&&!0===e.needReadReceipt).map(e=>{const{from:t,to:s,sequence:o,random:i,time:n,clientTime:r}=e;return{fromAccount:t,toAccount:s,sequence:o,random:i,time:n,clientTime:r}});if(0===n.length)return bs({code:ks.READ_RECEIPT_MSG_LIST_EMPTY});const r=this._n+".sendReadReceipt";return me.l(`${r}. peerAccount:${s} messageInfoList length:${n.length}`),this.request({protocolName:ei,requestData:{peerAccount:s,messageInfoList:n}}).then(e=>(o.end(),me.l(r+" ok"),Rs())).catch(e=>(this.probeNetwork().then(([t,s])=>{o.setError(e,t,s).end()}),me.w(r+" failed. error:",e),bs(e)))}getReadReceiptList(e){const t=this._n+".getReadReceiptList",s=this.getMyUserID(),o=e.filter(e=>e.from===s&&!0===e.needReadReceipt);return me.l(`${t} userID:${s} messageList length:${o.length}`),ws({messageList:o})}getMessageExtensions(e,t){return me.l(`${this._n}.getMessageExtensions startSequence:${t}`),this.request({protocolName:yo,requestData:{from:e.from,to:e.to,messageKey:this._getMessageKey(e),startSequence:t}})}modifyMessageExtensions(e,t,s=1){return me.l(`${this._n}.modifyMessageExtensions operateType:${s}`),this.request({protocolName:Co,requestData:{from:e.from,to:e.to,messageKey:this._getMessageKey(e),extensionList:t,operateType:s}})}_getMessageKey(e){const{clientSequence:t,random:s,time:o}=e;return`${t}_${s}_${o}`}reset(){me.l(this._n+".reset"),this._messageFromUnreadDBMap.clear(),this._noticeFromUnreadDBList.length=0}}class bn{constructor(){this.list=new Map,this._n="MessageListHandler",this._latestMessageSentByPeerMap=new Map,this._latestMessageSentByMeMap=new Map}getLocalOldestMessageByConversationID(e){if(!e)return null;if(!this.list.has(e))return null;const t=this.list.get(e).values();return t?t.next().value:null}pushIn(e,t=!1){const{conversationID:s}=e;let o=!0;this.list.has(s)||this.list.set(s,new Map);const i=this._getUniqueIDOfMessage(e);if(this.list.get(s).has(i)){const e=this.list.get(s).get(i);if(!t||!0===e.isModified)return o=!1,o}return this.list.get(s).set(i,e),this._setLatestMessageSentByPeer(s,e),this._setLatestMessageSentByMe(s,e),o}unshift(e,s){let o;if(Oe(e)?e.length>0&&(o=e[0].conversationID,this._unshiftMultipleMessages(e,s)):(o=e.conversationID,this._unshiftSingleMessage(e,s)),o){const e=Array.from(this.list.get(o).values()),s=e.length;if(0===s)return;for(let t=s-1;t>=0;t--)if("out"===e[t].flow){this._setLatestMessageSentByMe(o,e[t]);break}if(o.startsWith(t.CONV_C2C))for(let t=s-1;t>=0;t--)if("in"===e[t].flow){this._setLatestMessageSentByPeer(o,e[t]);break}}}_unshiftSingleMessage(e,t){const{conversationID:s}=e,o=this._getUniqueIDOfMessage(e);if(!this.list.has(s))return this.list.set(s,new Map),this.list.get(s).set(o,e),void t.push(e);const i=this.list.get(s),n=Array.from(i);i.has(o)||(n.unshift([o,e]),this.list.set(s,new Map(n)),t.push(e))}_unshiftMultipleMessages(e,t){const s=e.length,o=[],i=e[0].conversationID,n=this.list.get(i),r=this.list.has(i)?Array.from(n):[];for(let a=0;a<s;a++){const s=this._getUniqueIDOfMessage(e[a]);n&&n.has(s)||(o.push([s,e[a]]),t.push(e[a]))}this.list.set(i,new Map(o.concat(r)))}remove(e){const{conversationID:t}=e,s=this._getUniqueIDOfMessage(e);this.list.has(t)&&this.list.get(t).delete(s)}revoke(e,t,s){if(me.d("revoke message",e,t,s),this.list.has(e)){const o=this.list.get(e);for(const[,e]of o)if(e.sequence===t&&(Re(s)||e.random===s))return e.isRevoked||(e.isRevoked=!0),e}return null}removeByConversationID(e){this.list.has(e)&&(this.list.delete(e),this._latestMessageSentByPeerMap.delete(e),this._latestMessageSentByMeMap.delete(e))}findMessage(e){let t=null;for(const[,s]of this.list){const o=[...s.values()],i=o.length;for(let s=0;s<i;s++)if(o[s].ID===e){t=o[s];break}}return t}updateMessageIsPeerReadProperty(e,t){const s=[];if(this.list.has(e)){const o=this.list.get(e);for(const[,e]of o)e.time<=t&&!e.isPeerRead&&"out"===e.flow&&(e.isPeerRead=!0,s.push(e));me.l(`${this._n}.updateMessageIsPeerReadProperty conversationID:${e} peerReadTime:${t} count:${s.length}`)}return s}updateMessageIsModifiedProperty(e){const{conversationID:t}=e;if(!this.list.has(t))return;const s=this._getUniqueIDOfMessage(e),o=this.list.get(t).get(s);o&&(o.isModified=!0)}hasLocalMessageList(e){return this.list.has(e)}getLocalMessageList(e){return this.hasLocalMessageList(e)?[...this.list.get(e).values()]:[]}hasLocalMessage(e,t){let s=!1;const o=this.getLocalMessageList(e),i=o.length;for(let n=0;n<i;n++)o[n].ID===t&&(s=!0);return s}getLocalMessage(e,t){let s=null;const o=this.getLocalMessageList(e),i=o.length;for(let n=0;n<i;n++)if(o[n].ID===t){s=o[n];break}return s}getLocalLastMessage(e){const t=this.getLocalMessageList(e);return t[t.length-1]}getLocalOldestMessage(e){return this.getLocalMessageList(e)[0]}_setLatestMessageSentByPeer(e,s){e.startsWith(t.CONV_C2C)&&"in"===s.flow&&this._latestMessageSentByPeerMap.set(e,s)}_setLatestMessageSentByMe(e,t){"out"===t.flow&&this._latestMessageSentByMeMap.set(e,t)}getLatestMessageSentByPeer(e){return this._latestMessageSentByPeerMap.get(e)}getLatestMessageSentByMe(e){return this._latestMessageSentByMeMap.get(e)}modifyMessageSentByPeer(e){const{conversationID:t,latestNick:s,latestAvatar:o}=e,i=this.list.get(t);if(Ct(i))return;const n=Array.from(i.values()),r=n.length;if(0===r)return;let a=null,c=0,u=!1;for(let l=r-1;l>=0;l--)"in"===n[l].flow&&(a=n[l],a.nick!==s&&(a.setNickAndAvatar({nick:s}),u=!0),a.avatar!==o&&(a.setNickAndAvatar({avatar:o}),u=!0),u&&(c+=1));me.l(`${this._n}.modifyMessageSentByPeer conversationID:${t} count:${c}`)}modifyMessageSentByMe(e){const{conversationID:t,latestNick:s,latestAvatar:o}=e,i=this.list.get(t);if(Ct(i))return;const n=Array.from(i.values()),r=n.length;if(0===r)return;let a=null,c=0,u=!1;for(let l=r-1;l>=0;l--)"out"===n[l].flow&&(a=n[l],a.nick!==s&&(a.setNickAndAvatar({nick:s}),u=!0),a.avatar!==o&&(a.setNickAndAvatar({avatar:o}),u=!0),u&&(c+=1));me.l(`${this._n}.modifyMessageSentByMe conversationID:${t} count:${c}`)}getTopicConversationIDList(e){return[...this.list.keys()].filter(s=>s.startsWith(`${t.CONV_GROUP}${e}`))}traversal(){if(0!==this.list.size&&-1===me.getLevel()){console.group("conversationID-messageCount");for(const[e,t]of this.list)console.log(`${e}-${t.size}`);console.groupEnd()}}onMessageModified(e,t){if(!this.list.has(e))return{isUpdated:!1,message:null};const s=this._getUniqueIDOfMessage(t),o=this.list.get(e).has(s);if(me.d(`${this._n}.onMessageModified conversationID:${e} uniqueID:${s} has:${o}`),o){const o=this.list.get(e).get(s),{messageVersion:i,elements:n,cloudCustomData:r,checkResult:a}=t;return o.version<i?(o.version=i,o._elements=JSON.parse(JSON.stringify(n)),o.payload=JSON.parse(JSON.stringify(n[0].content)),o.type=n[0].type,o.cloudCustomData=r,o.isModified=!0,o.hasRiskContent=ft(a),{isUpdated:!0,message:o}):{isUpdated:!1,message:o}}return{isUpdated:!1,message:null}}_getUniqueIDOfMessage(e){const{from:t,to:s,random:o,sequence:i,time:n}=e;return`${t}-${s}-${o}-${i}-${n}`}reset(){this.list.clear(),this._latestMessageSentByPeerMap.clear(),this._latestMessageSentByMeMap.clear()}}const Fn={A2KEY_AND_TINYID_UPDATED:"_inner1",CLOUD_CONFIG_UPDATED:"_inner2",PROFILE_UPDATED:"_inner3",CONV_SYNC_COMPLETED:"_inner4",C2C_UNREAD_HANDLE_COMPLETED:"_inner5"};function $n(e){this.mixin(e)}$n.mixin=function(e){const t=e.prototype||e;t._isReady=!1,t.ready=function(e,t=!1){if(e)return this._isReady?void(t?e.call(this):setTimeout(e,1)):(this._readyQueue=this._readyQueue||[],void this._readyQueue.push(e))},t.triggerReady=function(){this._isReady=!0,setTimeout(()=>{const e=this._readyQueue;this._readyQueue=[],e&&e.length>0&&e.forEach((function(e){e.call(this)}),this)},1)},t.resetReady=function(){this._isReady=!1,this._readyQueue=[]},t.isReady=function(){return this._isReady}};const qn=["jpg","jpeg","gif","png","bmp","image","webp"],xn=["mp4","quicktime","mov"],Vn=1,Kn=2,Bn=3,Hn=255;class Wn{constructor(e){Ct(e)||(this.userID=e.userID||"",this.nick=e.nick||"",this.gender=e.gender||"",this.birthday=e.birthday||0,this.location=e.location||"",this.selfSignature=e.selfSignature||"",this.allowType=e.allowType||t.ALLOW_TYPE_ALLOW_ANY,this.language=e.language||0,this.avatar=e.avatar||"",this.messageSettings=e.messageSettings||0,this.adminForbidType=e.adminForbidType||t.FORBID_TYPE_NONE,this.level=e.level||0,this.role=e.role||0,this.lastUpdatedTime=0,this.profileCustomField=[],Ct(e.profileCustomField)||e.profileCustomField.forEach(e=>{this.profileCustomField.push({key:e.key,value:e.value})}))}validate(e){let t=!0,s="";if(Ct(e))return{valid:!1,tips:"empty options"};if(e.profileCustomField){const t=e.profileCustomField.length;let s=null;for(let o=0;o<t;o++){if(s=e.profileCustomField[o],!Ee(s.key)||-1===s.key.indexOf("Tag_Profile_Custom"))return{valid:!1,tips:"The prefix of keys of the custom profile key-value pairs (which is profileCustomField) must be Tag_Profile_Custom"};if(!Ee(s.value))return{valid:!1,tips:"The type of values of the custom profile key-value pairs (which is profileCustomField) must be String"}}}for(const o in e)if(Object.prototype.hasOwnProperty.call(e,o)){if("profileCustomField"===o)continue;if(Ct(e[o])&&!Ee(e[o])&&!Ne(e[o])){s="key:"+o+", invalid value:"+e[o],t=!1;continue}switch(o){case"nick":Ee(e[o])||(s="nick must be a string",t=!1),qe(e[o])>500&&(s=`nick name limited: must less than or equal to 500 bytes, current size: ${qe(e[o])} bytes`,t=!1);break;case"gender":Be(Ie,e.gender)||(s="key:gender, invalid value:"+e.gender,t=!1);break;case"birthday":Ne(e.birthday)||(s="birthday must be a number",t=!1);break;case"location":Ee(e.location)||(s="location must be a string",t=!1);break;case"selfSignature":Ee(e.selfSignature)||(s="selfSignature must be a string",t=!1);break;case"allowType":Be(ye,e.allowType)||(s="key:allowType, invalid value:"+e.allowType,t=!1);break;case"language":Ne(e.language)||(s="language must be a number",t=!1);break;case"avatar":Ee(e.avatar)||(s="avatar must be a string",t=!1);break;case"messageSettings":0!==e.messageSettings&&1!==e.messageSettings&&(s="messageSettings must be 0 or 1",t=!1);break;case"adminForbidType":Be(Ce,e.adminForbidType)||(s="key:adminForbidType, invalid value:"+e.adminForbidType,t=!1);break;case"level":Ne(e.level)||(s="level must be a number",t=!1);break;case"role":Ne(e.role)||(s="role must be a number",t=!1);break;default:s="unknown key:"+o+"  "+e[o],t=!1}}return{valid:t,tips:s}}}class Yn{constructor(e){this.value=e,this.next=null}}class zn{constructor(e){this.MAX_LENGTH=e,this.pTail=null,this.pNodeToDel=null,this.map=new Map}set(e){const t=new Yn(e);if(this.map.size<this.MAX_LENGTH)null===this.pTail?(this.pTail=t,this.pNodeToDel=t):(this.pTail.next=t,this.pTail=t),this.map.set(e,1);else{let s=this.pNodeToDel;this.pNodeToDel=this.pNodeToDel.next,this.map.delete(s.value),s.next=null,s=null,this.pTail.next=t,this.pTail=t,this.map.set(e,1)}}has(e){return this.map.has(e)}delete(e){this.has(e)&&this.map.delete(e)}tail(){return this.pTail}size(){return this.map.size}data(){return Array.from(this.map.keys())}reset(){let e;for(;null!==this.pNodeToDel;)e=this.pNodeToDel,this.pNodeToDel=this.pNodeToDel.next,e.next=null,e=null;this.pTail=null,this.map.clear()}}const jn=["groupID","name","avatar","type","introduction","notification","ownerID","selfInfo","createTime","infoSequence","lastInfoTime","lastMessage","nextMessageSeq","memberNum","maxMemberNum","memberList","joinOption","groupCustomField","muteAllMembers","isSupportTopic","inviteOption","_lastRevokedTime"];class Jn{constructor(e){this.groupID="",this.name="",this.avatar="",this.type="",this.introduction="",this.notification="",this.ownerID="",this.createTime="",this.infoSequence="",this.lastInfoTime="",this.selfInfo={messageRemindType:"",joinTime:"",nameCard:"",role:"",userID:"",memberCustomField:void 0,readedSequence:0,excludedUnreadSequenceList:void 0},this.lastMessage={lastTime:"",lastSequence:"",fromAccount:"",messageForShow:""},this.nextMessageSeq="",this.memberNum="",this.memberCount="",this.maxMemberNum="",this.maxMemberCount="",this.joinOption="",this.inviteOption="",this.groupCustomField=[],this.muteAllMembers=!1,this.isSupportTopic=!1,this._lastRevokedTime=0,this._initGroup(e)}set memberNum(e){}set maxMemberNum(e){}get memberNum(){return this.memberCount}get maxMemberNum(){return this.maxMemberCount}_initGroup(e){for(const t in e)jn.indexOf(t)<0||("selfInfo"!==t?("memberNum"===t&&(this.memberCount=e[t]),"maxMemberNum"===t&&(this.maxMemberCount=e[t]),"isSupportTopic"!==t?this[t]=e[t]:this.isSupportTopic=1===e[t]):this.updateSelfInfo(e[t]))}updateGroup(e){e.appid=void 0,e.grossTopicNextMsgSeq=void 0,e.selfInfo&&(e.selfInfo.grossTopicReadSeq=void 0);const t=JSON.parse(JSON.stringify(e));t.lastMsgTime&&(this.lastMessage.lastTime=t.lastMsgTime),Re(t.muteAllMembers)||("On"===t.muteAllMembers?t.muteAllMembers=!0:t.muteAllMembers=!1),t.groupCustomField&&Ye(this.groupCustomField,t.groupCustomField),Re(t.memberNum)||(this.memberCount=t.memberNum),Re(t.maxMemberNum)||(this.maxMemberCount=t.maxMemberNum),Re(t.isSupportTopic)||(this.isSupportTopic=Ne(t.isSupportTopic)?1===t.isSupportTopic:t.isSupportTopic),Fe(this,t,["members","errorCode","lastMsgTime","groupCustomField","memberNum","maxMemberNum","isSupportTopic"]),Oe(t.members)&&t.members.length>0&&t.members.forEach(e=>{e.userID===this.selfInfo.userID&&Fe(this.selfInfo,e,["sequence"])})}updateSelfInfo({nameCard:e,joinTime:t,role:s,messageRemindType:o,readedSequence:i,excludedUnreadSequenceList:n}){const r={nameCard:e,joinTime:t,role:s,messageRemindType:o,readedSequence:i,excludedUnreadSequenceList:n};Fe(this.selfInfo,{...r},[],["",null,void 0,0,NaN])}setSelfNameCard(e){this.selfInfo.nameCard=e}}const Xn=function(e,t){return Re(e)?{lastTime:0,lastSequence:0,fromAccount:0,messageForShow:"",payload:null,type:"",isRevoked:!1,cloudCustomData:"",onlineOnlyFlag:!1,nick:"",nameCard:"",version:0,isPeerRead:!1,revoker:null}:e instanceof Gn?{lastTime:e.time||0,lastSequence:e.sequence||0,fromAccount:e.from||"",messageForShow:ht(e.type,e.payload,t),payload:e.payload||null,type:e.type||null,isRevoked:e.isRevoked||!1,cloudCustomData:e.cloudCustomData||"",onlineOnlyFlag:e._onlineOnlyFlag||!1,nick:e.nick||"",nameCard:e.nameCard||"",version:e.version||0,isPeerRead:e.isPeerRead||!1,revoker:e.revoker||null}:{...e,messageForShow:ht(e.type,e.payload,t)}};class Qn{constructor(e,t){this.conversationID=e.conversationID||"",this.unreadCount=e.unreadCount||0,this.type=e.type||"",this.lastMessage=Xn(e.lastMessage,t),e.lastMsgTime&&(this.lastMessage.lastTime=e.lastMsgTime),this._isInfoCompleted=!1,this.peerReadTime=e.peerReadTime||0,this.groupAtInfoList=[],this.remark="",this.isPinned=e.isPinned||!1,this.messageRemindType=e.messageRemindType,this.markList=e.markList||[],this.customData=e.customData||"",this.conversationGroupList=e.conversationGroupList||[],this.draftText=e.draftText||"",this._initProfile(e)}get toAccount(){return this.conversationID.startsWith(t.CONV_C2C)?this.conversationID.replace(t.CONV_C2C,""):this.conversationID.startsWith(t.CONV_GROUP)?this.conversationID.replace(t.CONV_GROUP,""):""}get subType(){return this.groupProfile?this.groupProfile.type:""}_initProfile(e){Object.keys(e).forEach(t=>{switch(t){case"userProfile":this.userProfile=e.userProfile;break;case"groupProfile":this.groupProfile=e.groupProfile}}),Re(this.userProfile)&&this.type===t.CONV_C2C?this.userProfile=new Wn({userID:e.conversationID.replace("C2C","")}):Re(this.groupProfile)&&this.type===t.CONV_GROUP&&(this.groupProfile=new Jn({groupID:e.conversationID.replace("GROUP","")}))}updateUnreadCount(e){const{nextUnreadCount:s,isFromGetConversations:o,isUnreadC2CMessage:i}=e;Re(s)||(ze(this.subType)?this.unreadCount=0:o&&this.type===t.CONV_GROUP||o&&this.type===t.CONV_TOPIC||i&&this.type===t.CONV_C2C?this.unreadCount=s:this.unreadCount=this.unreadCount+s)}updateLastMessage(e){this.lastMessage=Xn(e)}updateGroupAtInfoList(e){if(this._isNeedMergeGroupAtInfo(e))return;let[...s]=e.groupAtType;-1!==s.indexOf(t.CONV_AT_ME)&&-1!==s.indexOf(t.CONV_AT_ALL)&&(s=[t.CONV_AT_ALL_AT_ME]);const o={from:e.from,groupID:e.groupID,topicID:e.topicID,messageSequence:e.sequence,atTypeArray:s,__random:e.__random,__sequence:e.__sequence};this.groupAtInfoList.push(o)}_isNeedMergeGroupAtInfo(e){const{groupID:s,sequence:o}=e;if(!je({groupID:s}))return!1;let i=!1;return this.groupAtInfoList.forEach(s=>{s.messageSequence===o&&(s.atTypeArray.indexOf(t.CONV_AT_ME)>-1&&e.groupAtType.indexOf(t.CONV_AT_ALL)>-1&&(s.atTypeArray=[t.CONV_AT_ALL_AT_ME]),s.atTypeArray.indexOf(t.CONV_AT_ALL)>-1&&e.groupAtType.indexOf(t.CONV_AT_ME)>-1&&(s.atTypeArray=[t.CONV_AT_ALL_AT_ME],s.__random=e.__random,s.__sequence=e.__sequence),i=!0)}),i}clearGroupAtInfoList(){this.groupAtInfoList.length=0}reduceUnreadCount(){return this.unreadCount>=1&&(this.unreadCount-=1,!0)}isLastMessageRevoked({sequence:e,time:s}){return this.type===t.CONV_C2C&&e===this.lastMessage.lastSequence&&s===this.lastMessage.lastTime||this.type===t.CONV_GROUP&&e===this.lastMessage.lastSequence}setLastMessageRevoked(e){this.lastMessage.isRevoked=e}setLastMessageRevoker(e){this.lastMessage.revoker=e}setDraftText(e){this.draftText=e}}class Zn{constructor(e){this._conversationModule=e,this._n="MessageRemindHandler"}getC2CMessageRemindType(e){const t=this._n+".getC2CMessageRemindType";return this._conversationModule.request({protocolName:_o,requestData:{toAccount:this._conversationModule.getMyUserID(),userIDList:e}}).then(s=>{me.l(`${t} ok. userIDList:${e}`);const{muteFlagList:o}=s.data;this._conversationModule.onC2CMessageRemindTypeFetched(o)}).catch(e=>{me.e(t+" failed. error:",e)})}set(e){return e.groupID?this._setGroupMessageRemindType(e):Oe(e.userIDList)?this._setC2CMessageRemindType(e):void 0}_setGroupMessageRemindType(e){const t=this._n+"._setGroupMessageRemindType",{groupID:s,messageRemindType:o}=e,i=`groupID:${s} messageRemindType:${o}`,n=new In("setMessageRemindType");n.setMessage(i);const r=this._getModule(rs);return r?r.modifyGroupMemberInfo({groupID:s,messageRemindType:o,userID:this._conversationModule.getMyUserID()}).then(()=>{n.setNetworkType(this._conversationModule.getNetworkType()).end(),me.l(`${t} ok. ${i}`);const s=this.onGroupMessageRemindTypeUpdated(e);return this._conversationModule.emitTotalUnreadMessageCountUpdate(),Rs(s)}).catch(e=>(this._conversationModule.probeNetwork().then(([t,s])=>{n.setError(e,t,s).end()}),me.e(t+" failed. error:",e),bs(e))):bs({code:ks.CANNOT_FIND_MODULE})}onGroupMessageRemindTypeUpdated(t){const{groupID:s,messageRemindType:o}=t;me.l(`${this._n}.onGroupMessageRemindTypeUpdated groupID:${s} messageRemindType:${o}`);const i=this._getModule(rs).getLocalGroupProfile(s);if(i&&(i.selfInfo.messageRemindType=o),Je(s)){const t=s,i=pt(t),n=this._getModule(cs).getLocalTopic(i,t);return n&&(n.updateSelfInfo({messageRemindType:o}),this._conversationModule.emitOuterEvent(e.TOPIC_UPDATED,{groupID:i,topic:n})),{topic:n}}return this._conversationModule.patchMessageRemindType({ID:s,isC2CConversation:!1,messageRemindType:o})&&this._emitConversationUpdate(),{group:i}}_setC2CMessageRemindType(e){const s=this._n+"._setC2CMessageRemindType",{userIDList:o,messageRemindType:i}=e,n=o.slice(0,30);let r=0;i===t.MSG_REMIND_DISCARD?r=1:i===t.MSG_REMIND_ACPT_NOT_NOTE&&(r=2);const a=`userIDList:${n} messageRemindType:${i}`,c=new In("setMessageRemindType");return c.setMessage(a),this._conversationModule.request({protocolName:go,requestData:{userIDList:n,muteFlag:r}}).then(e=>{c.setNetworkType(this._conversationModule.getNetworkType()).end();const{errorList:t}=e.data,o=[],r=[];Oe(t)&&t.forEach(e=>{o.push(e.userID),r.push({userID:e.userID,code:e.errorCode})});const u=n.filter(e=>-1===o.indexOf(e));me.l(`${s} ok. ${a} successUserIDList:${u} failureUserIDList:${JSON.stringify(r)}`);let l=0;return u.forEach(e=>{this._conversationModule.patchMessageRemindType({ID:e,isC2CConversation:!0,messageRemindType:i})&&(l+=1)}),l>=1&&this._emitConversationUpdate(),n.length=o.length=0,this._conversationModule.emitTotalUnreadMessageCountUpdate(),ws({successUserIDList:u.map(e=>({userID:e})),failureUserIDList:r})}).catch(e=>(this._conversationModule.probeNetwork().then(([t,s])=>{c.setError(e,t,s).end()}),me.e(s+" failed. error:",e),bs(e)))}_getModule(e){return this._conversationModule.getModule(e)}_emitConversationUpdate(){this._conversationModule.emitConversationUpdate(!0,!1)}reset(){me.l(this._n+".reset")}}class er{constructor(e){this._conversationModule=e,this._n="ConvGroupHandler",this._convGroupMap=new Map,this._startIndex=0,this._pagingStatus=Dt.NOT_START}setConvCustomData(e){const s=this._n+".setConvCustomData",{conversationIDList:o,customData:i}=e;me.l(s+" options:",e);const n=new In("setConvCustomData");n.setMessage(JSON.stringify(e));const r={fromAccount:this._getMyUserID(),itemList:[]},a=[],c=[];return o.forEach(e=>{if(!this._hasLocalConversation(e))return this._onConversationNotFound(c,e),!0;if(!Xe(e)&&!Qe(e))return this._onConversationIDInvalid(c,e),!0;const s={operationType:2,contactItem:void 0,customMark:i};Xe(e)?s.contactItem={type:1,toAccount:e.replace(t.CONV_C2C,"")}:Qe(e)&&(s.contactItem={type:2,groupID:e.replace(t.CONV_GROUP,"")}),r.itemList.push(s)}),c.length===o.length?ws({successConversationIDList:a,failureConversationIDList:c}):this._conversationModule.request({protocolName:Eo,requestData:r}).then(e=>{n.setNetworkType(this._conversationModule.getNetworkType()).end(),me.l(s+" ok");const{resultItem:t}=e.data;if(Oe(t)){let e,s,o=!1;t.forEach(t=>{e=this._concatConversationID(t.contactItem),0===t.resultCode?(a.push(e),s=this._getLocalConversation(e),s&&s.customData!==i&&(s.customData=i,o=!0)):c.push({conversationID:e,code:t.resultCode,message:t.resultInfo})}),!0===o&&this._emitConversationUpdate()}return Rs({successConversationIDList:a,failureConversationIDList:c})}).catch(e=>(this._conversationModule.probeNetwork().then(([t,s])=>{n.setError(e,t,s).end()}),me.e(s+" failed. error:",e),bs(e)))}markConversation(e){if(!this._conversationModule.canIUse(M.CONV_MARK))return this._conversationModule.cannotUseCommercialAbility("markConversation");const s=this._n+".markConversation",{conversationIDList:o,markType:i,enableMark:n}=e;me.l(s+" options:",e);const r=new In("markConversation");r.setMessage(JSON.stringify(e));let a=void 0,c=void 0;const u=this._getFlagBit(i);!0===n?c=[u]:a=[u];const l={fromAccount:this._getMyUserID(),itemList:[]},d=[],p=[];return o.forEach(e=>{if(!this._hasLocalConversation(e))return this._onConversationNotFound(p,e),!0;if(!Xe(e)&&!Qe(e))return this._onConversationIDInvalid(p,e),!0;const s={operationType:1,contactItem:void 0,clearMark:a,setMark:c};Xe(e)?s.contactItem={type:1,toAccount:e.replace(t.CONV_C2C,"")}:Qe(e)&&(s.contactItem={type:2,groupID:e.replace(t.CONV_GROUP,"")}),l.itemList.push(s)}),p.length===o.length?ws({successConversationIDList:d,failureConversationIDList:p}):this._conversationModule.request({protocolName:Ao,requestData:l}).then(e=>{r.setNetworkType(this._conversationModule.getNetworkType()).end(),me.l(s+" ok");const{resultItem:t}=e.data;if(Oe(t)){let e,s,o=!1;t.forEach(t=>{if(e=this._concatConversationID(t.contactItem),0===t.resultCode){if(d.push(e),s=this._getLocalConversation(e),s){const e=s.markList.indexOf(i);!0===n?-1===e&&(s.markList.push(i),o=!0):-1!==e&&(s.markList.splice(e,1),o=!0)}}else p.push({conversationID:e,code:t.resultCode,message:t.resultInfo})}),!0===o&&this._emitConversationUpdate()}return Rs({successConversationIDList:d,failureConversationIDList:p})}).catch(e=>(this._conversationModule.probeNetwork().then(([t,s])=>{r.setError(e,t,s).end()}),me.e(s+" failed. error:",e),bs(e)))}getLocalConvGroupList(){return me.l(`${this._n}.getLocalConvGroupList pagingStatus:${this._pagingStatus}`),this._pagingStatus===Dt.REJECTED?this.getRemoteConvGroupList().then(()=>Rs([...this._convGroupMap.values()])):ws([...this._convGroupMap.values()])}getRemoteConvGroupList(){const e=this._n+".getRemoteConvGroupList";return this._pagingStatus=Dt.PENDING,this._conversationModule.request({protocolName:Po,requestData:{fromAccount:this._getMyUserID(),startIndex:this._startIndex,startTime:pe()}}).then(t=>{const{completeFlag:s,contactItem:o,nextStartIndex:i=0,groupItem:n}=t.data;if(this._startIndex=i,me.l(`${e} completeFlag:${s} nextStartIndex:${i}`),Oe(n)&&n.forEach(e=>{const{convGroupID:t,groupName:s}=e;this._convGroupMap.set(t,s)}),Oe(o)){let e,t;o.forEach(s=>{const{standardMark:o,customData:i,convGroupIDList:n}=s;if(e=this._concatConversationID(s),t=this._getLocalConversation(e),t&&(t.markList=gt(o),t.customData=i||"",Oe(n))){const e=[];n.forEach(t=>{this._convGroupMap.has(t)&&e.push(this._convGroupMap.get(t))}),t.conversationGroupList=[...e],e.length=0}})}if(0===s)return this.getRemoteConvGroupList();1===s&&(this._pagingStatus=Dt.RESOLVED,this._emitConversationUpdate(),this._emitConvGroupListUpdate())}).catch(t=>{this._pagingStatus=Dt.REJECTED,me.w(e+" failed. error:",t)})}createConvGroup(e){const s="createConversationGroup";if(!this._conversationModule.canIUse(M.CONV_GROUP))return this._conversationModule.cannotUseCommercialAbility(s);const o=`${this._n}.${s}`;me.l(o+" options:",e);const i=new In(s);i.setMessage(JSON.stringify(e));const{groupName:n,conversationIDList:r}=e,a={fromAccount:this._getMyUserID(),itemList:[{groupName:n,contactItem:[]}]},c=[],u=[];return r.forEach(e=>this._hasLocalConversation(e)?Xe(e)||Qe(e)?void(Xe(e)?a.itemList[0].contactItem.push({type:1,toAccount:e.replace(t.CONV_C2C,"")}):Qe(e)&&a.itemList[0].contactItem.push({type:2,groupID:e.replace(t.CONV_GROUP,"")})):(this._onConversationIDInvalid(u,e),!0):(this._onConversationNotFound(u,e),!0)),u.length===r.length?ws({successConversationIDList:c,failureConversationIDList:u}):this._conversationModule.request({protocolName:Lo,requestData:a}).then(e=>{i.setNetworkType(this._conversationModule.getNetworkType()).end(),me.l(o+" ok");const{groupResultItem:t}=e.data,{groupItem:s,resultItem:r}=t[0];if(Le(s)&&(this._convGroupMap.set(s.convGroupID,s.groupName),this._emitConvGroupListUpdate()),Oe(r)){let e,t,s=!1;r.forEach(o=>{e=this._concatConversationID(o.contactItem),0===o.resultCode?(c.push(e),t=this._getLocalConversation(e),t&&-1===t.conversationGroupList.indexOf(n)&&(t.conversationGroupList.push(n),s=!0)):u.push({conversationID:e,code:o.resultCode,message:o.resultInfo})}),!0===s&&(this._emitConversationUpdate(),this._emitConvGroupListUpdate())}return Rs({successConversationIDList:c,failureConversationIDList:u})}).catch(e=>(this._conversationModule.probeNetwork().then(([t,s])=>{i.setError(e,t,s).end()}),me.e(o+" failed. error:",e),bs(e)))}deleteConvGroup(e){const t="deleteConversationGroup";if(!this._conversationModule.canIUse(M.CONV_GROUP))return this._conversationModule.cannotUseCommercialAbility(t);const s=`${this._n}.${t}`;me.l(`${s} groupName:${e}`);const o=new In(t);return o.setMessage(e),this._conversationModule.request({protocolName:Oo,requestData:{fromAccount:this._getMyUserID(),groupName:[e]}}).then(t=>{o.setNetworkType(this._conversationModule.getNetworkType()).end(),me.l(s+" ok");const{groupItem:i}=t.data;if(Oe(i)){let e=!1;i.forEach(t=>{this._convGroupMap.has(t.convGroupID)&&(this._convGroupMap.delete(t.convGroupID),e=!0)}),!0===e&&this._emitConvGroupListUpdate()}this._eraseFromConversationGroupList([e])}).catch(e=>(this._conversationModule.probeNetwork().then(([t,s])=>{o.setError(e,t,s).end()}),me.e(s+" failed. error:",e),bs(e)))}renameConvGroup(e){const t="renameConversationGroup";if(!this._conversationModule.canIUse(M.CONV_GROUP))return this._conversationModule.cannotUseCommercialAbility(t);const s=`${this._n}.${t}`;me.l(s+" options:",e);const o=new In(t);o.setMessage(JSON.stringify(e));const{oldName:i,newName:n}=e;return this._conversationModule.request({protocolName:Ro,requestData:{fromAccount:this._getMyUserID(),updateType:1,updateGroup:{updateGroupType:1,oldName:i,newName:n}}}).then(e=>{o.setNetworkType(this._conversationModule.getNetworkType()).end(),me.l(s+" ok");const{updateGroupResult:t}=e.data,{convGroupID:r}=t;this._convGroupMap.set(r,n),this._emitConvGroupListUpdate();const a=this._conversationModule.getLocalConversationList();let c,u,l=!1;a.forEach(e=>{c=e.conversationGroupList,u=c.indexOf(i),-1!==u&&(c.splice(u,1,n),l=!0)}),!0===l&&this._emitConversationUpdate()}).catch(e=>(this._conversationModule.probeNetwork().then(([t,s])=>{o.setError(e,t,s).end()}),me.e(s+" failed. error:",e),bs(e)))}addConvsToGroup(e){const s="addConversationsToGroup";if(!this._conversationModule.canIUse(M.CONV_GROUP))return this._conversationModule.cannotUseCommercialAbility(s);const o=`${this._n}.${s}`;me.l(o+" options:",e);const i=new In(s);i.setMessage(JSON.stringify(e));const{conversationIDList:n,groupName:r}=e,a={fromAccount:this._getMyUserID(),updateType:1,updateGroup:{updateGroupType:2,groupName:r,updateItem:[]}},c=[],u=[];return n.forEach(e=>this._hasLocalConversation(e)?Xe(e)||Qe(e)?void(Xe(e)?a.updateGroup.updateItem.push({operationType:1,contactItem:{type:1,toAccount:e.replace(t.CONV_C2C,"")}}):Qe(e)&&a.updateGroup.updateItem.push({operationType:1,contactItem:{type:2,groupID:e.replace(t.CONV_GROUP,"")}})):(this._onConversationIDInvalid(u,e),!0):(this._onConversationNotFound(u,e),!0)),u.length===n.length?ws({successConversationIDList:c,failureConversationIDList:u}):this._conversationModule.request({protocolName:Uo,requestData:a}).then(e=>{i.setNetworkType(this._conversationModule.getNetworkType()).end(),me.l(o+" ok");const{contactResultItem:t}=e.data.updateGroupResult;if(Oe(t)){let e,s,o=!1;t.forEach(t=>{e=this._concatConversationID(t.contactItem),0===t.resultCode?(s=this._getLocalConversation(e),s&&-1===s.conversationGroupList.indexOf(r)&&(s.conversationGroupList.push(r),c.push(e),o=!0)):u.push({conversationID:e,code:t.resultCode,message:t.resultInfo})}),!0===o&&(this._emitConversationUpdate(),this._emitConvInGroupUpdate(r))}return Rs({successConversationIDList:c,failureConversationIDList:u})}).catch(e=>(this._conversationModule.probeNetwork().then(([t,s])=>{i.setError(e,t,s).end()}),me.e(o+" failed. error:",e),bs(e)))}deleteConvsFromGroup(e){const s="deleteConversationsFromGroup";if(!this._conversationModule.canIUse(M.CONV_GROUP))return this._conversationModule.cannotUseCommercialAbility(s);const o=`${this._n}.${s}`;me.l(o+" options:",e);const i=new In(s);i.setMessage(JSON.stringify(e));const{conversationIDList:n,groupName:r}=e,a={fromAccount:this._getMyUserID(),updateType:1,updateGroup:{updateGroupType:2,groupName:r,updateItem:[]}},c=[],u=[];return n.forEach(e=>this._hasLocalConversation(e)?Xe(e)||Qe(e)?void(Xe(e)?a.updateGroup.updateItem.push({operationType:2,contactItem:{type:1,toAccount:e.replace(t.CONV_C2C,"")}}):Qe(e)&&a.updateGroup.updateItem.push({operationType:2,contactItem:{type:2,groupID:e.replace(t.CONV_GROUP,"")}})):(this._onConversationIDInvalid(u,e),!0):(this._onConversationNotFound(u,e),!0)),u.length===n.length?ws({successConversationIDList:c,failureConversationIDList:u}):this._conversationModule.request({protocolName:ko,requestData:a}).then(e=>{i.setNetworkType(this._conversationModule.getNetworkType()).end(),me.l(o+" ok");const{contactResultItem:t}=e.data.updateGroupResult;if(Oe(t)){let e,s,o=!1;t.forEach(t=>{if(e=this._concatConversationID(t.contactItem),0===t.resultCode){if(s=this._getLocalConversation(e),s){const t=s.conversationGroupList.indexOf(r);-1!==t&&(s.conversationGroupList.splice(t,1),c.push(e),o=!0)}}else u.push({conversationID:e,code:t.resultCode,message:t.resultInfo})}),!0===o&&(this._emitConversationUpdate(),this._emitConvInGroupUpdate(r))}return Rs({successConversationIDList:c,failureConversationIDList:u})}).catch(e=>(this._conversationModule.probeNetwork().then(([t,s])=>{i.setError(e,t,s).end()}),me.e(o+" failed. error:",e),bs(e)))}onConvMarkUpdated(e){if(Ct(e))return;let t,s;me.d(this._n+".onConvMarkUpdated markItemList:",e);let o=!1;e.forEach(e=>{const{recentContactItem:i,optType:n,standardMark:r,customMark:a}=e;if(t=this._concatConversationID(i),s=this._getLocalConversation(t),s)if(1===n){const e=gt(r);!0!==function(e,t){if(e===t)return!0;if(!e||!t)return!1;if(e.length!==t.length)return!1;for(let s=0,o=e.length;s<o;s++)if(e[s]!==t[s])return!1;return!0}(s.markList,e)&&(s.markList=e,o=!0)}else 2===n&&s.customData!==a&&void 0!==a&&(s.customData=a,o=!0)}),!0===o&&this._emitConversationUpdate()}onConvGroupCreated(e){me.d(this._n+".onConvGroupCreated resultList:",e);let t=!1,s=!1;Oe(e)&&(e.forEach(e=>{const{groupID:o,groupName:i}=e.msgGroupItem;this._convGroupMap.get(o)!==i&&(this._convGroupMap.set(o,i),s=!0);const n=e.msgRecentContactItem;if(Oe(n)){let e,s;n.forEach(o=>{e=this._concatConversationID(o),s=this._getLocalConversation(e),s&&-1===s.conversationGroupList.indexOf(i)&&(s.conversationGroupList.push(i),t=!0)})}}),!0===t&&this._emitConversationUpdate(),!0===s&&this._emitConvGroupListUpdate())}onConvGroupDeleted(e){me.d(this._n+".onConvGroupDeleted groupItemList:",e);const t=[];if(Oe(e)){let s=!1;e.forEach(e=>{const{groupID:o,groupName:i}=e;this._convGroupMap.has(o)&&(this._convGroupMap.delete(o),s=!0,t.push(i))}),!0===s&&this._emitConvGroupListUpdate()}this._eraseFromConversationGroupList(t)}_eraseFromConversationGroupList(e){if(Ct(e))return;this._conversationModule.getLocalConversationList().forEach(t=>{t.conversationGroupList=t.conversationGroupList.filter(t=>!e.includes(t))}),this._emitConversationUpdate()}onConvGroupNameUpdated(e){me.d(this._n+".onConvGroupNameUpdated options:",e);const{groupID:t,groupName:s,oldGroupName:o}=e;if(this._convGroupMap.get(t)===s)return;this._convGroupMap.set(t,s),this._emitConvGroupListUpdate();const i=this._conversationModule.getLocalConversationList();let n,r,a=!1;i.forEach(e=>{n=e.conversationGroupList,r=n.indexOf(o),-1!==r&&(n.splice(r,1,s),a=!0)}),!0===a&&this._emitConversationUpdate()}onConvInGroupUpdated(e){me.d(this._n+".onConvInGroupUpdated options:",e);const{oldGroupName:t,recentContactUpdateGroupItem:s}=e;if(Oe(s)){let e,o,i,n=!1;s.forEach(s=>{const{contactOptType:r,recentContactItem:a}=s;e=this._concatConversationID(a),o=this._getLocalConversation(e),o&&(i=o.conversationGroupList.indexOf(t),1===r?-1===i&&(o.conversationGroupList.push(t),n=!0):2===r&&-1!==i&&(o.conversationGroupList.splice(i,1),n=!0))}),!0===n&&(this._emitConversationUpdate(),this._emitConvInGroupUpdate(t))}}onConvAddedToOrDeletedFromGroup(e){me.d(this._n+".onConvAddedToOrDeletedFromGroup options:",e);const{msgRecentContactItem:t,msgRecentContactUpdateContactItem:s}=e,o=this._concatConversationID(t),i=this._getLocalConversation(o);if(i&&Oe(s)){let e,t=!1;s.forEach(s=>{const{groupOptType:o,recentContactGroupItem:n}=s,{groupName:r}=n;e=i.conversationGroupList.indexOf(r),1===o?-1===e&&(i.conversationGroupList.push(r),t=!0):2===o&&-1!==e&&(i.conversationGroupList.splice(e,1),t=!0),!0===t&&this._emitConvInGroupUpdate(r)}),!0===t&&this._emitConversationUpdate()}}onConvGroupListSynced(e){Oe(e)&&0!==e.length&&(me.l(this._n+".onConvGroupListSynced groupItemList:",e),e.forEach(e=>{this._convGroupMap.set(e.convGroupID,e.groupName)}))}getConvGroupListByID(e){if(Ct(e))return;const t=[];return e.forEach(e=>{this._convGroupMap.has(e)&&t.push(this._convGroupMap.get(e))}),t}_onConversationNotFound(e,t){e.push({conversationID:t,code:ks.CONV_NOT_FOUND,message:this._conversationModule.getErrorMessage(ks.CONV_NOT_FOUND)})}_onConversationIDInvalid(e,t){e.push({conversationID:t,code:ks.INVALID_CONV_ID,message:this._conversationModule.getErrorMessage(ks.INVALID_CONV_ID)})}_getFlagBit(e){const t=e.toString(2),s=t.length;for(let o=s-1;o>=0;o--)if("1"===t[o])return s-o-1}_concatConversationID(e){const{type:s,to:o,groupID:i,userID:n}=e;let r;return 1===s?Re(n)?Re(o)||(r=`${t.CONV_C2C}${o}`):r=`${t.CONV_C2C}${n}`:2===s&&(r=`${t.CONV_GROUP}${i}`),r}_getMyUserID(){return this._conversationModule.getMyUserID()}_insertConversationGroup(e,t){const s=this._getLocalConversation(e);if(s){const e=s.conversationGroupList;-1===e.indexOf(t)&&e.push(t)}}_getLocalConversation(e){return this._conversationModule.getLocalConversation(e)}_hasLocalConversation(e){return this._conversationModule.hasLocalConversation(e)}_emitConversationUpdate(){this._conversationModule.emitConversationUpdate(!0,!1)}_emitConvGroupListUpdate(){this._conversationModule.emitOuterEvent(e.CONVERSATION_GROUP_LIST_UPDATED,[...this._convGroupMap.values()])}_emitConvInGroupUpdate(t){const s={groupName:t,conversationList:[]},o=this._conversationModule.getLocalConversationList();s.conversationList=o.filter(e=>e.conversationGroupList.includes(t)),this._conversationModule.emitOuterEvent(e.CONVERSATION_IN_GROUP_UPDATED,s)}reset(){me.l(this._n+".reset"),this._convGroupMap.clear(),this._startIndex=0,this._pagingStatus=Dt.NOT_START}}class tr extends Fs{constructor(e){super(e),this._n="ConversationModule",$n.mixin(this),this._messageListHandler=new bn,this._messageRemindHandler=new Zn(this),this._convGroupHandler=new er(this),this.singlyLinkedList=new zn(100),this._pagingStatus=Dt.NOT_START,this._pagingTimeStamp=0,this._pagingStartIndex=0,this._pagingPinnedTimeStamp=0,this._pagingPinnedStartIndex=0,this._pagingConvIDMap=new Map,this._convIDFromUnreadDBMap=new Map,this._conversationMap=new Map,this._tmpGroupList=[],this._tmpGroupAtTipsList=[],this._peerReadTimeMap=new Map,this._completedMap=new Map,this._roamingMessageKeyAndTimeMap=new Map,this._roamingMessageSequenceMap=new Map,this._remoteGroupReadSequenceMap=new Map,this._convTotalUnreadCount=0,this._pagingGetCostList=[],this._initListeners()}_initListeners(){const e=this.getInnerEmitterInstance();e.on(Fn.A2KEY_AND_TINYID_UPDATED,this._init,this),e.on(Fn.PROFILE_UPDATED,this._onProfileUpdated,this)}onCheckTimer(e){e%60==0&&this._messageListHandler.traversal()}_init(){me.l(this._n+"._init");const e=this.getModule(ds).getItem("conversationMap"),t=this.isIntl();if(e){const s=e.length;for(let o=0;o<s;o++){const s=e[o];if(s){if(this._isNonExistentAccount(s.conversationID))continue;if(s.groupProfile){const e=s.groupProfile.type;if(ze(e))continue}}this._conversationMap.set(s.conversationID,new Qn(e[o],t))}this.emitConversationUpdate(!0,!1)}this.ready(()=>{this._tmpGroupList.length>0&&(this.updateConversationGroupProfile(this._tmpGroupList),this._tmpGroupList.length=0)}),this.syncConversationList()}_isNonExistentAccount(e){let s;return e.startsWith(t.CONV_C2C)&&(s=e.replace(t.CONV_C2C,"")),"@TLS#ERROR"===s||"@TLS#NOT_FOUND"===s}onMessageSent(e){this._onSendOrReceiveMessage({conversationOptionsList:e.conversationOptionsList,isInstantMessage:!0})}onNewMessage(e){this._onSendOrReceiveMessage(e)}_onSendOrReceiveMessage(e){const{conversationOptionsList:s,isInstantMessage:o=!0,isUnreadC2CMessage:i=!1,updateUnreadCount:n=!0,isSyncingEnded:r=!1}=e;if(!this._isReady)return void this.ready(()=>{this._onSendOrReceiveMessage(e)});if(0===s.length)return void(r&&this.emitInnerEvent(Fn.C2C_UNREAD_HANDLE_COMPLETED));!0===o&&this._checkNewConversation(s),this._updateLocalConversationList({conversationOptionsList:s,isInstantMessage:o,isUnreadC2CMessage:i,isFromGetConversations:!1,updateUnreadCount:n}),o||(this._convIDFromUnreadDBMap=new Map([...this._convIDFromUnreadDBMap,...s.map(e=>[e.conversationID,1])]),this._diffAndDeleteConversation(),r&&this.emitInnerEvent(Fn.C2C_UNREAD_HANDLE_COMPLETED));s.filter(e=>e.type===t.CONV_TOPIC).length>0||this.emitConversationUpdate()}updateConversationGroupProfile(e){if(Oe(e)&&0===e.length)return;if(0===this._conversationMap.size)return void(this._tmpGroupList=e);let t=!1;e.forEach(e=>{const s="GROUP"+e.groupID;if(this._conversationMap.has(s)){t=!0;const o=this._conversationMap.get(s);o.groupProfile=JSON.parse(JSON.stringify(e)),o.lastMessage.lastSequence<e.nextMessageSeq&&(o.lastMessage.lastSequence=e.nextMessageSeq-1),o.subType||(o.subType=e.type)}}),t&&this.emitConversationUpdate(!0,!1)}_updateConversationUserProfile({data:e}){e.forEach(e=>{const t="C2C"+e.userID;this._conversationMap.has(t)&&(this._conversationMap.get(t).userProfile=e)}),this.emitConversationUpdate(!0,!1)}onMessageRevoked(e){if(0===e.length)return;let s=null,o=!1;const i=[];e.forEach(e=>{s=this._conversationMap.get(e.conversationID),s&&(s.type===t.CONV_TOPIC?i.push(e):(s.reduceUnreadCount()&&(o=!0),s.isLastMessageRevoked({sequence:e.sequence,time:e.time})&&(s.setLastMessageRevoked(!0),s.setLastMessageRevoker(e.revoker),o=!0)))});this.getModule(cs).onMessageRevoked(i),o&&(this.emitConversationUpdate(!0,!1),this.emitTotalUnreadMessageCountUpdate())}emitMessageRevokedEvent(t){this._genRevokerInfo(t).then(s=>{t=t.map(e=>{const t=s.find(t=>t.userID===e.revoker);return e.revokerInfo=t||e.revokerInfo,e}),this.emitOuterEvent(e.MESSAGE_REVOKED,t)})}_genRevokerInfo(e){const t=[];for(let i=0;i<e.length;i++){const{revoker:s}=e[i];-1===t.indexOf(s)&&t.push(s)}const s=t.map(e=>({userID:e,nick:"",avatar:""})),o=this.getModule(is);return new Promise(e=>{o.getUserProfile({userIDList:t}).then(t=>{const{data:o}=t;if(!Oe(o)||0===o.length)return e(s);for(const{userID:e,nick:i,avatar:n}of o){const t=s.find(t=>t.userID===e);t&&(t.nick=i,t.avatar=n)}e(s)}).catch(()=>{e(s)})})}isLastMessageRevoked(e){let s=!1;const{conversationID:o,sequence:i,time:n}=e,r=this._conversationMap.get(o);if(r)if(r.type===t.CONV_TOPIC){s=this.getModule(cs).isLastMessageRevoked({topicID:o.replace(t.CONV_GROUP,""),sequence:i})}else s=r.isLastMessageRevoked({sequence:i,time:n});return me.l(`${this._n}.isLastMessageRevoked options:${JSON.stringify(e)} ret:${s}`),s}onMessageDeleted(e){if(0===e.length)return;let s=null;e.forEach(e=>{s=this._messageListHandler.getLocalMessage(e.conversationID,e.ID),s&&(s.isDeleted=!0),e!==s&&(e.isDeleted=!0)});const o=e[0].conversationID,i=this._messageListHandler.getLocalMessageList(o);let n={};for(let t=i.length-1;t>=0;t--)if(!i[t].isDeleted){n=i[t];break}const r=this._conversationMap.get(o);if(!r)return;let a=!1;r.lastMessage.lastSequence===n.sequence&&r.lastMessage.lastTime===n.time||(Ct(n)&&(n=void 0),r.updateLastMessage(n),r.type!==t.CONV_TOPIC&&(a=!0),me.l(`${this._n}.onMessageDeleted. update conversationID:${o} with lastMessage:`,r.lastMessage)),o.startsWith(t.CONV_C2C)&&this.updateUnreadCount(o),a&&this.emitConversationUpdate(!0,!1)}onMessageModified(s){const{conversationType:o,from:i,to:n,time:r,sequence:a,elements:c,cloudCustomData:u,messageVersion:l}=s;let d=`${o}${n}`;n===this.getMyUserID()&&o===t.CONV_C2C&&(d=`${o}${i}`);const{isUpdated:p,message:h}=this._messageListHandler.onMessageModified(d,s);!0===p&&this.emitOuterEvent(e.MESSAGE_MODIFIED,[h]);const g=this._isTopicConversation(d);if(me.l(`${this._n}.onMessageModified isUpdated:${p} isTopicMessage:${g} from:${i} to:${n}`),g){this.getModule(cs).onMessageModified(s)}else{const e=this._conversationMap.get(d);if(e){const t=e.lastMessage;me.d(this._n.onMessageModified+" lastMessage:",JSON.stringify(t),"options:",JSON.stringify(s)),t&&t.lastTime===r&&t.lastSequence===a&&t.version!==l&&(t.type=c[0].type,t.payload=c[0].content,t.messageForShow=ht(t.type,t.payload,this.isIntl()),t.cloudCustomData=u,t.version=l,this.emitConversationUpdate(!0,!1))}}return h}onNewGroupAtTips(e){const t=e.dataList;let s=null;t.forEach(e=>{e.groupAtTips?s=e.groupAtTips:e.elements?s={...e.elements,sync:!0}:e.groupAtType&&(s={...e,sync:!0}),s.__random=e.random,s.__sequence=e.clientSequence,this._tmpGroupAtTipsList.push(s)}),me.d(`${this._n}.onNewGroupAtTips isReady:${this._isReady}`,this._tmpGroupAtTipsList),this._isReady&&this._handleGroupAtTipsList()}_handleGroupAtTipsList(){if(0===this._tmpGroupAtTipsList.length)return;let e=!1;this._tmpGroupAtTipsList.forEach(s=>{const{groupID:o,from:i,topicID:n,sync:r=!1}=s;if(i!==this.getMyUserID())if(Re(n)){const i=this._conversationMap.get(`${t.CONV_GROUP}${o}`);i&&(i.updateGroupAtInfoList(s),e=!0)}else{const e=this._conversationMap.get(`${t.CONV_GROUP}${n}`);if(e){e.updateGroupAtInfoList(s);const t=this.getModule(cs),{groupAtInfoList:o}=e;t.onConversationProxy({topicID:n,groupAtInfoList:o})}if(Ct(e)&&r){this.updateTopicConversation([{conversationID:`${t.CONV_GROUP}${n}`,type:t.CONV_TOPIC}]);this._conversationMap.get(`${t.CONV_GROUP}${n}`).updateGroupAtInfoList(s)}}}),e&&this.emitConversationUpdate(!0,!1),this._tmpGroupAtTipsList.length=0}_checkNewConversation(e){let s=[],o=[];e.forEach(e=>{this._conversationMap.has(e.conversationID)||(e.type===t.CONV_C2C?s.push(e.conversationID.replace(t.CONV_C2C,"")):e.type===t.CONV_GROUP&&o.push(e.conversationID.replace(t.CONV_GROUP,"")))}),s.length>0&&(this._onNewC2CConversation(s),s=null),o.length>0&&(this._onNewGroupConversation(o),o=null)}_onNewC2CConversation(e){this.getModule(ns).getRemotePeerReadTime(e),this._messageRemindHandler.getC2CMessageRemindType(e)}_onNewGroupConversation(e){const t=this.getModule(rs);t&&t.getMessageRemindType(e)}_setStorageConversationList(e=!1){const s=this.getLocalConversationList().filter(e=>e.type===t.CONV_C2C||e.type===t.CONV_GROUP&&e.lastMessage.type!==t.MSG_GRP_TIP).slice(0,20).map(e=>({conversationID:e.conversationID,type:e.type,subType:e.subType,lastMessage:e.lastMessage,groupProfile:e.groupProfile,userProfile:e.userProfile}));this.getModule(ds).setItem("conversationMap",s,e)}emitConversationUpdate(t=!0,s=!0){const o=this.getLocalConversationList();if(s){const e=this.getModule(rs);e&&e.updateGroupLastMessage(o)}t&&this.emitOuterEvent(e.CONVERSATION_LIST_UPDATED)}getLocalConversationList(){return[...this._conversationMap.values()].filter(e=>e.type!==t.CONV_TOPIC)}getLocalConversation(e){return this._conversationMap.get(e)}hasLocalConversation(e){return this._conversationMap.has(e)}getLocalOldestMessage(e){return this._messageListHandler.getLocalOldestMessage(e)}syncConversationList(){const e=new In("syncConversationList");return this._pagingStatus===Dt.NOT_START&&this._conversationMap.clear(),this._pagingGetConversationList().then(t=>{const s=function(e){if(!Oe(e)||0===e.length)return;let t=0;return e.forEach(e=>{t+=e}),(t/e.length).toFixed(0)}(this._pagingGetCostList),o=function(e){if(!Oe(e)||0===e.length)return;let t=0;return e.forEach(e=>{t+=e}),t.toFixed(0)}(this._pagingGetCostList);this._pagingGetCostList.length=0,this._pagingStatus=Dt.RESOLVED,this._diffAndDeleteConversation(),this.emitConversationUpdate(!0,!1),this._setStorageConversationList(),this._handleC2CPeerReadTime(),this.emitInnerEvent(Fn.CONV_SYNC_COMPLETED);const i=`count:${this._conversationMap.size} sum:${o} avg:${s}`;return me.l(`${this._n}.syncConversationList. ${i}`),e.setMessage(i).setNetworkType(this.getNetworkType()).end(),t}).catch(t=>(this._pagingStatus=Dt.REJECTED,e.setMessage(this._pagingTimeStamp),this.probeNetwork().then(([s,o])=>{e.setError(t,s,o).end()}),bs(t)))}_diffAndDeleteConversation(){if(this._isSyncCompleted()){let e=[];this._conversationMap.forEach((t,s)=>{!this._pagingConvIDMap.has(s)&&this._convIDFromUnreadDBMap.has(s)&&(this._conversationMap.delete(s),e.push(s))}),me.l(`${this._n}._diffAndDeleteConversation list:${e}`),e=null}}_pagingGetConversationList(){const e=this._n+"._pagingGetConversationList";me.l(`${e} timeStamp:${this._pagingTimeStamp} startIndex:${this._pagingStartIndex} pinnedTimeStamp:${this._pagingPinnedTimeStamp} pinnedStartIndex:${this._pagingPinnedStartIndex}`);const t=Date.now();return this._pagingStatus=Dt.PENDING,this.request({protocolName:To,requestData:{fromAccount:this.getMyUserID(),timeStamp:this._pagingTimeStamp,startIndex:this._pagingStartIndex,pinnedTimeStamp:this._pagingPinnedTimeStamp,pinnedStartIndex:this._pagingPinnedStartIndex,orderType:1}}).then(s=>{const{completeFlag:o,conversations:i=[],timeStamp:n,startIndex:r,pinnedTimeStamp:a,pinnedStartIndex:c,groupItem:u}=s.data,l=Date.now()-t;if(this._pagingGetCostList.push(l),me.l(`${e} ok. completeFlag:${o} count:${i.length} cost ${l} ms`),this._convGroupHandler.onConvGroupListSynced(u),i.length>0){const e=this._getConversationOptions(i);this._pagingConvIDMap=new Map([...this._pagingConvIDMap,...e.map(e=>[e.conversationID,1])]),this._updateLocalConversationList({conversationOptionsList:e,isFromGetConversations:!0}),this.isLoggedIn()&&this.emitConversationUpdate()}if(!this._isReady){if(!this.isLoggedIn())return ws();this.triggerReady()}return this._pagingTimeStamp=n,this._pagingStartIndex=r,this._pagingPinnedTimeStamp=a,this._pagingPinnedStartIndex=c,1!==o?this._pagingGetConversationList():(this._handleGroupAtTipsList(),this._convGroupHandler.getRemoteConvGroupList(),ws())}).catch(t=>{throw this.isLoggedIn()&&(this._isReady||(me.w(e+" failed. error:",t),this.triggerReady())),t})}_updateLocalConversationList(e){const{isFromGetConversations:t,isInstantMessage:s}=e,o=Date.now();let i={toBeUpdatedConversationList:[],newConversationList:[]};i=this._getTmpConversationListMapping(e),this._conversationMap=new Map(this._sortConversationList([...i.toBeUpdatedConversationList,...this._conversationMap])),t||(this._updateUserOrGroupProfile(i.newConversationList),s&&this.emitTotalUnreadMessageCountUpdate()),me.d(`${this._n}._updateLocalConversationList cost ${Date.now()-o} ms`)}_getTmpConversationListMapping(e){const{conversationOptionsList:s,isFromGetConversations:o,isInstantMessage:i,isUnreadC2CMessage:n=!1,updateUnreadCount:r}=e,a=[],c=[],u=this.getModule(rs),l=this.getModule(as),d=this.isIntl();for(let g=0,_=s.length;g<_;g++){const e=new Qn(s[g],d),{conversationID:p}=e;if(!this._isNonExistentAccount(p))if(this._conversationMap.has(p)){const c=this._conversationMap.get(p),u=["unreadCount","allowType","adminForbidType","payload","isPinned"];!1===i&&u.push("lastMessage");const l=s[g].lastMessage,d=!Re(l);d||s[g].type===t.CONV_TOPIC||this._onLastMessageNotExist(s[g]),Re(i)&&d&&null===c.lastMessage.payload&&(c.lastMessage.payload=l.payload),Ct(c.lastMessage.revoker)||(c.lastMessage.revoker=null);Fe(c,e,u,[null,void 0,"",0,NaN]),!0===r&&c.updateUnreadCount({nextUnreadCount:e.unreadCount,isFromGetConversations:o,isUnreadC2CMessage:n}),i&&d&&(l.payload&&(c.lastMessage.payload=l.payload),c.type===t.CONV_GROUP&&(c.lastMessage.nameCard=l.nameCard,c.lastMessage.nick=l.nick)),d&&c.lastMessage.cloudCustomData!==l.cloudCustomData&&(c.lastMessage.cloudCustomData=l.cloudCustomData||""),this._conversationMap.delete(p),a.push([p,c])}else{if(e.type===t.CONV_GROUP&&u){const{groupID:t}=e.groupProfile,s=u.getLocalGroupProfile(t);s&&(e.groupProfile=s,!0===r&&e.updateUnreadCount({nextUnreadCount:0}))}else if(e.type===t.CONV_C2C){const s=p.replace(t.CONV_C2C,"");l&&l.isMyFriend(s)&&(e.remark=l.getFriendRemark(s))}c.push(e),a.push([p,e])}}const p=this.getModule(cs),h=a.length;for(let g=0;g<h;g++){if(a[g][1].type!==t.CONV_TOPIC)continue;const{conversationID:e,unreadCount:s,groupAtInfoList:o}=a[g][1];p.onConversationProxy({topicID:e.replace(t.CONV_GROUP,""),unreadCount:s,groupAtInfoList:Ct(o)?void 0:o})}return{toBeUpdatedConversationList:a,newConversationList:c}}_onLastMessageNotExist(e){new In("lastMessageNotExist").setMessage(JSON.stringify(e)).setNetworkType(this.getNetworkType()).end()}_sortConversationList(e){const t=[],s=[],o=[],i=[];return e.forEach(e=>{!0===e[1].isPinned?Ct(e[1].lastMessage.lastTime)?s.push(e):t.push(e):Ct(e[1].lastMessage.lastTime)?i.push(e):o.push(e)}),t.sort((e,t)=>t[1].lastMessage.lastTime-e[1].lastMessage.lastTime).concat(s).concat(o.sort((e,t)=>t[1].lastMessage.lastTime-e[1].lastMessage.lastTime)).concat(i)}_sortConversationListAndEmitEvent(){this._conversationMap=new Map(this._sortConversationList([...this._conversationMap])),this.emitConversationUpdate(!0,!1)}_updateUserOrGroupProfile(e){if(0===e.length)return;const s=[],o=[],i=this.getModule(is),n=this.getModule(rs);e.forEach(e=>{if(e.type===t.CONV_C2C)s.push(e.toAccount);else if(e.type===t.CONV_GROUP){const t=e.toAccount;n.hasLocalGroup(t)?e.groupProfile=n.getLocalGroupProfile(t):o.push(t)}}),me.l(`${this._n}._updateUserOrGroupProfile c2cUserIDList:${s} groupIDList:${o}`),s.length>0&&i.getUserProfile({userIDList:s}).then(({data:e})=>{Oe(e)?e.forEach(e=>{this._doUpdateUserProfile("C2C"+e.userID,e)}):this._doUpdateUserProfile("C2C"+e.userID,e)}),o.length>0&&n.getGroupProfileAdvance({groupIDList:o,responseFilter:{groupBaseInfoFilter:["Type","Name","FaceUrl"]}}).then(({data:{successGroupList:e}})=>{e.forEach(e=>{const t="GROUP"+e.groupID;if(this._conversationMap.has(t)){const s=this._conversationMap.get(t);Fe(s.groupProfile,e,[],[null,void 0,"",0,NaN]),!s.subType&&e.type&&(s.subType=e.type)}})})}_doUpdateUserProfile(e,t){this.hasLocalConversation(e)&&(this.getLocalConversation(e).userProfile=t)}_getConversationOptions(e){const t=[],s=e.filter(({type:e,userID:t})=>1===e&&!this._isNonExistentAccount(t)||2===e),o=this.getMyUserID(),i=s.map(e=>{if(Re(e.lastMsg)&&(e.lastMsg={elements:[]}),1===e.type){const s={userID:e.userID,nick:e.peerNick,avatar:e.peerAvatar};return t.push(s),{conversationID:"C2C"+e.userID,type:"C2C",lastMessage:{lastTime:e.time,lastSequence:e.sequence,fromAccount:e.lastC2CMsgFromAccount,type:e.lastMsg.elements[0]?e.lastMsg.elements[0].type:null,payload:e.lastMsg.elements[0]?this._amendLayersOverLimitProperty(e.lastMsg.elements[0].content):null,cloudCustomData:e.lastMsg.cloudCustomData||"",isRevoked:8===e.lastMessageFlag,onlineOnlyFlag:!1,nick:"",nameCard:"",version:0,isPeerRead:e.lastC2CMsgFromAccount===o&&e.time<=e.c2cPeerReadTime,revoker:e.lastMsg.revokerInfo?e.lastMsg.revokerInfo.revoker:null},userProfile:new Wn(s),peerReadTime:e.c2cPeerReadTime,isPinned:1===e.isPinned,customData:e.customMark||"",markList:gt(e.standardMark),conversationGroupList:this._convGroupHandler.getConvGroupListByID(e.contactGroupId),remark:e.friendRemark||"",messageRemindType:this._transMessageRemindType(e.messageRemindType)}}return{conversationID:"GROUP"+e.groupID,type:"GROUP",lastMessage:{lastTime:e.time,lastSequence:e.sequence,fromAccount:e.msgGroupFromAccount,...this._patchTypeAndPayload(e),cloudCustomData:e.lastMsg.cloudCustomData||"",isRevoked:2===e.lastMessageFlag,onlineOnlyFlag:!1,nick:e.senderNick||"",nameCard:e.senderNameCard||"",revoker:e.lastMsg.revokerInfo?e.lastMsg.revokerInfo.revoker:null},groupProfile:new Jn({groupID:e.groupID,name:e.groupNick,avatar:e.groupImage,type:e.groupType,nextMessageSeq:e.nextMessageSeq}),unreadCount:this._computeGroupUnreadCount(e),peerReadTime:0,isPinned:1===e.isPinned,version:0,customData:e.customMark||"",markList:gt(e.standardMark),conversationGroupList:this._convGroupHandler.getConvGroupListByID(e.contactGroupId),messageRemindType:this._transMessageRemindType(e.messageRemindType)}});if(t.length>0){this.getModule(is).onConversationsProfileUpdated(t)}return i}_transMessageRemindType(e){let s="";return 0===e?s=t.MSG_REMIND_ACPT_AND_NOTE:1===e?s=t.MSG_REMIND_DISCARD:2===e&&(s=t.MSG_REMIND_ACPT_NOT_NOTE),s}_computeGroupUnreadCount(e){const{unreadCount:t=0,noUnreadCount:s=0}=e,o=t-s;return o>0?o:0}_patchTypeAndPayload(e){const{event:s,elements:o=[],groupTips:i={}}=e.lastMsg;if(!Re(s)&&!Ct(i)){let e=new Gn(i);e.setElement({type:t.MSG_GRP_TIP,content:{...i.elements,groupProfile:i.groupProfile}});const s=JSON.parse(JSON.stringify(e.payload));return e=null,{type:t.MSG_GRP_TIP,payload:s}}return{type:o[0]?o[0].type:null,payload:o[0]?this._amendLayersOverLimitProperty(o[0].content):null}}_amendLayersOverLimitProperty(e){const{layersOverLimit:t}=e;return 0===t?e.layersOverLimit=!1:1===t&&(e.layersOverLimit=!0),e}getLocalMessageList(e){return this._messageListHandler.getLocalMessageList(e)}deleteLocalMessage(e){e instanceof Gn&&this._messageListHandler.remove(e)}onConversationDeleted(e){if(!Oe(e))return;const s=e.map(e=>{const{type:s,userID:o,groupID:i}=e;return 1===s?`${t.CONV_C2C}${o}`:2===s?`${t.CONV_GROUP}${i}`:void 0});me.l(`${this._n}.onConversationDeleted conversationIDList:${s}`),this.deleteLocalConversationList(s)}onConversationPinned(e){if(!Oe(e))return;let s=!1;e.forEach(e=>{const{type:o,userID:i,groupID:n}=e;let r;1===o?r=this.getLocalConversation(`${t.CONV_C2C}${i}`):2===o&&(r=this.getLocalConversation(`${t.CONV_GROUP}${n}`)),r&&(me.l(`${this._n}.onConversationPinned conversationID:${r.conversationID} isPinned:${r.isPinned}`),r.isPinned||(r.isPinned=!0,s=!0))}),s&&this._sortConversationListAndEmitEvent()}onConversationUnpinned(e){if(!Oe(e))return;let s=!1;e.forEach(e=>{const{type:o,userID:i,groupID:n}=e;let r;1===o?r=this.getLocalConversation(`${t.CONV_C2C}${i}`):2===o&&(r=this.getLocalConversation(`${t.CONV_GROUP}${n}`)),r&&(me.l(`${this._n}.onConversationUnpinned conversationID:${r.conversationID} isPinned:${r.isPinned}`),r.isPinned&&(r.isPinned=!1,s=!0))}),s&&this._sortConversationListAndEmitEvent()}getMessageList({conversationID:e,nextReqMessageID:t,count:s}){const o=this._n+".getMessageList",i=this.getLocalConversation(e);let n="";if(i&&i.groupProfile&&(n=i.groupProfile.type),ze(n))return me.l(`${o} not available in avchatroom. conversationID:${e}`),ws({messageList:[],nextReqMessageID:"",isCompleted:!0});(Re(s)||s>15)&&(s=15),!t&&this._isNotInCommunity(e)&&(this._messageListHandler.removeByConversationID(e),this._completedMap.delete(e),this._roamingMessageSequenceMap.delete(e));const r=this._computeRemainingCount({conversationID:e,nextReqMessageID:t}),a=this._completedMap.has(e);if(me.l(`${o} conversationID:${e} nextReqMessageID:${t} remainingCount:${r} count:${s} isCompleted:${a}`),this._needGetHistory({conversationID:e,remainingCount:r,count:s}))return this.getHistoryMessages({conversationID:e,nextReqMessageID:t,count:20}).then(t=>{const{nextReqID:s,storedMessageList:i}=t,n=this._completedMap.has(e);let a=i;if(r>0){a=this._messageListHandler.getLocalMessageList(e).slice(0,i.length+r)}const c={nextReqMessageID:n?"":s,messageList:a,isCompleted:n},u=c.messageList.filter(e=>e.isRevoked)||[];return me.l(`${o} ret.nextReqMessageID:${c.nextReqMessageID} ret.isCompleted:${c.isCompleted} ret.length:${a.length}`),Oe(u)&&0!==u.length?this._genRevokerInfo(u).then(e=>(u.forEach(t=>{const s=e.find(e=>e.userID===t.revoker),o=t.revokerInfo&&t.revokerInfo.reason;c.messageList=c.messageList.map(e=>(e.ID===t.ID&&(e.revokeReason=o||"",e.revokerInfo=s),e))}),Rs(c))):Rs(c)});this.modifyMessageList(e);const c=this._getMessageListFromMemory({conversationID:e,nextReqMessageID:t,count:s});return ws(c)}_getMessageListFromMemory({conversationID:e,nextReqMessageID:t,count:s}){const o=this._n+"._getMessageListFromMemory",i=this._messageListHandler.getLocalMessageList(e),n=i.length;let r=0;const a={isCompleted:!1,nextReqMessageID:"",messageList:[]};return t?(r=i.findIndex(e=>e.ID===t),r>s?(a.messageList=i.slice(r-s,r),a.nextReqMessageID=i[r-s].ID):(a.messageList=i.slice(0,r),a.isCompleted=!0)):n>s?(r=n-s,a.messageList=i.slice(r,n),a.nextReqMessageID=i[r].ID):(a.messageList=i.slice(0,n),a.isCompleted=!0),me.l(`${o} conversationID:${e} ret.nextReqMessageID:${a.nextReqMessageID} ret.isCompleted:${a.isCompleted} ret.length:${a.messageList.length}`),a}getMessageListHopping(e){let{conversationID:s,sequence:o,time:i,count:n,direction:r=0}=e;if((Re(n)||n>15)&&(n=15),s.startsWith(t.CONV_C2C)){const e=this.getModule(ns),o=s.replace(t.CONV_C2C,"");return e.getRoamingMessagesHopping({peerAccount:o,time:i,count:n,direction:r})}if(s.startsWith(t.CONV_GROUP)){const e=this.getModule(rs),i=s.replace(t.CONV_GROUP,"");return e.getRoamingMessagesHopping({groupID:i,sequence:o,count:n,direction:r})}}_computeRemainingCount({conversationID:e,nextReqMessageID:t}){const s=this._messageListHandler.getLocalMessageList(e),o=s.length;if(!t)return o;let i=0;return Xe(e)?i=s.findIndex(e=>e.ID===t):Qe(e)&&(i=-1!==t.indexOf("-")?s.findIndex(e=>e.ID===t):s.findIndex(e=>e.sequence===t)),-1===i&&(i=0),i}_getMessageListSize(e){return this._messageListHandler.getLocalMessageList(e).length}_needGetHistory({conversationID:e,remainingCount:t,count:s}){const o=this.getLocalConversation(e);let i="";return o&&o.groupProfile&&(i=o.groupProfile.type),!Ze(e)&&!ze(i)&&(!(Qe(e)&&this._isPagingGetGroupListCompleted()&&this._getLocalGroupCount()<=4e3&&!this._hasLocalGroup(e)&&!this._isTopicConversation(e))&&(t<=s&&!this._completedMap.has(e)))}_isTopicConversation(e){const s=e.replace(t.CONV_GROUP,"");return Je(s)}getHistoryMessages(e){const{conversationID:s,count:o}=e;if(s===t.CONV_SYSTEM)return ws();let i=15;o>20&&(i=20);let n=null;if(Xe(s)){const e=this._roamingMessageKeyAndTimeMap.has(s);return n=this.getModule(ns),n?n.getRoamingMessage({conversationID:s,peerAccount:s.replace(t.CONV_C2C,""),count:i,lastMessageTime:e?this._roamingMessageKeyAndTimeMap.get(s).lastMessageTime:0,messageKey:e?this._roamingMessageKeyAndTimeMap.get(s).messageKey:""}):bs({code:ks.CANNOT_FIND_MODULE})}if(Qe(s)){if(n=this.getModule(rs),!n)return bs({code:ks.CANNOT_FIND_MODULE});let e=null;this._conversationMap.has(s)&&(e=this._conversationMap.get(s).lastMessage);let o=0;e&&(o=e.lastSequence);const r=this._roamingMessageSequenceMap.get(s);return n.getRoamingMessage({conversationID:s,groupID:s.replace(t.CONV_GROUP,""),count:i,sequence:r||o})}return ws()}patchConversationLastMessage(e){const t=this.getLocalConversation(e);if(!t)return;const{messageForShow:s,payload:o}=t.lastMessage;if(Ct(s)||Ct(o)){const s=this._messageListHandler.getLocalMessageList(e);if(0===s.length)return;const o=s[s.length-1];me.l(`${this._n}.patchConversationLastMessage conversationID:${e} payload:`,o.payload),t.updateLastMessage(o)}}onRoamingMessage(e=[],s,o=!0){const i=s.startsWith(t.CONV_C2C)?t.CONV_C2C:t.CONV_GROUP;let n=null,r=[],a=[],c=0,u=e.length,l=null;const d=i===t.CONV_GROUP,p=this.getFileDownloadProxy();let h=()=>{c=d?e.length-1:0,u=d?0:e.length},g=()=>{d?--c:++c},_=()=>d?c>=u:c<u;for(h();_();g())if(d&&1===e[c].sequence&&o&&this.setCompleted(s),1!==e[c].isPlaceMessage)if(n=new Gn(e[c]),n.to=e[c].to,i!==t.CONV_GROUP||Re(e[c].topicID)||(n.to=e[c].topicID),n.isSystemMessage=!!e[c].isSystemMessage,n.conversationType=i,l=4===e[c].event?{type:t.MSG_GRP_TIP,content:{...e[c].elements,groupProfile:e[c].groupProfile}}:e[c].elements,d||n.setNickAndAvatar({nick:e[c].nick,avatar:e[c].avatar}),Ct(l)){const t=new In("emptyMessageBody");t.setMessage(`from:${n.from} to:${n.to} sequence:${n.sequence} event:${e[c].event}`),t.setNetworkType(this.getNetworkType()).setLevel("warning").end()}else n.setElement(l,p),n.reInitialize(this.getMyUserID()),r.push(n);return h=g=_=null,o?(this._messageListHandler.unshift(r,a),r=null,a):(a=null,r)}findMessage(e){return this._messageListHandler.findMessage(e)}_isNotInCommunity(e){let s=!1;if(e.startsWith(t.CONV_GROUP)&&this._isTopicConversation(e)){const o=pt(e.replace(t.CONV_GROUP,""));this.getModule(rs).hasLocalGroup(o)||(s=!0)}return s}deleteTopicRoamingMessageInfo(e){if(!je({groupID:e}))return;this._messageListHandler.getTopicConversationIDList(e).forEach(e=>{this._messageListHandler.removeByConversationID(e),this._completedMap.delete(e),this._roamingMessageSequenceMap.delete(e)})}deleteGroupRomaingMessageInfo(e){const s=`${t.CONV_GROUP}${e}`;this._messageListHandler.removeByConversationID(s),this._completedMap.delete(s),this._roamingMessageSequenceMap.delete(s)}setMessageRead({conversationID:e}){const s=this.getLocalConversation(e);if(me.l(`${this._n}.setMessageRead conversationID:${e} unreadCount:${s?s.unreadCount:0}`),!s)return ws();if(s.type!==t.CONV_GROUP&&s.type!==t.CONV_TOPIC||Ct(s.groupAtInfoList)||this.deleteGroupAtTips(e),0===s.unreadCount)return ws();if(s.type===t.CONV_GROUP&&!this._hasLocalGroup(e))return 0!==s.unreadCount&&(s.unreadCount=0,this.emitConversationUpdate(!0,!1)),ws();const o=this._messageListHandler.getLocalLastMessage(e);let i=s.lastMessage.lastTime;o&&i<o.time&&(i=o.time);let n=s.lastMessage.lastSequence;if(o&&n<o.sequence&&(n=o.sequence),s.type===t.CONV_TOPIC&&Re(o)){const s=this.getModule(cs),o=e.replace(t.CONV_GROUP,""),i=pt(o),r=s.getLocalTopic(i,o);r&&(n=r.nextMessageSeq-1)}let r=null;switch(s.type){case t.CONV_C2C:return r=this.getModule(ns),r?r.setMessageRead({conversationID:e,lastMessageTime:i}):bs({code:ks.CANNOT_FIND_MODULE});case t.CONV_GROUP:case t.CONV_TOPIC:return r=this.getModule(rs),r?r.setMessageRead({conversationID:e,lastMessageSeq:n}):bs({code:ks.CANNOT_FIND_MODULE});case t.CONV_SYSTEM:return s.unreadCount=0,this.emitConversationUpdate(!0,!1),ws();default:return ws()}}setAllMessageRead(e={}){const s=this._n+".setAllMessageRead";e.scope||(e.scope=t.READ_ALL_MSG),me.l(s+" options:",e);const o=this._createSetAllMessageReadPack(e);if(0===o.readAllC2CMessage&&0===o.groupMessageReadInfoList.length)return ws();const i=new In("setAllMessageRead");return this.request({protocolName:Jo,requestData:o}).then(t=>{const{data:s}=t,o=this._handleAllMessageRead(s);return i.setMessage(`scope:${e.scope} failureGroups:${JSON.stringify(o)}`).setNetworkType(this.getNetworkType()).end(),ws()}).catch(e=>(this.probeNetwork().then(([t,s])=>{i.setError(e,t,s).end()}),me.w(s+" failed. error:",e),bs({code:e&&e.code?e.code:ks.MSG_UNREAD_ALL_FAIL,message:e&&e.message?e.message:void 0})))}setConversationCustomData(e){return this._convGroupHandler.setConvCustomData(e)}markConversation(e){return this._convGroupHandler.markConversation(e)}getConversationGroupList(){return this._convGroupHandler.getLocalConvGroupList()}createConversationGroup(e){return this._convGroupHandler.createConvGroup(e)}deleteConversationGroup(e){return this._convGroupHandler.deleteConvGroup(e)}renameConversationGroup(e){return this._convGroupHandler.renameConvGroup(e)}addConversationsToGroup(e){return this._convGroupHandler.addConvsToGroup(e)}deleteConversationsFromGroup(e){return this._convGroupHandler.deleteConvsFromGroup(e)}onConversationMarkUpdated(e){this._convGroupHandler.onConvMarkUpdated(e)}onConversationGroupCreated(e){this._convGroupHandler.onConvGroupCreated(e)}onConversationGroupDeleted(e){this._convGroupHandler.onConvGroupDeleted(e)}onConversationGroupNameUpdated(e){this._convGroupHandler.onConvGroupNameUpdated(e)}onConversationInGroupUpdated(e){this._convGroupHandler.onConvInGroupUpdated(e)}onConversationAddedToOrDeletedFromGroup(e){this._convGroupHandler.onConvAddedToOrDeletedFromGroup(e)}_getConversationLastMessageSequence(e){const t=this._messageListHandler.getLocalLastMessage(e.conversationID);let s=e.lastMessage.lastSequence;return t&&s<t.sequence&&(s=t.sequence),s}_getConversationLastMessageTime(e){const t=this._messageListHandler.getLocalLastMessage(e.conversationID);let s=e.lastMessage.lastTime;return t&&s<t.time&&(s=t.time),s}_createSetAllMessageReadPack(e){const s={readAllC2CMessage:0,groupMessageReadInfoList:[]},{scope:o}=e;for(const[,i]of this._conversationMap)if(i.unreadCount>0)if(i.type===t.CONV_C2C&&0===s.readAllC2CMessage){if(o===t.READ_ALL_MSG)s.readAllC2CMessage=1;else if(o===t.READ_ALL_C2C_MSG){s.readAllC2CMessage=1;break}}else if(i.type===t.CONV_GROUP&&(o===t.READ_ALL_GROUP_MSG||o===t.READ_ALL_MSG)){const e=this._getConversationLastMessageSequence(i);s.groupMessageReadInfoList.push({groupID:i.groupProfile.groupID,messageSequence:e})}return s}onPushedAllMessageRead(e){this._handleAllMessageRead(e)}_handleAllMessageRead(e){const{groupMessageReadInfoList:t,readAllC2CMessage:s}=e,o=this._parseGroupReadInfo(t);return this._updateAllConversationUnreadCount({readAllC2CMessage:s})>=1&&(this.emitConversationUpdate(!0,!1),this.emitTotalUnreadMessageCountUpdate()),o}_parseGroupReadInfo(e){const t=[];if(e&&e.length)for(let s=0,o=e.length;s<o;s++){const{groupID:o,sequence:i,retCode:n,lastMessageSeq:r}=e[s];Re(n)?this._remoteGroupReadSequenceMap.set(o,r):(this._remoteGroupReadSequenceMap.set(o,i),0!==n&&t.push(`${o}-${i}-${n}`))}return t}_updateAllConversationUnreadCount(e){const{readAllC2CMessage:s}=e;let o=0;for(const[i,n]of this._conversationMap)if(n.unreadCount>=1){if(1===s&&n.type===t.CONV_C2C){const e=this._getConversationLastMessageTime(n);this.updateIsReadAfterReadReport({conversationID:i,lastMessageTime:e})}else if(n.type===t.CONV_GROUP){const e=i.replace(t.CONV_GROUP,"");if(this._remoteGroupReadSequenceMap.has(e)){const t=this._remoteGroupReadSequenceMap.get(e),s=this._getConversationLastMessageSequence(n);this.updateIsReadAfterReadReport({conversationID:i,remoteReadSequence:t}),s>=t&&this._remoteGroupReadSequenceMap.delete(e)}}this.updateUnreadCount(i,!1)&&(o+=1)}return o}isRemoteRead(e){const{conversationID:s,sequence:o}=e,i=s.replace(t.CONV_GROUP,"");let n=!1;if(this._remoteGroupReadSequenceMap.has(i)){const e=this._remoteGroupReadSequenceMap.get(i);o<=e&&(n=!0,me.l(`${this._n}.isRemoteRead conversationID:${s} messageSequence:${o} remoteReadSequence:${e}`)),o>=e+10&&this._remoteGroupReadSequenceMap.delete(i)}return n}updateIsReadAfterReadReport({conversationID:e,lastMessageSeq:t,lastMessageTime:s}){const o=this._messageListHandler.getLocalMessageList(e);if(0===o.length)return;let i;for(let n=o.length-1;n>=0;n--)if(i=o[n],!(s&&i.time>s||t&&i.sequence>t)){if("in"===i.flow&&i.isRead)break;i.setIsRead(!0)}}updateUnreadCount(e,s=!0){let o=!1;const i=this.getLocalConversation(e),n=this._messageListHandler.getLocalMessageList(e);if(!i)return;const r=i.unreadCount,a=n.filter(e=>!e.isRead&&!e._onlineOnlyFlag&&!e.isDeleted).length;if(r!==a&&(i.unreadCount=a,o=!0,me.l(`${this._n}.updateUnreadCount from ${r} to ${a}, conversationID:${e}`),!0===s&&(this.emitConversationUpdate(!0,!1),this.emitTotalUnreadMessageCountUpdate())),o&&i.type===t.CONV_TOPIC){const{unreadCount:s}=i,o=this.getModule(cs),n=e.replace(t.CONV_GROUP,"");o.onConversationProxy({topicID:n,unreadCount:s})}return o}clearGroupAtInfoList(e,s=!0){const o=this.getLocalConversation(e);if(o&&o.groupAtInfoList.length>0){if(o.clearGroupAtInfoList(),me.l(`${this._n}.clearGroupAtInfoList conversationID:${e}`),o.type===t.CONV_TOPIC){const{groupAtInfoList:s}=o,i=this.getModule(cs),n=e.replace(t.CONV_GROUP,"");i.onConversationProxy({topicID:n,groupAtInfoList:s})}!0===s&&this.emitConversationUpdate(!0,!1)}}updateReadReceiptInfo(s){const{userID:o,groupID:i,readReceiptList:n}=s;if(Ct(n))return;const r=[];if(Re(o)){if(!Re(i)){const e=`${t.CONV_GROUP}${i}`;n.forEach(t=>{const{tinyID:s,clientTime:o,random:n,readCount:a,unreadCount:c}=t,u=`${s}-${o}-${n}`,l=this._messageListHandler.getLocalMessage(e,u),d={groupID:i,messageID:u,readCount:0,unreadCount:0};l&&(Ne(a)&&(l.readReceiptInfo.readCount=a,d.readCount=a),Ne(c)&&(l.readReceiptInfo.unreadCount=c,d.unreadCount=c),r.push(d))})}}else{const e=`${t.CONV_C2C}${o}`;n.forEach(t=>{const{tinyID:s,clientTime:i,random:n}=t,a=`${s}-${i}-${n}`,c=this._messageListHandler.getLocalMessage(e,a);if(c){c.readReceiptInfo.isPeerRead=!0;const e={userID:o,messageID:a,isPeerRead:!0};r.push(e)}})}r.length>0&&this.emitOuterEvent(e.MESSAGE_READ_RECEIPT_RECEIVED,r)}updateIsRead(e){const s=this.getLocalConversation(e),o=this.getLocalMessageList(e);if(!s||0===o.length||Ze(s.type))return;const i=[];for(let t=0,r=o.length;t<r;t++)"in"!==o[t].flow?"out"!==o[t].flow||o[t].isRead||o[t].setIsRead(!0):i.push(o[t]);let n=0;if(s.type===t.CONV_C2C){const e=i.slice(-s.unreadCount).filter(e=>e.isRevoked).length;n=i.length-s.unreadCount-e}else n=i.length-s.unreadCount;for(let t=0;t<n&&!i[t].isRead;t++)i[t].setIsRead(!0)}deleteGroupAtTips(e){const s=this._n+".deleteGroupAtTips";me.l(""+s);const o=this._conversationMap.get(e);if(!o)return Promise.resolve();const i=o.groupAtInfoList;if(0===i.length)return Promise.resolve();let n=void 0;e.startsWith(t.CONV_GROUP)&&(n=e.replace(t.CONV_GROUP,""));let r=[...i];if((je({groupID:n})||Je(n))&&(r=i.filter(e=>!e.atTypeArray.includes(t.CONV_AT_ALL)),0===r.length))return this.clearGroupAtInfoList(e,!1),Promise.resolve();const a=this.getMyUserID();return this.request({protocolName:No,requestData:{messageListToDelete:r.map(e=>({from:e.from,to:a,messageSeq:e.__sequence,messageRandom:e.__random,groupID:Re(e.topicID)?e.groupID:e.topicID}))}}).then(()=>(me.l(`${s} ok. count:${i.length}`),this.clearGroupAtInfoList(e,!1),Promise.resolve())).catch(e=>(me.e(s+" failed. error:",e),bs(e)))}appendToMessageList(e){return this._messageListHandler.pushIn(e)}setMessageRandom(e){this.singlyLinkedList.set(e.random)}deleteMessageRandom(e){this.singlyLinkedList.delete(e.random)}pushIntoMessageList(e,t,s){return!(!this._messageListHandler.pushIn(t,s)||this._isMessageFromCurrentInstance(t)&&!s)&&(e.push(t),!0)}_isMessageFromCurrentInstance(e){return this.singlyLinkedList.has(e.random)}revoke(e,t,s){return this._messageListHandler.revoke(e,t,s)}getPeerReadTime(e){return this._peerReadTimeMap.get(e)}recordPeerReadTime(e,t){if(this._peerReadTimeMap.has(e)){this._peerReadTimeMap.get(e)<t&&this._peerReadTimeMap.set(e,t)}else this._peerReadTimeMap.set(e,t)}updateMessageIsPeerReadProperty(s,o){if(s.startsWith(t.CONV_C2C)&&o>0){const t=this._messageListHandler.updateMessageIsPeerReadProperty(s,o);if(t.length>0&&this.emitOuterEvent(e.MESSAGE_READ_BY_PEER,t),this._conversationMap.has(s)){const e=this._conversationMap.get(s).lastMessage;Ct(e)||e.fromAccount===this.getMyUserID()&&e.lastTime<=o&&!e.isPeerRead&&(e.isPeerRead=!0,this.emitConversationUpdate(!0,!1))}}}updateMessageIsModifiedProperty(e){this._messageListHandler.updateMessageIsModifiedProperty(e)}setCompleted(e){me.l(`${this._n}.setCompleted. conversationID:${e}`),this._completedMap.set(e,!0)}updateRoamingMessageKeyAndTime(e,t,s){this._roamingMessageKeyAndTimeMap.set(e,{messageKey:t,lastMessageTime:s})}updateRoamingMessageSequence(e,t){this._roamingMessageSequenceMap.set(e,t)}getConversationList(e){const t=this._n+".getConversationList",s=`pagingStatus:${this._pagingStatus}, local conversation count:${this._conversationMap.size}, options:${e}`;if(me.l(`${t}. ${s}`),this._pagingStatus===Dt.REJECTED){const o=new In("getConversationList");return o.setMessage(s),this.syncConversationList().then(()=>{o.setNetworkType(this.getNetworkType()).end();const t=this._getConversationList(e);return Rs({conversationList:t,isSyncCompleted:this._isSyncCompleted()})}).catch(e=>(this.probeNetwork().then(([t,s])=>{o.setError(e,t,s).end()}),me.e(t+" failed. error:",e),bs(e)))}const o=this._getConversationList(e);return me.l(`${t}. returned conversation count:${o.length}`),ws({conversationList:o,isSyncCompleted:this._isSyncCompleted()})}_getConversationList(e){if(Re(e))return this.getLocalConversationList();if(Oe(e)){if(0===e.length)return[];return this.getLocalConversationList().filter(t=>e.includes(t.conversationID))}if(Le(e)){const{type:s,markType:o,groupName:i}=e;return this.getLocalConversationList().filter(e=>(s!==t.CONV_C2C&&s!==t.CONV_GROUP||e.type===s)&&(!Ee(i)||e.conversationGroupList.includes(i))&&(!Ne(o)||e.markList.includes(o)))}return[]}_handleC2CPeerReadTime(){for(const[e,s]of this._conversationMap)s.type===t.CONV_C2C&&(me.d(`${this._n}._handleC2CPeerReadTime conversationID:${e} peerReadTime:${s.peerReadTime}`),this.recordPeerReadTime(e,s.peerReadTime))}_isPagingGetGroupListCompleted(){const e=this.getModule(rs);return!e||e.isPagingGetCompleted()}_getLocalGroupCount(){const e=this.getModule(rs);return e?e.getLocalGroupList().length:0}_hasLocalGroup(e){const s=this.getModule(rs);return!!s&&s.hasLocalGroup(e.replace(t.CONV_GROUP,""))}getConversationProfile(e){let s;if(s=this._conversationMap.has(e)?this._conversationMap.get(e):new Qn({conversationID:e,type:e.slice(0,3)===t.CONV_C2C?t.CONV_C2C:t.CONV_GROUP},this.isIntl()),s._isInfoCompleted||s.type===t.CONV_SYSTEM)return ws({conversation:s});if(Qe(e)&&!this._hasLocalGroup(e))return ws({conversation:s});const o=this._n+".getConversationProfile",i=new In("getConversationProfile");return me.l(`${o}. conversationID:${e} remark:${s.remark} lastMessage:`,s.lastMessage),this._updateUserOrGroupProfileCompletely(s).then(n=>{i.setNetworkType(this.getNetworkType()).setMessage(`conversationID:${e} unreadCount:${n.data.conversation.unreadCount}`).end();const r=this.getModule(as);if(r&&s.type===t.CONV_C2C){const i=e.replace(t.CONV_C2C,"");if(r.isMyFriend(i)){const t=r.getFriendRemark(i);s.remark!==t&&(s.remark=t,me.l(`${o}. conversationID:${e} patch remark:${s.remark}`))}}return me.l(`${o} ok. conversationID:${e}`),n}).catch(t=>(this.probeNetwork().then(([s,o])=>{i.setError(t,s,o).setMessage("conversationID:"+e).end()}),me.e(o+" failed. error:",t),bs(t)))}_updateUserOrGroupProfileCompletely(e){if(e.type===t.CONV_C2C){return this.getModule(is).getUserProfile({userIDList:[e.toAccount]}).then(t=>{const s=t.data;return 0===s.length?bs(new Us({code:ks.USER_OR_GRP_NOT_FOUND})):(e.userProfile=s[0],e._isInfoCompleted=!0,this._unshiftConversation(e),ws({conversation:e}))})}return this.getModule(rs).getGroupProfile({groupID:e.toAccount}).then(t=>(e.groupProfile=t.data.group,e._isInfoCompleted=!0,this._unshiftConversation(e),ws({conversation:e})))}_unshiftConversation(e){e instanceof Qn&&!this._conversationMap.has(e.conversationID)&&(this._conversationMap=new Map([[e.conversationID,e],...this._conversationMap]),this._setStorageConversationList(),this.emitConversationUpdate(!0,!1))}_onProfileUpdated(e){e.data.forEach(e=>{const{userID:s}=e;if(s===this.getMyUserID())this._onMyProfileModified({latestNick:e.nick,latestAvatar:e.avatar});else{const o=this._conversationMap.get(`${t.CONV_C2C}${s}`);o&&(o.userProfile=e)}})}_isSyncCompleted(){return this._pagingStatus===Dt.RESOLVED}_errorLog(e,t,s,o){const i=new Error("Params validate failed."),n=`${this.getErrorMessage("API_REFER")}${e}`;throw me.w(`[${e}] | ${t} | ${this.getErrorMessage(s,o)}, ${n}`),me.e(`[${e}] Invalid ${t}: type check failed for ${t}.`),i}_isValidConversationID(e){return Xe(e)||Qe(e)||Ze(e)}deleteConversation(e){const t="deleteConversation";return Ee(e)||Ae(e)||this._errorLog(t,"options","StringOrObjectRequiredLog"),Ee(e)?(this._isValidConversationID(e)||this._errorLog(t,"options","InvalidConversationID",e),me.l(`${this._n}.${t} conversationID:${e}`),this.deleteConversationList({conversationIDList:[e],flag:1})):(Oe(e.conversationIDList)||this._errorLog(t,"conversationIDList","ArrayRequiredLog"),0===e.conversationIDList.length&&this._errorLog(t,"conversationIDList","NonEmptyArrayLog"),e.conversationIDList.forEach(e=>{this._isValidConversationID(e)||this._errorLog(t,"conversationIDList","InvalidConversationID",e)}),"clearHistoryMessage"in e&&"boolean"!=typeof e.clearHistoryMessage&&this._errorLog(t,"clearHistoryMessage","BooleanRequiredLog"),e.conversationIDList.length>100&&(e.conversationIDList=e.conversationIDList.slice(0,100)),this.deleteConversationList(e))}deleteConversationList(e){const{conversationIDList:t=[],clearHistoryMessage:s=!0,flag:o=0}=e,i=this._n+".deleteConversationList";me.l(`${i} conversationIDList.length:${t.length} clearHistoryMessage:${s}`);const n=new In("deleteConversationList");return n.setMessage("conversationIDList:"+t),Promise.all([this.rmLocalOnlyConversationList(t),this.rmLocalAndRemoteConversationList(t,s)]).then(e=>{n.setNetworkType(this.getNetworkType()).end();const t=[...e[0],...e[1]];return 0===t.length?bs(new Us({code:ks.CONV_NOT_FOUND})):(me.l(i+" ok"),ws(1===o?{conversationID:t[0]}:{conversationIDList:t}))}).catch(e=>(this.probeNetwork().then(([t,s])=>{n.setError(e,t,s).end()}),me.e(i+" failed. error:",e),bs(e)))}rmLocalOnlyConversationList(e){return e.filter(e=>{if(!this._conversationMap.has(e))return!1;const s=this.getLocalConversation(e).type;if(s===t.CONV_GROUP&&!this._hasLocalGroup(e))return this.deleteLocalConversation(e),!0;if(s===t.CONV_SYSTEM){return this.getModule(rs).deleteGroupSystemNotice({messageList:this._messageListHandler.getLocalMessageList(e)}),this.deleteLocalConversation(e),!0}return!1})}rmLocalAndRemoteConversationList(e,s){const o={fromAccount:this.getMyUserID(),conversationList:[],clearHistoryMessage:s?1:0};return e.forEach(e=>{if(this._conversationMap.has(e)){const s=this.getLocalConversation(e).type;s===t.CONV_C2C?o.conversationList.push({toAccount:e.replace(s,""),type:1}):s===t.CONV_GROUP&&this._hasLocalGroup(e)&&o.conversationList.push({toGroupID:e.replace(s,""),type:2})}}),0===o.conversationList.length?[]:this.request({protocolName:vo,requestData:o}).then(e=>{const s=[];return e.data.resultList.length>0&&e.data.resultList.map(e=>{if(0===e.code){const o=1===e.type?`${t.CONV_C2C}${e.to}`:`${t.CONV_GROUP}${e.groupID}`;s.push(o)}}),this.deleteLocalConversationList(s),s})}setConversationDraft(e){const{conversationID:t,draftText:s}=e,o=this._n+".setConversationDraft";if(me.l(`${o} conversationID:${t} draftText:${s}`),!this._conversationMap.has(t))return bs({code:ks.CONV_NOT_FOUND});const i=this._conversationMap.get(t);return i.setDraftText(s),ws({code:0,conversation:i})}clearHistoryMessage(e){const s={fromAccount:this.getMyUserID(),toAccount:void 0,type:void 0,toGroupID:void 0};if(!this._conversationMap.has(e))return bs({code:ks.CONV_NOT_FOUND});const o=this._conversationMap.get(e).type;if(o===t.CONV_C2C)s.type=1,s.toAccount=e.replace(t.CONV_C2C,"");else{if(o!==t.CONV_GROUP){if(o===t.CONV_SYSTEM){return this.getModule(rs).deleteGroupSystemNotice({messageList:this._messageListHandler.getLocalMessageList(e)}),ws({conversationID:e})}return bs({code:ks.CONV_UN_RECORDED_TYPE})}s.type=2,s.toGroupID=e.replace(t.CONV_GROUP,"")}const i=this._n+".clearHistoryMessage",n=new In("clearHistoryMessage");return n.setMessage("conversationID:"+e),me.l(`${i}. conversationID:${e}`),this.setMessageRead({conversationID:e}).then(()=>this.request({protocolName:Do,requestData:s})).then(()=>{n.setNetworkType(this.getNetworkType()).end(),me.l(i+" ok"),this._messageListHandler.removeByConversationID(e),this.setCompleted(e);const t=this.getLocalConversation(e);return t&&(t.updateLastMessage(),this._sortConversationListAndEmitEvent()),ws({conversationID:e})}).catch(e=>(this.probeNetwork().then(([t,s])=>{n.setError(e,t,s).end()}),me.e(i+" failed. error:",e),bs(e)))}pinConversation(e){const{conversationID:s,isPinned:o}=e;if(!this._conversationMap.has(s))return bs({code:ks.CONV_NOT_FOUND});const i=this.getLocalConversation(s);if(i.isPinned===o)return ws({conversationID:s});const n=this._n+".pinConversation",r=new In("pinConversation");r.setMessage(`conversationID:${s} isPinned:${o}`),me.l(`${n}. conversationID:${s} isPinned:${o}`);let a=null;return Xe(s)?a={type:1,toAccount:s.replace(t.CONV_C2C,"")}:Qe(s)&&(a={type:2,groupID:s.replace(t.CONV_GROUP,"")}),this.request({protocolName:So,requestData:{fromAccount:this.getMyUserID(),operationType:!0===o?1:2,itemList:[a]}}).then(()=>(r.setNetworkType(this.getNetworkType()).end(),me.l(n+" ok"),i.isPinned!==o&&(i.isPinned=o,this._sortConversationListAndEmitEvent()),Rs({conversationID:s}))).catch(e=>(this.probeNetwork().then(([t,s])=>{r.setError(e,t,s).end()}),me.e(n+" failed. error:",e),bs(e)))}setMessageRemindType(e){return this._messageRemindHandler.set(e)}patchMessageRemindType(e){const{ID:s,isC2CConversation:o,messageRemindType:i}=e;let n=!1;const r=this.getLocalConversation(o?`${t.CONV_C2C}${s}`:`${t.CONV_GROUP}${s}`);return r&&r.messageRemindType!==i&&(r.messageRemindType=i,n=!0),me.d(this._n+".patchMessageRemindType options:",e,"ret:"+n),n}onC2CMessageRemindTypeFetched(e){if(Oe(e)&&e.length>0){let t=0;e.forEach(e=>{const{userID:s,muteFlag:o}=e,i=this._transMessageRemindType(o);!0===this.patchMessageRemindType({ID:s,isC2CConversation:!0,messageRemindType:i})&&(t+=1)}),me.l(`${this._n}.onC2CMessageRemindTypeFetched updateCount:${t}`),t>=1&&this.emitConversationUpdate(!0,!1)}}onC2CMessageRemindTypeSynced(e){const t=this._n+".onC2CMessageRemindTypeSynced";me.d(t,e),e.dataList.forEach(e=>{if(!Ct(e.muteNotificationsSync)){const{to:s,muteFlag:o}=e.muteNotificationsSync,i=this._transMessageRemindType(o);let n=0;this.patchMessageRemindType({ID:s,isC2CConversation:!0,messageRemindType:i})&&(n+=1),me.l(`${t} updateCount:${n}`),n>=1&&this.emitConversationUpdate(!0,!1)}})}onGroupMessageRemindTypeUpdated(e){me.d(this._n+".onGroupMessageRemindTypeUpdated options:",e),this._messageRemindHandler.onGroupMessageRemindTypeUpdated(e)}deleteLocalConversation(e,t=!0){const s=this._conversationMap.has(e);if(me.d(`${this._n}.deleteLocalConversation conversationID:${e} has:${s}`),s&&(this._conversationMap.delete(e),this._roamingMessageKeyAndTimeMap.has(e)&&this._roamingMessageKeyAndTimeMap.delete(e),this._roamingMessageSequenceMap.has(e)&&this._roamingMessageSequenceMap.delete(e),this._setStorageConversationList(),this._messageListHandler.removeByConversationID(e),this._completedMap.delete(e),t)){const t=!this._isTopicConversation(e);this.emitConversationUpdate(t,!1)}}deleteLocalConversationList(e){let t=0,s=!1;e.forEach(e=>{this._conversationMap.has(e)&&(t+=this._conversationMap.get(e).unreadCount||0,this.deleteLocalConversation(e,!1),s=!0)}),me.l(`${this._n}.deleteLocalConversationList conversationIDList.length:${e.length} isConvIDExisted:${s}`),s&&(this.emitConversationUpdate(!0,!1),t>0&&this.emitTotalUnreadMessageCountUpdate())}isMessageSentByCurrentInstance(e){return!(!this._messageListHandler.hasLocalMessage(e.conversationID,e.ID)&&!this.singlyLinkedList.has(e.random))}modifyMessageList(e){if(!e.startsWith(t.CONV_C2C))return;if(!this._conversationMap.has(e))return;const s=this._conversationMap.get(e),o=Date.now();this._messageListHandler.modifyMessageSentByPeer({conversationID:e,latestNick:s.userProfile.nick,latestAvatar:s.userProfile.avatar});const i=this.getModule(is).getNickAndAvatarByUserID(this.getMyUserID());this._messageListHandler.modifyMessageSentByMe({conversationID:e,latestNick:i.nick,latestAvatar:i.avatar}),me.l(`${this._n}.modifyMessageList conversationID:${e} cost ${Date.now()-o} ms`)}updateUserProfileSpecifiedKey(e){me.l(this._n+".updateUserProfileSpecifiedKey options:",e);const{conversationID:t,nick:s,avatar:o}=e;if(this._conversationMap.has(t)){const e=this._conversationMap.get(t).userProfile;Ee(s)&&e.nick!==s&&(e.nick=s),Ee(o)&&e.avatar!==o&&(e.avatar=o),this.emitConversationUpdate(!0,!1)}}_onMyProfileModified(e){const t=this.getLocalConversationList(),s=Date.now();t.forEach(t=>{this.modifyMessageSentByMe({conversationID:t.conversationID,...e})}),me.l(`${this._n}._onMyProfileModified. modify all messages sent by me, cost ${Date.now()-s} ms`)}modifyMessageSentByMe(e){this._messageListHandler.modifyMessageSentByMe(e)}getLatestMessageSentByMe(e){return this._messageListHandler.getLatestMessageSentByMe(e)}modifyMessageSentByPeer(e){this._messageListHandler.modifyMessageSentByPeer(e)}getLatestMessageSentByPeer(e){return this._messageListHandler.getLatestMessageSentByPeer(e)}pushIntoNoticeResult(e,t){return!(!this._messageListHandler.pushIn(t)||this.singlyLinkedList.has(t.random))&&(e.push(t),!0)}getLocalLastMessage(e){return this._messageListHandler.getLocalLastMessage(e)}checkAndPatchRemark(){const e=this.getModule(as);if(0===this._conversationMap.size||!e)return;const s=[...this._conversationMap.values()].filter(e=>e.type===t.CONV_C2C);if(0===s.length)return;let o=0;s.forEach(s=>{const i=s.conversationID.replace(t.CONV_C2C,"");if(e.isMyFriend(i)){const t=e.getFriendRemark(i);s.remark!==t&&(s.remark=t,o+=1)}}),me.l(`${this._n}.checkAndPatchRemark. c2c conversation count:${s.length}, patched count:${o}`)}updateTopicConversation(e){this._updateLocalConversationList({conversationOptionsList:e,isFromGetConversations:!0})}sendReadReceipt(e){const s=e[0];let o=null;return s.conversationType===t.CONV_C2C?o=this._m.getModule(ns):s.conversationType===t.CONV_GROUP&&(o=this._m.getModule(rs)),o?o.sendReadReceipt(e):bs({code:ks.CANNOT_FIND_MODULE})}getReadReceiptList(e){const s=e[0];let o=null;return s.conversationType===t.CONV_C2C?o=this._m.getModule(ns):s.conversationType===t.CONV_GROUP&&(o=this._m.getModule(rs)),o?o.getReadReceiptList(e):bs({code:ks.CANNOT_FIND_MODULE})}getLastMessageTime(e){const t=this.getLocalConversation(e);return t?t.lastMessage.lastTime:0}getTotalUnreadMessageCount(){const e=this.getLocalConversationList();let s=0;return e.forEach(e=>{e.type!==t.CONV_SYSTEM&&(""!==e.messageRemindType&&e.messageRemindType!==t.MSG_REMIND_ACPT_AND_NOTE||(s+=e.unreadCount))}),s}emitTotalUnreadMessageCountUpdate(){const t=this.getTotalUnreadMessageCount();this._convTotalUnreadCount!==t&&(me.l(`${this._n}.emitTotalUnreadMessageCountUpdate from ${this._convTotalUnreadCount} to ${t}`),this._convTotalUnreadCount=t,this.emitOuterEvent(e.TOTAL_UNREAD_MESSAGE_COUNT_UPDATED))}reset(){me.l(this._n+".reset"),this._setStorageConversationList(!0),this._pagingStatus=Dt.NOT_START,this._messageListHandler.reset(),this._messageRemindHandler.reset(),this._roamingMessageKeyAndTimeMap.clear(),this._roamingMessageSequenceMap.clear(),this.singlyLinkedList.reset(),this._peerReadTimeMap.clear(),this._completedMap.clear(),this._conversationMap.clear(),this._pagingTimeStamp=0,this._pagingStartIndex=0,this._pagingPinnedTimeStamp=0,this._pagingPinnedStartIndex=0,this._remoteGroupReadSequenceMap.clear(),this._convTotalUnreadCount=0,this._pagingGetCostList.length=0,this._pagingConvIDMap.clear(),this._convIDFromUnreadDBMap.clear(),this._pagingGetCostList.length=0,this.resetReady()}}const sr=["topicID","topicName","avatar","introduction","notification","unreadCount","muteAllMembers","customData","groupAtInfoList","nextMessageSeq","selfInfo"],or=function(e,t){return Ct(e)?{lastTime:0,lastSequence:0,fromAccount:"",payload:null,type:"",messageForShow:"",nick:"",version:0,cloudCustomData:"",isRevoked:!1,revoker:null}:{lastTime:e.time||0,lastSequence:e.sequence||0,fromAccount:e.from||"",payload:e.payload||null,type:e.type||"",messageForShow:ht(e.type,e.payload,t),nick:e.nick||"",version:e.version||0,cloudCustomData:e.cloudCustomData||"",isRevoked:e.isRevoked||!1,revoker:e.revoker||null}};class ir{constructor(e,t){this.topicID="",this.topicName="",this.avatar="",this.introduction="",this.notification="",this.unreadCount=0,this.muteAllMembers=!1,this.customData="",this.groupAtInfoList=[],this.nextMessageSeq=0,this.lastMessage=or(e.lastMessage,t),this.selfInfo={muteTime:0,readedSequence:0,messageRemindType:"",excludedUnreadSequenceList:void 0},this._initTopic(e)}_initTopic(e){for(const t in e)sr.indexOf(t)<0||("selfInfo"===t?this.updateSelfInfo(e[t]):this[t]="muteAllMembers"===t?1===e[t]:e[t])}updateUnreadCount(e=0){this.unreadCount=e}updateNextMessageSeq(e){this.nextMessageSeq=e}updateLastMessage(e){this.lastMessage=or(e)}updateGroupAtInfoList(e){this.groupAtInfoList=JSON.parse(JSON.stringify(e))}updateTopic(e){Re(e.selfInfo)||this.updateSelfInfo(e.selfInfo),Re(e.muteAllMembers)||(this.muteAllMembers=1===e.muteAllMembers),Fe(this,e,["groupID","lastMessageTime","selfInfo","muteAllMembers","lastMsg"])}updateSelfInfo(e){return 0!==Fe(this.selfInfo,e,[],[""])}reduceUnreadCount(){return this.unreadCount>=1&&(this.unreadCount-=1,!0)}isLastMessageRevoked({sequence:e}){return e===this.lastMessage.lastSequence}setLastMessageRevoked(e){this.lastMessage.isRevoked=e}setLastMessageRevoker(e){this.lastMessage.revoker=e}}class nr extends Fs{constructor(e){super(e),this._n="TopicModule",this._topicMap=new Map,this._getTopicTimeMap=new Map,this.TOPIC_CACHE_TIME=300,this.TOPIC_LAST_ACTIVE_TIME=3600;this.getInnerEmitterInstance().on(Fn.CLOUD_CONFIG_UPDATED,this._onCloudConfigUpdated,this)}_onCloudConfigUpdated(){const e=this.getCloudConfig("topic_cache_time"),t=this.getCloudConfig("topic_last_active_time");Re(e)||(this.TOPIC_CACHE_TIME=Number(e)),Re(t)||(this.TOPIC_LAST_ACTIVE_TIME=Number(t))}onTopicCreated(t){const{groupID:s}=t;this.resetGetTopicTime(s),this.emitOuterEvent(e.TOPIC_CREATED,t)}onTopicDeleted(t){const{groupID:s,topicIDList:o=[]}=t;o.forEach(e=>{this._deleteLocalTopic(s,e)}),this.emitOuterEvent(e.TOPIC_DELETED,t)}onTopicMessageRemindTypeUpdated(t){const{groupID:s,topicID:o,messageRemindType:i}=t,n=this.getLocalTopic(s,o);if(n){const t=n.updateSelfInfo({messageRemindType:i});t&&this.emitOuterEvent(e.TOPIC_UPDATED,{groupID:s,topic:n}),me.d(`${this._n}.onTopicMessageRemindTypeUpdated topicID:${o} messageRemindType:${i} isTopicUpdated:${t}`)}}onTopicProfileUpdated(t){const{groupID:s,topicID:o}=t,i=this.getLocalTopic(s,o);i&&(i.updateTopic(t),this.emitOuterEvent(e.TOPIC_UPDATED,{groupID:s,topic:i}))}onConversationProxy(t){const{topicID:s,unreadCount:o,groupAtInfoList:i}=t,n=pt(s),r=this.getLocalTopic(n,s);let a=!1;r&&(Re(o)||r.unreadCount===o||(r.updateUnreadCount(o),a=!0),Re(i)||(r.updateGroupAtInfoList(i),a=!0)),a&&this.emitOuterEvent(e.TOPIC_UPDATED,{groupID:n,topic:r})}onMessageSent(t){const{groupID:s,topicID:o,lastMessage:i}=t,n=this.getLocalTopic(s,o);n&&(n.nextMessageSeq+=1,n.updateLastMessage(i),this.emitOuterEvent(e.TOPIC_UPDATED,{groupID:s,topic:n}))}onMessageModified(t){const{to:s,time:o,sequence:i,elements:n,cloudCustomData:r,messageVersion:a}=t,c=pt(s),u=this.getLocalTopic(c,s);if(u){const l=u.lastMessage;me.d(`${this._n}.onMessageModified topicID:${s} lastMessage:`,JSON.stringify(l),"options:",JSON.stringify(t)),l&&(null===l.payload||l.lastTime===o&&l.lastSequence===i&&l.version!==a)&&(l.type=n[0].type,l.payload=n[0].content,l.messageForShow=ht(l.type,l.payload,this.isIntl()),l.cloudCustomData=r,l.version=a,l.lastSequence=i,l.lastTime=o,this.emitOuterEvent(e.TOPIC_UPDATED,{groupID:c,topic:u}))}}onMessageRevoked(t){if(0===t.length)return;let s=null,o=null,i=!1;t.forEach(e=>{const t=e.to;o=pt(t),s=this.getLocalTopic(o,t),s&&(s.reduceUnreadCount()&&(i=!0),s.isLastMessageRevoked(e)&&(s.setLastMessageRevoked(!0),s.setLastMessageRevoker(e.revoker),i=!0))}),i&&this.emitOuterEvent(e.TOPIC_UPDATED,{groupID:o,topic:s})}isLastMessageRevoked(e){const{topicID:t,sequence:s}=e,o=pt(t),i=this.getLocalTopic(o,t);let n=!1;return i&&(n=i.isLastMessageRevoked({sequence:s})),n}getJoinedCommunityList(){return this.getModule(rs).syncCommunityWithTopic()}createTopicInCommunity(e){const t=this._n+".createTopicInCommunity",{topicID:s}=e;if(!Re(s)&&!Je(s))return bs({code:ks.ILLEGAL_TOPIC_ID});if(e.topicName&&!1===this._filterProfanity("topicName",e))return bs({code:ks.PROFANITY_FOUND});if(e.introduction&&!1===this._filterProfanity("introduction",e))return bs({code:ks.PROFANITY_FOUND});if(e.notification&&!1===this._filterProfanity("notification",e))return bs({code:ks.PROFANITY_FOUND});const o=new In("createTopicInCommunity");return this.request({protocolName:Bi,requestData:{...e}}).then(s=>{const{topicID:i}=s.data;return o.setMessage("topicID:"+i).setNetworkType(this.getNetworkType()).end(),me.l(`${t} ok. topicID:${i}`),this._updateTopicMap([{...e,topicID:i}]),Rs({topicID:i})}).catch(e=>(this.probeNetwork().then(([t,s])=>{o.setError(e,t,s).end()}),me.e(t+" failed. error:",e),bs(e)))}deleteTopicFromCommunity(e){const t=this._n+".deleteTopicFromCommunity",{groupID:s,topicIDList:o=[]}=e,i=new In("deleteTopicFromCommunity");return i.setMessage(`groupID:${s} topicIDList:${o}`),this.request({protocolName:Hi,requestData:{groupID:s,topicIDList:o}}).then(e=>{const{resultList:o=[]}=e.data,n=[],r=[];o.forEach(e=>{const{topicID:t,errorCode:s,errorInfo:o}=e;0===s?n.push({topicID:t}):r.push({topicID:t,code:s,message:o})});const a=`success count:${n.length}, fail count:${r.length}`;return i.setMoreMessage(a).setNetworkType(this.getNetworkType()).end(),me.l(`${t} ok. ${a}`),n.forEach(e=>{this._deleteLocalTopic(s,e.topicID)}),Rs({successTopicList:n,failureTopicList:r})}).catch(e=>(this.probeNetwork().then(([t,s])=>{i.setError(e,t,s).end()}),me.e(t+" failed. error:",e),bs(e)))}updateTopicProfile(e){const t=this._n+".updateTopicProfile";if(me.l(t+" options:",e),e.topicName&&!1===this._filterProfanity("topicName",e))return bs({code:ks.PROFANITY_FOUND});if(e.introduction&&!1===this._filterProfanity("introduction",e))return bs({code:ks.PROFANITY_FOUND});if(e.notification&&!1===this._filterProfanity("notification",e))return bs({code:ks.PROFANITY_FOUND});const s=new In("updateTopicProfile");return s.setMessage(`groupID:${e.groupID} topicID:${e.topicID}`),Re(e.muteAllMembers)||(e.muteAllMembers=!0===e.muteAllMembers?"On":"Off"),this.request({protocolName:Wi,requestData:{...e}}).then(()=>(s.setNetworkType(this.getNetworkType()).end(),me.l(t+" ok"),this._updateTopicMap([e]),Rs({topic:this.getLocalTopic(e.groupID,e.topicID)}))).catch(e=>(this.probeNetwork().then(([t,o])=>{s.setError(e,t,o).end()}),me.e(t+" failed. error:",e),bs(e)))}getTopicList(e){const t=this._n+".getTopicList",{groupID:s,topicIDList:o=[]}=e,i=0===o.length,n=new In("getTopicList");if(n.setMessage("groupID:"+s),this._getTopicTimeMap.has(s)){const{isGetAll:e,time:r}=this._getTopicTimeMap.get(s);if((e||!e&&!i)&&Date.now()-r<1e3*this.TOPIC_CACHE_TIME){const e=this._getLocalTopicList(s,o);if(i||e.length===o.length)return n.setNetworkType(this.getNetworkType()).setMoreMessage("from cache, topic count:"+e.length).end(),me.l(`${t} groupID:${s} from cache, topic count:${e.length}`),ws({successTopicList:e,failureTopicList:[]})}}return this.request({protocolName:Yi,requestData:{groupID:s,topicIDList:o}}).then(e=>{const{topicInfoList:o=[]}=e.data,r=[],a=[],c=[];o.forEach(e=>{const{topic:t,selfInfo:s,errorCode:o,errorInfo:i}=e,{topicID:n}=t;0===o?(r.push({...t,selfInfo:s}),a.push(n)):c.push({topicID:n,code:o,message:i})}),this._updateTopicMap(r),this._handleTopicAtInfo(r);const u=`success count:${a.length}, fail count:${c.length}`;n.setNetworkType(this.getNetworkType()).setMoreMessage(u).end(),me.l(`${t} groupID:${s} from remote, ${u}`);let l=[];return Ct(a)||(this._getTopicTimeMap.set(s,{time:Date.now(),isGetAll:i}),l=this._getLocalTopicList(s,a)),Rs({successTopicList:l,failureTopicList:c})}).catch(e=>(this.probeNetwork(e).then(([t,s])=>{n.setError(e,t,s).end()}),me.e(t+" failed. error:",e),bs(e)))}hasLocalTopic(e,t){return!!this._topicMap.has(e)&&this._topicMap.get(e).has(t)}getLocalTopic(e,t){let s=null;return this._topicMap.has(e)&&(s=this._topicMap.get(e).get(t)),s}_getLocalTopicList(e,t=[]){const s=this._topicMap.get(e);let o=[];return s&&(o=[...s.values()]),0===t.length?o:o.filter(e=>t.includes(e.topicID))}_deleteLocalTopic(e,t){this._topicMap.has(e)&&this._topicMap.get(e).has(t)&&(this._topicMap.get(e).delete(t),me.l(`${this._n}._deleteLocalTopic groupID:${e} topicID:${t}`))}_updateTopicMap(e){const s=[];if(e.forEach(e=>{const{groupID:o,topicID:i}=e;let n=null;this._topicMap.has(o)||this._topicMap.set(o,new Map);this._topicMap.get(o).has(i)?(n=this._topicMap.get(o).get(i),n.updateTopic(e)):(this._getTopicLastMessage(e),n=new ir(e,this.isIntl()),this._topicMap.get(o).set(i,n));const r=this._computeUnreadCount(n);n.updateUnreadCount(r),s.push({conversationID:`${t.CONV_GROUP}${i}`,type:t.CONV_TOPIC,unreadCount:r})}),s.length>0){this.getModule(us).updateTopicConversation(s)}}resetGetTopicTime(e){if(!Re(e))return void this._getTopicTimeMap.set(e,0);[...this._getTopicTimeMap.keys()].forEach(e=>{this._getTopicTimeMap.set(e,0)})}getTopicListOnReconnected(){const e=[...this._topicMap.keys()],t=[],s=this.getModule(us);e.forEach(e=>{const o=[],i=this._getLocalTopicList(e);s.deleteTopicRoamingMessageInfo(e),i.forEach(e=>{const{lastMessage:{lastTime:t=0}}=e;Date.now()-1e3*t<1e3*this.TOPIC_LAST_ACTIVE_TIME&&o.push(e.topicID)}),o.length>0&&t.push({groupID:e,topicIDList:o})}),me.l(`${this._n}.getTopicListOnReconnected. active community count:${t.length}`),this._relayGetTopicList(t)}_relayGetTopicList(e){if(0===e.length)return;const t=e.shift(),s=t.topicIDList.length>5?"topicIDList.length:"+t.topicIDList.length:"topicIDList:"+t.topicIDList,o=new In("relayGetTopicList");o.setMessage(s),me.l(`${this._n}._relayGetTopicList. ${s}`),this.getTopicList(t).then(()=>{o.setNetworkType(this.getNetworkType()).end(),this._relayGetTopicList(e)}).catch(t=>{this.probeNetwork().then(([e,s])=>{o.setError(t,e,s).end()}),this._relayGetTopicList(e)})}_handleTopicAtInfo(e){0!==e.length&&e.forEach(e=>{const{groupID:t,topicID:s,groupAtInfoList:o}=e,i=[];if(!Re(o)){o.forEach(e=>{i.push({...e,groupID:t,topicID:s})});this.getModule(us).onNewGroupAtTips({dataList:i})}})}_getTopicLastMessage(e){if(!Re(e.lastMsg)){const t={time:e.lastMsg.time,sequence:e.lastMsg.sequence,from:e.lastMsg.from,payload:e.lastMsg.elements[0]?e.lastMsg.elements[0].content:null,type:e.lastMsg.elements[0]?e.lastMsg.elements[0].type:"",nick:e.lastMsg.nick,version:e.lastMsg.messageVersion,cloudCustomData:e.lastMsg.cloudCustomData,isRevoked:2===e.lastMsg.isPlaceMessage,revoker:Ct(e.lastMsg.revokerInfo)?null:e.lastMsg.revokerInfo.revoker};e.lastMessage=t}}deleteTopicListInCommunity(e){const s=this._getLocalTopicList(e),o=this.getModule(us);s.forEach(s=>{const{topicID:i}=s;this._deleteLocalTopic(e,i),this._getTopicTimeMap.delete(e),o.deleteLocalConversation(`${t.CONV_GROUP}${i}`)})}_computeUnreadCount(e){const{excludedUnreadSequenceList:t,readedSequence:s}=e.selfInfo;let o=e.nextMessageSeq-e.selfInfo.readedSequence-1;if(Oe(t)){let i=0;t.forEach(t=>{t>=s&&t<=e.nextMessageSeq-1&&(i+=1)}),i>=1&&(o-=i)}return o<0?0:o}_filterProfanity(e,t){const s=this.getModule(Ns);if(!s)return!0;const{isAllowedToSend:o,modifiedText:i}=s.filterText(t[e],v);return!0===o&&(t[e]=i,!0)}updateLastMessage(t,s){const o=pt(t),i=this.getLocalTopic(o,t);if(i){const t=s.sequence+1;i.updateNextMessageSeq(t),i.updateLastMessage(s),this.emitOuterEvent(e.TOPIC_UPDATED,{groupID:o,topic:i})}}getMessageExtensions(e,t){me.l(`${this._n}.getMessageExtensions startSequence:${t}`);const s=pt(e.to);return this.request({protocolName:_i,requestData:{groupID:s,topicID:e.to,messageSequence:e.sequence,startSequence:t}})}modifyMessageExtensions(e,t,s=1){me.l(`${this._n}.modifyMessageExtensions operateType:${s}`);const o=pt(e.to);return this.request({protocolName:gi,requestData:{groupID:o,topicID:e.to,messageSequence:e.sequence,extensionList:t,operateType:s}})}reset(){me.l(this._n+".reset"),this._topicMap.clear(),this._getTopicTimeMap.clear(),this.TOPIC_CACHE_TIME=300,this.TOPIC_LAST_ACTIVE_TIME=3600}}class rr{constructor(e){this._userModule=e,this._n="ProfileHandler",this.TAG="profile",this.accountProfileMap=new Map,this.expirationTime=864e5}setExpirationTime(e){this.expirationTime=e}getUserProfile(e){const t=this._n+".getUserProfile",s=e.userIDList;e.fromAccount=this._userModule.getMyAccount(),s.length>100&&(me.w(`${t} ${mt(100)}`),s.length=100);const o=[],i=[];let n;for(let u=0,l=s.length;u<l;u++)n=s[u],this._userModule.isMyFriend(n)&&this._contains(n)?i.push(this._getProfileFromMap(n)):o.push(n);if(0===o.length)return ws(i);e.toAccount=o;const r=e.bFromGetMyProfile||!1,a=[];e.toAccount.forEach(e=>{a.push({toAccount:e,standardSequence:0,customSequence:0})}),e.userItem=a;const c=new In("getUserProfile");c.setMessage(s.length>5?"userIDList.length:"+s.length:"userIDList:"+s);return this._userModule.request({protocolName:Ws,requestData:e}).then(e=>{c.setNetworkType(this._userModule.getNetworkType()).end(),me.i(t+" ok");const s=this._handleResponse(e).concat(i);return Rs(r?s[0]:s)}).catch(e=>(this._userModule.probeNetwork().then(([t,s])=>{c.setError(e,t,s).end()}),me.e(t+" failed. error:",e),bs(e)))}getMyProfile(){const e=this._userModule.getMyAccount(),t=this._n+".getMyProfile";if(me.l(`${t} myAccount:${e}`),this._fill(),this._contains(e)){const s=this._getProfileFromMap(e);return me.d(`${t} from cache, myProfile:${JSON.stringify(s)}`),ws(s)}return this.getUserProfile({fromAccount:e,userIDList:[e],bFromGetMyProfile:!0})}_handleResponse(e){const{userProfileItem:t}=e.data;if(!Oe(t))return[];const s=[],o=Date.now();for(let i=0,n=t.length;i<n;i++){const{to:e,profileItem:o}=t[i];if("@TLS#NOT_FOUND"===e||""===e)continue;const{latestProfile:n}=this._update(e,this._getLatestProfileFromResponse(e,o));s.push(n)}return me.l(`${this._n}._handleResponse cost ${Date.now()-o} ms`),s}_getLatestProfileFromResponse(e,t){const s={userID:e,profileCustomField:[]};if(!Ct(t))for(let o=0,i=t.length;o<i;o++)if(t[o].tag.indexOf("Tag_Profile_Custom")>-1)s.profileCustomField.push({key:t[o].tag,value:t[o].value});else switch(t[o].tag){case fe.NICK:s.nick=t[o].value;break;case fe.GENDER:s.gender=t[o].value;break;case fe.BIRTHDAY:s.birthday=t[o].value;break;case fe.LOCATION:s.location=t[o].value;break;case fe.SELFSIGNATURE:s.selfSignature=t[o].value;break;case fe.ALLOWTYPE:s.allowType=t[o].value;break;case fe.LANGUAGE:s.language=t[o].value;break;case fe.AVATAR:s.avatar=t[o].value;break;case fe.MESSAGESETTINGS:s.messageSettings=t[o].value;break;case fe.ADMINFORBIDTYPE:s.adminForbidType=t[o].value;break;case fe.LEVEL:s.level=t[o].value;break;case fe.ROLE:s.role=t[o].value;break;default:me.w(this._n+"._getLatestProfileFromResponse unknown tag:",t[o].tag,t[o].value)}return s}updateMyProfile(t){const s=this._n+".updateMyProfile";if(t.nick&&!1===this._userModule.filterProfanity("nick",t))return bs({code:ks.PROFANITY_FOUND});if(t.selfSignature&&!1===this._userModule.filterProfanity("selfSignature",t))return bs({code:ks.PROFANITY_FOUND});const o=new In("updateMyProfile");o.setMessage(JSON.stringify(t));const i=(new Wn).validate(t);if(!i.valid)return o.setCode(ks.UPDATE_PROFILE_INVALID_PARAM).setMoreMessage("info:"+i.tips).setNetworkType(this._userModule.getNetworkType()).end(),me.e(`${s} info:${i.tips}`),bs({code:ks.UPDATE_PROFILE_INVALID_PARAM});const n=[];for(const e in t)Object.prototype.hasOwnProperty.call(t,e)&&("profileCustomField"===e?t.profileCustomField.forEach(e=>{n.push({tag:e.key,value:e.value})}):n.push({tag:fe[e.toUpperCase()],value:t[e]}));if(0===n.length){const e=new Us({code:ks.UPDATE_PROFILE_NO_KEY});return o.setError(e,!0,this._userModule.getNetworkType()).end(),me.e(s+" failed. error:",e),bs(e)}const r=this._userModule.getMyAccount();return this._userModule.request({protocolName:Ys,requestData:{fromAccount:r,profileItem:n}}).then(i=>{o.setNetworkType(this._userModule.getNetworkType()).end(),me.i(s+" ok");const{isProfileUpdated:n,latestProfile:a}=this._update(r,t);return!0===n&&this._userModule.emitOuterEvent(e.PROFILE_UPDATED,[a]),ws(a)}).catch(e=>(this._userModule.probeNetwork().then(([t,s])=>{o.setError(e,t,s).end()}),me.e(s+" failed. error:",e),bs(e)))}onProfileModified(t){const s=t.dataList;if(Ct(s))return;const o=s.length;let i;me.d(`${this._n}.onProfileModified count:${o} dataList:`,t.dataList);const n=[];for(let e=0;e<o;e++){i=s[e].userID;const{isProfileUpdated:t,latestProfile:o}=this._update(i,this._getLatestProfileFromResponse(i,s[e].profileList));!0===t&&n.push(o)}n.length>0&&(this._userModule.emitInnerEvent(Fn.PROFILE_UPDATED,n),this._userModule.emitOuterEvent(e.PROFILE_UPDATED,n))}_fill(){if(0===this.accountProfileMap.size){const e=this._getCachedProfiles(),t=Date.now();for(let s=0,o=e.length;s<o;s++)t-e[s].lastUpdatedTime<this.expirationTime&&this.accountProfileMap.set(e[s].userID,e[s]);me.l(`${this._n}._fill from cache, size:${this.accountProfileMap.size}`)}}_update(e,t){let s,o=!1;const i=Date.now();if(this._contains(e)){s=this._getProfileFromMap(e),t.profileCustomField&&!0===Ye(s.profileCustomField,t.profileCustomField)&&(s.lastUpdatedTime=i,o=!0);Fe(s,t,["profileCustomField"])>0&&(s.lastUpdatedTime=i,o=!0)}else s=new Wn(t),(this._userModule.isMyFriend(e)||e===this._userModule.getMyAccount())&&(s.lastUpdatedTime=i,o=!0,this.accountProfileMap.set(e,s));return this._flush(e===this._userModule.getMyAccount()),!0===o&&me.l(`${this._n}._update account:${e} isProfileUpdated:${o}`),{isProfileUpdated:o,latestProfile:s}}_flush(e){const t=[...this.accountProfileMap.values()],s=this._userModule.getStorageModule();me.d(`${this._n}._flush length:${t.length} flushAtOnce:${e}`),s.setItem(this.TAG,t,e)}_contains(e){return this.accountProfileMap.has(e)}_getProfileFromMap(e){return this.accountProfileMap.get(e)}_getCachedProfiles(){const e=this._userModule.getStorageModule().getItem(this.TAG);return Ct(e)?[]:e}onConversationsProfileUpdated(e){let t,s;const o=[];let i;for(let n=0,r=e.length;n<r;n++)t=e[n],s=t.userID,this._userModule.isMyFriend(s)&&(this._contains(s)?(i=this._getProfileFromMap(s),Fe(i,t)>0&&o.push(s)):o.push(t.userID));0!==o.length&&(me.i(`${this._n}.onConversationsProfileUpdated toAccountList:${o}`),this.getUserProfile({userIDList:o}))}getNickAndAvatarByUserID(e){if(this._contains(e)){const t=this._getProfileFromMap(e);return{nick:t.nick,avatar:t.avatar}}return{nick:"",avatar:""}}reset(){this._flush(!0),this.accountProfileMap.clear()}}class ar{constructor(e){Ct||(this.userID=e.userID||"",this.timeStamp=e.timeStamp||0)}}class cr{constructor(e){this._userModule=e,this._n="BlacklistHandler",this._blacklistMap=new Map,this.startIndex=0,this.maxLimited=100,this.currentSequence=0}getLocalBlacklist(){return[...this._blacklistMap.keys()]}getBlacklist(){const t=this._n+".getBlacklist",s={fromAccount:this._userModule.getMyAccount(),maxLimited:this.maxLimited,startIndex:0,lastSequence:this.currentSequence},o=new In("getBlacklist");return this._userModule.request({protocolName:zs,requestData:s}).then(s=>{const{blackListItem:i,currentSequence:n}=s.data,r=Ct(i)?0:i.length;o.setNetworkType(this._userModule.getNetworkType()).setMessage("count:"+r).end(),me.i(t+" ok"),this.currentSequence=n,this._handleResponse(i,!0),this._userModule.emitOuterEvent(e.BLACKLIST_UPDATED,[...this._blacklistMap.keys()])}).catch(e=>(this._userModule.probeNetwork().then(([t,s])=>{o.setError(e,t,s).end()}),me.e(t+" failed. error:",e),bs(e)))}addBlacklist(e){const t=new In("addToBlacklist"),s=this._n+".addBlacklist",o=this._userModule.getMyAccount();if(1===e.userIDList.length&&e.userIDList[0]===o){const e=ks.CANNOT_ADD_SELF_TO_BLACKLIST,o=this._userModule.getErrorMessage(e);t.setCode(e).setMessage(o).setNetworkType(this._userModule.getNetworkType()).end();const i=new Us({code:e});return me.e(s+" failed. error:",i),bs(i)}e.userIDList.includes(o)&&(e.userIDList=e.userIDList.filter(e=>e!==o)),e.fromAccount=this._userModule.getMyAccount(),e.toAccount=e.userIDList;return this._userModule.request({protocolName:js,requestData:e}).then(o=>(t.setNetworkType(this._userModule.getNetworkType()).setMessage(e.userIDList.length>5?"userIDList.length:"+e.userIDList.length:"userIDList:"+e.userIDList).end(),me.i(s+" ok"),this._handleResponse(o.resultItem,!0),Rs([...this._blacklistMap.keys()]))).catch(e=>(this._userModule.probeNetwork().then(([s,o])=>{t.setError(e,s,o).end()}),me.e(s+" failed. error:",e),bs(e)))}_handleResponse(e,t){if(!Ct(e)){let s,o,i;for(let n=0,r=e.length;n<r;n++)o=e[n].to,i=e[n].resultCode,(Re(i)||0===i)&&(t?(s=this._blacklistMap.has(o)?this._blacklistMap.get(o):new ar,s.userID=o,!Ct(e[n].addBlackTimeStamp)&&(s.timeStamp=e[n].addBlackTimeStamp),this._blacklistMap.set(o,s)):this._blacklistMap.has(o)&&(s=this._blacklistMap.get(o),this._blacklistMap.delete(o)))}me.l(`${this._n}._handleResponse total:${this._blacklistMap.size} bAdd:${t}`)}deleteBlacklist(e){const t=this._n+".deleteBlacklist",s=new In("removeFromBlacklist");e.fromAccount=this._userModule.getMyAccount(),e.toAccount=e.userIDList;return this._userModule.request({protocolName:Js,requestData:e}).then(o=>(s.setNetworkType(this._userModule.getNetworkType()).setMessage(e.userIDList.length>5?"userIDList.length:"+e.userIDList.length:"userIDList:"+e.userIDList).end(),me.i(t+" ok"),this._handleResponse(o.data.resultItem,!1),Rs([...this._blacklistMap.keys()]))).catch(e=>(this._userModule.probeNetwork().then(([t,o])=>{s.setError(e,t,o).end()}),me.e(t+" failed. error:",e),bs(e)))}onAccountDeleted(t){const s=[];let o;for(let e=0,i=t.length;e<i;e++)o=t[e],this._blacklistMap.has(o)&&(this._blacklistMap.delete(o),s.push(o));s.length>0&&(me.l(`${this._n}.onAccountDeleted count:${s.length} userIDList:`,s),this._userModule.emitOuterEvent(e.BLACKLIST_UPDATED,[...this._blacklistMap.keys()]))}onAccountAdded(t){const s=[];let o;for(let e=0,i=t.length;e<i;e++)o=t[e],this._blacklistMap.has(o)||(this._blacklistMap.set(o,new ar({userID:o})),s.push(o));s.length>0&&(me.l(`${this._n}.onAccountAdded count:${s.length} userIDList:`,s),this._userModule.emitOuterEvent(e.BLACKLIST_UPDATED,[...this._blacklistMap.keys()]))}reset(){this._blacklistMap.clear(),this.startIndex=0,this.maxLimited=100,this.currentSequence=0}}const ur=function(e){const t=String(e).replace(/[=]+$/,"");let s="";if(t.length%4==1)return"";for(let i,n,r=0,a=0;n=t.charAt(a++);~n&&(i=r%4?64*i+n:n,r++%4)?s+=String.fromCharCode(255&i>>(-2*r&6)):0)n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(n);try{return decodeURIComponent(escape(s))}catch(o){return""}};class lr{constructor(e){this._userModule=e,this._n="UserStatusHandler",this.MAX_QUERY_USER_COUNT=500,this.MAX_SUBSCRIBE_USER_COUNT=100,this.MAX_UNSUBSCRIBE_USER_COUNT=100;this._userModule.getInnerEmitterInstance().on(Fn.CLOUD_CONFIG_UPDATED,this._onCloudConfigUpdated,this)}_onCloudConfigUpdated(){const e=this._userModule.getCloudConfig("status_query_count"),t=this._userModule.getCloudConfig("status_sub_count"),s=this._userModule.getCloudConfig("status_unsub_count");me.l(`${this._n}._onCloudConfigUpdated statusQueryCount:${e} statusSubscribeCount:${t} statusUnsubscribeCount:`+s),Re(e)||(this.MAX_QUERY_USER_COUNT=parseInt(e,10)),Re(e)||(this.MAX_SUBSCRIBE_USER_COUNT=parseInt(t,10)),Re(e)||(this.MAX_UNSUBSCRIBE_USER_COUNT=parseInt(s,10))}onUserStatusUpdated(t){const{dataList:s}=t,o=this._userModule.getMyUserID(),i=this._userModule.getModule(ls),n=s.map(e=>{const{to:t,statusType:s,customStatus:n}=e,r=ur(n);return t===o&&i.setCustomStatus(r),{userID:t,statusType:s,customStatus:r}});this._userModule.emitOuterEvent(e.USER_STATUS_UPDATED,n)}setSelfStatus(e){const t=this._n+".setSelfStatus";if(!1===this._userModule.filterProfanity("customStatus",e))return bs({code:ks.PROFANITY_FOUND});const s=new In("setSelfStatus"),{customStatus:o}=e;return this._userModule.request({protocolName:zi,requestData:{customStatus:o}}).then(e=>{s.setNetworkType(this._userModule.getNetworkType()).setMessage("customStatus:"+o).end(),me.l(`${t} ok. customStatus:${o}`);return this._userModule.getModule(ls).setCustomStatus(o),Rs({userID:this._userModule.getMyUserID(),statusType:1,customStatus:o})}).catch(e=>(this._userModule.probeNetwork().then(([t,o])=>{s.setError(e,t,o).end()}),me.e(t+" failed. error:",e),bs(e)))}getUserStatus(e){const t=this._n+".getUserStatus",{userIDList:s=[]}=e,o=this._userModule.getMyUserID();let i=[...s],n=void 0;const r=i.indexOf(o);if(r>-1){i.splice(r,1);const e=this._userModule.getModule(ls).getCustomStatus();n={userID:o,statusType:1,customStatus:e}}if(0===i.length)return ws({successUserList:[n],failureUserList:[]});if(!this._userModule.canIUse(M.USER_STATUS))return this._userModule.cannotUseCommercialAbility("getUserStatus");i.length>this.MAX_QUERY_USER_COUNT&&(me.w(`${t} ${mt(this.MAX_QUERY_USER_COUNT)}`),i=s.slice(0,this.MAX_QUERY_USER_COUNT));const a=new In("getUserStatus");return this._userModule.request({protocolName:ji,requestData:{userIDList:i}}).then(e=>{const{successUserList:o=[],failureUserList:i=[]}=e.data,r=o.map(e=>{const{userID:t,statusType:s,customStatus:o}=e;return{userID:t,statusType:s,customStatus:ur(o)}}),c=i.map(e=>{const{userID:t,invalidUserID:s,errorCode:o,errorInfo:i}=e;return{userID:Ct(s)?t:s,code:o,message:i}});Re(n)||r.unshift(n);const u=`userID count:${s.length}, success count:${r.length}, fail count:${c.length}`;return a.setNetworkType(this._userModule.getNetworkType()).setMessage(""+u).end(),me.l(`${t} ok. ${u}.`),Rs({successUserList:r,failureUserList:c})}).catch(e=>(this._userModule.probeNetwork().then(([t,o])=>{a.setMessage("userID count:"+s.length).setError(e,t,o).end()}),me.e(t+" failed. error:",e),bs(e)))}subscribeUserStatus(e){const t="subscribeUserStatus";if(!this._userModule.canIUse(M.USER_STATUS))return this._userModule.cannotUseCommercialAbility(t);const s=`${this._n}.${t}`,{userIDList:o=[]}=e;let i=[...o];i.length>this.MAX_SUBSCRIBE_USER_COUNT&&(me.w(`${s} ${mt(this.MAX_SUBSCRIBE_USER_COUNT)}`),i=o.slice(0,this.MAX_SUBSCRIBE_USER_COUNT));const n=new In(t),r="userID count:"+o.length;return me.l(`${s} ${r}`),this._userModule.request({protocolName:Ji,requestData:{userIDList:i}}).then(e=>{const{failureUserList:t=[]}=e.data,o=t.map(e=>{const{userID:t,invalidUserID:s,errorCode:o,errorInfo:i}=e;return{userID:Ct(s)?t:s,code:o,message:i}});return n.setNetworkType(this._userModule.getNetworkType()).setMessage(`${r} fail count:${o.length}`).end(),me.l(`${s} ok. fail count:${o.length}.`),Rs({failureUserList:o})}).catch(e=>(this._userModule.probeNetwork().then(([t,s])=>{n.setMessage(""+r).setError(e,t,s).end()}),me.e(s+" failed. error:",e),bs(e)))}unsubscribeUserStatus(e){const t="unsubscribeUserStatus";if(!this._userModule.canIUse(M.USER_STATUS))return this._userModule.cannotUseCommercialAbility(t);const s=`${this._n}.${t}`,{userIDList:o=[]}=e||{};let i=[...o];o.length>this.MAX_UNSUBSCRIBE_USER_COUNT&&(me.w(`${s} ${mt(this.MAX_UNSUBSCRIBE_USER_COUNT)}`),i=o.slice(0,this.MAX_UNSUBSCRIBE_USER_COUNT));const n=new In(t),r="userID count:"+o.length;me.l(`${s} ${r}`);const a={userIDList:i};return 0===i.length&&(a.userIDList=void 0,a.unsubscribeAll=1),this._userModule.request({protocolName:Xi,requestData:a}).then(e=>{const{failureUserList:t=[]}=e.data,o=t.map(e=>{const{userID:t,invalidUserID:s,errorCode:o,errorInfo:i}=e;return{userID:Ct(s)?t:s,code:o,message:i}});return n.setNetworkType(this._userModule.getNetworkType()).setMessage(`${r} fail count:${o.length}`).end(),me.l(`${s} ok. fail count:${o.length}.`),Rs({failureUserList:o})}).catch(e=>(this._userModule.probeNetwork().then(([t,s])=>{n.setMessage(""+r).setError(e,t,s).end()}),me.e(s+" failed. error:",e),bs(e)))}reset(){this.MAX_QUERY_USER_COUNT=500,this.MAX_SUBSCRIBE_USER_COUNT=100,this.MAX_UNSUBSCRIBE_USER_COUNT=100}}class dr extends Fs{constructor(e){super(e),this._n="UserModule",this._profileHandler=new rr(this),this._blacklistHandler=new cr(this),this._userStatusHandler=new lr(this);this.getInnerEmitterInstance().on(Fn.A2KEY_AND_TINYID_UPDATED,this.onContextUpdated,this)}onContextUpdated(e){this._profileHandler.getMyProfile(),this._blacklistHandler.getBlacklist()}mockOnNickAvatarModified(e,t){me.l(`${this._n}._mockOnNickAvatarModified nick:${e} avatar:${t}`),this.onProfileModified({dataList:[{pushType:1,userID:this.getMyUserID(),profileList:[{tag:fe.NICK,value:e},{tag:fe.AVATAR,value:t}]}]})}onProfileModified(e){this._profileHandler.onProfileModified(e)}onRelationChainModified(e){const{dataList:t}=e;if(Ct(t))return;const s=[];t.forEach(e=>{e.blackListDelAccount&&s.push(...e.blackListDelAccount)}),s.length>0&&this._blacklistHandler.onAccountDeleted(s);const o=[];t.forEach(e=>{e.blackListAddAccount&&o.push(...e.blackListAddAccount)}),o.length>0&&this._blacklistHandler.onAccountAdded(o)}onConversationsProfileUpdated(e){this._profileHandler.onConversationsProfileUpdated(e)}getMyAccount(){return this.getMyUserID()}getMyNick(){return this._profileHandler.getNickAndAvatarByUserID(this.getMyUserID()).nick}getMyProfile(){return this._profileHandler.getMyProfile()}getStorageModule(){return this.getModule(ds)}filterProfanity(e,t){const s=this.getModule(Ns);if(!s)return!0;const{isAllowedToSend:o,modifiedText:i}=s.filterText(t[e],T);return!0===o&&(t[e]=i,!0)}isMyFriend(e){const t=this.getModule(as);return!!t&&t.isMyFriend(e)}getUserProfile(e){return this._profileHandler.getUserProfile(e)}updateMyProfile(e){return this._profileHandler.updateMyProfile(e)}getNickAndAvatarByUserID(e){return this._profileHandler.getNickAndAvatarByUserID(e)}getLocalBlacklist(){const e=this._blacklistHandler.getLocalBlacklist();return ws(e)}addBlacklist(e){return this._blacklistHandler.addBlacklist(e)}deleteBlacklist(e){return this._blacklistHandler.deleteBlacklist(e)}onUserStatusUpdated(e){this._userStatusHandler.onUserStatusUpdated(e)}setSelfStatus(e){return this._userStatusHandler.setSelfStatus(e)}getUserStatus(e){return this._userStatusHandler.getUserStatus(e)}subscribeUserStatus(e){return this._userStatusHandler.subscribeUserStatus(e)}unsubscribeUserStatus(e){return this._userStatusHandler.unsubscribeUserStatus(e)}reset(){me.l(this._n+".reset"),this._profileHandler.reset(),this._blacklistHandler.reset(),this._userStatusHandler.reset()}}class pr{constructor(e,t){this._m=e,this._isLoggedIn=!1,this._SDKAppID=t.SDKAppID,this._userID=t.userID||"",this._userSig=t.userSig||"",this._version="3.1.3",this._a2Key="",this._tinyID="",this._customStatus="",this._contentType="json",this._unlimitedAVChatRoom=t.unlimitedAVChatRoom,this._scene=t.scene||"",this._oversea=t.oversea,this._instanceID=t.instanceID,this._statusInstanceID=0,this._isDevMode=t.devMode,this._proxyServer=t.proxyServer,this._fileUploadProxy=t.fileUploadProxy,this._fileDownloadProxy=t.fileDownloadProxy}isLoggedIn(){return this._isLoggedIn}isOversea(){return this._oversea}isPrivateNetWork(){return this._proxyServer}isDevMode(){return this._isDevMode}isSingaporeSite(){return this._SDKAppID>=2e7&&this._SDKAppID<3e7||this._SDKAppID>=172e7&&this._SDKAppID<173e7}isKoreaSite(){return this._SDKAppID>=3e7&&this._SDKAppID<4e7||this._SDKAppID>=173e7&&this._SDKAppID<174e7}isGermanySite(){return this._SDKAppID>=4e7&&this._SDKAppID<5e7||this._SDKAppID>=174e7&&this._SDKAppID<175e7}isIndiaSite(){return this._SDKAppID>=5e7&&this._SDKAppID<6e7||this._SDKAppID>=175e7&&this._SDKAppID<176e7}isJapanSite(){return this._SDKAppID>=6e7&&this._SDKAppID<7e7||this._SDKAppID>=176e7&&this._SDKAppID<177e7}isUSASite(){return this._SDKAppID>=7e7&&this._SDKAppID<8e7||this._SDKAppID>=177e7&&this._SDKAppID<178e7}isIntl(){return 0===(e=this._SDKAppID)||e>=2e7&&e<8e7||e>=172e7&&e<178e7;var e}isUnlimitedAVChatRoom(){return this._unlimitedAVChatRoom}setUserID(e){this._userID=e}getUserID(){return this._userID}setUserSig(e){this._userSig=e}getUserSig(){return this._userSig}getSDKAppID(){return this._SDKAppID}setTinyID(e){this._tinyID=e,this._isLoggedIn=!0}getTinyID(){return this._tinyID}setCustomStatus(e){this._customStatus=e}getCustomStatus(){return this._customStatus}getScene(){return te?window.tencent_cloud_im_csig_flutter_for_web_25F_cy:this._isTUIKit()?"tuikit":this._scene}getInstanceID(){return this._instanceID}getStatusInstanceID(){return this._statusInstanceID}setStatusInstanceID(e){this._statusInstanceID=e}getVersion(){return this._version}getA2Key(){return this._a2Key}setA2Key(e){this._a2Key=e}getContentType(){return this._contentType}getProxyServer(){return this._proxyServer}getFileUploadProxy(){return this._fileUploadProxy}getFileDownloadProxy(){return this._fileDownloadProxy}_isTUIKit(){let e=!1,t=!1,s=!1,o=!1,i=[];U&&(i=Object.keys(P)),k&&(i=R?Object.keys(uni):Object.keys(window));for(let r=0,a=i.length;r<a;r++)if(i[r].toLowerCase().includes("uikit")){e=!0;break}if(i=null,U&&!ke(P.createGamePortal)&&ke(getApp)&&!Re(getApp())){const e=getApp().globalData;Le(e)&&!0===e.isTUIKit&&(t=!0)}!0===this._m.getModule(ds).getStorageSync(`TIM_${this._SDKAppID}_isTUIKit`)&&(s=!0);let n=null;if(D&&!N&&"undefined"==typeof uni&&__wxConfig&&(n=__wxConfig.pages),S&&"undefined"==typeof uni&&__qqConfig&&(n=__qqConfig.pages),Oe(n)&&n.length>0){for(let e=0,t=n.length;e<t;e++)if(n[e].toLowerCase().includes("tui")){o=!0;break}n=null}return e||t||s||o}reset(){this._isLoggedIn=!1,this._userSig="",this._a2Key="",this._tinyID="",this._customStatus="",this._statusInstanceID=0}}const hr={"k-vue2-pc":1,"k-vue2-h5":2,"k-vue2-h5-uni":3,"k-vue2-app-uni":4,"k-vue2-mp-uni":5,"k-vue2-pc-uni":6,"k-vue3-pc":7,"k-vue3-h5":8,"k-vue3-h5-uni":9,"k-vue3-app-uni":10,"k-vue3-mp-uni":11,"k-vue3-pc-uni":12};class gr extends Fs{constructor(e){super(e),this._n="SignModule",this._helloInterval=120,this._lastLoginTs=0,this._lastWsHelloTs=0,this._isWebUniapp=0,$n.mixin(this)}onCheckTimer(e){this.isLoggedIn()&&e%this._helloInterval==0&&this._hello()}login(e){let t="";if(this.isLoggedIn()){const e=this.getMyUserID();return t=this.getErrorMessage("RepeatLogin",e),t&&me.w(t),ws({actionStatus:"OK",errorCode:0,errorInfo:t,repeatLogin:!0})}if(Date.now()-this._lastLoginTs<=15e3)return this.outputWarning("LoggingIn",e.userID),bs({code:ks.REPEAT_LOGIN});me.l(`${this._n}.login userID:${e.userID}`);const s=this._checkLoginInfo(e);if(0!==s.code)return bs(s);const o=this.getModule(ls),{userID:i,userSig:n}=e;o.setUserID(i),o.setUserSig(n);return this.getModule(Ms).updateProtocolConfig(),this._login()}_login(){const e=this.getModule(ls);let t=e.getScene(),s=0,o=t;t&&t.startsWith("k-")&&(o=hr[t],t="tuikit");const i=new In("login");i.setMessage(""+o).setMoreMessage("identifier:"+this.getMyUserID());const n="tuikit"===t;R?n?3===o||4===o||5===o||6===o?i.setUIPlatform(31):9===o||10===o||11===o||12===o?i.setUIPlatform(32):i.setUIPlatform(4):i.setUIPlatform(3):U?"tuikit"===t?i.setUIPlatform(12):i.setUIPlatform(11):k&&(te?"flutter_web_uikit"===t?i.setUIPlatform(21):i.setUIPlatform(20):this._isReactUIKit()?ee?i.setUIPlatform(25):i.setUIPlatform(24):n?1===o||2===o?i.setUIPlatform(29):7===o||8===o?i.setUIPlatform(30):ee?i.setUIPlatform(17):i.setUIPlatform(14):ee?i.setUIPlatform(16):i.setUIPlatform(13));const r=this.getModule(Ss);if(r.canIUseOfflinePush()){this._isWebUniapp=r.getUniAppPlatform();const t=this._getStatusInstanceID();e.setStatusInstanceID(t);this.getModule(Ms).updateProtocolConfig(),s=r.getDeviceBrand()}const a=this._n+"._login";return this._lastLoginTs=Date.now(),this.request({protocolName:$s,requestData:{deviceBrand:s,isWebUniapp:this._isWebUniapp}}).then(s=>{this._lastLoginTs=0;const o=Date.now();let n=null;const{a2Key:r,tinyID:c,helloInterval:u,instanceID:l,timeStamp:d,customStatus:p="",purchaseBits:h}=s.data;me.l(`${a} ok. scene:${t} helloInterval:${u} instanceID:${l} timeStamp:${d}`);const g=1e3*d,_=o-i.getStartTs(),m=g+parseInt(_/2)-o,M=i.getStartTs()+m;if(i.start(M),function(e,t){ue=t;const s=new Date;s.setTime(e),me.i(`baseTime from server:${s} offset:${ue}`)}(g,m),!c)throw n=new Us({code:ks.NO_TINYID}),i.setError(n,!0,this.getNetworkType()).end(),n;if(!r)throw n=new Us({code:ks.NO_A2KEY}),i.setError(n,!0,this.getNetworkType()).end(),n;const f=ur(p);if(i.setNetworkType(this.getNetworkType()).setMoreMessage(`helloInterval:${u} instanceID:${l} offset:${m} customStatus:${f}`).end(),e.setA2Key(r),e.setTinyID(c),e.setStatusInstanceID(l),e.setCustomStatus(f),h){this.getModule(Ds).onPushedConfig({errorCode:0,expiredTime:0,purchaseBits:h})}this.getModule(Ms).updateProtocolConfig(),this.emitInnerEvent(Fn.A2KEY_AND_TINYID_UPDATED),this._helloInterval=u,this.triggerReady();const I=this.getModule(Ss);I.canIUseOfflinePush()&&(uni.setStorageSync("timUniAppInstanceID",l),I.init()),this._fetchCloudControlConfig();return this.getModule(Ns).init(),s}).catch(e=>(this.probeNetwork().then(([t,s])=>{i.setError(e,t,s).end(!0)}),this._m.setNotReadyReason(ks.LOGIN_FAILED),me.e(a+" failed. error:",e),this._lastLoginTs=0,this._m.onLoginFailed(),bs(e)))}logout(e=0){if(!this.isLoggedIn())return bs({code:ks.USER_NOT_LOGGED_IN});new In("logout").setNetworkType(this.getNetworkType()).setMessage("identifier:"+this.getMyUserID()).end(!0);const t=this._n+".logout";return me.i(`${t} type:${e}`),0===e&&this._m.setNotReadyReason(ks.LOGGED_OUT),this.request({protocolName:qs,requestData:{type:e}}).then(()=>(this.resetReady(),ws({}))).catch(e=>(me.e(t+" error:",e),this.resetReady(),ws({})))}_fetchCloudControlConfig(){this.getModule(Cs).fetchConfig()}_getStatusInstanceID(){return uni.getStorageSync("timUniAppInstanceID")}_hello(){this._lastWsHelloTs=Date.now(),this.request({protocolName:xs,requestData:{isWebUniapp:this._isWebUniapp}}).catch(e=>{me.w(this._n+"._hello error:",e)})}getLastWsHelloTs(){return this._lastWsHelloTs}_checkLoginInfo(e){let t=0;return Ct(this.getModule(ls).getSDKAppID())?t=ks.NO_SDKAPPID:Ct(e.userID)?t=ks.NO_IDENTIFIER:Ct(e.userSig)&&(t=ks.NO_USERSIG),{code:t}}_isReactUIKit(){return k&&void 0!==window.tencent_cloud_im_csig_react_uikit_23F_xa}onMultipleAccountKickedOut(s){new In("kickedOut").setNetworkType(this.getNetworkType()).setMessage(`type:${t.KICKED_OUT_MULT_ACCOUNT} newInstanceInfo:${JSON.stringify(s)}`).end(!0),me.w(`${this._n}.onMultipleAccountKickedOut userID:${this.getMyUserID()} newInstanceInfo:`,s),this.logout(1).then(()=>{this.emitOuterEvent(e.KICKED_OUT,{type:t.KICKED_OUT_MULT_ACCOUNT}),this._m.setNotReadyReason(ks.KICKED_OUT_MULT_ACCOUNT),this._m.reset()})}onMultipleDeviceKickedOut(s){new In("kickedOut").setNetworkType(this.getNetworkType()).setMessage(`type:${t.KICKED_OUT_MULT_DEVICE} newInstanceInfo:${JSON.stringify(s)}`).end(!0),me.w(`${this._n}.onMultipleDeviceKickedOut userID:${this.getMyUserID()} newInstanceInfo:`,s),this.logout(1).then(()=>{this.emitOuterEvent(e.KICKED_OUT,{type:t.KICKED_OUT_MULT_DEVICE}),this._m.setNotReadyReason(ks.KICKED_OUT_MULT_DEVICE),this._m.reset()})}onUserSigExpired(){new In("kickedOut").setNetworkType(this.getNetworkType()).setMessage(t.KICKED_OUT_USERSIG_EXPIRED).end(!0),me.w(`${this._n}.onUserSigExpired userID:${this.getMyUserID()}`);0!==this.getModule(ls).getStatusInstanceID()&&(this.emitOuterEvent(e.KICKED_OUT,{type:t.KICKED_OUT_USERSIG_EXPIRED}),this._m.setNotReadyReason(ks.KICKED_OUT_USERSIG_EXPIRED),this._m.reset())}onRestApiKickedOut(s){new In("kickedOut").setNetworkType(this.getNetworkType()).setMessage(`type:${t.KICKED_OUT_REST_API} newInstanceInfo:${JSON.stringify(s)}`).end(!0),me.w(`${this._n}.onRestApiKickedOut userID:${this.getMyUserID()} newInstanceInfo:`,s);if(0!==this.getModule(ls).getStatusInstanceID()){this.emitOuterEvent(e.KICKED_OUT,{type:t.KICKED_OUT_REST_API}),this._m.setNotReadyReason(ks.KICKED_OUT_REST_API),this._m.reset();this.getModule(fs).onRestApiKickedOut()}}reset(){me.l(this._n+".reset"),this.resetReady(),this._helloInterval=120,this._lastLoginTs=0,this._lastWsHelloTs=0,this._isWebUniapp=0}}function _r(){return null}class mr{constructor(e){this._m=e,this._n="StorageModule",this._storageQueue=new Map,this._errorTolerantHandle()}_errorTolerantHandle(){U||!Re(window)&&this._canIUseCookies()||(this.getItem=_r,this.setItem=_r,this.removeItem=_r,this.clear=_r)}onCheckTimer(e){if(e%20==0){if(0===this._storageQueue.size)return;this._doFlush()}}_doFlush(){try{for(const[e,t]of this._storageQueue)this._setStorageSync(this._getKey(e),t);this._storageQueue.clear()}catch(e){me.w(this._n+"._doFlush error:",e)}}_getPrefix(){const e=this._m.getModule(ls);return`TIM_${e.getSDKAppID()}_${e.getUserID()}_`}_getKey(e){return`${this._getPrefix()}${e}`}getItem(e,t=!0){try{const s=t?this._getKey(e):e;return this.getStorageSync(s)}catch(s){return me.w(this._n+".getItem error:",s),{}}}setItem(e,t,s=!1,o=!0){if(s){const s=o?this._getKey(e):e;this._setStorageSync(s,t)}else this._storageQueue.set(e,t)}clear(){try{U?P.clearStorageSync():this._canIUseCookies()&&localStorage.clear()}catch(e){me.w(this._n+".clear error:",e)}}removeItem(e,t=!0){try{const s=t?this._getKey(e):e;this._removeStorageSync(s)}catch(s){me.w(this._n+".removeItem error:",s)}}getSize(e,t="b"){try{const s={size:0,limitSize:5242880,unit:t};if(Object.defineProperty(s,"leftSize",{enumerable:!0,get:()=>s.limitSize-s.size}),U&&(s.limitSize=1024*P.getStorageInfoSync().limitSize),e)s.size=JSON.stringify(this.getItem(e)).length+this._getKey(e).length;else if(U){const{keys:e}=P.getStorageInfoSync();e.forEach(e=>{s.size+=JSON.stringify(this.getStorageSync(e)).length+this._getKey(e).length})}else if(this._canIUseCookies())for(const e in localStorage)localStorage.hasOwnProperty(e)&&(s.size+=localStorage.getItem(e).length+e.length);return this._convertUnit(s)}catch(s){me.w(this._n+" error:",s)}}_convertUnit(e){const t={},{unit:s}=e;t.unit=s;for(const o in e)"number"==typeof e[o]&&("kb"===s.toLowerCase()?t[o]=Math.round(e[o]/1024):"mb"===s.toLowerCase()?t[o]=Math.round(e[o]/1024/1024):t[o]=e[o]);return t}_setStorageSync(e,t){U?A?my.setStorageSync({key:e,data:t}):P.setStorageSync(e,t):this._canIUseCookies()&&localStorage.setItem(e,JSON.stringify(t))}getStorageSync(e){if(U){if(A){return my.getStorageSync({key:e}).data}return P.getStorageSync(e)}return this._canIUseCookies()?JSON.parse(localStorage.getItem(e)):{}}_removeStorageSync(e){U?A?my.removeStorageSync({key:e}):P.removeStorageSync(e):this._canIUseCookies()&&localStorage.removeItem(e)}_canIUseCookies(){return navigator&&navigator.cookieEnabled&&localStorage}reset(){me.l(this._n+".reset"),this._doFlush()}}class Mr{constructor(e){this._n="SSOLogBody",this._report=[]}pushIn(e){me.d(this._n+".pushIn",this._report.length,e),this._report.push(e)}backfill(e){Oe(e)&&0!==e.length&&(me.d(this._n+".backfill",this._report.length,e.length),this._report.unshift(...e))}getLogsNumInMemory(){return this._report.length}isEmpty(){return 0===this._report.length}_reset(){this._report.length=0,this._report=[]}getLogsInMemory(){const e=this._report.slice();return this._reset(),e}}const fr=function(e){const t=e.getModule(ls);return{SDKType:10,SDKAppID:t.getSDKAppID(),SDKVersion:t.getVersion(),tinyID:Number(t.getTinyID()),userID:t.getUserID(),platform:e.getPlatform(),instanceID:t.getInstanceID(),traceID:le()}};class Ir extends Fs{constructor(e){super(e),this._n="EventStatModule",this.TAG="im-ssolog-event",this._reportBody=new Mr,this.MIN_THRESHOLD=20,this.MAX_THRESHOLD=100,this.WAITING_TIME=6e4,this.REPORT_LEVEL=[4,5,6],this.REPORT_SDKAPPID_BLACKLIST=[],this.REPORT_TINYID_WHITELIST=[],this._lastReportTime=Date.now();const t=this.getInnerEmitterInstance();t.on(Fn.A2KEY_AND_TINYID_UPDATED,this._onLoginSuccess,this),t.on(Fn.CLOUD_CONFIG_UPDATED,this._onCloudConfigUpdated,this)}reportAtOnce(){this._report()}_onLoginSuccess(){const e=this.getModule(ds),t=e.getItem(this.TAG,!1);!Ct(t)&&ke(t.forEach)&&(me.l(`${this._n}._onLoginSuccess. logs count:${t.length}`),t.forEach(e=>{this._reportBody.pushIn(e)}),e.removeItem(this.TAG,!1))}_onCloudConfigUpdated(){const e=this.getCloudConfig("evt_rpt_threshold"),t=this.getCloudConfig("evt_rpt_waiting"),s=this.getCloudConfig("evt_rpt_level"),o=this.getCloudConfig("evt_rpt_sdkappid_bl"),i=this.getCloudConfig("evt_rpt_tinyid_wl");Re(e)||(this.MIN_THRESHOLD=Number(e)),Re(t)||(this.WAITING_TIME=Number(t)),Re(s)||(this.REPORT_LEVEL=s.split(",").map(e=>Number(e))),Re(o)||(this.REPORT_SDKAPPID_BLACKLIST=o.split(",").map(e=>Number(e))),Re(i)||(this.REPORT_TINYID_WHITELIST=i.split(","))}pushIn(e){e instanceof In&&(e.updateTimeStamp(),this._reportBody.pushIn(e),this._reportBody.getLogsNumInMemory()>=this.MIN_THRESHOLD&&this._report())}onCheckTimer(){Date.now()<this._lastReportTime+this.WAITING_TIME||this._reportBody.isEmpty()||this._report()}_filterLogs(e){const t=this.getModule(ls),s=t.getSDKAppID(),o=t.getTinyID();if(lt(this.REPORT_SDKAPPID_BLACKLIST,s)&&!dt(this.REPORT_TINYID_WHITELIST,o))return[];return e.filter(e=>this.REPORT_LEVEL.includes(e.level))}_report(){if(this._reportBody.isEmpty())return;const e=this._reportBody.getLogsInMemory(),t=this._filterLogs(e);if(0===t.length)return void(this._lastReportTime=Date.now());const s={header:fr(this),event:t};this.request({protocolName:Oi,requestData:{...s}}).then(()=>{this._lastReportTime=Date.now()}).catch(t=>{me.w(`${this._n}._report failed. networkType:${this.getNetworkType()} error:`,t),this._lastReportTime=Date.now(),this._reportBody.backfill(e);this._reportBody.getLogsNumInMemory()>this.MAX_THRESHOLD&&this._flushAtOnce()})}_flushAtOnce(){const e=this.getModule(ds),t=e.getItem(this.TAG,!1),s=this._reportBody.getLogsInMemory(),o=this._n+"._flushAtOnce";if(Ct(t))me.l(`${o} count:${s.length}`),e.setItem(this.TAG,s,!0,!1);else{let i=s.concat(t);i.length>this.MAX_THRESHOLD&&(i=i.slice(0,this.MAX_THRESHOLD)),me.l(`${o} count:${i.length}`),e.setItem(this.TAG,i,!0,!1)}}reset(){me.l(this._n+".reset"),this._lastReportTime=0,this._report(),this.REPORT_SDKAPPID_BLACKLIST=[],this.REPORT_TINYID_WHITELIST=[]}}const Cr="none",yr="online",Tr=[ks.OVER_FREQUENCY_LIMIT,ks.OPEN_SERVICE_OVERLOAD_ERROR];class vr{constructor(e){this._m=e,this._networkType="",this._n="NetMonitorModule",this.MAX_WAIT_TIME=3e3,this._mpNetworkStatusCallback=null,this._webOnlineCallback=null,this._webOfflineCallback=null}start(){U?(P.getNetworkType({success:e=>{this._networkType=e.networkType||e.subtype||"",e.networkType===Cr?me.w(this._n+".start no network, please check!"):me.i(`${this._n}.start networkType:${e.networkType}`)}}),this._mpNetworkStatusCallback=this._onNetworkStatusChange.bind(this),P.onNetworkStatusChange(this._mpNetworkStatusCallback)):(this._networkType=yr,this._webOnlineCallback=this._onWebOnline.bind(this),this._webOfflineCallback=this._onWebOffline.bind(this),window&&(window.addEventListener("online",this._webOnlineCallback),window.addEventListener("offline",this._webOfflineCallback)))}_onWebOnline(){this._onNetworkStatusChange({isConnected:!0,networkType:yr})}_onWebOffline(){this._onNetworkStatusChange({isConnected:!1,networkType:Cr})}_onNetworkStatusChange(e){const{isConnected:t,networkType:s}=e,o=this._n+"._onNetworkStatusChange";let i=!1;if(t){if(me.i(`${o} previous:${this._networkType} current:${s}`),this._networkType!==s){i=!0;this._m.getModule(fs).reConnect(!0)}}else if(this._networkType!==s){i=!0,me.w(o+" no network, please check!");this._m.getModule(fs).offline()}if(i){new In("networkChange").setMessage(`isConnected:${t} previousNetworkType:${this._networkType} networkType:${s}`).end(),this._networkType=s}}probe(e){if(!Re(e)&&Tr.includes(e.code))return Promise.resolve([!0,this._networkType]);const t=this._n+".probe";return new Promise((e,s)=>{U?P.getNetworkType({success:s=>{this._networkType=s.networkType,s.networkType===Cr?(me.w(t+" no network, please check!"),e([!1,s.networkType])):(me.i(`${t} networkType:${s.networkType}`),e([!0,s.networkType]))}}):this._networkType===Cr?e([!1,Cr]):e([!0,yr])})}getNetworkType(){return this._networkType}reset(){me.l(this._n+".reset"),U?null!==this._mpNetworkStatusCallback&&(P.offNetworkStatusChange&&(O||N?P.offNetworkStatusChange(this._mpNetworkStatusCallback):P.offNetworkStatusChange()),this._mpNetworkStatusCallback=null):window&&(null!==this._webOnlineCallback&&(window.removeEventListener("online",this._webOnlineCallback),this._webOnlineCallback=null),null!==this._onWebOffline&&(window.removeEventListener("offline",this._webOfflineCallback),this._webOfflineCallback=null))}}var Dr=function(e,t){return e(t={exports:{}},t.exports),t.exports}((function(e){var t=Object.prototype.hasOwnProperty,s="~";function o(){}function i(e,t,s){this.fn=e,this.context=t,this.once=s||!1}function n(e,t,o,n,r){if("function"!=typeof o)throw new TypeError("The listener must be a function");var a=new i(o,n||e,r),c=s?s+t:t;return e._events[c]?e._events[c].fn?e._events[c]=[e._events[c],a]:e._events[c].push(a):(e._events[c]=a,e._eventsCount++),e}function r(e,t){0==--e._eventsCount?e._events=new o:delete e._events[t]}function a(){this._events=new o,this._eventsCount=0}Object.create&&(o.prototype=Object.create(null),(new o).__proto__||(s=!1)),a.prototype.eventNames=function(){var e,o,i=[];if(0===this._eventsCount)return i;for(o in e=this._events)t.call(e,o)&&i.push(s?o.slice(1):o);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},a.prototype.listeners=function(e){var t=s?s+e:e,o=this._events[t];if(!o)return[];if(o.fn)return[o.fn];for(var i=0,n=o.length,r=new Array(n);i<n;i++)r[i]=o[i].fn;return r},a.prototype.listenerCount=function(e){var t=s?s+e:e,o=this._events[t];return o?o.fn?1:o.length:0},a.prototype.emit=function(e,t,o,i,n,r){var a=s?s+e:e;if(!this._events[a])return!1;var c,u,l=this._events[a],d=arguments.length;if(l.fn){switch(l.once&&this.removeListener(e,l.fn,void 0,!0),d){case 1:return l.fn.call(l.context),!0;case 2:return l.fn.call(l.context,t),!0;case 3:return l.fn.call(l.context,t,o),!0;case 4:return l.fn.call(l.context,t,o,i),!0;case 5:return l.fn.call(l.context,t,o,i,n),!0;case 6:return l.fn.call(l.context,t,o,i,n,r),!0}for(u=1,c=new Array(d-1);u<d;u++)c[u-1]=arguments[u];l.fn.apply(l.context,c)}else{var p,h=l.length;for(u=0;u<h;u++)switch(l[u].once&&this.removeListener(e,l[u].fn,void 0,!0),d){case 1:l[u].fn.call(l[u].context);break;case 2:l[u].fn.call(l[u].context,t);break;case 3:l[u].fn.call(l[u].context,t,o);break;case 4:l[u].fn.call(l[u].context,t,o,i);break;default:if(!c)for(p=1,c=new Array(d-1);p<d;p++)c[p-1]=arguments[p];l[u].fn.apply(l[u].context,c)}}return!0},a.prototype.on=function(e,t,s){return n(this,e,t,s,!1)},a.prototype.once=function(e,t,s){return n(this,e,t,s,!0)},a.prototype.removeListener=function(e,t,o,i){var n=s?s+e:e;if(!this._events[n])return this;if(!t)return r(this,n),this;var a=this._events[n];if(a.fn)a.fn!==t||i&&!a.once||o&&a.context!==o||r(this,n);else{for(var c=0,u=[],l=a.length;c<l;c++)(a[c].fn!==t||i&&!a[c].once||o&&a[c].context!==o)&&u.push(a[c]);u.length?this._events[n]=1===u.length?u[0]:u:r(this,n)}return this},a.prototype.removeAllListeners=function(e){var t;return e?(t=s?s+e:e,this._events[t]&&r(this,t)):(this._events=new o,this._eventsCount=0),this},a.prototype.off=a.prototype.removeListener,a.prototype.addListener=a.prototype.on,a.prefixed=s,a.EventEmitter=a,e.exports=a}));class Sr extends Fs{constructor(e){super(e),this._n="UploadModule",this.TIMUploadPlugin=null,this.timUploadPlugin=null,this.COSSDK=null,this._cosUploadMethod=null,this.expiredTimeLimit=600,this.appid=0,this.bucketName="",this.ciUrl="",this.directory="",this.downloadUrl="",this.uploadUrl="",this.region="ap-shanghai",this.cos=null,this.cosOptions={secretId:"",secretKey:"",sessionToken:"",expiredTime:0},this.uploadFileType="",this.duration=900,this.tryCount=0,this.UPLOAD_SIZE_LIMIT={A:20971520,F:104857600,I:20971520,V:104857600};const t=this.getInnerEmitterInstance();t.on(Fn.A2KEY_AND_TINYID_UPDATED,this._init,this),t.on(Fn.CLOUD_CONFIG_UPDATED,this._onCloudConfigUpdated,this)}_init(){const e=this.getModule(_s);if(this.TIMUploadPlugin=e.getPlugin("tim-upload-plugin"),this.TIMUploadPlugin)return void this._initUploaderMethod();const t=U?"cos-wx-sdk":"cos-js-sdk";this.COSSDK=e.getPlugin(t),this.COSSDK?(this._getAuthorizationKey(),this.outputWarning("CosReplacement",t)):this.outputWarning("PluginUndetected")}_onCloudConfigUpdated(){const e=this._n+"._onCloudConfigUpdated",t=this.getCloudConfig("upload_size_limit");if(me.l(`${e} uploadSizeLimit:${t}`),!Re(t))try{const e=JSON.parse(t);this.UPLOAD_SIZE_LIMIT={A:e.a?1048576*parseInt(e.a):this.UPLOAD_SIZE_LIMIT.A,F:e.f?1048576*parseInt(e.f):this.UPLOAD_SIZE_LIMIT.F,I:e.i?1048576*parseInt(e.i):this.UPLOAD_SIZE_LIMIT.I,V:e.v?1048576*parseInt(e.v):this.UPLOAD_SIZE_LIMIT.V}}catch(s){me.e(e+" JSON parse error. uploadSizeLimit:",t)}}_getAuthorizationKey(){const e=this._n+"._getAuthorizationKey",t=new In("_getAuthorizationKey"),s=Math.ceil(Date.now()/1e3);this.request({protocolName:Ei,requestData:{duration:this.expiredTimeLimit}}).then(o=>{const{data:i}=o;me.l(e+" ok. data:",i);const n=i.expiredTime-s;t.setMessage(`requestId:${i.requestId} requestTime:${s} expiredTime:${i.expiredTime} diff:${n}s`).setNetworkType(this.getNetworkType()).end(),!U&&i.region&&(this.region=i.region),this.appid=i.appid,this.bucketName=i.bucketName,this.ciUrl=i.ciUrl,this.directory=i.directory,this.downloadUrl=i.downloadUrl,this.uploadUrl=i.uploadUrl,this.cosOptions={secretId:i.secretId,secretKey:i.secretKey,sessionToken:i.sessionToken,expiredTime:i.expiredTime},me.l(`${e} ok. region:${this.region} bucketName:${this.bucketName}`),this._initUploaderMethod()}).catch(s=>{this.probeNetwork().then(([e,o])=>{t.setError(s,e,o).end()}),me.w(e+" failed. error:",s)})}_getCosPreSigUrl(e){const t=this._n+"._getCosPreSigUrl",s=Math.ceil(Date.now()/1e3),o=new In("_getCosPreSigUrl");return this.request({protocolName:Ai,requestData:{fileType:e.fileType,fileName:e.fileName,uploadMethod:e.uploadMethod,duration:e.duration}}).then(e=>{this.tryCount=0;const i=e.data||{},n=i.expiredTime-s;return me.l(t+" ok. data:",i),o.setMessage(`requestId:${i.requestId} expiredTime:${i.expiredTime} diff:${n}s`).setNetworkType(this.getNetworkType()).end(),i}).catch(s=>(-1===s.code&&(s.code=ks.COS_GET_SIG_FAIL),this.probeNetwork().then(([e,t])=>{o.setError(s,e,t).end()}),me.w(t+" failed. error:",s),this.tryCount<1?(this.tryCount++,this._getCosPreSigUrl(e)):(this.tryCount=0,bs({code:ks.COS_GET_SIG_FAIL}))))}_initUploaderMethod(){if(this.TIMUploadPlugin)return this.timUploadPlugin=new this.TIMUploadPlugin,void(this._cosUploadMethod=(e,t)=>{this.timUploadPlugin.uploadFile(e,t)});this.appid&&(this.cos=U?new this.COSSDK({ForcePathStyle:!0,getAuthorization:this._getAuthorization.bind(this)}):new this.COSSDK({getAuthorization:this._getAuthorization.bind(this)}),this._cosUploadMethod=U?(e,t)=>{this.cos.postObject(e,t)}:(e,t)=>{this.cos.uploadFiles(e,t)})}onCheckTimer(e){if(!this.COSSDK)return;if(this.TIMUploadPlugin)return;if(!this.isLoggedIn())return;if(e%60!=0)return;Math.ceil(Date.now()/1e3)>=this.cosOptions.expiredTime-120&&this._getAuthorizationKey()}_getAuthorization(e,t){t({TmpSecretId:this.cosOptions.secretId,TmpSecretKey:this.cosOptions.secretKey,XCosSecurityToken:this.cosOptions.sessionToken,ExpiredTime:this.cosOptions.expiredTime})}upload(e){if(!0===e.getRelayFlag())return Promise.resolve();const s=this.getModule(vs);switch(e.type){case t.MSG_IMAGE:return s.addTotalCount(dn),this._uploadImage(e);case t.MSG_FILE:return s.addTotalCount(dn),this._uploadFile(e);case t.MSG_AUDIO:return s.addTotalCount(dn),this._uploadAudio(e);case t.MSG_VIDEO:return s.addTotalCount(dn),this._uploadVideo(e);default:return Promise.resolve()}}_uploadImage(e){const t=this.getModule(ss),s=e.getElements()[0],o=t.getMessageOption(e.clientSequence);return this.doUploadImage({file:o.payload.file,to:o.to,onProgress:e=>{if(s.updatePercent(e),ke(o.onProgress))try{o.onProgress(e)}catch(t){return bs({code:ks.MSG_ONPROGRESS_FUNCTION_ERROR})}}}).then(({location:t,fileType:o,fileSize:i,width:n,height:r,smallImageUrl:a,smallImageWidth:c,smallImageHeight:u,largeImageUrl:l,largeImageWidth:d,largeImageHeight:p})=>{const h=this.isPrivateNetWork()?t:We(t);let g,_;return s.updateImageFormat(o),a&&l?(g={url:a,width:c,height:u},_={url:l,width:d,height:p}):(g=rt({originUrl:h,originWidth:n,originHeight:r,min:198}),_=rt({originUrl:h,originWidth:n,originHeight:r,min:720})),s.updateImageInfoArray([{size:i,url:h,width:n,height:r},{..._},{...g}]),e})}_uploadFile(e){const t=this.getModule(ss),s=e.getElements()[0],o=t.getMessageOption(e.clientSequence);return this.doUploadFile({file:o.payload.file,to:o.to,onProgress:e=>{if(s.updatePercent(e),ke(o.onProgress))try{o.onProgress(e)}catch(t){return bs({code:ks.MSG_ONPROGRESS_FUNCTION_ERROR})}}}).then(({location:t})=>{const o=this.isPrivateNetWork()?t:We(t);return s.updateFileUrl(o),e})}_uploadAudio(e){const t=this.getModule(ss),s=e.getElements()[0],o=t.getMessageOption(e.clientSequence);return this.doUploadAudio({file:o.payload.file,to:o.to,onProgress:e=>{if(s.updatePercent(e),ke(o.onProgress))try{o.onProgress(e)}catch(t){return bs({code:ks.MSG_ONPROGRESS_FUNCTION_ERROR})}}}).then(({location:t})=>{const o=this.isPrivateNetWork()?t:We(t);return s.updateAudioUrl(o),e})}_uploadVideo(e){const t=this.getModule(ss),s=e.getElements()[0],o=t.getMessageOption(e.clientSequence);return this.doUploadVideo({file:o.payload.file,to:o.to,onProgress:e=>{if(s.updatePercent(e),ke(o.onProgress))try{o.onProgress(e)}catch(t){return bs({code:ks.MSG_ONPROGRESS_FUNCTION_ERROR})}}}).then(t=>{const{location:o,snapshotInfo:i}=t,n=this.isPrivateNetWork()?o:We(o);return s.updateVideoUrl(n),Ct(i)||s.updateSnapshotInfo(i),e})}_checkSizeError(e){let t="";return"A"===e?t="audio":"I"===e?t="image":"V"===e?t="video":"F"===e&&(t="file"),bs({code:ks[`MSG_${e}_SIZE_LIMIT`],message:this.getErrorMessage("UploadSizeLimit",t,this.UPLOAD_SIZE_LIMIT[e]/1048576+"MB")})}doUploadImage(e){if(!e.file||this._isEmptyFileList(e.file.files))return bs({code:ks.MSG_I_SELECT_F_FIRST});const t=this._checkImageType(e.file);if(!0!==t)return t;const s=this._checkImageSize(e.file);if(!0!==s)return s;let o=null;return this._setUploadFileType(Vn),this.uploadByCOS(e).then(e=>(o=e,this.isPrivateNetWork()?st(e.location):st("https://"+e.location))).then(e=>(o.width=e.width,o.height=e.height,Promise.resolve(o)))}_checkImageType(e){let t="";return t=U?e.url.slice(e.url.lastIndexOf(".")+1):e.files[0].name.slice(e.files[0].name.lastIndexOf(".")+1),qn.indexOf(t.toLowerCase())>=0||bs({code:ks.MSG_I_TYPES_LIMIT})}_checkImageSize(e){let t=0;return t=U?e.size:e.files[0].size,0===t?bs({code:ks.MSG_F_IS_EMPTY}):t<this.UPLOAD_SIZE_LIMIT.I||this._checkSizeError("I")}doUploadFile(e){let t=null;return!e.file||this._isEmptyFileList(e.file.files)?(t={code:ks.MSG_F_SELECT_F_FIRST},bs(t)):e.file.files[0].size>this.UPLOAD_SIZE_LIMIT.F?this._checkSizeError("F"):0===e.file.files[0].size?(t={code:ks.MSG_F_IS_EMPTY},bs(t)):(this._setUploadFileType(Hn),this.uploadByCOS(e))}doUploadVideo(e){return e.file.videoFile.size>this.UPLOAD_SIZE_LIMIT.V?this._checkSizeError("V"):0===e.file.videoFile.size?bs({code:ks.MSG_F_IS_EMPTY}):-1===xn.indexOf(e.file.videoFile.type)?bs({code:ks.MSG_V_TYPES_LIMIT}):(this._setUploadFileType(Kn),U?this.handleVideoUpload({file:e.file.videoFile,onProgress:e.onProgress}):k?this.handleVideoUpload(e):void 0)}handleVideoUpload(e){return new Promise((t,s)=>{this.uploadByCOS(e).then(e=>{t(e)}).catch(()=>{this.uploadByCOS(e).then(e=>{t(e)}).catch(()=>{s(new Us({code:ks.MSG_V_UPLOAD_FAIL}))})})})}doUploadAudio(e){return e.file?e.file.size>this.UPLOAD_SIZE_LIMIT.A?this._checkSizeError("A"):0===e.file.size?bs({code:ks.MSG_F_IS_EMPTY}):(this._setUploadFileType(Bn),this.uploadByCOS(e)):bs({code:ks.MSG_A_UPLOAD_FAIL})}uploadByCOS(e){if(!ke(this._cosUploadMethod))return this.outputWarning("PluginUndetected"),bs({code:ks.COS_UNDETECTED});if(this.timUploadPlugin)return this._uploadWithPreSigUrl(e);const t=new In("upload"),s=this._n+".uploadByCOS",o=Date.now(),i=this._getFile(e);return new Promise((n,r)=>{const a=U?this._createCosOptionsWXMiniApp(e):this._createCosOptionsWeb(e),c=this;this._cosUploadMethod(a,(e,a)=>{const u=Object.create(null);if(a){if(e||Oe(a.files)&&a.files[0].error){const e=new Us({code:ks.MSG_F_UPLOAD_FAIL});return t.setError(e,!0,this.getNetworkType()).end(),me.l(s+" failed. error:",a.files[0].error),403===a.files[0].error.statusCode&&(me.w(s+" failed. cos AccessKeyId was invalid, regain auth key!"),this._getAuthorizationKey()),void r(e)}u.fileName=i.name,u.fileSize=i.size,u.fileType=i.type.slice(i.type.indexOf("/")+1).toLowerCase(),u.location=U?a.Location:a.files[0].data.Location;const l=Date.now()-o,d=`size:${c._formatFileSize(i.size)} time:${l}ms speed:${c._formatSpeed(1e3*i.size/l)}`;me.l(`${s} success. name:${i.name} ${d}`),n(u);const p=this.getModule(vs);return p.addCost(dn,l),p.addFileSize(dn,i.size),void t.setNetworkType(this.getNetworkType()).setMessage(d).end()}const l=new Us({code:ks.MSG_F_UPLOAD_FAIL});t.setError(l,!0,c.getNetworkType()).end(),me.w(s+" failed. error:",e),403===e.statusCode&&(me.w(s+" failed. cos AccessKeyId was invalid, regain auth key!"),this._getAuthorizationKey()),r(l)})})}_uploadWithPreSigUrl(e){const t=this._n+"._uploadWithPreSigUrl",s=this._getFile(e);return this._createCosOptionsPreSigUrl(e).then(e=>new Promise((o,i)=>{const n=new In("upload"),{requestSnapshotUrl:r,...a}=e,c=Date.now();this._cosUploadMethod(a,(e,u)=>{if(e||403===u.statusCode)return n.setError(new Us(e),!0,this.getNetworkType()).end(),me.l(t+" failed, error:",e),void i(new Us({code:ks.MSG_F_UPLOAD_FAIL}));const l=Object.create(null);let d=u.data.location||"";this.isPrivateNetWork()||0!==d.indexOf("https://")&&0!==d.indexOf("http://")||(d=d.split("//")[1]),l.fileName=s.name,l.fileSize=s.size,l.fileType=s.type.slice(s.type.indexOf("/")+1).toLowerCase(),l.location=d;const p=Date.now()-c,h=`size:${this._formatFileSize(s.size)},time:${p}ms,speed:${this._formatSpeed(1e3*s.size/p)} res:${JSON.stringify(u.data)}`;me.l(`${t} success name:${s.name},${h}`),n.setNetworkType(this.getNetworkType()).setMessage(h).end();const g=this.getModule(vs);g.addCost(dn,p),g.addFileSize(dn,s.size);let _=[];if(a.thumbUrl&&a.largeUrl&&(_=[this._getSmallImageInfoByUrl(a.thumbUrl,l),this._getLargeImageInfoByUrl(a.largeUrl,l)]),r&&_.push(this._getSnapshotInfoByUrl(r,l)),_.length>0)return Promise.all(_).then(()=>{o(l)});o(l)})}))}_getRawOrUploadProxyUrl(e){const t=this.getModule(ls).getFileUploadProxy();let s=e;return t&&(s=e.replace(/^https:\/\/[^/]+/,t)),s}_getFile(e){let t=null;return t=Oe(e.file.files)||Ge(e.file.files)?e.file.files[0]:e.file,t}_formatFileSize(e){return e<1024?e+"B":e<1048576?Math.floor(e/1024)+"KB":Math.floor(e/1048576)+"MB"}_formatSpeed(e){if(e<=1048576){return ut(e/1024,1)+"KB/s"}return ut(e/1048576,1)+"MB/s"}_createCosOptionsWeb(e){const t=this._getFile(e),s=t.name,o=s.slice(s.lastIndexOf(".")),i=this._genFileName(`${xe(999999)}${o}`);return{files:[{Bucket:`${this.bucketName}-${this.appid}`,Region:this.region,Key:`${this.directory}/${i}`,Body:t}],SliceSize:1048576,onProgress:t=>{if("function"==typeof e.onProgress)try{e.onProgress(t.percent)}catch(s){me.w("onProgress callback error:",s)}},onFileFinish:(e,t,s)=>{}}}_createCosOptionsWXMiniApp(e){const t=this._getFile(e),s=this._genFileName(t.name),o=t.url;return{Bucket:`${this.bucketName}-${this.appid}`,Region:this.region,Key:`${this.directory}/${s}`,FilePath:o,onProgress:t=>{if(me.l(JSON.stringify(t)),"function"==typeof e.onProgress)try{e.onProgress(t.percent)}catch(s){me.w("onProgress callback error:",s)}}}}_createCosOptionsPreSigUrl(e){let t="",s="",o=0;const i=this._getFile(e);if(U)t=this._genFileName(i.name),s=i.url,o=1;else{const e=i.name,n=e.slice(e.lastIndexOf("."));t=this._genFileName(`${xe(999999)}${n}`),s=i,o=0}return this._getCosPreSigUrl({fileType:this.uploadFileType,fileName:t,uploadMethod:o,duration:this.duration}).then(o=>{const{uploadUrl:i,downloadUrl:n,requestSnapshotUrl:r,thumbUrl:a,largeUrl:c,fileKey:u}=o;return{url:this._getRawOrUploadProxyUrl(i),fileType:this.uploadFileType,fileName:t,resources:s,downloadUrl:n,requestSnapshotUrl:r,thumbUrl:a,largeUrl:c,fileKey:u,onProgress:t=>{if("function"==typeof e.onProgress)try{e.onProgress(t.percent)}catch(s){me.w("onProgress callback error:",s),me.e(s)}}}})}_genFileName(e){return`${ot()}-${e}`}_setUploadFileType(e){this.uploadFileType=e}_getSnapshotInfoByUrl(e,t){const s=new In("getSnapshotInfo");return this.request({protocolName:Li,requestData:{platform:this.getPlatform(),coverName:this._genFileName(xe(99999)),requestSnapshotUrl:e}}).then(e=>{const{snapshotUrl:o}=e.data||{};return s.setMessage("snapshotUrl:"+o).end(),Ct(o)?{}:st(o).then(e=>{t.snapshotInfo={snapshotUrl:o,snapshotWidth:e.width,snapshotHeight:e.height}})}).catch(e=>(me.w(this._n+"._getSnapshotInfoByUrl failed. error:",e),s.setCode(e.errorCode).setMessage(e.errorInfo).end(),{}))}_getSmallImageInfoByUrl(e,t){return st(e).then(s=>{t.smallImageUrl=e,t.smallImageWidth=s.width,t.smallImageHeight=s.height})}_getLargeImageInfoByUrl(e,t){return st(e).then(s=>{t.largeImageUrl=e,t.largeImageWidth=s.width,t.largeImageHeight=s.height})}_isEmptyFileList(e){return!(!Ge(e)||0!==e.length)}reset(){me.l(this._n+".reset")}}class Nr{constructor(e){this._n="MergerMessageHandler",this._messageModule=e}uploadMergerMessage(e,t){const s=this._n+".uploadMergerMessage";me.d(s+" message:",e,"messageBytes:"+t);const{messageList:o}=e.payload,i=o.length,n=new In("uploadMergerMessage");return this._messageModule.request({protocolName:bi,requestData:{messageList:o}}).then(e=>{me.d(s+" ok. response:",e.data);const{pbDownloadKey:o,downloadKey:r}=e.data,a={pbDownloadKey:o,downloadKey:r,messageNumber:i};return n.setNetworkType(this._messageModule.getNetworkType()).setMessage(`${i}-${t}-${r}`).end(),a}).catch(e=>{throw me.w(s+" failed. error:",e),this._messageModule.probeNetwork().then(([t,s])=>{n.setError(e,t,s).end()}),e})}downloadMergerMessage(e){const t=this._n+".downloadMergerMessage";me.d(t+" message:",e);const{downloadKey:s}=e.payload,o=this._messageModule.getFileDownloadProxy(),i=new In("downloadMergerMessage");return i.setMessage("downloadKey:"+s),this._messageModule.request({protocolName:Fi,requestData:{downloadKey:s}}).then(s=>{if(me.d(t+" ok. response:",s.data),ke(e.clearElement)){const{downloadKey:t,pbDownloadKey:i,messageList:n,...r}=e.payload;e.clearElement(),e.setElement({type:e.type,content:{messageList:s.data.messageList,...r}},o)}else{const t=[];s.data.messageList.forEach(e=>{if(!Ct(e)){const s=new Un(e,o);t.push(s)}}),e.payload.messageList=t,e.payload.downloadKey="",e.payload.pbDownloadKey=""}return i.setNetworkType(this._messageModule.getNetworkType()).end(),e}).catch(e=>{throw me.w(`${t} failed. key:${s} error:`,e),this._messageModule.probeNetwork().then(([t,s])=>{i.setError(e,t,s).end()}),e})}createMergerMessagePack(e,s,o){return e.conversationType===t.CONV_C2C?this._createC2CMergerMessagePack(e,s,o):this._createGroupMergerMessagePack(e,s,o)}_createC2CMergerMessagePack(e,t,s){let o=null;t&&(t.offlinePushInfo&&(o=t.offlinePushInfo),!0===t.onlineUserOnly&&(o?o.disablePush=!0:o={disablePush:!0}));const i=[];if(Le(t)&&Le(t.messageControlInfo)){const{excludedFromUnreadCount:e,excludedFromLastMessage:s,excludedFromContentModeration:o}=t.messageControlInfo;!0===e&&i.push("NoUnread"),!0===s&&i.push("NoLastMsg"),!0===o&&i.push("NoMsgCheck")}let n="";Ee(e.cloudCustomData)&&e.cloudCustomData.length>0&&(n=e.cloudCustomData);const{pbDownloadKey:r,downloadKey:a,messageNumber:c}=s,{title:u,abstractList:l,compatibleText:d}=e.payload,p=this._messageModule.getModule(ns),h=p&&p.isOnlineMessage(e,t)?0:void 0;return{protocolName:Bs,tjgID:this._messageModule.generateTjgID(e),requestData:{fromAccount:this._messageModule.getMyUserID(),toAccount:e.to,msgBody:[{msgType:e.type,msgContent:{pbDownloadKey:r,downloadKey:a,title:u,abstractList:l,compatibleText:d,messageNumber:c}}],cloudCustomData:n,clientTime:e.clientTime,msgSeq:e.sequence,msgRandom:e.random,msgLifeTime:h,offlinePushInfo:o?{pushFlag:!0===o.disablePush?1:0,title:o.title||"",desc:o.description||"",ext:o.extension||"",apnsInfo:{badgeMode:!0===o.ignoreIOSBadge?1:0},androidInfo:{OPPOChannelID:o.androidOPPOChannelID||""}}:void 0,messageControlInfo:0!==h?i:void 0,isSupportExtension:!0===e.isSupportExtension?1:0,isRelayMessage:!0===e._relayFlag?1:0}}}_createGroupMergerMessagePack(e,t,s){let o=null;t&&t.offlinePushInfo&&(o=t.offlinePushInfo);const i=[];if(Le(t)&&Le(t.messageControlInfo)){const{excludedFromUnreadCount:e,excludedFromLastMessage:s,excludedFromContentModeration:o}=t.messageControlInfo;!0===e&&i.push("NoUnread"),!0===s&&i.push("NoLastMsg"),!0===o&&i.push("NoMsgCheck")}let n="";Ee(e.cloudCustomData)&&e.cloudCustomData.length>0&&(n=e.cloudCustomData);const{pbDownloadKey:r,downloadKey:a,messageNumber:c}=s,{title:u,abstractList:l,compatibleText:d}=e.payload,p=this._messageModule.getModule(rs),h=p&&p.isOnlineMessage(e,t)?1:0;return{protocolName:Hs,tjgID:this._messageModule.generateTjgID(e),requestData:{fromAccount:this._messageModule.getMyUserID(),groupID:e.to,msgBody:[{msgType:e.type,msgContent:{pbDownloadKey:r,downloadKey:a,title:u,abstractList:l,compatibleText:d,messageNumber:c}}],random:e.random,priority:e.priority,clientSequence:e.clientSequence,groupAtInfo:void 0,cloudCustomData:n,onlineOnlyFlag:h,offlinePushInfo:o?{pushFlag:!0===o.disablePush?1:0,title:o.title||"",desc:o.description||"",ext:o.extension||"",apnsInfo:{badgeMode:!0===o.ignoreIOSBadge?1:0},androidInfo:{OPPOChannelID:o.androidOPPOChannelID||""}}:void 0,clientTime:e.clientTime,needReadReceipt:!0!==e.needReadReceipt||p.isMessageFromOrToAVChatroom(e.to)?0:1,messageControlInfo:0===h?i:void 0,isSupportExtension:!0===e.isSupportExtension?1:0,isRelayMessage:!0===e._relayFlag?1:0}}}}const Er={ERR_SVR_COMM_SENSITIVE_TEXT:80001,ERR_SVR_COMM_BODY_SIZE_LIMIT:80002,OPEN_SERVICE_OVERLOAD_ERROR:60022,ERR_SVR_MSG_PKG_PARSE_FAILED:20001,ERR_SVR_MSG_INTERNAL_AUTH_FAILED:20002,ERR_SVR_MSG_INVALID_ID:20003,ERR_SVR_MSG_PUSH_DENY:20006,ERR_SVR_MSG_IN_PEER_BLACKLIST:20007,ERR_SVR_MSG_BOTH_NOT_FRIEND:20009,ERR_SVR_MSG_NOT_PEER_FRIEND:20010,ERR_SVR_MSG_NOT_SELF_FRIEND:20011,ERR_SVR_MSG_SHUTUP_DENY:20012,ERR_SVR_GROUP_INVALID_PARAMETERS:10004,ERR_SVR_GROUP_PERMISSION_DENY:10007,ERR_SVR_GROUP_NOT_FOUND:10010,ERR_SVR_GROUP_INVALID_GROUPID:10015,ERR_SVR_GROUP_REJECT_FROM_THIRDPARTY:10016,ERR_SVR_GROUP_SHUTUP_DENY:10017,MSG_SEND_FAIL:2100,OVER_FREQUENCY_LIMIT:2996},Ar=[ks.MSG_ONPROGRESS_FUNCTION_ERROR,ks.MSG_I_SELECT_F_FIRST,ks.MSG_I_TYPES_LIMIT,ks.MSG_F_IS_EMPTY,ks.MSG_I_SIZE_LIMIT,ks.MSG_F_SELECT_F_FIRST,ks.MSG_F_SIZE_LIMIT,ks.MSG_V_SIZE_LIMIT,ks.MSG_V_TYPES_LIMIT,ks.MSG_A_UPLOAD_FAIL,ks.MSG_A_SIZE_LIMIT,ks.COS_UNDETECTED];function Lr(e){let t=!1;return Object.values(Er).includes(e)&&(t=!0),(e>=120001&&e<=13e4||e>=10100&&e<=10200)&&(t=!0),t}class Or extends Fs{constructor(e){super(e),this._n="MessageModule",this._messageOptionsMap=new Map,this._mergerMessageHandler=new Nr(this)}createTextMessage(e){const t=this.getMyUserID();e.currentUser=t,e.senderTinyID=this.getMyTinyID();const s=new Gn(e),o=Ee(e.payload)?e.payload:e.payload.text,i=new Cn({text:o}),n=this._getNickAndAvatarByUserID(t);return s.setElement(i),s.setNickAndAvatar(n),s.setNameCard(this._getNameCardByGroupID(s)),s}createImageMessage(e){const t=this.getMyUserID();e.currentUser=t,e.senderTinyID=this.getMyTinyID();const s=new Gn(e);if(U){const{file:t}=e.payload;if(Se(t))return void this.outputWarning("FileUnsupportedInMP","createImageMessage");const s=t.tempFiles[0].path||t.tempFiles[0].tempFilePath,o={url:s,name:s.slice(s.lastIndexOf("/")+1),size:t.tempFiles&&t.tempFiles[0].size||1,type:s.slice(s.lastIndexOf(".")+1).toLowerCase()};e.payload.file=o}else if(k)if(Se(e.payload.file)){const t=e.payload.file;e.payload.file={files:[t]}}else if(Le(e.payload.file)&&"undefined"!=typeof uni){const t=e.payload.file.tempFiles[0];e.payload.file={files:[t]}}const o=new yn({imageFormat:Me.UNKNOWN,uuid:this._generateUUID(e.payload.file),file:e.payload.file}),i=this._getNickAndAvatarByUserID(t);return s.setElement(o),s.setNickAndAvatar(i),s.setNameCard(this._getNameCardByGroupID(s)),this._messageOptionsMap.set(s.clientSequence,e),s}createAudioMessage(e){const{file:t}=e.payload;if(U){const s={url:t.tempFilePath,name:t.tempFilePath.slice(t.tempFilePath.lastIndexOf("/")+1),size:t.fileSize,second:parseInt(t.duration)/1e3,type:t.tempFilePath.slice(t.tempFilePath.lastIndexOf(".")+1).toLowerCase()};e.payload.file=s}const s=this.getMyUserID();e.currentUser=s,e.senderTinyID=this.getMyTinyID();const o=new Gn(e),i=new vn({second:Math.floor(t.duration/1e3),size:t.fileSize||t.size,url:t.tempFilePath,uuid:this._generateUUID(e.payload.file)}),n=this._getNickAndAvatarByUserID(s);return o.setElement(i),o.setNickAndAvatar(n),o.setNameCard(this._getNameCardByGroupID(o)),this._messageOptionsMap.set(o.clientSequence,e),o}createVideoMessage(e){const t=this.getMyUserID();e.currentUser=t,e.senderTinyID=this.getMyTinyID(),e.payload.file.thumbUrl="",e.payload.file.thumbSize=0;const s={};if(U){if(A)return void this.outputWarning("VideoUnsupportedInAlipay");if(Se(e.payload.file))return void this.outputWarning("FileUnsupportedInMP","createVideoMessage");let{file:t}=e.payload;Oe(t.tempFiles)&&(t=t.tempFiles[0]),s.url=t.tempFilePath,s.name=t.tempFilePath.slice(t.tempFilePath.lastIndexOf("/")+1),s.size=t.size||1,s.second=t.duration||0,s.type=t.tempFilePath.slice(t.tempFilePath.lastIndexOf(".")+1).toLowerCase()}else if(k){if(Se(e.payload.file)){const t=e.payload.file;e.payload.file.files=[t]}else if(Le(e.payload.file)&&"undefined"!=typeof uni){const t=e.payload.file.tempFile;e.payload.file.files=[t]}const{file:t}=e.payload;s.url=window.URL.createObjectURL(t.files[0]),s.name=t.files[0].name,s.size=t.files[0].size||1,s.second=t.files[0].duration||0,s.type=t.files[0].type.split("/")[1]}e.payload.file.videoFile=s;const o=new Gn(e),i=new On({videoFormat:s.type,videoSecond:ut(s.second,0),videoSize:s.size,remoteVideoUrl:"",videoUrl:s.url,videoUUID:this._generateUUID(e.payload.file.videoFile),thumbUUID:this._generateUUID(e.payload.file.videoFile),thumbWidth:e.payload.file.width||200,thumbHeight:e.payload.file.height||200,thumbUrl:e.payload.file.thumbUrl,thumbSize:e.payload.file.thumbSize,thumbFormat:e.payload.file.thumbUrl.slice(e.payload.file.thumbUrl.lastIndexOf(".")+1).toLowerCase()}),n=this._getNickAndAvatarByUserID(t);return o.setElement(i),o.setNickAndAvatar(n),o.setNameCard(this._getNameCardByGroupID(o)),this._messageOptionsMap.set(o.clientSequence,e),o}createCustomMessage(e){const t=this.getMyUserID();e.currentUser=t,e.senderTinyID=this.getMyTinyID();const s=new Gn(e),o=new Ln({data:e.payload.data,description:e.payload.description,extension:e.payload.extension}),i=this._getNickAndAvatarByUserID(t);return s.setElement(o),s.setNickAndAvatar(i),s.setNameCard(this._getNameCardByGroupID(s)),s}createFaceMessage(e){const t=this.getMyUserID();e.currentUser=t,e.senderTinyID=this.getMyTinyID();const s=new Gn(e),o=new Tn(e.payload),i=this._getNickAndAvatarByUserID(t);return s.setElement(o),s.setNickAndAvatar(i),s.setNameCard(this._getNameCardByGroupID(s)),s}createMergerMessage(e){const t=this.getMyUserID();e.currentUser=t,e.senderTinyID=this.getMyTinyID();const s=this._getNickAndAvatarByUserID(t),o=new Gn(e),i=new kn(e.payload);return o.setElement(i),o.setNickAndAvatar(s),o.setNameCard(this._getNameCardByGroupID(o)),o.setRelayFlag(!0),o}createForwardMessage(e){const{to:s,conversationType:o,priority:i,payload:n,needReadReceipt:r,receiverList:a}=e,c=this.getMyUserID(),u=this._getNickAndAvatarByUserID(c);if(n.type===t.MSG_GRP_TIP)return bs({code:ks.MSG_FORWARD_TYPE_INVALID});const l={to:s,conversationType:o,conversationID:`${o}${s}`,priority:i,isPlaceMessage:0,status:vt.UNSEND,currentUser:c,senderTinyID:this.getMyTinyID(),cloudCustomData:e.cloudCustomData||n.cloudCustomData||"",needReadReceipt:r,receiverList:a,isSupportExtension:e.isSupportExtension||!1},d=new Gn(l);return d.setElement(n.getElements()[0]),d.setNickAndAvatar(u),d.setNameCard(this._getNameCardByGroupID(n)),d.setRelayFlag(!0),d}downloadMergerMessage(e){return this._mergerMessageHandler.downloadMergerMessage(e)}createFileMessage(e){if(U){if(!D&&!S&&!O)return;let e;const t=P.getSystemInfoSync().SDKVersion;if(D&&(e="2.5.0",nt(t,e)<0))return void this.outputWarning("WXChooseMessageFile");if(S&&(e="1.18.0",nt(t,e)<0))return void this.outputWarning("QQChooseMessageFile")}if(k||O){if(Se(e.payload.file)){const t=e.payload.file;e.payload.file={files:[t]}}else if(Le(e.payload.file)&&"undefined"!=typeof uni){const{tempFiles:t,files:s}=e.payload.file;let o=null;Oe(t)?o=t[0]:Oe(s)&&(o=s[0]),e.payload.file={files:[o]}}}else if(D||S){const{tempFiles:t}=e.payload.file,s={...t[0],url:t[0].path};e.payload.file={files:[s]}}const t=this.getMyUserID();e.currentUser=t,e.senderTinyID=this.getMyTinyID();const s=new Gn(e),o=new An({uuid:this._generateUUID(e.payload.file),file:e.payload.file}),i=this._getNickAndAvatarByUserID(t);return s.setElement(o),s.setNickAndAvatar(i),s.setNameCard(this._getNameCardByGroupID(s)),this._messageOptionsMap.set(s.clientSequence,e),s}createLocationMessage(e){const t=this.getMyUserID();e.currentUser=t,e.senderTinyID=this.getMyTinyID();const s=new Gn(e),o=new Rn(e.payload),i=this._getNickAndAvatarByUserID(t);return s.setElement(o),s.setNickAndAvatar(i),s.setNameCard(this._getNameCardByGroupID(s)),s}_onCannotFindModule(){return bs({code:ks.CANNOT_FIND_MODULE})}sendMessageInstance(e,s){if(!1===this.getModule(Ns).filterMessage(e,s))return this._onSendMessageFailed(e,new Us({code:ks.PROFANITY_FOUND}));let o=null;if(e.conversationType===t.CONV_C2C)o=this.getModule(ns);else{if(e.conversationType!==t.CONV_GROUP)return bs({code:ks.MSG_INVALID_CONV_TYPE});o=this.getModule(rs)}const i=this._n+".sendMessageInstance",n=this.getModule(us),r=o.isOnlineMessage(e,s);let a;return this.getModule(gs).upload(e).then(()=>{if(this._getSendMessageSpecifiedKey(e)===ln){this.getModule(vs).addSuccessCount(dn)}return this._guardForGroup(e).then(()=>{if(!e.isSendable())return bs({code:ks.MSG_F_URL_IS_EMPTY});this._addSendMessageTotalCount(e),a=Date.now();const i=function(e){let t="utf-8";k&&document&&(t=document.charset.toLowerCase());let s,o=0,i=0;if(i=e.length,"utf-8"===t||"utf8"===t)for(let n=0;n<i;n++)s=e.codePointAt(n),s<=127?o+=1:s<=2047?o+=2:s<=65535?o+=3:(o+=4,n++);else if("utf-16"===t||"utf16"===t)for(let n=0;n<i;n++)s=e.codePointAt(n),s<=65535?o+=2:(o+=4,n++);else o=e.replace(/[^\x00-\xff]/g,"aa").length;return o}(JSON.stringify(e));return e.type===t.MSG_MERGER&&i>11264?this._mergerMessageHandler.uploadMergerMessage(e,i).then(t=>{const o=this._mergerMessageHandler.createMergerMessagePack(e,s,t);return this.request(o)}):(n.setMessageRandom(e),o.sendMessage(e,s))}).then(o=>{const{time:c,sequence:u,readReceiptCode:l}=o.data;if(Ne(l)&&0!==l){new In("sendMessageWithReceipt").setMessage(`from:${e.from} to:${e.to} sequence:${u} readReceiptCode:${l}`).end(),me.w(`${i} readReceiptCode:${l} message:${this.getErrorMessage(l)}`)}if(this._addSendMessageSuccessCount(e,a),this._messageOptionsMap.delete(e.clientSequence),!0===e.isResend){const t=n.findMessage(e.ID);t&&(me.l(`${i} resend ok. ID:${t.ID}`),n.deleteLocalMessage(t))}e.status=vt.SUCCESS,e.time=c;let d=!1;if(e.conversationType===t.CONV_GROUP)e.sequence=u;else if(e.conversationType===t.CONV_C2C){const t=n.getLatestMessageSentByMe(e.conversationID);if(t){const{nick:s,avatar:o}=t;s===e.nick&&o===e.avatar||(d=!0)}}if(d&&n.modifyMessageSentByMe({conversationID:e.conversationID,latestNick:e.nick,latestAvatar:e.avatar}),!0===r)e._onlineOnlyFlag=!0;else{n.appendToMessageList(e);let o=e;Le(s)&&Le(s.messageControlInfo)&&(!0===s.messageControlInfo.excludedFromLastMessage&&(e._isExcludedFromLastMessage=!0,o=""),!0===s.messageControlInfo.excludedFromUnreadCount&&(e._isExcludedFromUnreadCount=!0));let i=e.conversationType;if(Je(e.to)){i=t.CONV_TOPIC;this.getModule(cs).onMessageSent({groupID:pt(e.to),topicID:e.to,lastMessage:o})}n.onMessageSent({conversationOptionsList:[{conversationID:e.conversationID,unreadCount:0,type:i,subType:e.conversationSubType,lastMessage:o}]})}return e.getRelayFlag()||"TIMImageElem"!==e.type||at(e.payload.imageInfoArray),Rs({message:e})})}).catch(t=>this._onSendMessageFailed(e,t,r))}_guardForGroup(e){if(e.conversationType!==t.CONV_GROUP)return Promise.resolve();const s=this.getModule(rs);if(!s)return this._onCannotFindModule();if(je({groupID:e.to})){const t=s.getLocalGroupProfile(e.to);if(t&&t.isSupportTopic)return bs({code:ks.MSG_SEND_GRP_WITH_TOPIC_FAIL})}return s.guardForAVChatRoom(e)}_onSendMessageFailed(e,t,s=!1){const o=this._n+"._onSendMessageFailed";e.status=vt.FAIL;const i=this.getModule(us);if(i.deleteMessageRandom(e),!s){!0===i.appendToMessageList(e)&&me.l(`${o} message stored, ID:${e.ID}`)}this._addSendMessageFailCountOnUser(e,t);const n=new In("sendMessage");return n.setMessage(`tjg_id:${this.generateTjgID(e)} type:${e.type} from:${e.from} to:${e.to}`),this.probeNetwork().then(([e,s])=>{n.setError(t,e,s).end()}),me.e(o+" error:",t),bs(new Us({code:t&&t.code?t.code:ks.MSG_SEND_FAIL,message:t&&t.message?t.message:void 0,data:{message:e}}))}_getSendMessageSpecifiedKey(e){if([t.MSG_IMAGE,t.MSG_AUDIO,t.MSG_VIDEO,t.MSG_FILE].includes(e.type))return ln;if(e.conversationType===t.CONV_C2C)return an;if(e.conversationType===t.CONV_GROUP){const t=this.getModule(rs);if(!t)return;const s=t.getLocalGroupProfile(e.to);if(!s)return;const o=s.type;return ze(o)?un:cn}}_addSendMessageTotalCount(e){const t=this._getSendMessageSpecifiedKey(e);if(t){this.getModule(vs).addTotalCount(t)}}_addSendMessageSuccessCount(e,t){const s=Math.abs(Date.now()-t),o=this._getSendMessageSpecifiedKey(e);if(o){const e=this.getModule(vs);e.addSuccessCount(o),e.addCost(o,s)}}_addSendMessageFailCountOnUser(e,t){const{code:s=-1}=t,o=this.getModule(vs),i=this._getSendMessageSpecifiedKey(e);i===ln&&function(e){let t=!1;return Ar.includes(e)&&(t=!0),t}(s)?o.addFailedCountOfUserSide(dn):Lr(s)&&i&&o.addFailedCountOfUserSide(i)}resendMessage(e,t){return e.isResend=!0,e.status=vt.UNSEND,this.sendMessageInstance(e,t)}revokeMessage(e){let s=null;if(e.conversationType===t.CONV_C2C?s=this.getModule(ns):e.conversationType===t.CONV_GROUP&&(s=this.getModule(rs)),!s)return this._onCannotFindModule();const o=new In("revokeMessage");o.setMessage(`tjg_id:${this.generateTjgID(e)} type:${e.type} from:${e.from} to:${e.to}`);const i=this._n+".revokeMessage";return s.revokeMessage(e).then(t=>{const s=t.data.recallRetList;if(!Ct(s)&&0!==s[0].retCode){const t=new Us({code:s[0].retCode,data:{message:e}});return o.setCode(t.code).setMoreMessage(t.message).end(),bs(t)}me.i(`${i} ok. ID:${e.ID}`),e.isRevoked=!0,o.end();return this.getModule(us).onMessageRevoked([e]),Rs({message:e})}).catch(t=>{this.probeNetwork().then(([e,s])=>{o.setError(t,e,s).end()});const s=new Us({code:t&&t.code?t.code:ks.MSG_REVOKE_FAIL,message:t&&t.message?t.message:void 0,data:{message:e}});return me.w(i+" failed. error:",t),bs(s)})}deleteMessage(e){let s=null;const o=e[0],i=o.conversationID;let n="",r=[],a=[];if(o.conversationType===t.CONV_C2C)s=this.getModule(ns),n=i.replace(t.CONV_C2C,""),e.forEach(e=>{e&&e.status===vt.SUCCESS&&e.conversationID===i&&(e._onlineOnlyFlag||r.push(`${e.sequence}_${e.random}_${e.time}`),a.push(e))});else if(o.conversationType===t.CONV_GROUP)s=this.getModule(rs),n=i.replace(t.CONV_GROUP,""),e.forEach(e=>{e&&e.status===vt.SUCCESS&&e.conversationID===i&&(e._onlineOnlyFlag||r.push(""+e.sequence),a.push(e))});else if(o.conversationType===t.CONV_SYSTEM)return bs({code:ks.CANNOT_DELETE_GRP_SYSTEM_NOTICE});if(!s)return this._onCannotFindModule();if(0===r.length)return this._onMessageDeleted(a);r.length>30&&(r=r.slice(0,30),a=a.slice(0,30));const c=new In("deleteMessage");c.setMessage(`to:${n} count:${r.length}`);const u=this._n+".deleteMessage";return s.deleteMessage({to:n,keyList:r}).then(e=>(c.end(),me.i(u+" ok"),this._onMessageDeleted(a))).catch(e=>{this.probeNetwork().then(([t,s])=>{c.setError(e,t,s).end()}),me.w(u+" failed. error:",e);const t=new Us({code:e&&e.code?e.code:ks.MSG_DELETE_FAIL,message:e&&e.message?e.message:void 0});return bs(t)})}_onMessageDeleted(e){return this.getModule(us).onMessageDeleted(e),ws({messageList:e})}translateText(e){const t=this._n+".translateText",{sourceTextList:s,sourceLanguage:o,targetLanguage:i}=e,n=new In("translateText");return n.setMessage(`sourceLanguage:${o} targetLanguage:${i}`),this.request({protocolName:sn,requestData:{sourceTextList:s,source:o||"auto",target:i,from:this.getMyTinyID(),SDKAppID:this.getSDKAppID()}}).then(e=>{const{error:s,requestID:o,translatedTextList:i}=e.data,{code:r}=s;if(0===r)return n.end(),me.i(`${t} ok. requestID:${o}`),Rs({translatedTextList:i});throw{...s,requestID:o}}).catch(e=>(n.setCode(e.code).setMoreMessage(e.requestID).end(),me.w(t+" failed. error:",e),bs({code:ks.TRANSLATE_TEXT_FAIL})))}convertVoiceToText(e){const{message:t,language:s}=e;let o=t.payload.url;t.from===this.getMyUserID()&&"out"===t.flow&&(o=t.payload.remoteAudioUrl);const i=/\.(wav|pcm|ogg-opus|speex|silk|mp3|m4a|aac|amr)/;if(!i.test(o))return bs({code:ks.UNSUPPORTED_VOICE_FORMAT});const n=i.exec(o)[1]||"mp3";let r="16k_zh-PY";s?"zh (cmn-Hans-CN)"===s?r="16k_zh":"en-US"===s?r="16k_en":"yue-Hant-HK"===s?r="16k_yue":"ja-JP"===s&&(r="16k_ja"):r="16k_zh-PY";const a=`serviceType:${r} url:${o}`,c=this._n+".convertVoiceToText";me.i(`${c} ${a}`);const u=new In("convertVoiceToText");return u.setMessage(a),this.request({protocolName:on,requestData:{url:o,language:r,SDKAppID:this.getSDKAppID(),format:n}}).then(e=>{const{error:t,requestID:s,result:o}=e.data,{code:i}=t;if(0===i)return u.end(),me.i(`${c} ok. requestID:${s}`),Rs({result:o});throw{...t,requestID:s}}).catch(e=>(u.setCode(e.code).setMoreMessage(e.requestID||"").end(),me.w(c+" failed. error:",e),bs({code:ks.VOICE_TO_TEXT_FAIL})))}modifyRemoteMessage(e){let s=null;const{conversationType:o,to:i}=e,n=this.getModule(rs);if(!n)return this._onCannotFindModule();if(n.isMessageFromOrToAVChatroom(i))return bs({code:ks.MSG_MODIFY_DISABLED_IN_AVCHATROOM,data:{message:e}});if(!1===this.getModule(Ns).filterMessage(e))return bs({code:ks.PROFANITY_FOUND,data:{message:e}});o===t.CONV_C2C?s=this.getModule(ns):o===t.CONV_GROUP&&(s=this.getModule(rs));const r=new In("modifyMessage");r.setMessage("to:"+i);const a=this._n+".modifyRemoteMessage";return s.modifyRemoteMessage(e).then(t=>{r.end(),me.i(a+" ok");const s=this._onModifyRemoteMessageResp(e,t.data);return Rs({message:s})}).catch(t=>{if(r.setCode(t.code).setMoreMessage(t.message).end(),me.w(a+" failed. error:",t),20027===t.code){const s=this._onModifyRemoteMessageResp(e,t.data);return bs({code:ks.MSG_MODIFY_CONFLICT,data:{message:s}})}return bs({code:t.code,message:t.message,data:{message:e}})})}_generateSearchRequestData(e){const{conversationID:s,timePosition:o,timePeriod:i,...n}=e,r={...n,endTime:o};return Re(s)||(Xe(s)&&(r.account=s.replace(t.CONV_C2C,"")),Qe(s)&&(r.groupID=s.replace(t.CONV_GROUP,""))),Ne(i)&&(r.startTime=o?o-(i||0):Number(((new Date).getTime()/1e3).toFixed(0))-(i||0)),Ne(r.count)&&(r.count=Math.min(r.count,100)),r}searchCloudMessages(e){const t="searchCloudMessages",s=`${this._n}.${t}`;if(!e)return bs({code:ks.OPTIONS_IS_EMPTY,message:this.getErrorMessage(ks.OPTIONS_IS_EMPTY,t)});const{keywordList:o,keywordListMatchType:i,conversationID:n,cursor:r}=e,a=Oe(e.senderUserIDList)&&e.senderUserIDList.length>0,c=Oe(e.messageTypeList)&&e.messageTypeList.length>0;if(!o&&!a&&!c)throw me.e(`[${t}] Missing required params: "keywordList".`),new Error("Params validate failed.");const u=Date.now(),l=new In(t),d=`keywordList:${o} keywordListMatchType:${i} conversationID:${n} cursor:${r}`;return me.l(`${s} ${d}`),this.request({protocolName:ki,requestData:this._generateSearchRequestData(e)}).then(t=>{const{errorCode:o,errorInfo:i}=t.data,n=Date.now()-u;if(0!==o){if(o===ks.ERR_SVR_COMM_INVALID_SERVICE||o===ks.MSG_SEARCH_CURSOR_INVALID||o===ks.MSG_SEARCH_CURSOR_EXPIRED)return bs({code:o});throw new Us({code:o,message:i})}const{cursor:r,totalCount:a,searchResult:c}=t.data;l.setNetworkType(this.getNetworkType()).setMessage(`${d} totalCount:${a} cost ${n} ms`).end();const p=this._handleSearchResults(c,!e.conversationID);return me.l(`${s} ok. cursor:${r} totalCount:${a} cost ${n} ms`),Rs({searchResultList:p,cursor:r,totalCount:a})}).catch(e=>(this.probeNetwork().then(([t,s])=>{l.setMessage(d).setError(e,t,s).end()}),bs(e)))}_handleSearchResults(e,s){const o=this.getModule(us);return Oe(e)&&0!==e.length?e.map(({groupID:e,userID:i,messageCount:n,messageList:r})=>{const a=e?`${t.CONV_GROUP}${e}`:`${t.CONV_C2C}${i}`,c={conversationID:a,messageCount:n,messageList:[]};return s&&n>1||r&&r.length>0&&(c.messageList=o.onRoamingMessage(r,a,!1)),c}):[]}_onModifyRemoteMessageResp(e,t){me.d(this._n+"._onModifyRemoteMessageResp options:",t);const{conversationType:s,from:o,to:i,random:n,sequence:r,time:a}=e,{elements:c,messageVersion:u,cloudCustomData:l=""}=t;return this.getModule(us).onMessageModified({conversationType:s,from:o,to:i,time:a,random:n,sequence:r,elements:c,cloudCustomData:l,messageVersion:u})}_generateUUID(e){const t=this.getModule(ls);let s=`${t.getSDKAppID()}-${t.getUserID()}-${function(){let e="";for(let t=32;t>0;--t)e+=Ve[Math.floor(Math.random()*Ke)];return e}()}`;const o=e.name||e.value||e.url||e.tempFilePath,i=o&&o.slice(o.lastIndexOf(".")+1);return i&&(s=`${s}.${i}`),s}getMessageOption(e){return this._messageOptionsMap.get(e)}_getNickAndAvatarByUserID(e){return this.getModule(is).getNickAndAvatarByUserID(e)}_getNameCardByGroupID(e){if(e.conversationType===t.CONV_GROUP){const t=this.getModule(rs);if(t)return t.getMyNameCardByGroupID(e.to)}return""}reset(){me.l(this._n+".reset"),this._messageOptionsMap.clear()}}class Rr extends Fs{constructor(e){super(e),this._n="MessageExtensionModule",this.messageExtensionMap=new Map,this.globalSeqMap=new Map,this.getMessageExtensionsMap=new Map}onMessageExtensionNotify(t){const{messageInfo:s,operateType:o,operateResultList:i,tinyID:n,globalSequence:r}=t.dataList,{clientTime:a,random:c}=s,u=`${n}-${a}-${c}`,l=[],d=[];me.l(`${this._n}.onMessageExtensionNotify messageID:${u} operateType:${o} globalSequence:${r}`),this._updateGlobalSequence(u,r);let p=!1,h=!1;i.forEach(e=>{const{extensions:t=[],clearSequence:s}=e;if(1===o)p=!0,t.forEach(e=>{l.push({key:e.key,value:e.value})}),this._updateLocalExtension(u,t);else if(2===o)h=!0,t.forEach(e=>{d.push(e.key)}),this._updateLocalExtension(u,t);else if(3===o){if(h=!0,this._hasLocalExtension(u)){this._getLocalExtension(u).forEach((e,t)=>{e.seq<=s&&!Ct(e.value)&&d.push(t)})}this._clearLocalExtension(u,s)}}),p&&this.emitOuterEvent(e.MESSAGE_EXTENSIONS_UPDATED,{messageID:u,extensions:l}),h&&this.emitOuterEvent(e.MESSAGE_EXTENSIONS_DELETED,{messageID:u,keyList:d})}setMessageExtensions(e,t){const s="setMessageExtensions";if(!this.canIUse(M.MSG_EXT))return this.cannotUseCommercialAbility(s);const o=`${this._n}.${s}`,{ID:i,conversationID:n,sequence:r,time:a}=e;let c=[...t];t.length>20&&(c=t.slice(0,20),me.w(o+". the length of extensions cannot exceed 20."));const u=`conversationID:${n} messageID:${i} sequence:${r} time:${a} count:${c.length}`,l=new In(s);return l.setMessage(u),me.l(`${o} ${u}`),this._modifyMessageExtensions(e,c).then(e=>{const{resultList:t,successCount:s,failureCount:i}=e,n=`success count:${s} fail count:${i}`;return l.setMoreMessage(n).end(),me.l(`${o} ok. ${n}`),Rs({extensions:t})}).catch(e=>(this.probeNetwork().then(([t,s])=>{l.setError(e,t,s).end()}),me.e(o+" failed. error:",e),bs(e)))}getMessageExtensions(e){const t="getMessageExtensions";if(!this.canIUse(M.MSG_EXT))return this.cannotUseCommercialAbility(t);const s=`${this._n}.${t}`,{ID:o,conversationID:i,sequence:n,time:r}=e,a=`conversationID:${i} messageID:${o} sequence:${n} time:${r}`,c=new In(t);c.setMessage(a),me.l(`${s} ${a}`);let u=void 0;return this.getMessageExtensionsMap.has(o)&&(u=this._getGlobalSequence(o)),this._getMessageExtensions(e,u).then(e=>(c.end(),me.l(`${s} ok. total count:${e.length}`),Re(u)&&e.length>0&&this.getMessageExtensionsMap.set(o,1),Rs({extensions:e}))).catch(e=>(this.probeNetwork().then(([t,s])=>{c.setError(e,t,s).end()}),me.e(s+" failed. error:",e),bs(e)))}deleteMessageExtensions(e,t){const s="deleteMessageExtensions";if(!this.canIUse(M.MSG_EXT))return this.cannotUseCommercialAbility(s);const o=`${this._n}.${s}`,i=[];let n=3;Ct(t)||(n=2,t.forEach(e=>{i.push({key:e,value:"",seq:0})}));const{ID:r,conversationID:a,sequence:c,time:u}=e,l=`conversationID:${a} messageID:${r} sequence:${c} time:${u} operateType:${n}`,d=new In(s);return d.setMessage(l),me.l(`${o} ${l}`),this._modifyMessageExtensions(e,i,n).then(e=>{const{resultList:t,successCount:s,failureCount:i}=e;let r="";return 2===n&&(r=`success count:${s} fail count:${i}`),d.setMoreMessage(""+r).end(),me.l(`${o} ok. ${r}`),Rs({extensions:t})}).catch(e=>(this.probeNetwork().then(([t,s])=>{d.setError(e,t,s).end()}),me.e(o+" failed. error:",e),bs(e)))}_modifyMessageExtensions(e,s,o=1){const i=Je(e.to)?t.CONV_TOPIC:e.conversationType;let n=void 0;3!==o&&(n=this._getRequestExtensions(e,s));let r=null;switch(i){case t.CONV_C2C:r=this.getModule(ns);break;case t.CONV_GROUP:r=this.getModule(rs);break;case t.CONV_TOPIC:r=this.getModule(cs);break;default:return bs({code:ks.CANNOT_FIND_MODULE})}return r.modifyMessageExtensions(e,n,o).then(t=>{let{extensions:s,seq:o}=t.data;const i=[];let n=0,r=0,a=[];return s=Ct(s)?[]:s,s.forEach(e=>{const{errorCode:t,extension:s}=e,{key:o,value:c,seq:u}=s;i.push({code:t,key:o,value:c}),0===t?n++:r++,a.push({key:o,value:c,seq:u})}),this._updateGlobalSequence(e.ID,o),a.length>0&&(this._updateLocalExtension(e.ID,a),a=null),{resultList:i,successCount:n,failureCount:r}}).catch(e=>bs(e))}_getRequestExtensions(e,t){const s=[];if(this._hasLocalExtension(e.ID)){const o=this._getLocalExtension(e.ID);return t.forEach(e=>{const{key:t,value:i}=e;let n=0;o.has(t)&&(n=o.get(t).seq),s.push({key:t,value:i,seq:n})}),s}return t.forEach(e=>{const{key:t,value:o}=e;s.push({key:t,value:o,seq:0})}),s}_getMessageExtensions(e,s){const o=this._n+"._getMessageExtensions",{ID:i,to:n}=e;let r=null;switch(Je(n)?t.CONV_TOPIC:e.conversationType){case t.CONV_C2C:r=this.getModule(ns);break;case t.CONV_GROUP:r=this.getModule(rs);break;case t.CONV_TOPIC:r=this.getModule(cs);break;default:return bs({code:ks.CANNOT_FIND_MODULE})}return r.getMessageExtensions(e,s).then(t=>{let{extensions:s,completeFlag:n,globalSequence:r,clearSequence:a}=t.data;if(s=Ct(s)?[]:s,me.l(`${o} ok. completeFlag:${n} globalSequence:${r} clearSequence:${a} count:${s.length}`),this._updateLocalExtension(i,s),this._clearLocalExtension(i,a),this._updateGlobalSequence(i,r),1!==n){const t=s.slice(-1)[0].seq+1;return this._getMessageExtensions(e,t)}return this._getLocalExtensions(i)}).catch(e=>bs(e))}_hasLocalExtension(e){return this.messageExtensionMap.has(e)}_getLocalExtension(e){return this.messageExtensionMap.get(e)}_updateLocalExtension(e,t){this._hasLocalExtension(e)||this.messageExtensionMap.set(e,new Map);const s=this._getLocalExtension(e);t.forEach(e=>{const{key:t,value:o="",seq:i}=e;s.set(t,{value:o,seq:i})})}_clearLocalExtension(e,t){if(!(t<=0)&&this._hasLocalExtension(e)){const s=this._getLocalExtension(e);s.forEach((e,o)=>{e.seq<=t&&s.delete(o)})}}_getLocalExtensions(e){const t=[];if(this._hasLocalExtension(e)){this._getLocalExtension(e).forEach((e,s)=>{const{value:o}=e;Ct(o)||t.push({key:s,value:o})})}return t}_getGlobalSequence(e){return this.globalSeqMap.get(e)}_updateGlobalSequence(e,t){this.globalSeqMap.set(e,t)}reset(){me.l(this._n+".reset"),this.messageExtensionMap.clear(),this.globalSeqMap.clear(),this.getMessageExtensionsMap.clear()}}class Ur extends Fs{constructor(e){super(e),this._n="ComboMessageModule"}sendMessage(e){const s=this._constructMessageInstance(e);if(null===s)return bs({code:ks.MSG_SEND_FAIL});this._addSendMessageTotalCount(s);const o=Date.now();return this.getModule(us).setMessageRandom(s),this._sendComboMessage(s,e).then(e=>{const{time:i,sequence:n,readReceiptCode:r}=e.data;if(Ne(r)&&0!==r){new In("sendMessageWithReceipt").setMessage(`from:${s.from} to:${s.to} sequence:${n} readReceiptCode:${r}`).end(),me.w(`${this._n}.sendMessage readReceiptCode:${r} message:${this.getErrorMessage(r)}`)}this._addSendMessageSuccessCount(s,o);const a=this.getModule(us);s.status=vt.SUCCESS,s.time=i,s.conversationType===t.CONV_GROUP&&(s.sequence=n),a.appendToMessageList(s);let c=s;return!0===s._isExcludedFromLastMessage&&(c=""),a.onMessageSent({conversationOptionsList:[{conversationID:s.conversationID,unreadCount:0,type:s.conversationType,subType:s.conversationSubType,lastMessage:c}]}),Rs({message:s})}).catch(e=>this._onSendMessageFailed(s,e))}_sendComboMessage(e,s){const o=this._m.getModule(Ms);let i="";return e.conversationType===t.CONV_C2C&&(i=`${m.NAME.OPEN_IM}.${Bs}`),e.conversationType===t.CONV_GROUP&&(i=`${m.NAME.GROUP}.${Hs}`),o.sendComboMessage({servcmd:i,data:s})}_constructMessageInstance(e){const s=this._n+"._constructMessageInstance";let o=null;try{const i=this.getMyUserID(),n={};if(n.senderTinyID=this.getMyTinyID(),n.currentUser=i,n.from=e.From_Account||i,e.GroupId?(n.conversationID=`${t.CONV_GROUP}${e.GroupId}`,n.conversationType=t.CONV_GROUP,n.to=e.GroupId):e.To_Account&&(n.conversationID=`${t.CONV_C2C}${e.To_Account}`,n.conversationType=t.CONV_C2C,n.to=e.To_Account),n.time=e.MsgTimeStamp||0,n.random=e.Random||e.MsgRandom||0,n.priority=e.MsgPriority,Ee(e.CloudCustomData)&&e.CloudCustomData.length>0&&(n.cloudCustomData=e.CloudCustomData),Oe(e.SendMsgControl)&&(n.messageControlInfo={},e.SendMsgControl.includes("NoUnread")&&(n.messageControlInfo.excludedFromUnreadCount=1),e.SendMsgControl.includes("NoLastMsg")&&(n.messageControlInfo.excludedFromLastMessage=1)),n.conversationType===t.CONV_GROUP&&Oe(e.To_Account)&&e.To_Account.length>0){let t=e.To_Account;e.To_Account.length>50&&(t=e.To_Account.slice(0,50),me.w(s+" To_Account must be less than or equal to 50.")),n.receiverList=[...t],e.To_Account=[...t]}1!==e.IsNeedReadReceipt&&1!==e.NeedReadReceipt||(n.needReadReceipt=!0),1===e.SupportMessageExtension&&(n.isSupportExtension=!0),o=new Gn(n),o.status=vt.UNSEND,e.MsgClientTime=o.clientTime,o.conversationType===t.CONV_C2C&&(e.MsgSeq=o.sequence);const r=e.MsgBody.length;let a;for(let t=0;t<r;t++)a=e.MsgBody[t],"TIMTextElem"===a.MsgType?o.setTextElement(a.MsgContent.Text):"TIMCustomElem"===a.MsgType?o.setCustomElement({data:a.MsgContent.Data||"",description:a.MsgContent.Desc||"",extension:a.MsgContent.Ext||""}):"TIMFaceElem"===a.MsgType&&o.setFaceElement({index:a.MsgContent.Index,data:a.MsgContent.Data});const c=o.getElements();o.payload=c[0].content,o.type=c[0].type}catch(i){o=null,me.e(s+" failed. error:",i)}return o}_onSendMessageFailed(e,t){e.status=vt.FAIL;this.getModule(us).deleteMessageRandom(e),this._addSendMessageFailCountOnUser(e,t);const s=new In("sendMessage");return s.setMessage(`tjg_id:${this.generateTjgID(e)} type:${e.type} from:${e.from} to:${e.to}`),this.probeNetwork().then(([e,o])=>{s.setError(t,e,o).end()}),me.e(this._n+"._onSendMessageFailed error:",t),bs(new Us({code:t&&t.code?t.code:ks.MSG_SEND_FAIL,message:t&&t.message?t.message:void 0,data:{message:e}}))}_getSendMessageSpecifiedKey(e){if(e.conversationType===t.CONV_C2C)return an;if(e.conversationType===t.CONV_GROUP){const t=this.getModule(rs).getLocalGroupProfile(e.to);if(!t)return;const s=t.type;return ze(s)?un:cn}}_addSendMessageTotalCount(e){const t=this._getSendMessageSpecifiedKey(e);if(t){this.getModule(vs).addTotalCount(t)}}_addSendMessageSuccessCount(e,t){const s=Math.abs(Date.now()-t),o=this._getSendMessageSpecifiedKey(e);if(o){const e=this.getModule(vs);e.addSuccessCount(o),e.addCost(o,s)}}_addSendMessageFailCountOnUser(e,t){const{code:s=-1}=t,o=this.getModule(vs),i=this._getSendMessageSpecifiedKey(e);Lr(s)&&i&&o.addFailedCountOfUserSide(i)}}class kr extends Fs{constructor(e){super(e),this._n="PluginModule",this.plugins={}}registerPlugin(e){Object.keys(e).forEach(t=>{this.plugins[t]=e[t]});new In("registerPlugin").setMessage(""+Object.keys(e)).end()}getPlugin(e){return this.plugins[e]}reset(){}}class Pr extends Fs{constructor(e){super(e),this._n="SyncUnreadMessageModule",this._cookie="",this._onlineSyncFlag=!1,this.getInnerEmitterInstance().on(Fn.A2KEY_AND_TINYID_UPDATED,this._onLoginSuccess,this)}_onLoginSuccess(e){this._startSync({cookie:this._cookie,syncFlag:0,isOnlineSync:0})}_startSync(e){const{cookie:t,syncFlag:s,isOnlineSync:o}=e,i=this._n+"._startSync";me.l(`${i} cookie:${t} syncFlag:${s} isOnlineSync:${o}`),this.request({protocolName:Ks,requestData:{cookie:t,syncFlag:s,isOnlineSync:o}}).then(e=>{const{cookie:t,syncFlag:s}=e.data;this._cookie=t,Ct(t)||(0===s||1===s?(this._dispatchUnreadMessage({...e.data,isSyncingEnded:!1}),this._startSync({cookie:t,syncFlag:s,isOnlineSync:0})):2===s&&this._dispatchUnreadMessage({...e.data,isSyncingEnded:!0}))}).catch(e=>{me.e(i+" failed. error:",e)})}_dispatchUnreadMessage(e){if(e.eventArray){this.getModule(Ms).onMessage({head:{},body:{eventArray:e.eventArray,isInstantMessage:this._onlineSyncFlag,isSyncingEnded:e.isSyncingEnded}})}this.getModule(ns).onNewC2CMessage({dataList:e.messageList,isInstantMessage:!!e.isSyncingEnded&&this._onlineSyncFlag,C2CRemainingUnreadList:e.C2CRemainingUnreadList,C2CPairUnreadList:e.C2CPairUnreadList,isSyncingEnded:e.isSyncingEnded})}startOnlineSync(){me.l(this._n+".startOnlineSync"),this._onlineSyncFlag=!0,this._startSync({cookie:this._cookie,syncFlag:0,isOnlineSync:1})}startSyncOnReconnected(){me.l(this._n+".startSyncOnReconnected."),this._onlineSyncFlag=!0,this._startSync({cookie:this._cookie,syncFlag:0,isOnlineSync:0})}reset(){me.l(this._n+".reset"),this._onlineSyncFlag=!1,this._cookie=""}}const Gr={request:{toAccount:"To_Account",fromAccount:"From_Account",to:"To_Account",from:"From_Account",groupID:"GroupId",groupAtUserID:"GroupAt_Account",extension:"Ext",data:"Data",description:"Desc",elements:"MsgBody",sizeType:"Type",downloadFlag:"Download_Flag",thumbUUID:"ThumbUUID",videoUUID:"VideoUUID",remoteAudioUrl:"Url",remoteVideoUrl:"VideoUrl",videoUrl:"",imageUrl:"URL",fileUrl:"Url",uuid:"UUID",priority:"MsgPriority",receiverUserID:"To_Account",receiverGroupID:"GroupId",messageSender:"SenderId",messageReceiver:"ReceiverId",nick:"From_AccountNick",avatar:"From_AccountHeadurl",messageNumber:"MsgNum",pbDownloadKey:"PbMsgKey",downloadKey:"JsonMsgKey",applicationType:"PendencyType",userIDList:"To_Account",groupNameList:"GroupName",userID:"To_Account",groupAttributeList:"GroupAttr",mainSequence:"AttrMainSeq",avChatRoomKey:"BytesKey",attributeControl:"AttrControl",sequence:"seq",messageControlInfo:"SendMsgControl",updateSequence:"UpdateSeq",clientTime:"MsgClientTime",sequenceList:"MsgSeqList",topicID:"TopicId",customData:"CustomString",isSupportTopic:"SupportTopic",isWebUniapp:"is_web_uniapp",isSupportExtension:"SupportMessageExtension",messageSequence:"MsgSeq",messageKey:"MsgKey",startSequence:"startSeq",simplifiedMessage:"DownsizeFlag",isRelayMessage:"IsRelayMsg"},response:{MsgPriority:"priority",ThumbUUID:"thumbUUID",VideoUUID:"videoUUID",Download_Flag:"downloadFlag",GroupId:"groupID",Member_Account:"userID",MsgList:"messageList",SyncFlag:"syncFlag",To_Account:"to",From_Account:"from",MsgSeq:"sequence",MsgRandom:"random",MsgTime:"time",MsgTimeStamp:"time",MsgContent:"content",MsgBody:"elements",From_AccountNick:"nick",From_AccountHeadurl:"avatar",GroupWithdrawInfoArray:"revokedInfos",GroupReadInfoArray:"groupMessageReadNotice",LastReadMsgSeq:"lastMessageSeq",WithdrawC2cMsgNotify:"c2cMessageRevokedNotify",C2cWithdrawInfoArray:"revokedInfos",C2cReadedReceipt:"c2cMessageReadReceipt",ReadC2cMsgNotify:"c2cMessageReadNotice",LastReadTime:"peerReadTime",MsgRand:"random",MsgType:"type",MsgShow:"messageShow",NextMsgSeq:"nextMessageSeq",FaceUrl:"avatar",ProfileDataMod:"profileModify",Profile_Account:"userID",ValueBytes:"value",ValueNum:"value",NoticeSeq:"noticeSequence",NotifySeq:"notifySequence",MsgFrom_AccountExtraInfo:"messageFromAccountExtraInformation",Operator_Account:"operatorID",OpType:"operationType",ReportType:"operationType",UserId:"userID",User_Account:"userID",List_Account:"userIDList",MsgOperatorMemberExtraInfo:"operatorInfo",MsgMemberExtraInfo:"memberInfoList",ImageUrl:"avatar",NickName:"nick",MsgGroupNewInfo:"newGroupProfile",MsgAppDefinedData:"groupCustomField",Owner_Account:"ownerID",GroupFaceUrl:"avatar",GroupIntroduction:"introduction",GroupNotification:"notification",GroupApplyJoinOption:"joinOption",MsgKey:"messageKey",GroupInfo:"groupProfile",ShutupTime:"muteTime",Desc:"description",Ext:"extension",GroupAt_Account:"groupAtUserID",MsgNum:"messageNumber",PbMsgKey:"pbDownloadKey",JsonMsgKey:"downloadKey",MsgModifiedFlag:"isModified",PendencyItem:"applicationItem",PendencyType:"applicationType",AddTime:"time",AddSource:"source",AddWording:"wording",ProfileImImage:"avatar",PendencyAdd:"friendApplicationAdded",FrienPencydDel_Account:"friendApplicationDeletedUserIDList",Peer_Account:"userID",GroupAttr:"groupAttributeList",GroupAttrAry:"groupAttributeList",AttrMainSeq:"mainSequence",seq:"sequence",GroupAttrOption:"groupAttributeOption",BytesChangedKeys:"changedKeyList",GroupAttrInfo:"groupAttributeList",GroupAttrSeq:"mainSequence",PushChangedAttrValFlag:"isWithChangedAttributeInfo",SubKeySeq:"sequence",Val:"value",MsgGroupFromCardName:"senderNameCard",MsgGroupFromNickName:"senderNick",C2cNick:"peerNick",C2cImage:"peerAvatar",SendMsgControl:"messageControlInfo",NoLastMsg:"excludedFromLastMessage",NoUnread:"excludedFromUnreadCount",UpdateSeq:"updateSequence",MuteNotifications:"muteFlag",MsgClientTime:"clientTime",TinyId:"tinyID",GroupMsgReceiptList:"readReceiptList",ReadNum:"readCount",UnreadNum:"unreadCount",TopicId:"topicID",MillionGroupFlag:"communityType",SupportTopic:"isSupportTopic",MsgTopicNewInfo:"newTopicInfo",ShutupAll:"muteAllMembers",CustomString:"customData",TopicFaceUrl:"avatar",TopicIntroduction:"introduction",TopicNotification:"notification",TopicIdArray:"topicIDList",MsgVersion:"messageVersion",C2cMsgModNotifys:"c2cMessageModified",GroupMsgModNotifys:"groupMessageModified",ApplyJoinOption:"joinOption",MsgFlag:"messageRemindType",AtInfoList:"groupAtInfoList",AtFlagList:"groupAtType",AtMsgSeq:"sequence",BanDuration:"duration",BanDescription:"reason",NotVisible:"invisible",BytesTag:"tag",BytesValue:"value",RptBytesValue:"value",LatestSeq:"globalSequence",ClearSeq:"clearSequence",SupportMessageExtension:"isSupportExtension",ExtensionList:"extensions",GroupCounter:"counterList",Revoker_Account:"revoker",MsgExtensionNotify:"messageExtensionNotify",ExtensionC2cMsgInfo:"messageInfo",ExtensionGroupMsgInfo:"messageInfo",MsgOptType:"operateType",SetKVInfo:"operateResultList",DeleteKVInfo:"operateResultList",ClearKVInfo:"operateResultList",MsgKeyValue:"extensions",ClearMsgSeq:"clearSequence",MsgLastSeq:"globalSequence",InviteJoinOption:"inviteOption",MemberList_Account:"inviteeList",MsgMemberExtraInfoList:"inviteeInfoList",E:"event",GInf:"groupProfile",MCT:"clientTime",MR:"random",MP:"priority",MTS:"time",GId:"groupID",MS:"sequence",CCD:"cloudCustomData",F_Account:"from",F_Hd:"avatar",F_NN:"nick",GN:"groupName",GT:"groupType",IsSys:"isSystemMessage",OpInf:"operatorInfo",Img:"avatar",NN:"nick",OnlineInf:"onlineMemberInfo",ET:"expireTime",Num:"onlineMemberNum",Opt:"operationType",O_Account:"operatorID",RT:"operationType",UDF:"userDefinedField",L_Account:"userIDList",IsPlaceMsg:"isPlaceMessage",MsgCheckResult:"checkResult"},ignoreKeyWord:["C2C","ID","USP"]};function wr(e,t){if("string"!=typeof e&&!Array.isArray(e))throw new TypeError("Expected the input to be `string | string[]`");t=Object.assign({pascalCase:!1},t);if(0===(e=Array.isArray(e)?e.map(e=>e.trim()).filter(e=>e.length).join("-"):e.trim()).length)return"";if(1===e.length)return t.pascalCase?e.toUpperCase():e.toLowerCase();return e!==e.toLowerCase()&&(e=br(e)),e=e.replace(/^[_.\- ]+/,"").toLowerCase().replace(/[_.\- ]+(\w|$)/g,(e,t)=>t.toUpperCase()).replace(/\d+(\w|$)/g,e=>e.toUpperCase()),s=e,t.pascalCase?s.charAt(0).toUpperCase()+s.slice(1):s;var s}const br=e=>{let t=!1,s=!1,o=!1;for(let i=0;i<e.length;i++){const n=e[i];t&&/[a-zA-Z]/.test(n)&&n.toUpperCase()===n?(e=e.slice(0,i)+"-"+e.slice(i),t=!1,o=s,s=!0,i++):s&&o&&/[a-zA-Z]/.test(n)&&n.toLowerCase()===n?(e=e.slice(0,i-1)+"-"+e.slice(i-1),o=s,s=!1,t=!0):(t=n.toLowerCase()===n&&n.toUpperCase()!==n,o=s,s=n.toUpperCase()===n&&n.toLowerCase()!==n)}return e};function Fr(e,t){let s=0;return function e(t,o){if(s++,s>100)return s--,t;if(Oe(t)){const i=t.map(t=>Ae(t)?e(t,o):t);return s--,i}if(Ae(t)){let i=function(e,t){const s=Object.create(null);return Object.keys(e).forEach(o=>{const i=t(e[o],o);i&&(s[i]=e[o])}),s}(t,(e,t)=>{if(!be(t))return!1;if((s=t)!==wr(s))for(let o=0;o<Gr.ignoreKeyWord.length&&!t.includes(Gr.ignoreKeyWord[o]);o++);var s;return Re(o[t])?function(e){if("OPPOChannelID"===e)return e;return e[0].toUpperCase()+wr(e).slice(1)}(t):o[t]});return i=et(i,(t,s)=>Oe(t)||Ae(t)?e(t,o):t),s--,i}}(e,t)}function $r(e,t){if(Oe(e))return e.map(e=>Ae(e)?$r(e,t):e);if(Ae(e)){let s=function(e,t){const s={};return Object.keys(e).forEach(o=>{s[t(e[o],o)]=e[o]}),s}(e,(e,s)=>Re(t[s])?wr(s):t[s]);return s=et(s,e=>Oe(e)||Ae(e)?$r(e,t):e),s}}const qr=String.fromCharCode,xr=function(e){let t=0|e.charCodeAt(0);if(55296<=t)if(t<56320){const s=0|e.charCodeAt(1);if(56320<=s&&s<=57343){if(t=(t<<10)+s-56613888|0,t>65535)return qr(240|t>>>18,128|t>>>12&63,128|t>>>6&63,128|63&t)}else t=65533}else t<=57343&&(t=65533);return t<=2047?qr(192|t>>>6,128|63&t):qr(224|t>>>12,128|t>>>6&63,128|63&t)},Vr=function(e){const t=void 0===e?"":(""+e).replace(/[\x80-\uD7ff\uDC00-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]?/g,xr),s=0|t.length,o=new Uint8Array(s);let i=0;for(;i<s;i=i+1|0)o[i]=0|t.charCodeAt(i);return o},Kr=function(e){const t=new Uint8Array(e);let s="",o=0;const i=t.length;for(;o<i;){let e=t[o],n=0,r=0;if(e<=127?(n=0,r=255&e):e<=223?(n=1,r=31&e):e<=239?(n=2,r=15&e):e<=244&&(n=3,r=7&e),i-o-n>0){let s=0;for(;s<n;)e=t[o+s+1],r=r<<6|63&e,s+=1}else r=65533,n=i-o;s+=String.fromCodePoint(r),o+=n+1}return s};class Br{constructor(e){this._handler=e;const t=e.getURL();if(this._socket=null,this._workerSocket=null,this._id=xe(),this._handler.getIsWorkerEnabled()){const e=URL.createObjectURL(new Blob([';let _socket = null;onmessage = function(event) {  if (event.data.cmd === "start") {    const url = event.data.url;    _socket = new WebSocket(url);    _socket.binaryType = "arraybuffer";    _socket.onopen = function() {      postMessage({ callback: "onOpen" });    };    _socket.onclose = function(e) {      postMessage({ callback: "onOpen", e: { code: e.code, reason: e.reason } });    };    _socket.onmessage = function(e) {      postMessage({ callback: "onMessage", data: e.data });    };    _socket.onerror = function(e) {      postMessage({ callback: "onError", e: { isTrusted: "true" } });    };  } else if (event.data.cmd === "sendMessage") {    if (_socket !== null) {      _socket.send(event.data.data);    }  } else if (event.data.cmd === "stop") {    if (_socket !== null) {      _socket.close(event.data.code);      _socket = null;    }  }};'],{type:"application/javascript; charset=utf-8"}));this._workerSocket=new Worker(e);const s=this;this._workerSocket.onmessage=function(e){const{callback:t,e:o}=e.data;"onOpen"===t?s._onOpen():"onClose"===t?s._onClose(o):"onError"===t?s._onError(o):"onMessage"===t&&s._onMessage(e.data)},this._workerSocket.postMessage({cmd:"start",id:this._id,url:t})}else U?A?(P.connectSocket({url:t,header:{"content-type":"application/json"}}),P.onSocketClose(this._onClose.bind(this)),P.onSocketOpen(this._onOpen.bind(this)),P.onSocketMessage(this._onMessage.bind(this)),P.onSocketError(this._onError.bind(this))):(this._socket=P.connectSocket({url:t,header:{"content-type":"application/json"},complete:()=>{}}),this._socket.onClose(this._onClose.bind(this)),this._socket.onOpen(this._onOpen.bind(this)),this._socket.onMessage(this._onMessage.bind(this)),this._socket.onError(this._onError.bind(this))):k&&(this._socket=new WebSocket(t),this._socket.binaryType="arraybuffer",this._socket.onopen=this._onOpen.bind(this),this._socket.onmessage=this._onMessage.bind(this),this._socket.onclose=this._onClose.bind(this),this._socket.onerror=this._onError.bind(this));this._canIUseBinaryFrame=e.canIUseBinaryFrame()}getID(){return this._id}_onOpen(){this._handler.onOpen({id:this._id})}_onClose(e){this._handler.onClose({id:this._id,e:e})}_onMessage(e){this._handler.onMessage({data:this._canIUseBinaryFrame?Kr(e.data):e.data})}_onError(e){this._handler.onError({id:this._id,e:e})}setIsWorkerEnabled(e){this._isWorkerEnabled=!0}close(e){if(this._workerSocket&&(this._workerSocket.postMessage({cmd:"stop",code:e}),this._workerSocket.terminate(),this._workerSocket=null),A)return P.offSocketClose(),P.offSocketMessage(),P.offSocketOpen(),P.offSocketError(),void P.closeSocket();this._socket&&(U?(this._socket.onClose(()=>{}),this._socket.onOpen(()=>{}),this._socket.onMessage(()=>{}),this._socket.onError(()=>{})):k&&(this._socket.onopen=null,this._socket.onmessage=null,this._socket.onclose=null,this._socket.onerror=null),E?this._socket.close({code:e}):this._socket.close(e),this._socket=null)}send(e){if(this._workerSocket)this._workerSocket.postMessage({cmd:"sendMessage",data:this._canIUseBinaryFrame?Vr(e.data).buffer:e.data});else{if(A)return void P.sendSocketMessage({data:e.data,fail:()=>{e.fail&&e.requestID&&e.fail(e.requestID)}});this._socket&&(U?this._socket.send({data:this._canIUseBinaryFrame?Vr(e.data).buffer:e.data,fail:()=>{e.fail&&e.requestID&&e.fail(e.requestID)}}):k&&this._socket.send(this._canIUseBinaryFrame?Vr(e.data).buffer:e.data))}}}const Hr=4e3,Wr=4001,Yr="connected",zr="connecting",jr="disconnected";class Jr{constructor(e){this._channelModule=e,this._n="SocketHandler",this._promiseMap=new Map,this._readyState=jr,this._simpleRequestMap=new Map,this.MAX_SIZE=100,this._startSequence=xe(),this._startTs=0,this._reConnectFlag=!1,this._nextPingTs=0,this._reConnectCount=0,this.MAX_RECONNECT_COUNT=3,this._socketID=-1,this._random=0,this._socket=null,this._url="",this._onOpenTs=0,this._canIUseBinaryFrame=!0,this._isWorkerEnabled=!0,this._setWebsocketHost(),this._initConnection()}_setWebsocketHost(){const e=this._channelModule.getModule(ls);let t=c;this._channelModule.isOversea()&&(t=u),e.isSingaporeSite()?t=l:e.isKoreaSite()?t=d:e.isGermanySite()?t=p:e.isIndiaSite()?t=h:e.isJapanSite()?t=g:e.isUSASite()&&(t=_),m.HOST.setCurrent(t)}_initConnection(){Re(m.HOST.CURRENT.BACKUP)||""===this._url?this._url=m.HOST.CURRENT.DEFAULT:this._url===m.HOST.CURRENT.DEFAULT?this._url=m.HOST.CURRENT.BACKUP:this._url===m.HOST.CURRENT.BACKUP?this._url=this._canIUseAnyCast()?m.HOST.CURRENT.ANYCAST:m.HOST.CURRENT.DEFAULT:this._url===m.HOST.CURRENT.ANYCAST&&(m.HOST.CURRENT.ANYCAST="",this._url=m.HOST.CURRENT.DEFAULT);const e=this._channelModule.getModule(ls).getProxyServer();Ct(e)||(this._url=e),this._connect(),this._nextPingTs=0}_canIUseAnyCast(){return k&&m.HOST.CURRENT.ANYCAST}onCheckTimer(e){e%1==0&&this._checkPromiseMap()}_checkPromiseMap(){0!==this._promiseMap.size&&this._promiseMap.forEach((e,t)=>{const{reject:s,timestamp:o}=e;let i=15e3;-1!==t.indexOf($s)&&(i=9e4),Date.now()-o>=i&&(me.l(`${this._n}._checkPromiseMap request timeout, delete requestID:${t}`),this._promiseMap.delete(t),s(new Us({code:ks.NETWORK_TIMEOUT})),this._channelModule.onRequestTimeout(t))})}onOpen(e){if(""===this._readyState)return;this._onOpenTs=Date.now();const{id:t}=e;this._socketID=t;const s=Date.now()-this._startTs;me.l(`${this._n}._onOpen cost ${s} ms. socketID:${t}`);new In("wsOnOpen").setMessage(s).setCostTime(s).setMoreMessage("socketID:"+t).end(),e.id===this._socketID&&(this._readyState=Yr,this._reConnectCount=0,this._resend(),!0===this._reConnectFlag&&(this._channelModule.onReconnected(),this._reConnectFlag=!1),this._channelModule.onOpen())}onClose(e){const t=new In("wsOnClose"),{id:s,e:o}=e,i=`sourceSocketID:${s} currentSocketID:${this._socketID} code:${o.code} reason:${o.reason}`;let n=0;0!==this._onOpenTs&&(n=Date.now()-this._onOpenTs),t.setMessage(n).setCostTime(n).setMoreMessage(i).setCode(o.code).end(),me.l(`${this._n}._onClose ${i} onlineTime:${n}`),s===this._socketID&&(this._readyState=jr,n<1e3?this._channelModule.onReconnectFailed():this._channelModule.onClose())}onError(e){const{id:t,e:s}=e,o=`sourceSocketID:${t} currentSocketID:${this._socketID}`;new In("wsOnError").setMessage(s.errMsg||$e(s)).setMoreMessage(o).setLevel("error").end(),me.w(this._n+"._onError",s,o),t===this._socketID&&(this._readyState="",this._channelModule.onError())}onMessage(e){let t;try{t=JSON.parse(e.data)}catch(i){new In("jsonParseError").setMessage(e.data).end()}if(!t||!t.head)return;const s=this._getRequestIDFromHead(t.head);let o=t.body;if(!this._isTRTCCommand(s)){const e=ct(t.head);o=$r(t.body,this._getResponseKeyMap(e))}if(me.d(`${this._n}.onMessage ret:${JSON.stringify(o)} requestID:${s} has:${this._promiseMap.has(s)}`),this._setNextPingTs(),this._promiseMap.has(s)){const{resolve:e,reject:t,timestamp:i}=this._promiseMap.get(s);return this._promiseMap.delete(s),this._calcRTT(i),void(o.errorCode&&0!==o.errorCode?(this._channelModule.onErrorCodeNotZero(o),t(new Us({code:o.errorCode,message:o.errorInfo||"",data:s.includes(Io)||s.includes(ci)?{elements:o.elements,messageVersion:o.messageVersion,cloudCustomData:o.cloudCustomData}:void 0}))):e(Rs(o)))}this._channelModule.onMessage({head:t.head,body:o})}_isTRTCCommand(e){const t=this._channelModule.getModule(Es).getCommandList();let s=!1;for(let o=0;o<t.length;o++)if(e.startsWith(t[o])){s=!0;break}return s}_calcRTT(e){const t=Date.now()-e;this._channelModule.getModule(vs).addRTT(t)}_connect(){this._startTs=Date.now(),this._onOpenTs=0,this._socket=new Br(this),this._socketID=this._socket.getID(),this._readyState=zr,me.l(`${this._n}._connect isWorkerEnabled:${this.getIsWorkerEnabled()} socketID:${this._socketID} url:${this.getURL()}`);new In("wsConnect").setMessage(`socketID:${this._socketID} url:${this.getURL()}`).end()}getURL(){this._channelModule.isDevMode()&&(this._canIUseBinaryFrame=!1);const e=it();(A||D&&"windows"===e||O)&&(this._canIUseBinaryFrame=!1);let t=-1;"ios"===e?t=K||-1:"android"===e&&(t=H||-1);const s=this._channelModule.getModule(ls),o=this._channelModule.getPlatform(),i=`sdkappid=${s.getSDKAppID()}&instanceid=${s.getInstanceID()}&random=${this._getRandom()}&platform=${o}&host=${e}&version=${t}&sdkversion=3.1.3`;return this._canIUseBinaryFrame?`${this._url}/binfo?${i}`:`${this._url}/info?${i}`}_closeConnection(e){me.l(`${this._n}._closeConnection socketID:${this._socketID}`),this._socket&&(this._socket.close(e),this._socketID=-1,this._socket=null,this._readyState=jr)}_resend(){if(me.l(`${this._n}._resend reConnectFlag:${this._reConnectFlag}`,`promiseMap.size:${this._promiseMap.size} simpleRequestMap.size:${this._simpleRequestMap.size}`),this._promiseMap.size>0&&this._promiseMap.forEach((e,t)=>{const{uplinkData:s,resolve:o,reject:i}=e;this._promiseMap.set(t,{resolve:o,reject:i,timestamp:Date.now(),uplinkData:s}),this._execute(t,s)}),this._simpleRequestMap.size>0){for(const[e,t]of this._simpleRequestMap)this._execute(e,t);this._simpleRequestMap.clear()}}send(e){e.head.seq=this._getSequence(),e.head.reqtime=Math.floor(Date.now()/1e3);const{keyMap:t,...s}=e,o=this._getRequestIDFromHead(e.head),i=JSON.stringify(s);return new Promise((e,t)=>{if(this._promiseMap.set(o,{resolve:e,reject:t,timestamp:Date.now(),uplinkData:i}),me.d(`${this._n}.send uplinkData:${JSON.stringify(s)} requestID:${o} readyState:${this._readyState}`),this._readyState!==Yr)this._reConnect();else{this._execute(o,i);this._channelModule.getModule(vs).addRequestCount()}})}simplySend(e){e.head.seq=this._getSequence(),e.head.reqtime=Math.floor(Date.now()/1e3);const{keyMap:t,...s}=e,o=this._getRequestIDFromHead(e.head),i=JSON.stringify(s);this._readyState!==Yr?(this._simpleRequestMap.size<this.MAX_SIZE?this._simpleRequestMap.set(o,i):me.l(this._n+".simplySend. simpleRequestMap is full, drop request!"),this._reConnect()):this._execute(o,i)}_execute(e,t){this._socket.send({data:t,fail:U?this._onSendFail.bind(this):void 0,requestID:e})}_onSendFail(e){me.l(`${this._n}._onSendFail requestID:${e}`)}_getSequence(){let e;if(this._startSequence<2415919103)return e=this._startSequence,this._startSequence+=1,2415919103===this._startSequence&&(this._startSequence=xe()),e}_getRequestIDFromHead(e){const{servcmd:t,seq:s}=e;return t+s}_getResponseKeyMap(e){const t=this._channelModule.getKeyMap(e);return{...Gr.response,...t.response}}_reConnect(){this._readyState!==Yr&&this._readyState!==zr&&this.forcedReconnect()}forcedReconnect(){const e=this._n+".forcedReconnect";me.l(`${e} count:${this._reConnectCount} readyState:${this._readyState}`),this._reConnectFlag=!0,this._resetRandom(),this._reConnectCount<this.MAX_RECONNECT_COUNT?(this._reConnectCount+=1,this._closeConnection(Wr),this._initConnection()):(this._reConnectCount=0,this._channelModule.probeNetwork().then(([t,s])=>{t?(me.w(e+" disconnected from wsserver but network is ok, continue..."),this._closeConnection(Wr),this._initConnection()):this._channelModule.onReconnectFailed()}))}getReconnectFlag(){return this._reConnectFlag}_setNextPingTs(){this._nextPingTs=Date.now()+1e4}getNextPingTs(){return this._nextPingTs}isConnected(){return this._readyState===Yr}canIUseBinaryFrame(){return this._canIUseBinaryFrame}setIsWorkerEnabled(e){me.l(`${this._n}.setIsWorkerEnabled flag:${e}`),this._isWorkerEnabled=e}getIsWorkerEnabled(){return this._isWorkerEnabled&&Z}_getRandom(){return 0===this._random&&(this._random=Math.random()),this._random}_resetRandom(){this._random=0}close(){me.l(this._n+".close"),this._closeConnection(Hr),this._promiseMap.clear(),this._startSequence=xe(),this._readyState=jr,this._simpleRequestMap.clear(),this._reConnectFlag=!1,this._reConnectCount=0,this._onOpenTs=0,this._url="",this._random=0,this._canIUseBinaryFrame=!0,this._isWorkerEnabled=!0}}class Xr extends Fs{constructor(e){if(super(e),this._n="ChannelModule",this._socketHandler=new Jr(this),this._probing=!1,this._isAppShowing=!0,this._previousState=t.NET_STATE_CONNECTED,U&&"function"==typeof P.onAppShow&&"function"==typeof P.onAppHide){const e=this._onAppHide.bind(this),t=this._onAppShow.bind(this);"function"==typeof P.offAppHide&&P.offAppHide(e),"function"==typeof P.offAppShow&&P.offAppShow(t),P.onAppHide(e),P.onAppShow(t)}this._timerForNotLoggedIn=-1,this._timerForNotLoggedIn=setInterval(this.onCheckTimer.bind(this),1e3),this._fatalErrorFlag=!1}onCheckTimer(e){this._socketHandler&&(this.isLoggedIn()?(this._timerForNotLoggedIn>0&&(clearInterval(this._timerForNotLoggedIn),this._timerForNotLoggedIn=-1),this._socketHandler.onCheckTimer(e)):this._socketHandler.onCheckTimer(1),this._checkNextPing())}onErrorCodeNotZero(e){this.getModule(Ms).onErrorCodeNotZero(e)}onMessage(e){this.getModule(Ms).onMessage(e)}send(e){if(!this._socketHandler)return Promise.reject();if(this._previousState!==t.NET_STATE_CONNECTED&&e.head.servcmd.includes(Oi)){this.reConnect();return this.getModule(ls).getProxyServer()?Promise.resolve():this._sendLogViaHTTP(e)}return this._socketHandler.send(e)}_sendLogViaHTTP(e){const t=m.HOST.CURRENT.STAT;return new Promise((s,o)=>{const i=`${t}/v4/imopenstat/tim_web_report_v2?sdkappid=${e.head.sdkappid}&reqtime=${Date.now()}`,n=JSON.stringify(e.body),r="application/x-www-form-urlencoded;charset=UTF-8";if(U)P.request({url:i,data:n,method:"POST",timeout:3e3,header:{"content-type":r},success:()=>{s()},fail:()=>{o(new Us({code:ks.NETWORK_ERROR}))}});else{const e=new XMLHttpRequest,t=setTimeout(()=>{e.abort(),o(new Us({code:ks.NETWORK_TIMEOUT}))},3e3);e.onreadystatechange=function(){4===e.readyState&&(clearTimeout(t),200===e.status||304===e.status?s():o(new Us({code:ks.NETWORK_ERROR})))},e.open("POST",i,!0),e.setRequestHeader("Content-type",r),e.send(n)}})}simplySend(e){return this._socketHandler?this._socketHandler.simplySend(e):Promise.reject()}onOpen(){this._ping()}onClose(){if(this._socketHandler){this._socketHandler.getReconnectFlag()&&this._emitNetStateChangeEvent(t.NET_STATE_DISCONNECTED)}this.reConnect()}onError(){U&&!O&&this.outputWarning("DomainNameInMP"),this._emitNetStateChangeEvent(t.NET_STATE_DISCONNECTED)}getKeyMap(e){return this.getModule(Ms).getKeyMap(e)}_onAppHide(){this._isAppShowing=!1}_onAppShow(){this._isAppShowing=!0}onRequestTimeout(e){}onReconnected(){me.l(this._n+".onReconnected"),this._m.restartTimer();this.getModule(Ms).onReconnected(),this._emitNetStateChangeEvent(t.NET_STATE_CONNECTED)}onReconnectFailed(){me.l(this._n+".onReconnectFailed"),this._emitNetStateChangeEvent(t.NET_STATE_DISCONNECTED)}setIsWorkerEnabled(e){this._socketHandler&&this._socketHandler.setIsWorkerEnabled(!1)}offline(){this._emitNetStateChangeEvent(t.NET_STATE_DISCONNECTED)}reConnect(e=!1){let s=!1;this._socketHandler&&(s=this._socketHandler.getReconnectFlag());const o=`forcedFlag:${e} fatalErrorFlag:${this._fatalErrorFlag} previousState:${this._previousState} reconnectFlag:${s}`;if(me.l(`${this._n}.reConnect ${o}`),!this._fatalErrorFlag&&this._socketHandler){if(!0===e)this._socketHandler.forcedReconnect();else{if(this._previousState===t.NET_STATE_CONNECTING&&s)return;this._socketHandler.forcedReconnect()}this._emitNetStateChangeEvent(t.NET_STATE_CONNECTING)}}_emitNetStateChangeEvent(t){this._previousState!==t&&(me.l(`${this._n}._emitNetStateChangeEvent from ${this._previousState} to ${t}`),this._previousState=t,this.emitOuterEvent(e.NET_STATE_CHANGE,{state:t}))}_ping(){if(!0===this._probing)return;this._probing=!0;const e=this.getModule(Ms).getProtocolData({protocolName:Ri});this.send(e).then(()=>{this._probing=!1}).catch(e=>{if(me.w(this._n+"._ping failed. error:",e),this._probing=!1,e&&60002===e.code){return new In("error").setMessage(`code:${e.code} message:${e.message}`).setNetworkType(this.getModule(hs).getNetworkType()).end(),this._fatalErrorFlag=!0,void this._emitNetStateChangeEvent(t.NET_STATE_DISCONNECTED)}this.probeNetwork().then(([e,s])=>{me.l(`${this._n}._ping failed. probe network, isAppShowing:${this._isAppShowing} online:${e} networkType:${s}`),e?this.reConnect():this._emitNetStateChangeEvent(t.NET_STATE_DISCONNECTED)})})}_checkNextPing(){if(!this._socketHandler)return;if(!this._socketHandler.isConnected())return;Date.now()>=this._socketHandler.getNextPingTs()&&this._ping()}dealloc(){this._socketHandler&&(this._socketHandler.close(),this._socketHandler=null),this._timerForNotLoggedIn>-1&&clearInterval(this._timerForNotLoggedIn)}onRestApiKickedOut(){this._socketHandler&&(this._socketHandler.close(),this.reConnect(!0))}reset(){me.l(this._n+".reset"),this._previousState=t.NET_STATE_CONNECTED,this._probing=!1,this._fatalErrorFlag=!1,this._timerForNotLoggedIn=setInterval(this.onCheckTimer.bind(this),1e3)}}class Qr{constructor(e){this._n="ProtocolHandler",this._sessionModule=e,this._configMap=new Map,this._fillConfigMap()}_fillConfigMap(){this._configMap.clear();const e=this._sessionModule.genCommonHead(),t=this._sessionModule.genCosSpecifiedHead(),s=this._sessionModule.genSSOReportHead();this._configMap.set($s,function(e){return{head:{...e,servcmd:`${m.NAME.IM_OPEN_STATUS}.${$s}`},body:{state:"Online",isWebUniapp:0,deviceBrand:0},keyMap:{request:{deviceBrand:"InstType"},response:{InstId:"instanceID",HelloInterval:"helloInterval"}}}}(e)),this._configMap.set(qs,function(e){return{head:{...e,servcmd:`${m.NAME.IM_OPEN_STATUS}.${qs}`},body:{type:0},keyMap:{request:{type:"wslogout_type"}}}}(e)),this._configMap.set(xs,function(e){return{head:{...e,servcmd:`${m.NAME.IM_OPEN_STATUS}.${xs}`},body:{isWebUniapp:0},keyMap:{response:{NewInstInfo:"newInstanceInfo"}}}}(e)),this._configMap.set(Vs,function(e){return{head:{...e,servcmd:`${m.NAME.STAT_SERVICE}.${Vs}`},body:{}}}(e)),this._configMap.set(Ei,function(e){return{head:{...e,servcmd:`${m.NAME.IM_COS_SIGN}.${Ei}`},body:{cmd:"open_im_cos_svc",subCmd:"get_cos_token",duration:300,version:2},keyMap:{request:{userSig:"usersig",subCmd:"sub_cmd",cmd:"cmd",duration:"duration",version:"version"},response:{expired_time:"expiredTime",bucket_name:"bucketName",session_token:"sessionToken",tmp_secret_id:"secretId",tmp_secret_key:"secretKey"}}}}(t)),this._configMap.set(Ai,function(e){return{head:{...e,servcmd:`${m.NAME.CUSTOM_UPLOAD}.${Ai}`},body:{fileType:void 0,fileName:void 0,uploadMethod:0,duration:900},keyMap:{request:{userSig:"usersig",fileType:"file_type",fileName:"file_name",uploadMethod:"upload_method"},response:{expired_time:"expiredTime",request_id:"requestId",head_url:"headUrl",upload_url:"uploadUrl",download_url:"downloadUrl",ci_url:"ciUrl",snapshot_url:"requestSnapshotUrl"}}}}(t)),this._configMap.set(Li,function(e){return{head:{...e,servcmd:`${m.NAME.CUSTOM_UPLOAD}.${Li}`},body:{version:1,platform:void 0,coverName:void 0,requestSnapshotUrl:void 0},keyMap:{request:{version:"version",platform:"platform",coverName:"cover_name",requestSnapshotUrl:"snapshot_url"},response:{error_code:"errorCode",error_msg:"errorInfo",download_url:"snapshotUrl"}}}}(t)),this._configMap.set(xi,function(e){return{head:{...e,servcmd:`${m.NAME.IM_CONFIG_MANAGER}.${xi}`},body:{SDKAppID:0},keyMap:{request:{SDKAppID:"uint32_sdkappid"},response:{int32_error_code:"errorCode",str_error_message:"errorMessage",str_purchase_bits:"purchaseBits",uint32_expired_time:"expiredTime"}}}}(e)),this._configMap.set(Vi,function(e){return{head:{...e,servcmd:`${m.NAME.IM_CONFIG_MANAGER}.${Vi}`},body:{},keyMap:{response:{int32_error_code:"errorCode",str_error_message:"errorMessage",str_purchase_bits:"purchaseBits",uint32_expired_time:"expiredTime"}}}}(e)),this._configMap.set($i,function(e){return{head:{...e,servcmd:`${m.NAME.IM_CONFIG_MANAGER}.${$i}`},body:{SDKAppID:0,version:0},keyMap:{request:{SDKAppID:"uint32_sdkappid",version:"uint64_version"},response:{int32_error_code:"errorCode",str_error_message:"errorMessage",str_json_config:"cloudControlConfig",uint32_expired_time:"expiredTime",uint32_sdkappid:"SDKAppID",uint64_version:"version"}}}}(e)),this._configMap.set(qi,function(e){return{head:{...e,servcmd:`${m.NAME.IM_CONFIG_MANAGER}.${qi}`},body:{},keyMap:{response:{int32_error_code:"errorCode",str_error_message:"errorMessage",str_json_config:"cloudControlConfig",uint32_expired_time:"expiredTime",uint32_sdkappid:"SDKAppID",uint64_version:"version"}}}}(e)),this._configMap.set(Ki,function(e){return{head:{...e,servcmd:`${m.NAME.OVERLOAD_PUSH}.${Ki}`},body:{},keyMap:{response:{OverLoadServCmd:"overloadCommand",DelaySecs:"waitingTime"}}}}(e)),this._configMap.set(Ks,function(e){return{head:{...e,servcmd:`${m.NAME.OPEN_IM}.${Ks}`},body:{cookie:"",syncFlag:0,needAbstract:1,isOnlineSync:0,needSignaling:1},keyMap:{request:{fromAccount:"From_Account",toAccount:"To_Account",from:"From_Account",to:"To_Account",time:"MsgTimeStamp",sequence:"MsgSeq",random:"MsgRandom",elements:"MsgBody"},response:{MsgList:"messageList",SyncFlag:"syncFlag",To_Account:"to",From_Account:"from",ClientSeq:"clientSequence",MsgSeq:"sequence",NoticeSeq:"noticeSequence",NotifySeq:"notifySequence",MsgRandom:"random",MsgTimeStamp:"time",MsgContent:"content",ToGroupId:"to",MsgKey:"messageKey",GroupTips:"groupTips",MsgBody:"elements",MsgType:"type",C2CRemainingUnreadCount:"C2CRemainingUnreadList",C2CPairUnreadCount:"C2CPairUnreadList"}}}}(e)),this._configMap.set(tn,function(e){return{head:{...e,servcmd:`${m.NAME.IM_MSG_AUDIT_MGR}.${tn}`},body:{version:0,deviceID:"",startIndex:void 0},keyMap:{request:{version:"uint64_version",deviceID:"str_device_id",startIndex:"uint64_start_index"},response:{msg_cmd_error_code:"errorInfo",str_err_msg:"errorMessage",uint32_code:"errorCode",msg_scene_ctl_config:"filterConfig",uint64_c2c_custom_msg_flag:"c2c_custom_message",uint64_c2c_text_msg_flag:"c2c_text_message",uint64_group_custom_msg_flag:"group_custom_message",uint64_group_text_msg_flag:"group_text_message",uint64_group_info_flag:"group_profile",uint64_group_member_info_flag:"group_member_profile",uint64_relation_chain_flag:"sns",uint64_user_info_flag:"user_profile",rpt_msg_dirty_word:"lexicon",str_dirty_word:"profanity",str_replaced_content:"replacement",uint64_filter_type:"filterType",uint64_id:"id",uint64_word_type:"profanityType",uint64_complete_flag:"completeFlag",uint64_next_start_index:"nextStartIndex",uint64_version:"version",uint64_expired_time:"expiredTime"}}}}(e)),this._configMap.set(Bs,function(e){return{head:{...e,servcmd:`${m.NAME.OPEN_IM}.${Bs}`},body:{fromAccount:"",toAccount:"",msgSeq:0,msgRandom:0,msgBody:[],cloudCustomData:void 0,nick:"",avatar:"",msgLifeTime:void 0,offlinePushInfo:{pushFlag:0,title:"",desc:"",ext:"",apnsInfo:{badgeMode:0,isVoipPush:void 0},androidInfo:{OPPOChannelID:""}},messageControlInfo:void 0,clientTime:void 0,needReadReceipt:0,isSupportExtension:0,isRelayMessage:0},keyMap:{request:{fromAccount:"From_Account",toAccount:"To_Account",msgTimeStamp:"MsgTimeStamp",msgSeq:"MsgSeq",msgRandom:"MsgRandom",msgBody:"MsgBody",count:"MaxCnt",lastMessageTime:"LastMsgTime",messageKey:"MsgKey",peerAccount:"Peer_Account",data:"Data",description:"Desc",extension:"Ext",type:"MsgType",content:"MsgContent",sizeType:"Type",uuid:"UUID",url:"",imageUrl:"URL",fileUrl:"Url",remoteAudioUrl:"Url",remoteVideoUrl:"VideoUrl",thumbUUID:"ThumbUUID",videoUUID:"VideoUUID",videoUrl:"",downloadFlag:"Download_Flag",nick:"From_AccountNick",avatar:"From_AccountHeadurl",from:"From_Account",time:"MsgTimeStamp",messageRandom:"MsgRandom",messageSequence:"MsgSeq",elements:"MsgBody",clientSequence:"ClientSeq",payload:"MsgContent",messageList:"MsgList",messageNumber:"MsgNum",abstractList:"AbstractList",messageBody:"MsgBody",needReadReceipt:"IsNeedReadReceipt"}}}}(e)),this._configMap.set(Hs,function(e){return{head:{...e,servcmd:`${m.NAME.GROUP}.${Hs}`},body:{fromAccount:"",groupID:"",random:0,clientSequence:0,priority:"",msgBody:[],cloudCustomData:void 0,onlineOnlyFlag:0,offlinePushInfo:{pushFlag:0,title:"",desc:"",ext:"",apnsInfo:{badgeMode:0,isVoipPush:void 0},androidInfo:{OPPOChannelID:""}},groupAtInfo:[],messageControlInfo:void 0,clientTime:void 0,needReadReceipt:0,topicID:void 0,receiverList:void 0,isSupportExtension:0,isRelayMessage:0},keyMap:{request:{to:"GroupId",extension:"Ext",data:"Data",description:"Desc",random:"Random",sequence:"ReqMsgSeq",count:"ReqMsgNumber",type:"MsgType",priority:"MsgPriority",content:"MsgContent",elements:"MsgBody",sizeType:"Type",uuid:"UUID",url:"",imageUrl:"URL",fileUrl:"Url",remoteAudioUrl:"Url",remoteVideoUrl:"VideoUrl",thumbUUID:"ThumbUUID",videoUUID:"VideoUUID",videoUrl:"",downloadFlag:"Download_Flag",clientSequence:"ClientSeq",from:"From_Account",time:"MsgTimeStamp",messageRandom:"MsgRandom",messageSequence:"MsgSeq",payload:"MsgContent",messageList:"MsgList",messageNumber:"MsgNum",abstractList:"AbstractList",messageBody:"MsgBody",needReadReceipt:"NeedReadReceipt",receiverList:"To_Account"},response:{MsgTime:"time",MsgSeq:"sequence"}}}}(e)),this._configMap.set(po,function(e){return{head:{...e,servcmd:`${m.NAME.OPEN_IM}.${po}`},body:{msgInfo:{fromAccount:"",toAccount:"",msgTimeStamp:0,msgSeq:0,msgRandom:0}},keyMap:{request:{msgInfo:"MsgInfo",msgTimeStamp:"MsgTimeStamp",msgSeq:"MsgSeq",msgRandom:"MsgRandom"}}}}(e)),this._configMap.set(zo,function(e){return{head:{...e,servcmd:`${m.NAME.GROUP}.${zo}`},body:{groupID:"",msgSeqList:void 0,topicID:""},keyMap:{request:{msgSeqList:"MsgSeqList",msgSeq:"MsgSeq"}}}}(e)),this._configMap.set(mo,function(e){return{head:{...e,servcmd:`${m.NAME.OPEN_IM}.${mo}`},body:{peerAccount:"",count:15,lastMessageTime:0,messageKey:"",withRecalledMessage:1,direction:0},keyMap:{request:{messageKey:"MsgKey",peerAccount:"Peer_Account",count:"MaxCnt",lastMessageTime:"LastMsgTime",withRecalledMessage:"WithRecalledMsg",direction:"GetDirection"},response:{LastMsgTime:"lastMessageTime",IsNeedReadReceipt:"needReadReceipt",IsPeerRead:"readReceiptSentByPeer"}}}}(e)),this._configMap.set(Io,function(e){return{head:{...e,servcmd:`${m.NAME.OPEN_IM}.${Io}`},body:{from:"",to:"",sequence:0,random:0,time:0,version:0,elements:void 0,cloudCustomData:void 0},keyMap:{request:{sequence:"MsgSeq",random:"MsgRandom",time:"MsgTime",version:"MsgVersion",type:"MsgType",content:"MsgContent"}}}}(e)),this._configMap.set(Xo,function(e){return{head:{...e,servcmd:`${m.NAME.GROUP}.${Xo}`},body:{withRecalledMsg:1,groupID:"",count:15,sequence:"",topicID:void 0},keyMap:{request:{sequence:"ReqMsgSeq",count:"ReqMsgNumber",withRecalledMessage:"WithRecalledMsg"},response:{Random:"random",MsgTime:"time",MsgSeq:"sequence",ReqMsgSeq:"sequence",RspMsgList:"messageList",IsSystemMsg:"isSystemMessage",ToGroupId:"to",EnumFrom_AccountType:"fromAccountType",EnumTo_AccountType:"toAccountType",GroupCode:"groupCode",MsgPriority:"priority",MsgBody:"elements",MsgType:"type",MsgContent:"content",IsFinished:"complete",Download_Flag:"downloadFlag",ClientSeq:"clientSequence",ThumbUUID:"thumbUUID",VideoUUID:"videoUUID",ToTopicId:"topicID",InvisibleMsgSeq:"invisibleSequenceList"}}}}(e)),this._configMap.set(ho,function(e){return{head:{...e,servcmd:`${m.NAME.OPEN_IM}.${ho}`},body:{C2CMsgReaded:void 0},keyMap:{request:{lastMessageTime:"LastedMsgTime"}}}}(e)),this._configMap.set(go,function(e){return{head:{...e,servcmd:`${m.NAME.OPEN_IM}.${go}`},body:{userIDList:void 0,muteFlag:0},keyMap:{request:{userIDList:"Peer_Account",muteFlag:"Mute_Notifications"}}}}(e)),this._configMap.set(_o,function(e){return{head:{...e,servcmd:`${m.NAME.OPEN_IM}.${_o}`},body:{toAccount:void 0,userIDList:void 0},keyMap:{request:{userIDList:"Peer_Account"},response:{MuteNotificationsList:"muteFlagList"}}}}(e)),this._configMap.set(jo,function(e){return{head:{...e,servcmd:`${m.NAME.GROUP}.${jo}`},body:{groupID:void 0,messageReadSeq:void 0,topicID:void 0},keyMap:{request:{messageReadSeq:"MsgReadedSeq"}}}}(e)),this._configMap.set(Jo,function(e){return{head:{...e,servcmd:`${m.NAME.OPEN_IM}.${Jo}`},body:{readAllC2CMessage:0,groupMessageReadInfoList:[]},keyMap:{request:{readAllC2CMessage:"C2CReadAllMsg",groupMessageReadInfoList:"GroupReadInfo",messageSequence:"MsgSeq"},response:{C2CReadAllMsg:"readAllC2CMessage",GroupReadInfoArray:"groupMessageReadInfoList"}}}}(e)),this._configMap.set(fo,function(e){return{head:{...e,servcmd:`${m.NAME.OPEN_IM}.${fo}`},body:{fromAccount:"",to:"",keyList:void 0},keyMap:{request:{keyList:"MsgKeyList"}}}}(e)),this._configMap.set(ai,function(e){return{head:{...e,servcmd:`${m.NAME.GROUP}.${ai}`},body:{groupID:"",deleter:"",keyList:void 0,topicID:void 0},keyMap:{request:{deleter:"Deleter_Account",keyList:"Seqs"}}}}(e)),this._configMap.set(sn,function(e){return{head:{...e,servcmd:`${m.NAME.IM_OPEN_TRANSLATE}.${sn}`},body:{sourceTextList:void 0,SDKAppID:0,from:0,source:"",target:""},keyMap:{request:{sourceTextList:"SourceText",SDKAppID:"SdkAppId",from:"FromAccount"},response:{TargetText:"translatedTextList",RequestId:"requestID",CmdErrorCode:"error",ErrorCode:"code",ErrorInfo:"message"}}}}(e)),this._configMap.set(on,function(e){return{head:{...e,servcmd:`${m.NAME.IM_OPEN_SPEECH}.${on}`},body:{url:"",SDKAppID:0,format:"",sourceType:0,language:""},keyMap:{request:{url:"BytesUrl",SDKAppID:"Uint32Sdkappid",format:"BytesVoiceFormat",sourceType:"Uint64SourceType",language:"BytesEngServiceType"},response:{BytesRequestid:"requestID",BytesResult:"result",CmdErrorCode:"error",ErrorCode:"code",ErrorInfo:"message"}}}}(e)),this._configMap.set(ci,function(e){return{head:{...e,servcmd:`${m.NAME.OPEN_IM}.${ci}`},body:{groupID:"",topicID:void 0,sequence:0,version:0,elements:void 0,cloudCustomData:void 0},keyMap:{request:{sequence:"MsgSeq",version:"MsgVersion",type:"MsgType",content:"MsgContent"}}}}(e)),this._configMap.set(Qo,function(e){return{head:{...e,servcmd:`${m.NAME.GROUP}.${Qo}`},body:{groupID:"",sequenceList:void 0},keyMap:{request:{sequence:"MsgSeq"}}}}(e)),this._configMap.set(ei,function(e){return{head:{...e,servcmd:`${m.NAME.OPEN_IM}.${ei}`},body:{peerAccount:"",messageInfoList:void 0},keyMap:{request:{peerAccount:"Peer_Account",messageInfoList:"C2CMsgInfo",fromAccount:"From_Account",toAccount:"To_Account",sequence:"MsgSeq",random:"MsgRandom",time:"MsgTime",clientTime:"MsgClientTime"}}}}(e)),this._configMap.set(Zo,function(e){return{head:{...e,servcmd:`${m.NAME.GROUP}.${Zo}`},body:{groupID:"",sequenceList:void 0},keyMap:{request:{sequenceList:"MsgSeqList",sequence:"MsgSeq"}}}}(e)),this._configMap.set(ti,function(e){return{head:{...e,servcmd:`${m.NAME.GROUP}.${ti}`},body:{groupID:"",sequence:void 0,flag:0,cursor:0,count:0},keyMap:{request:{sequence:"MsgSeq",count:"Num"},response:{ReadList:"readUserIDList",Read_Account:"userID",UnreadList:"unreadUserIDList",Unread_Account:"userID",IsFinish:"isCompleted"}}}}(e)),this._configMap.set(Co,function(e){return{head:{...e,servcmd:`${m.NAME.OPEN_IM_MSG_EXT}.${Co}`},body:{from:void 0,to:void 0,messageKey:void 0,operateType:void 0,extensionList:void 0}}}(e)),this._configMap.set(yo,function(e){return{head:{...e,servcmd:`${m.NAME.OPEN_IM_MSG_EXT}.${yo}`},body:{from:void 0,to:void 0,messageKey:void 0,startSequence:void 0}}}(e)),this._configMap.set(gi,function(e){return{head:{...e,servcmd:`${m.NAME.OPEN_IM_MSG_EXT}.${gi}`},body:{groupID:void 0,topicID:void 0,messageSequence:void 0,operateType:void 0,extensionList:void 0}}}(e)),this._configMap.set(_i,function(e){return{head:{...e,servcmd:`${m.NAME.OPEN_IM_MSG_EXT}.${_i}`},body:{groupID:void 0,topicID:void 0,messageSequence:void 0,startSequence:void 0}}}(e)),this._configMap.set(ki,function(e){return{head:{...e,servcmd:`${m.NAME.MESSAGE_SEARCH}.${ki}`},body:{keywordList:void 0,keywordListMatchType:"or",account:void 0,groupID:void 0,count:100,cursor:void 0,messageTypeList:void 0,senderUserIDList:void 0,startTime:void 0,endTime:void 0},keyMap:{request:{keywordListMatchType:"MatchType",account:"PeerAccount",groupID:"GroupID",messageTypeList:"MsgTypeList",senderUserIDList:"SendUserIDList"},response:{GroupID:"groupID",UserID:"userID",Count:"messageCount",LastMsgTime:"lastMessageTime",ConversationMsgs:"searchResult",IsNeedReadReceipt:"needReadReceipt",IsPeerRead:"readReceiptSentByPeer",MsgSeq:"sequence",ReqMsgSeq:"sequence",IsSystemMsg:"isSystemMessage",ToGroupId:"to",EnumFrom_AccountType:"fromAccountType",EnumTo_AccountType:"toAccountType",GroupCode:"groupCode",MsgContent:"content",ClientSeq:"clientSequence",ToTopicId:"topicID",InvisibleMsgSeq:"invisibleSequenceList"}}}}(e)),this._configMap.set(Mo,function(e){return{head:{...e,servcmd:`${m.NAME.OPEN_IM}.${Mo}`},body:{userIDList:void 0},keyMap:{request:{userIDList:"To_Account"},response:{ReadTime:"peerReadTimeList"}}}}(e)),this._configMap.set(To,function(e){return{head:{...e,servcmd:`${m.NAME.RECENT_CONTACT}.${To}`},body:{fromAccount:void 0,timeStamp:void 0,startIndex:void 0,pinnedTimeStamp:void 0,pinnedStartIndex:void 0,orderType:void 0,messageAssistFlag:15,assistFlag:31},keyMap:{request:{messageAssistFlag:"MsgAssistFlags",assistFlag:"AssistFlags",pinnedTimeStamp:"TopTimeStamp",pinnedStartIndex:"TopStartIndex"},response:{SessionItem:"conversations",ToAccount:"groupID",To_Account:"userID",UnreadMsgCount:"unreadCount",MsgGroupReadedSeq:"messageReadSeq",C2cPeerReadTime:"c2cPeerReadTime",LastMsgFlags:"lastMessageFlag",TopFlags:"isPinned",TopTimeStamp:"pinnedTimeStamp",TopStartIndex:"pinnedStartIndex",GroupId:"convGroupID",C2cRemark:"friendRemark",MsgRecvOption:"messageRemindType",GroupIgnoredUnreadSeqCount:"noUnreadCount",GroupNextMsgSeq:"nextMessageSeq"}}}}(e)),this._configMap.set(vo,function(e){return{head:{...e,servcmd:`${m.NAME.RECENT_CONTACT}.${vo}`},body:{fromAccount:"",conversationList:void 0,clearHistoryMessage:void 0},keyMap:{request:{toGroupID:"ToGroupid",clearHistoryMessage:"ClearRamble",conversationList:"ContactItem"},response:{ResultItem:"resultList",ToGroupid:"groupID",ResultCode:"code",ResultInfo:"info"}}}}(e)),this._configMap.set(Do,function(e){return{head:{...e,servcmd:`${m.NAME.RECENT_CONTACT}.${Do}`},body:{fromAccount:"",toAccount:void 0,type:1,toGroupID:void 0},keyMap:{request:{toGroupID:"ToGroupid"}}}}(e)),this._configMap.set(So,function(e){return{head:{...e,servcmd:`${m.NAME.RECENT_CONTACT}.${So}`},body:{fromAccount:"",operationType:1,itemList:void 0},keyMap:{request:{itemList:"RecentContactItem"}}}}(e)),this._configMap.set(No,function(e){return{head:{...e,servcmd:`${m.NAME.OPEN_IM}.${No}`},body:{messageListToDelete:void 0},keyMap:{request:{messageListToDelete:"DelMsgList",messageSeq:"MsgSeq",messageRandom:"MsgRandom"}}}}(e)),this._configMap.set(Eo,function(e){return{head:{...e,servcmd:`${m.NAME.RECENT_CONTACT}.${Ao}`},body:{fromAccount:"",itemList:void 0},keyMap:{request:{itemList:"MarkItem",operationType:"OptType",groupID:"ToGroupId"},response:{ToGroupId:"groupID",OptType:"operationType"}}}}(e)),this._configMap.set(Ao,function(e){return{head:{...e,servcmd:`${m.NAME.RECENT_CONTACT}.${Ao}`},body:{fromAccount:"",itemList:void 0},keyMap:{request:{itemList:"MarkItem",operationType:"OptType",groupID:"ToGroupId"},response:{ToGroupId:"groupID",OptType:"operationType"}}}}(e)),this._configMap.set(Lo,function(e){return{head:{...e,servcmd:`${m.NAME.RECENT_CONTACT}.${Ro}`},body:{fromAccount:"",itemList:void 0},keyMap:{request:{itemList:"GroupContactItem",groupID:"ToGroupId"},response:{GroupId:"convGroupID",ToGroupId:"groupID",OptType:"operationType"}}}}(e)),this._configMap.set(Oo,function(e){return{head:{...e,servcmd:`${m.NAME.RECENT_CONTACT}.${Oo}`},body:{fromAccount:"",groupName:void 0},keyMap:{request:{},response:{GroupId:"convGroupID"}}}}(e)),this._configMap.set(Ro,function(e){return{head:{...e,servcmd:`${m.NAME.RECENT_CONTACT}.${Ro}`},body:{fromAccount:"",updateType:void 0,updateGroup:void 0},keyMap:{request:{oldName:"OldGroupName",newName:"NewGroupName",groupID:"ToGroupId",operationType:"ContactOptType",groupName:"OldGroupName",updateItem:"ContactUpdateItem"},response:{ContactOptType:"operationType",ToGroupId:"groupID",GroupId:"convGroupID"}}}}(e)),this._configMap.set(Uo,function(e){return{head:{...e,servcmd:`${m.NAME.RECENT_CONTACT}.${Ro}`},body:{fromAccount:"",updateType:void 0,updateGroup:{groupName:void 0,updateGroupType:void 0,updateItem:void 0}},keyMap:{request:{},response:{}}}}(e)),this._configMap.set(ko,function(e){return{head:{...e,servcmd:`${m.NAME.RECENT_CONTACT}.${Ro}`},body:{fromAccount:"",updateType:void 0,updateGroup:void 0},keyMap:{request:{},response:{}}}}(e)),this._configMap.set(Po,function(e){return{head:{...e,servcmd:`${m.NAME.RECENT_CONTACT}.${Po}`},body:{fromAccount:"",startTime:void 0,startIndex:void 0},keyMap:{request:{},response:{GroupId:"convGroupID",ToGroupId:"groupID",OptType:"operationType",CustomMark:"customData",ContactGroupId:"convGroupIDList"}}}}(e)),this._configMap.set(Ws,function(e){return{head:{...e,servcmd:`${m.NAME.PROFILE}.${Ws}`},body:{fromAccount:"",userItem:[]},keyMap:{request:{toAccount:"To_Account",standardSequence:"StandardSequence",customSequence:"CustomSequence"}}}}(e)),this._configMap.set(Ys,function(e){return{head:{...e,servcmd:`${m.NAME.PROFILE}.${Ys}`},body:{fromAccount:"",profileItem:[{tag:fe.NICK,value:""},{tag:fe.GENDER,value:""},{tag:fe.ALLOWTYPE,value:""},{tag:fe.AVATAR,value:""}]},keyMap:{request:{toAccount:"To_Account",standardSequence:"StandardSequence",customSequence:"CustomSequence"}}}}(e)),this._configMap.set(zs,function(e){return{head:{...e,servcmd:`${m.NAME.FRIEND}.${zs}`},body:{fromAccount:"",startIndex:0,maxLimited:30,lastSequence:0},keyMap:{response:{CurruentSequence:"currentSequence"}}}}(e)),this._configMap.set(js,function(e){return{head:{...e,servcmd:`${m.NAME.FRIEND}.${js}`},body:{fromAccount:"",toAccount:[]}}}(e)),this._configMap.set(Js,function(e){return{head:{...e,servcmd:`${m.NAME.FRIEND}.${Js}`},body:{fromAccount:"",toAccount:[]}}}(e)),this._configMap.set(zi,function(e){return{head:{...e,servcmd:`${m.NAME.IM_OPEN_STATUS}.${zi}`},body:{customStatus:""},keyMap:{}}}(e)),this._configMap.set(ji,function(e){return{head:{...e,servcmd:`${m.NAME.IM_OPEN_STATUS}.${ji}`},body:{userIDList:void 0},keyMap:{response:{UserStatusList:"successUserList",ErrorList:"failureUserList",To_Account:"userID",Invalid_Account:"invalidUserID",Status:"statusType"}}}}(e)),this._configMap.set(Ji,function(e){return{head:{...e,servcmd:`${m.NAME.IM_OPEN_STATUS}.${Ji}`},body:{userIDList:void 0},keyMap:{response:{ErrorList:"failureUserList",To_Account:"userID",Invalid_Account:"invalidUserID"}}}}(e)),this._configMap.set(Xi,function(e){return{head:{...e,servcmd:`${m.NAME.IM_OPEN_STATUS}.${Xi}`},body:{userIDList:void 0,unsubscribeAll:void 0},keyMap:{response:{ErrorList:"failureUserList",To_Account:"userID",Invalid_Account:"invalidUserID"}}}}(e)),this._configMap.set(Xs,function(e){return{head:{...e,servcmd:`${m.NAME.FRIEND}.${Xs}`},body:{fromAccount:"",startIndex:0,standardSequence:0,customSequence:0},keyMap:{response:{FriendNum:"friendCount",UserDataItem:"resultList",ValueItem:"tagValueList"}}}}(e)),this._configMap.set(to,function(e){return{head:{...e,servcmd:`${m.NAME.FRIEND}.${to}`},body:{fromAccount:"",addFriendItem:[],type:""},keyMap:{request:{source:"AddSource",wording:"AddWording",type:"AddType"},response:{ResultItem:"resultList"}}}}(e)),this._configMap.set(so,function(e){return{head:{...e,servcmd:`${m.NAME.FRIEND}.${so}`},body:{fromAccount:"",updateItem:void 0},keyMap:{request:{snsItem:"SnsItem"},response:{ResultItem:"resultList"}}}}(e)),this._configMap.set(eo,function(e){return{head:{...e,servcmd:`${m.NAME.FRIEND}.${eo}`},body:{fromAccount:"",userIDList:[],type:""},keyMap:{request:{type:"DeleteType"},response:{ResultItem:"resultList"}}}}(e)),this._configMap.set(Qs,function(e){return{head:{...e,servcmd:`${m.NAME.FRIEND}.${Qs}`},body:{fromAccount:"",userIDList:void 0},keyMap:{response:{InfoItem:"resultList",SnsProfileItem:"tagValueList"}}}}(e)),this._configMap.set(Zs,function(e){return{head:{...e,servcmd:`${m.NAME.FRIEND}.${Zs}`},body:{fromAccount:"",userIDList:[],type:""},keyMap:{request:{type:"CheckType"},response:{InfoItem:"resultList"}}}}(e)),this._configMap.set(io,function(e){return{head:{...e,servcmd:`${m.NAME.FRIEND}.${io}`},body:{fromAccount:"",applicationType:"",startTime:0,maxLimited:0,lastSequence:0},keyMap:{response:{PendencyItem:"resultList",AddSource:"source",AddTime:"time",AddWording:"wording",Image:"avatar",UnreadPendencyCount:"unreadCount",To_Account:"userID",PendencyType:"type"}}}}(e)),this._configMap.set(oo,function(e){return{head:{...e,servcmd:`${m.NAME.FRIEND}.${oo}`},body:{fromAccount:"",responseFriendItem:[]},keyMap:{request:{tag:"TagName",action:"ResponseAction"},response:{ResultItem:"resultList"}}}}(e)),this._configMap.set(no,function(e){return{head:{...e,servcmd:`${m.NAME.FRIEND}.${no}`},body:{fromAccount:"",type:"",userIDList:void 0},keyMap:{request:{type:"PendencyType",userIDList:"To_Account"},response:{ResultItem:"resultList"}}}}(e)),this._configMap.set(ro,function(e){return{head:{...e,servcmd:`${m.NAME.FRIEND}.${ro}`},body:{fromAccount:"",latestTimeStamp:""},keyMap:{request:{latestTimeStamp:"LatestPendencyTimeStamp"}}}}(e)),this._configMap.set(co,function(e){return{head:{...e,servcmd:`${m.NAME.FRIEND}.${co}`},body:{fromAccount:"",groupName:void 0,userIDList:void 0},keyMap:{request:{groupName:"GroupName",userIDList:"To_Account"},response:{ResultItem:"resultList"}}}}(e)),this._configMap.set(uo,function(e){return{head:{...e,servcmd:`${m.NAME.FRIEND}.${uo}`},body:{fromAccount:"",nameList:void 0},keyMap:{request:{nameList:"GroupName"}}}}(e)),this._configMap.set(ao,function(e){return{head:{...e,servcmd:`${m.NAME.FRIEND}.${ao}`},body:{fromAccount:"",lastSequence:0,needFriend:"Need_Friend_Type_Yes"},keyMap:{response:{ResultItem:"resultList",GroupName:"name",FriendNumber:"friendCount",To_Account:"userIDList"}}}}(e)),this._configMap.set(lo,function(e){return{head:{...e,servcmd:`${m.NAME.FRIEND}.${lo}`},body:{fromAccount:"",oldName:"",newName:void 0,updateGroupItem:void 0},keyMap:{request:{oldName:"GroupOldName",newName:"GroupNewName"},response:{UpdateType:"type",ResultItem:"resultList"}}}}(e)),this._configMap.set(Go,function(e){return{head:{...e,servcmd:`${m.NAME.GROUP}.${Go}`},body:{memberAccount:"",limit:void 0,offset:void 0,groupType:void 0,responseFilter:{groupBaseInfoFilter:void 0,selfInfoFilter:void 0},isSupportTopic:0},keyMap:{request:{memberAccount:"Member_Account"},response:{GroupIdList:"groups",NoUnreadSeqList:"excludedUnreadSequenceList",MsgSeq:"readedSequence",LastRecallTime:"_lastRevokedTime"}}}}(e)),this._configMap.set(wo,function(e){return{head:{...e,servcmd:`${m.NAME.GROUP}.${wo}`},body:{groupIDList:void 0,responseFilter:{groupBaseInfoFilter:void 0,groupCustomFieldFilter:void 0,memberInfoFilter:void 0,memberCustomFieldFilter:void 0}},keyMap:{request:{groupIDList:"GroupIdList",groupCustomField:"AppDefinedData",memberCustomField:"AppMemberDefinedData",groupCustomFieldFilter:"AppDefinedDataFilter_Group",memberCustomFieldFilter:"AppDefinedDataFilter_GroupMember"},response:{GroupIdList:"groups",AppDefinedData:"groupCustomField",AppMemberDefinedData:"memberCustomField",AppDefinedDataFilter_Group:"groupCustomFieldFilter",AppDefinedDataFilter_GroupMember:"memberCustomFieldFilter",InfoSeq:"infoSequence",MemberList:"members",GroupInfo:"groups",ShutUpUntil:"muteUntil",ShutUpAllMember:"muteAllMembers"}}}}(e)),this._configMap.set(bo,function(e){return{head:{...e,servcmd:`${m.NAME.GROUP}.${bo}`},body:{type:void 0,name:void 0,groupID:void 0,ownerID:void 0,introduction:void 0,notification:void 0,maxMemberNum:void 0,joinOption:void 0,memberList:void 0,groupCustomField:void 0,memberCustomField:void 0,webPushFlag:1,avatar:"",isSupportTopic:void 0,inviteOption:void 0},keyMap:{request:{ownerID:"Owner_Account",userID:"Member_Account",avatar:"FaceUrl",maxMemberNum:"MaxMemberCount",joinOption:"ApplyJoinOption",groupCustomField:"AppDefinedData",memberCustomField:"AppMemberDefinedData",inviteOption:"InviteJoinOption"},response:{HugeGroupFlag:"avChatRoomFlag",OverJoinedGroupLimit_Account:"overLimitUserIDList"}}}}(e)),this._configMap.set(Fo,function(e){return{head:{...e,servcmd:`${m.NAME.GROUP}.${Fo}`},body:{groupID:void 0}}}(e)),this._configMap.set($o,function(e){return{head:{...e,servcmd:`${m.NAME.GROUP}.${$o}`},body:{groupID:void 0,name:void 0,introduction:void 0,notification:void 0,avatar:void 0,joinOption:void 0,groupCustomField:void 0,muteAllMembers:void 0,inviteOption:void 0},keyMap:{request:{groupCustomField:"AppDefinedData",muteAllMembers:"ShutUpAllMember",joinOption:"ApplyJoinOption",avatar:"FaceUrl",inviteOption:"InviteJoinOption"},response:{AppDefinedData:"groupCustomField",ShutUpAllMember:"muteAllMembers"}}}}(e)),this._configMap.set(qo,function(e){return{head:{...e,servcmd:`${m.NAME.GROUP}.${qo}`},body:{groupID:void 0,applyMessage:void 0,userDefinedField:void 0,webPushFlag:1,historyMessageFlag:void 0},keyMap:{request:{applyMessage:"ApplyMsg",historyMessageFlag:"HugeGroupHistoryMsgFlag"},response:{HugeGroupFlag:"avChatRoomFlag",AVChatRoomKey:"avChatRoomKey",RspMsgList:"messageList",ToGroupId:"to"}}}}(e)),this._configMap.set(xo,function(e){const{a2:t,tinyid:s,...o}=e;return{head:{...o,servcmd:`${m.NAME.BIG_GROUP_NO_AUTH}.${qo}`},body:{groupID:void 0,applyMessage:void 0,userDefinedField:void 0,webPushFlag:1},keyMap:{request:{applyMessage:"ApplyMsg"},response:{HugeGroupFlag:"avChatRoomFlag"}}}}(e)),this._configMap.set(Vo,function(e){return{head:{...e,servcmd:`${m.NAME.GROUP}.${Vo}`},body:{groupID:void 0}}}(e)),this._configMap.set(Ko,function(e){return{head:{...e,servcmd:`${m.NAME.GROUP}.${Ko}`},body:{groupIDList:void 0,responseFilter:{groupBasePublicInfoFilter:["Type","Name","Introduction","Notification","FaceUrl","CreateTime","Owner_Account","LastInfoTime","LastMsgTime","NextMsgSeq","MemberNum","MaxMemberNum","ApplyJoinOption","InviteJoinOption"]}},keyMap:{response:{}}}}(e)),this._configMap.set(Bo,function(e){return{head:{...e,servcmd:`${m.NAME.GROUP}.${Bo}`},body:{groupID:void 0,newOwnerID:void 0},keyMap:{request:{newOwnerID:"NewOwner_Account"}}}}(e)),this._configMap.set(Ho,function(e){return{head:{...e,servcmd:`${m.NAME.GROUP}.${Ho}`},body:{groupID:void 0,applicant:void 0,handleAction:void 0,handleMessage:void 0,authentication:void 0,messageKey:void 0,userDefinedField:void 0},keyMap:{request:{applicant:"Applicant_Account",handleAction:"HandleMsg",handleMessage:"ApprovalMsg",messageKey:"MsgKey"}}}}(e)),this._configMap.set(Wo,function(e){return{head:{...e,servcmd:`${m.NAME.GROUP}.${Wo}`},body:{groupID:void 0,applicant:void 0,invitee:void 0,handleAction:void 0,handleMessage:void 0,authentication:void 0,userDefinedField:void 0},keyMap:{request:{applicant:"Applicant_Account",invitee:"Invited_Account",handleAction:"HandleMsg",handleMessage:"ApprovalMsg"}}}}(e)),this._configMap.set(Yo,function(e){return{head:{...e,servcmd:`${m.NAME.GROUP}.${Yo}`},body:{groupID:void 0,inviter:void 0,handleAction:void 0,handleMessage:void 0,authentication:void 0,messageKey:void 0,userDefinedField:void 0},keyMap:{request:{inviter:"Inviter_Account",handleAction:"HandleMsg",handleMessage:"ApprovalMsg",messageKey:"MsgKey"}}}}(e)),this._configMap.set(si,function(e){return{head:{...e,servcmd:`${m.NAME.GROUP}.${si}`},body:{startTime:void 0,limit:void 0,handleAccount:void 0},keyMap:{request:{handleAccount:"Handle_Account"},response:{To_Account:"userID",ApplyInviteMsg:"note"}}}}(e)),this._configMap.set(oi,function(e){return{head:{...e,servcmd:`${m.NAME.OPEN_IM}.${oi}`},body:{messageListToDelete:void 0},keyMap:{request:{messageListToDelete:"DelMsgList",messageSeq:"MsgSeq",messageRandom:"MsgRandom"}}}}(e)),this._configMap.set(ii,function(e){return{head:{...e,servcmd:`${m.NAME.BIG_GROUP_LONG_POLLING}.${ii}`},body:{USP:1,startSeq:1,startBroadcastSeq:void 0,holdTime:90,key:void 0,simplifiedMessage:void 0},keyMap:{request:{USP:"USP"},response:{ToGroupId:"groupID",RspBroadcastMsgList:"broadcastMessageList",IsSystemMsg:"isSystemMessage"}}}}(e)),this._configMap.set(ni,function(e){const{a2:t,tinyid:s,...o}=e;return{head:{...o,servcmd:`${m.NAME.BIG_GROUP_LONG_POLLING_NO_AUTH}.${ii}`},body:{USP:1,startSeq:1,holdTime:90,key:void 0,simplifiedMessage:void 0},keyMap:{request:{USP:"USP"},response:{ToGroupId:"groupID",RspBroadcastMsgList:"broadcastMessageList",IsSystemMsg:"isSystemMessage"}}}}(e)),this._configMap.set(ri,function(e){return{head:{...e,servcmd:`${m.NAME.GROUP}.${ri}`},body:{groupID:void 0}}}(e)),this._configMap.set(ui,function(e){return{head:{...e,servcmd:`${m.NAME.GROUP}.${ui}`},body:{groupID:void 0,groupAttributeList:void 0,mainSequence:void 0,avChatRoomKey:void 0,attributeControl:["RaceConflict"]},keyMap:{request:{key:"key",value:"value"}}}}(e)),this._configMap.set(li,function(e){return{head:{...e,servcmd:`${m.NAME.GROUP}.${li}`},body:{groupID:void 0,groupAttributeList:void 0,mainSequence:void 0,avChatRoomKey:void 0,attributeControl:["RaceConflict"]},keyMap:{request:{key:"key",value:"value"}}}}(e)),this._configMap.set(di,function(e){return{head:{...e,servcmd:`${m.NAME.GROUP}.${di}`},body:{groupID:void 0,groupAttributeList:void 0,mainSequence:void 0,avChatRoomKey:void 0,attributeControl:["RaceConflict"]},keyMap:{request:{key:"key"}}}}(e)),this._configMap.set(pi,function(e){return{head:{...e,servcmd:`${m.NAME.GROUP}.${pi}`},body:{groupID:void 0,mainSequence:void 0,avChatRoomKey:void 0,attributeControl:["RaceConflict"]}}}(e)),this._configMap.set(hi,function(e){return{head:{...e,servcmd:`${m.NAME.GROUP_ATTR}.${hi}`},body:{groupID:void 0,avChatRoomKey:void 0,groupType:1},keyMap:{request:{avChatRoomKey:"Key",groupType:"GroupType"}}}}(e)),this._configMap.set(mi,function(e){return{head:{...e,servcmd:`${m.NAME.GROUP}.${mi}`},body:{notifyReqList:[]},keyMap:{request:{notifyReqList:"NotifyReqList"},response:{NextMsgTime:"nextRevokedTime",NotifyMsgList:"notifyList",NotifyRspList:"notifyRspList"}}}}(e)),this._configMap.set(Mi,function(e){return{head:{...e,servcmd:`${m.NAME.GROUP}.${Mi}`},body:{groupID:void 0,counterList:void 0,avChatRoomKey:void 0,mode:void 0},keyMap:{request:{counterList:"GroupCounter"}}}}(e)),this._configMap.set(fi,function(e){return{head:{...e,servcmd:`${m.NAME.GROUP}.${fi}`},body:{groupID:void 0,keyList:[],avChatRoomKey:void 0},keyMap:{request:{keyList:"GroupCounterKeys"}}}}(e)),this._configMap.set(Bi,function(e){return{head:{...e,servcmd:`${m.NAME.GROUP_COMMUNITY}.${Bi}`},body:{groupID:void 0,topicName:void 0,avatar:void 0,customData:void 0,topicID:void 0,notification:void 0,introduction:void 0},keyMap:{request:{avatar:"FaceUrl"}}}}(e)),this._configMap.set(Hi,function(e){return{head:{...e,servcmd:`${m.NAME.GROUP_COMMUNITY}.${Hi}`},body:{groupID:void 0,topicIDList:void 0},keyMap:{request:{topicIDList:"TopicIdList"},response:{DestroyResultItem:"resultList"}}}}(e)),this._configMap.set(Wi,function(e){return{head:{...e,servcmd:`${m.NAME.GROUP_COMMUNITY}.${Wi}`},body:{groupID:void 0,topicID:void 0,avatar:void 0,customData:void 0,notification:void 0,introduction:void 0,muteAllMembers:void 0,topicName:void 0},keyMap:{request:{avatar:"FaceUrl",muteAllMembers:"ShutUpAllMember"}}}}(e)),this._configMap.set(Yi,function(e){return{head:{...e,servcmd:`${m.NAME.GROUP_COMMUNITY}.${Yi}`},body:{groupID:void 0,topicIDList:void 0,MemberInfoFilter:["NoUnreadSeqList"]},keyMap:{request:{topicIDList:"TopicIdList"},response:{TopicAndSelfInfo:"topicInfoList",TopicInfo:"topic",GroupID:"groupID",ShutUpTime:"muteTime",ShutUpAllFlag:"muteAllMembers",LastMsgTime:"lastMessageTime",MsgSeq:"readedSequence",LastMsgSeq:"sequence",NoUnreadSeqList:"excludedUnreadSequenceList"}}}}(e)),this._configMap.set(Ii,function(e){return{head:{...e,servcmd:`${m.NAME.GROUP}.${Ii}`},body:{groupID:void 0,limit:0,offset:void 0,next:void 0,memberRoleFilter:void 0,memberInfoFilter:["Role","NameCard","ShutUpUntil","JoinTime"],memberCustomFieldFilter:void 0},keyMap:{request:{memberCustomFieldFilter:"AppDefinedDataFilter_GroupMember"},response:{AppMemberDefinedData:"memberCustomField",AppDefinedDataFilter_GroupMember:"memberCustomFieldFilter",MemberList:"members",ShutUpUntil:"muteUntil"}}}}(e)),this._configMap.set(Ci,function(e){return{head:{...e,servcmd:`${m.NAME.GROUP_AVCHATROOM}.${Ci}`},body:{groupID:void 0,offset:void 0,filter:void 0},keyMap:{request:{offset:"Timestamp",filter:"Mark"},response:{NextTimestamp:"offset"}}}}(e)),this._configMap.set(yi,function(e){return{head:{...e,servcmd:`${m.NAME.GROUP}.${yi}`},body:{groupID:void 0,userIDList:void 0,memberInfoFilter:void 0,memberCustomFieldFilter:void 0},keyMap:{request:{userIDList:"Member_List_Account",memberCustomFieldFilter:"AppDefinedDataFilter_GroupMember"},response:{MemberList:"members",ShutUpUntil:"muteUntil",AppDefinedDataFilter_GroupMember:"memberCustomFieldFilter",AppMemberDefinedData:"memberCustomField"}}}}(e)),this._configMap.set(Ti,function(e){return{head:{...e,servcmd:`${m.NAME.GROUP}.${Ti}`},body:{groupID:void 0,silence:void 0,userIDList:void 0},keyMap:{request:{userID:"Member_Account",userIDList:"MemberList"},response:{MemberList:"members"}}}}(e)),this._configMap.set(vi,function(e){return{head:{...e,servcmd:`${m.NAME.GROUP}.${vi}`},body:{groupID:void 0,userIDList:void 0,reason:void 0},keyMap:{request:{userIDList:"MemberToDel_Account"}}}}(e)),this._configMap.set(Di,function(e){return{head:{...e,servcmd:`${m.NAME.GROUP}.${Di}`},body:{groupID:void 0,userIDList:void 0,duration:void 0,reason:""},keyMap:{request:{userIDList:"Members_Account",duration:"Duration",reason:"Description"}}}}(e)),this._configMap.set(Si,function(e){return{head:{...e,servcmd:`${m.NAME.GROUP}.${Si}`},body:{groupID:void 0,topicID:void 0,userID:void 0,messageRemindType:void 0,nameCard:void 0,role:void 0,memberCustomField:void 0,muteTime:void 0},keyMap:{request:{userID:"Member_Account",memberCustomField:"AppMemberDefinedData",muteTime:"ShutUpTime",messageRemindType:"MsgFlag"}}}}(e)),this._configMap.set(Ni,function(e){return{head:{...e,servcmd:`${m.NAME.GROUP_AVCHATROOM}.${Ni}`},body:{groupID:void 0,operationType:1,memberList:[]},keyMap:{request:{operationType:"CommandType",memberList:"MemberList",markType:"Marks",userID:"Member_Account"},response:{CommandType:"operationType",Marks:"markType",Member_Account:"userID"}}}}(e)),this._configMap.set(Oi,function(e){return{head:{...e,servcmd:`${m.NAME.IM_OPEN_STAT}.${Oi}`},body:{header:{},event:[],quality:[]},keyMap:{request:{SDKType:"sdk_type",SDKVersion:"sdk_version",deviceType:"device_type",platform:"platform",instanceID:"instance_id",traceID:"trace_id",SDKAppID:"sdk_app_id",userID:"user_id",tinyID:"tiny_id",extension:"extension",timestamp:"timestamp",networkType:"network_type",eventType:"event_type",code:"error_code",message:"error_message",moreMessage:"more_message",duplicate:"duplicate",costTime:"cost_time",level:"level",qualityType:"quality_type",reportIndex:"report_index",wholePeriod:"whole_period",totalCount:"total_count",rttCount:"success_count_business",successRateOfRequest:"percent_business",countLessThan1Second:"success_count_business",percentOfCountLessThan1Second:"percent_business",countLessThan3Second:"success_count_platform",percentOfCountLessThan3Second:"percent_platform",successCountOfBusiness:"success_count_business",successRateOfBusiness:"percent_business",successCountOfPlatform:"success_count_platform",successRateOfPlatform:"percent_platform",successCountOfMessageReceived:"success_count_business",successRateOfMessageReceived:"percent_business",avgRTT:"average_value",avgDelay:"average_value",avgValue:"average_value",uiPlatform:"ui_platform"}}}}(s)),this._configMap.set(Ri,function(e){return{head:{...e,servcmd:`${m.NAME.HEARTBEAT}.${Ri}`},body:{}}}(e)),this._configMap.set(Ui,function(e){return{head:{...e,servcmd:`${m.NAME.IM_OPEN_PUSH}.${Ui}`},body:{},keyMap:{response:{C2cMsgArray:"C2CMessageArray",GroupMsgArray:"groupMessageArray",GroupTips:"groupTips",C2cNotifyMsgArray:"C2CNotifyMessageArray",C2cMsgInfo:"C2CReadReceiptArray",ClientSeq:"clientSequence",MsgPriority:"priority",NoticeSeq:"noticeSequence",MsgContent:"content",MsgType:"type",MsgBody:"elements",ToGroupId:"to",Desc:"description",Ext:"extension",IsSyncMsg:"isSyncMessage",Flag:"needSync",NeedAck:"needAck",PendencyAdd_Account:"userID",ProfileImNick:"nick",PendencyType:"applicationType",C2CReadAllMsg:"readAllC2CMessage",IsNeedReadReceipt:"needReadReceipt",Status:"statusType"}}}}(e)),this._configMap.set(Pi,function(e){return{head:{...e,servcmd:`${m.NAME.IM_OPEN_PUSH}.${Pi}`},body:{},keyMap:{response:{GroupMsgArray:"groupMessageArray",GroupTips:"groupTips",ClientSeq:"clientSequence",MsgPriority:"priority",NoticeSeq:"noticeSequence",MsgContent:"content",MsgType:"type",MsgBody:"elements",ToGroupId:"to",Desc:"description",Ext:"extension",IsSyncMsg:"isSyncMessage",Flag:"needSync",NeedAck:"needAck",PendencyType:"applicationType"}}}}(e)),this._configMap.set(Gi,function(e){return{head:{...e,servcmd:`${m.NAME.OPEN_IM}.${Gi}`},body:{sessionData:void 0},keyMap:{request:{sessionData:"SessionData"}}}}(e)),this._configMap.set(wi,function(e){return{head:{...e,servcmd:`${m.NAME.IM_OPEN_STATUS}.${wi}`},body:{},keyMap:{response:{C2cNotifyMsgArray:"C2CNotifyMessageArray",NoticeSeq:"noticeSequence",KickoutMsgNotify:"kickoutMsgNotify",NewInstInfo:"newInstanceInfo"}}}}(e)),this._configMap.set(Fi,function(e){return{head:{...e,servcmd:`${m.NAME.IM_LONG_MESSAGE}.${Fi}`},body:{downloadKey:""},keyMap:{response:{Data:"data",Desc:"description",Ext:"extension",Download_Flag:"downloadFlag",ThumbUUID:"thumbUUID",VideoUUID:"videoUUID"}}}}(e)),this._configMap.set(bi,function(e){return{head:{...e,servcmd:`${m.NAME.IM_LONG_MESSAGE}.${bi}`},body:{messageList:[]},keyMap:{request:{fromAccount:"From_Account",toAccount:"To_Account",msgTimeStamp:"MsgTimeStamp",msgSeq:"MsgSeq",msgRandom:"MsgRandom",msgBody:"MsgBody",type:"MsgType",content:"MsgContent",data:"Data",description:"Desc",extension:"Ext",sizeType:"Type",uuid:"UUID",url:"",imageUrl:"URL",fileUrl:"Url",remoteAudioUrl:"Url",remoteVideoUrl:"VideoUrl",thumbUUID:"ThumbUUID",videoUUID:"VideoUUID",videoUrl:"",downloadFlag:"Download_Flag",from:"From_Account",time:"MsgTimeStamp",messageRandom:"MsgRandom",messageSequence:"MsgSeq",elements:"MsgBody",clientSequence:"ClientSeq",payload:"MsgContent",messageList:"MsgList",messageNumber:"MsgNum",abstractList:"AbstractList",messageBody:"MsgBody"}}}}(e)),this._configMap.set(en,function(e){return{head:{...e,servcmd:`${m.NAME.IM_OPEN_STATUS}.${en}`},body:{tokenID:"",pushMsg:0,sdkAppID:0,businessID:"",deviceBrand:"",deviceToken:"",isTpns:0,isWebUniapp:0},keyMap:{request:{tokenID:"TokenID",pushMsg:"PushMsg",sdkAppID:"EnterVersion",businessID:"BusiID",deviceBrand:"InstType",deviceToken:"VarToken",isTpns:"IsTpns"}}}}(e)),this._configMap.set(Zi,function(e){return{head:{...e,servcmd:`${m.NAME.IM_OPEN_STATUS}.${Zi}`},body:{isWebUniapp:0}}}(e)),this._configMap.set(Qi,function(e){return{head:{...e,servcmd:`${m.NAME.IM_OPEN_STATUS}.${Qi}`},body:{C2CUnread:0,GroupUnread:0,isWebUniapp:0},keyMap:{request:{c2cUnreadCount:"C2cUnread",groupUnreadCount:"GrpUnread"}}}}(e))}has(e){return this._configMap.has(e)}get(e){return this._configMap.get(e)}update(){this._fillConfigMap()}getKeyMap(e){return this.has(e)?this.get(e).keyMap||{}:(me.w(`${this._n}.getKeyMap unknown protocolName:${e}`),{})}getProtocolData(e){const{protocolName:t,requestData:s}=e,o=this.get(t);let i=null;if(s){const e=this._simpleDeepCopy(o),t=this._updateService(s,e),n=t.body,r=Object.create(null);for(const o in n)if(Object.prototype.hasOwnProperty.call(n,o)){if(r[o]=n[o],void 0===s[o])continue;r[o]=s[o]}t.body=r,i=this._getUplinkData(t)}else i=this._getUplinkData(o);return i}_getUplinkData(e){const t=this._requestDataCleaner(e),s=ct(t.head),o=Fr(t.body,this._getRequestKeyMap(s));return t.body=o,t}_updateService(e,t){const s=ct(t.head);if(this._isFromGroupRequest(t)){let{type:o,groupID:i,groupIDList:n=[]}=e;Re(i)&&(i=n[0]||""),je({type:o,groupID:i})&&(t.head.servcmd=`${m.NAME.GROUP_COMMUNITY}.${s}`)}return t}_isFromGroupRequest(e){return e.head.servcmd.includes(m.NAME.GROUP)||e.head.servcmd.includes(m.NAME.GROUP_ATTR)}_getRequestKeyMap(e){const t=this.getKeyMap(e);return{...Gr.request,...t.request}}_requestDataCleaner(e){const t=Array.isArray(e)?[]:Object.create(null);for(const s in e)Object.prototype.hasOwnProperty.call(e,s)&&be(s)&&null!==e[s]&&void 0!==e[s]&&("object"!=typeof e[s]?t[s]=e[s]:t[s]=this._requestDataCleaner.bind(this)(e[s]));return t}_simpleDeepCopy(e){const t=Object.keys(e),s={};let o;for(let i=0,n=t.length;i<n;i++)o=t[i],Oe(e[o])?s[o]=Array.from(e[o]):Ae(e[o])?s[o]=this._simpleDeepCopy(e[o]):s[o]=e[o];return s}}const Zr=[Gi];class ea{constructor(e){this._sessionModule=e,this._n="DownlinkHandler",this._eventHandlerMap=new Map,this._eventHandlerMap.set("C2CMessageArray",this._c2cMessageArrayHandler.bind(this)),this._eventHandlerMap.set("groupMessageArray",this._groupMessageArrayHandler.bind(this)),this._eventHandlerMap.set("groupTips",this._groupTipsHandler.bind(this)),this._eventHandlerMap.set("C2CNotifyMessageArray",this._C2CNotifyMessageArrayHandler.bind(this)),this._eventHandlerMap.set("C2CReadReceiptArray",this._C2CReadReceiptArrayHandler.bind(this)),this._eventHandlerMap.set("profileModify",this._profileHandler.bind(this)),this._eventHandlerMap.set("friendListMod",this._relationChainHandler.bind(this)),this._eventHandlerMap.set("recentContactMod",this._recentContactHandler.bind(this)),this._eventHandlerMap.set("readAllC2CMessage",this._allMessageReadHandler.bind(this)),this._eventHandlerMap.set("c2cMessageModified",this._c2cMessageModifiedHandler.bind(this)),this._eventHandlerMap.set("groupMessageModified",this._groupMessageModifiedHandler.bind(this)),this._eventHandlerMap.set("userStatusList",this._userStatusListHandler.bind(this)),this._eventHandlerMap.set("messageExtensionNotify",this._messageExtensionNotifyHandler.bind(this)),this._keys=[...this._eventHandlerMap.keys()]}_c2cMessageArrayHandler(e){const t=this._sessionModule.getModule(ns);if(t){if(e.dataList.forEach(e=>{if(1===e.isSyncMessage){const t=e.from;e.from=e.to,e.to=t}}),1===e.needSync){this._sessionModule.getModule(ms).startOnlineSync()}t.onNewC2CMessage({dataList:e.dataList,isInstantMessage:!0})}}_c2cMessageModifiedHandler(e){const t=this._sessionModule.getModule(ns);t&&t.onC2CMessageModified(e)}_groupMessageArrayHandler(e){const t=this._sessionModule.getModule(rs);t&&t.onNewGroupMessage({event:e.event,dataList:e.dataList,isInstantMessage:!0})}_groupMessageModifiedHandler(e){const t=this._sessionModule.getModule(rs);t&&t.onGroupMessageModified(e)}_groupTipsHandler(e){const t=this._sessionModule.getModule(rs);if(!t)return;const{event:s,dataList:o,isInstantMessage:i=!0,isSyncingEnded:n}=e;switch(s){case 4:case 6:t.onNewGroupTips({event:s,dataList:o});break;case 5:for(let e=0;e<o.length;e++)if(Oe(o[e].elements.revokedInfos))t.onGroupMessageRevoked({dataList:o});else if(Oe(o[e].elements.groupMessageReadNotice))t.onGroupMessageReadNotice({dataList:o});else{if(!Oe(o[e].elements.readReceiptList)){t.onNewGroupSystemNotice({dataList:o,isInstantMessage:i,isSyncingEnded:n});break}t.onReadReceiptList({dataList:o})}break;case 12:this._sessionModule.getModule(us).onNewGroupAtTips({dataList:o});break;default:me.l(`${this._n}._groupTipsHandler unknown event:${s} dataList:`,o)}}_C2CNotifyMessageArrayHandler(e){const{dataList:t}=e;if(!Oe(t))return;const s=this._sessionModule.getModule(ns);t.forEach(e=>{if(Le(e))if(e.hasOwnProperty("kickoutMsgNotify")){const{kickoutMsgNotify:{kickType:t,newInstanceInfo:s={}}}=e;1===t?this._sessionModule.onMultipleAccountKickedOut(s):2===t?this._sessionModule.onMultipleDeviceKickedOut(s):3===t&&this._sessionModule.onRestApiKickedOut(s)}else if(e.hasOwnProperty("c2cMessageRevokedNotify"))s&&s.onC2CMessageRevoked({dataList:t});else if(e.hasOwnProperty("c2cMessageReadReceipt"))s&&s.onC2CMessageReadReceipt({dataList:t});else if(e.hasOwnProperty("c2cMessageReadNotice"))s&&s.onC2CMessageReadNotice({dataList:t});else if(e.hasOwnProperty("muteNotificationsSync")){this._sessionModule.getModule(us).onC2CMessageRemindTypeSynced({dataList:t})}})}_C2CReadReceiptArrayHandler(e){this._sessionModule.getModule(ns).onReadReceiptList(e)}_profileHandler(e){this._sessionModule.getModule(is).onProfileModified({dataList:e.dataList});const t=this._sessionModule.getModule(as);t&&t.onFriendProfileModified({dataList:e.dataList})}_relationChainHandler(e){this._sessionModule.getModule(is).onRelationChainModified({dataList:e.dataList});const t=this._sessionModule.getModule(as);t&&t.onRelationChainModified({dataList:e.dataList})}_recentContactHandler(e){const{dataList:t}=e;if(!Oe(t))return;const s=this._sessionModule.getModule(us);s&&t.forEach(e=>{const{pushType:t}=e;if(1===t){const{recentContactDeleteItem:t}=e;s.onConversationDeleted(t.recentContactList)}else if(2===t){const{recentContactTopItem:t}=e;s.onConversationPinned(t.recentContactList)}else if(3===t){const{recentContactTopItem:t}=e;s.onConversationUnpinned(t.recentContactList)}else if(4===t){const{recentContactMarkContact:t}=e;s.onConversationMarkUpdated(t.recentContactMarkContactItem)}else if(5===t){const{recentContactCreateContactGroup:t}=e;s.onConversationGroupCreated(t.msgContactGroupContactItem)}else if(6===t){const{recentContactDelContactGroup:t}=e;s.onConversationGroupDeleted(t.msgGroupItem)}else if(7===t){const{recentContactUpdateContactGroup:t}=e,{updateType:o,msgUpdateGroup:i,msgUpdateContact:n}=t;if(1===o){const{updateGroupType:e}=i;1===e?s.onConversationGroupNameUpdated(i):2===e&&s.onConversationInGroupUpdated(i)}else 2===o&&s.onConversationAddedToOrDeletedFromGroup(n)}})}_allMessageReadHandler(e){const{dataList:t}=e,s=this._sessionModule.getModule(us);s&&s.onPushedAllMessageRead(t)}_userStatusListHandler(e){this._sessionModule.getModule(is).onUserStatusUpdated(e)}_messageExtensionNotifyHandler(e){this._sessionModule.getModule(os).onMessageExtensionNotify(e)}onMessage(e){const{body:t}=e;if(!this._filterMessageFromIMOpenPush(e))return;const{eventArray:s,isInstantMessage:o,isSyncingEnded:i,needSync:n}=t;if(!Oe(s))return;let r=null,a=null,c=0;for(let u=0,l=s.length;u<l;u++){r=s[u],c=r.event;const e=Object.keys(r).find(e=>-1!==this._keys.indexOf(e));e?(a=14===c?{readAllC2CMessage:r[e],groupMessageReadInfoList:r.groupMessageReadNotice||[]}:16===c?{userID:r.userID,readReceiptList:r[e]}:r[e],this._eventHandlerMap.get(e)({event:c,dataList:a,isInstantMessage:o,isSyncingEnded:i,needSync:n})):me.l(`${this._n}.onMessage unknown eventItem:${r}`)}}_filterMessageFromIMOpenPush(e){const{head:t,body:s}=e,{servcmd:o}=t;let i=!1;if(Re(o)||(i=o.includes(m.NAME.IM_CONFIG_MANAGER)||o.includes(m.NAME.OVERLOAD_PUSH)||o.includes(m.NAME.STAT_SERVICE)),!i)return!0;if(o.includes(qi)){this._sessionModule.getModule(Cs).onPushedCloudControlConfig(s)}else if(o.includes(Vi)){this._sessionModule.getModule(Ds).onPushedConfig(s)}else if(o.includes(Ki))this._sessionModule.onPushedServerOverload(s);else if(o.includes(Vs)){const e=Date.now();this._sessionModule.reLoginOnKickOther();const t=new In("kickOther"),s=this._sessionModule.getModule(ts).getLastWsHelloTs(),o=e-s;t.setMessage(`last wshello time:${s} diff:${o}ms`).setNetworkType(this._sessionModule.getNetworkType()).end()}return!1}}const ta=[{cmd:wo,interval:1,count:20},{cmd:Ci,interval:3,count:1},{cmd:si,interval:1,count:15},{cmd:Yi,interval:1,count:10},{cmd:ui,interval:5,count:10},{cmd:li,interval:5,count:10},{cmd:di,interval:5,count:10},{cmd:pi,interval:5,count:10},{cmd:hi,interval:5,count:20},{cmd:Mi,interval:5,count:20},{cmd:fi,interval:5,count:20},{cmd:Jo,interval:1,count:1},{cmd:ji,interval:5,count:20},{cmd:Ji,interval:5,count:20},{cmd:Xi,interval:5,count:20},{cmd:ki,interval:1,count:2}];class sa extends Fs{constructor(e){super(e),this._n="SessionModule",this._platform=this.getPlatform(),this._protocolHandler=new Qr(this),this._messageDispatcher=new ea(this),this._commandFrequencyLimitMap=new Map,this._commandRequestInfoMap=new Map,this._serverOverloadInfoMap=new Map,this._init();this.getInnerEmitterInstance().on(Fn.CLOUD_CONFIG_UPDATED,this._onCloudConfigUpdated,this)}_init(){this._updateCommandFrequencyLimitMap(ta)}_onCloudConfigUpdated(){let e=this.getCloudConfig("cmd_frequency_limit");Re(e)||(e=JSON.parse(e),this._updateCommandFrequencyLimitMap(e))}_updateCommandFrequencyLimitMap(e){e.forEach(e=>{this._commandFrequencyLimitMap.set(e.cmd,{interval:e.interval,count:e.count})})}updateProtocolConfig(){this._protocolHandler.update()}request(e){me.d(this._n+".request options:",e);const{protocolName:t,tjgID:s}=e;if(!this._protocolHandler.has(t))return me.w(`${this._n}.request unknown protocol:${t}`),bs({code:ks.CANNOT_FIND_PROTOCOL});const o=this.getProtocolData(e),{servcmd:i}=o.head;if(this._isFrequencyOverLimit(i))return bs({code:ks.OVER_FREQUENCY_LIMIT});if(this._isServerOverload(i))return bs({code:ks.OPEN_SERVICE_OVERLOAD_ERROR});Ct(s)||(o.head.tjgID=s);const n=this.getModule(fs);return Zr.includes(t)?n.simplySend(o):n.send(o)}getKeyMap(e){return this._protocolHandler.getKeyMap(e)}genCommonHead(){const e=this.getModule(ls);return{ver:"v4",platform:this._platform,websdkappid:a,websdkversion:r,a2:e.getA2Key()||void 0,tinyid:e.getTinyID()||void 0,status_instid:e.getStatusInstanceID(),sdkappid:e.getSDKAppID(),contenttype:e.getContentType(),reqtime:0,identifier:e.getA2Key()?void 0:e.getUserID(),usersig:e.getA2Key()?void 0:e.getUserSig(),sdkability:192371,tjgID:""}}genCosSpecifiedHead(){const e=this.getModule(ls);return{ver:"v4",platform:this._platform,websdkappid:a,websdkversion:r,sdkappid:e.getSDKAppID(),contenttype:e.getContentType(),reqtime:0,identifier:e.getUserID(),usersig:e.getUserSig(),status_instid:e.getStatusInstanceID(),sdkability:192371}}genSSOReportHead(){const e=this.getModule(ls);return{ver:"v4",platform:this._platform,websdkappid:a,websdkversion:r,sdkappid:e.getSDKAppID(),contenttype:"",reqtime:0,identifier:"",usersig:"",status_instid:e.getStatusInstanceID(),sdkability:192371}}getProtocolData(e){return this._protocolHandler.getProtocolData(e)}trans(e){const{servcmd:t,data:s}=e,o={head:{...this.genCommonHead(),servcmd:t},body:s};return this.getModule(fs).send(o)}sendComboMessage(e){const{servcmd:t,data:s}=e,o={head:{...this.genCommonHead(),servcmd:t},body:s};return this.getModule(fs).send(o)}onErrorCodeNotZero(e){const{errorCode:t}=e;if(t===ks.HELLO_ANSWER_KICKED_OUT){const{kickType:t,newInstanceInfo:s={}}=e;1===t?this.onMultipleAccountKickedOut(s):2===t?this.onMultipleDeviceKickedOut(s):3===t&&this.onRestApiKickedOut(s)}if(t===ks.MSG_A2KEY_EXPIRED||t===ks.ACCOUNT_A2KEY_EXPIRED){this._onUserSigExpired();this.getModule(fs).reConnect()}}onMessage(e){const{body:t}=e,{needAck:s=0,sessionData:o}=t;1===s&&this._sendACK(o),this._messageDispatcher.onMessage(e)}onReconnected(){this._reLoginOnReconnected()}reLoginOnKickOther(){me.l(this._n+".reLoginOnKickOther"),this._reLogin()}_reLoginOnReconnected(){me.l(this._n+"._reLoginOnReconnected"),this._reLogin()}_reLogin(){if(this.isLoggedIn()){let e=0;const t=this.getModule(Ss);t.canIUseOfflinePush()&&(e=t.getUniAppPlatform()),this.request({protocolName:$s,requestData:{isWebUniapp:e}}).then(e=>{const{instanceID:t}=e.data;this.getModule(ls).setStatusInstanceID(t),me.l(`${this._n}._reLogin ok. instanceID:${t}`);this.getModule(us).syncConversationList().then(()=>{me.l(this._n+"._reLogin, sync conversation list ok.");this.getModule(Ts).start()});const s=this.getModule(rs);s&&s.updateLocalMainSequenceOnReconnected();const o=this.getModule(cs);o.resetGetTopicTime(),o.getTopicListOnReconnected()})}}onMultipleAccountKickedOut(e){this.getModule(ts).onMultipleAccountKickedOut(e)}onMultipleDeviceKickedOut(e){this.getModule(ts).onMultipleDeviceKickedOut(e)}_onUserSigExpired(){this.getModule(ts).onUserSigExpired()}onRestApiKickedOut(e){this.getModule(ts).onRestApiKickedOut(e)}_sendACK(e){this.request({protocolName:Gi,requestData:{sessionData:e}})}_isFrequencyOverLimit(e){const t=e.split(".")[1];if(!this._commandFrequencyLimitMap.has(t))return!1;if(!this._commandRequestInfoMap.has(t))return this._commandRequestInfoMap.set(t,{startTime:Date.now(),requestCount:1}),!1;const{count:s,interval:o}=this._commandFrequencyLimitMap.get(t);let{startTime:i,requestCount:n}=this._commandRequestInfoMap.get(t);if(Date.now()-i>1e3*o)return this._commandRequestInfoMap.set(t,{startTime:Date.now(),requestCount:1}),!1;n+=1,this._commandRequestInfoMap.set(t,{startTime:i,requestCount:n});let r=!1;return n>s&&(r=!0),r}_isServerOverload(e){if(!this._serverOverloadInfoMap.has(e))return!1;const{overloadTime:t,waitingTime:s}=this._serverOverloadInfoMap.get(e);let o=!1;return Date.now()-t<=1e3*s?o=!0:(this._serverOverloadInfoMap.delete(e),o=!1),o}onPushedServerOverload(e){const{overloadCommand:t,waitingTime:s}=e;this._serverOverloadInfoMap.set(t,{overloadTime:Date.now(),waitingTime:s}),me.w(`${this._n}.onPushedServerOverload waitingTime:${s}s`)}reset(){me.l(this._n+".reset"),this._updateCommandFrequencyLimitMap(ta),this._commandRequestInfoMap.clear(),this._serverOverloadInfoMap.clear()}}class oa extends Fs{constructor(e){super(e),this._n="MessageLossDetectionModule",this._maybeLostSequencesMap=new Map,this._firstRoundRet=[]}onMessageMaybeLost(e,t,s){this._maybeLostSequencesMap.has(e)||this._maybeLostSequencesMap.set(e,[]);const o=this._maybeLostSequencesMap.get(e);for(let i=0;i<s;i++)-1===o.indexOf(t+i)&&o.push(t+i)}detectFirstRound(e,t){const s=this._maybeLostSequencesMap.get(e);if(Ct(s)||Ct(t))return;const o=s.filter(e=>-1===t.indexOf(e));0===o.length?me.i(`${this._n}.detectFirstRound no message loss. conversationID:${e}`):this._firstRoundRet=this._firstRoundRet.concat(o),s.length=0}detectSecondRound(e,t){if(Ct(this._firstRoundRet)||Ct(t))return;const s=this._firstRoundRet.filter(e=>-1===t.indexOf(e));this._firstRoundRet.length=0;const o=s.length;if(0===o)return;let i;o<=5?i=e+"-"+s.join("-"):(s.sort((e,t)=>e-t),i=e+" start:"+s[0]+" end:"+s[o-1]+" count:"+o);new In("messageLoss").setMessage(i).setNetworkType(this.getNetworkType()).setLevel("warning").end(),me.i(`${this._n}.detectSecondRound message loss detected. conversationID:${e} lostSequences:${s}`)}reset(){me.l(this._n+".reset"),this._maybeLostSequencesMap.clear(),this._firstRoundRet.length=0}}class ia extends Fs{constructor(e){super(e),this._n="CloudControlModule",this._cloudConfig=new Map,this._expiredTime=0,this._version=0,this._isFetching=!1}getCloudConfig(e){return Re(e)?this._cloudConfig:this._cloudConfig.has(e)?this._cloudConfig.get(e):void 0}_canFetchConfig(){return this.isLoggedIn()&&!this._isFetching&&Date.now()>=this._expiredTime}fetchConfig(){const e=this._canFetchConfig();if(me.l(`${this._n}.fetchConfig canFetchConfig:${e}`),!e)return;const t=new In("fetchCloudControlConfig"),s=this.getModule(ls).getSDKAppID();this._isFetching=!0,this.request({protocolName:$i,requestData:{SDKAppID:s,version:this._version}}).then(e=>{this._isFetching=!1,t.setMessage(`version:${this._version} newVersion:${e.data.version} config:${e.data.cloudControlConfig}`).setNetworkType(this.getNetworkType()).end(),me.l(this._n+".fetchConfig ok"),this._parseCloudControlConfig(e.data)}).catch(e=>{this._isFetching=!1,this.probeNetwork().then(([s,o])=>{t.setError(e,s,o).end()}),me.l(this._n+".fetchConfig failed. error:",e),this._setExpiredTimeOnResponseError(12e4)})}onPushedCloudControlConfig(e){me.l(this._n+".onPushedCloudControlConfig");new In("pushedCloudControlConfig").setNetworkType(this.getNetworkType()).setMessage(`newVersion:${e.version} config:${e.cloudControlConfig}`).end(),this._parseCloudControlConfig(e)}onCheckTimer(e){this._canFetchConfig()&&this.fetchConfig()}_parseCloudControlConfig(e){const t=this._n+"._parseCloudControlConfig",{errorCode:s,errorMessage:o,cloudControlConfig:i,version:n,expiredTime:r}=e;if(0===s){if(this._version!==n){let e=null;try{e=JSON.parse(i)}catch(a){this.isPrivateNetWork()||me.e(t+" JSON parse error. cloudControlConfig:",i)}e&&(this._cloudConfig.clear(),Object.keys(e).forEach(t=>{this._cloudConfig.set(t,e[t])}),this._version=n,this.emitInnerEvent(Fn.CLOUD_CONFIG_UPDATED))}this._expiredTime=Date.now()+1e3*r}else Re(s)?(me.l(t+" failed. Invalid message format:",e),this._setExpiredTimeOnResponseError(36e5)):(me.e(`${t} errorCode:${s} errorMessage:${o}`),this._setExpiredTimeOnResponseError(12e4))}_setExpiredTimeOnResponseError(e){this._expiredTime=Date.now()+e}reset(){me.l(this._n+".reset"),this._cloudConfig.clear(),this._expiredTime=0,this._version=0,this._isFetching=!1}}class na extends Fs{constructor(e){super(e),this._n="RecoverMessageModule",this.PULL_LIMIT_COUNT=15}start(){this._recoverGroupChat(),this._recoverC2CChat()}_recoverGroupChat(){const e=this._getLocalConversationList().filter(e=>e.type===t.CONV_GROUP&&e.groupProfile.type!==t.GRP_AVCHATROOM),s=this.getModule(us);let o,i,n=0,r=0,a=0;const c=[];e.forEach(e=>{const{conversationID:u,lastMessage:l}=e;i=u.replace(t.CONV_GROUP,""),o=s.getLocalLastMessage(u),l&&0!==l.lastSequence&&o?(r=l.lastSequence,n=o.sequence,a=r-n,n>0&&a>=1&&a<300?this._recoverGroupMessage({groupID:i,localLastMessageSequence:n,remoteLastMessageSequence:r}):c.push(i)):c.push(i)}),this._getGroupNotify(c)}_recoverC2CChat(){const e=this._getLocalConversationList().filter(e=>e.type===t.CONV_C2C),s=this.getModule(us);let o,i=0,n=0,r=0;const a=[Promise.resolve()];e.forEach(e=>{const{conversationID:t,lastMessage:c}=e;o=s.getLocalLastMessage(t),c&&0!==c.lastTime&&o&&(n=c.lastTime,i=o.time,r=n-i,i>0&&r>=1&&r<=600&&a.push(this._recoverC2CMessage({conversationID:t,localLastMessageTime:i,remoteLastMessageTime:n})))}),Promise.all(a).then(()=>{me.l(this._n+"._recoverC2CChat all promise fulfilled, start to sync unread messages");this.getModule(ms).startSyncOnReconnected()})}_getLocalConversationList(){return this.getModule(us).getLocalConversationList()}_recoverGroupMessage(e){const s=this._n+"._recoverGroupMessage";me.l(s+" options:",e);const{groupID:o,localLastMessageSequence:i,remoteLastMessageSequence:n}=e;this._getGroupRoamingMessage({groupID:o,sequence:i}).then(e=>{const{complete:i,messageList:r}=e.data;if(!Re(r)){const e=r[0].sequence,a=`groupID:${o} pkgLastSequence:${e} remoteLastSequence:${n} complete:${i} count:${r.length}`;me.l(`${s} ${a}`),e<n&&2!==i&&this._recoverGroupMessage({groupID:o,localLastMessageSequence:e,remoteLastMessageSequence:n});new In("recoverMessage").setNetworkType(this.getNetworkType()).setMessage(a).end();const c=this.getModule(rs);r.length>1&&r.sort((e,t)=>e.sequence-t.sequence);for(let s=0;s<r.length;s++){const e=r[s];e.from!==t.CONV_SYSTEM?c.onNewGroupMessage({dataList:[e],isInstantMessage:!1,updateUnreadCount:!1}):c.onNewGroupTips({event:e.event,dataList:[e]})}this._getGroupNotify([o])}})}_genMultiGroupIDList(e,t){const s=t&&t>1?t:1,o=e.length,i=[];if(o>0)for(let n=0;n<o;n+=s)i.push(e.slice(n,n+s));return i}_getGroupNotify(e){const t=this._genMultiGroupIDList(e,10);if(t.length>0){const e=this.getModule(rs);for(let s=0,o=t.length;s<o;s++)e.getGroupNotify(t[s])}}_getGroupRoamingMessage(e){const{groupID:t,sequence:s}=e;return this.request({protocolName:Xo,requestData:{groupID:t,count:this.PULL_LIMIT_COUNT,sequence:s+this.PULL_LIMIT_COUNT-1}})}_recoverC2CMessage(e){const s=this._n+"._recoverC2CMessage";me.l(s+" options:",e);const{conversationID:o,localLastMessageTime:i,remoteLastMessageTime:n}=e;return this._getC2CRoamingMessage({conversationID:o,time:i}).then(e=>{const{complete:i,messageList:r}=e.data;if(!Re(r)){const e=r.length;this.getModule(ns).onNewC2CMessage({dataList:r,isInstantMessage:!0});const a=r[e-1].time,c=`peerAccount:${o.replace(t.CONV_C2C,"")} pkgLastTime:${a} remoteLastTime:${n} complete:${i} count:${e}`;me.l(`${s} ${c}`);if(new In("recoverMessage").setNetworkType(this.getNetworkType()).setMessage(c).end(),a<n&&1!==i)return this._recoverC2CMessage({conversationID:o,localLastMessageTime:a,remoteLastMessageTime:n})}})}_getC2CRoamingMessage(e){const{conversationID:s,time:o}=e;return this.request({protocolName:mo,requestData:{peerAccount:s.replace(t.CONV_C2C,""),count:this.PULL_LIMIT_COUNT+1,lastMessageTime:o,direction:1}})}reset(){me.l(this._n+".reset")}}class ra{constructor(){this._n="AvgE2EDelay",this._e2eDelayArray=[]}addMessageDelay(e){const t=pe()-e;t>=0&&this._e2eDelayArray.push(t)}_calcAvg(e,t){if(0===t)return 0;let s=0;return e.forEach(e=>{s+=e}),ut(s/t,1)}_calcCountWithLimit(e){const{e2eDelayArray:t,min:s,max:o}=e;return t.filter(e=>s<=e&&e<o).length}_calcPercent(e,t){let s=ut(e/t*100,2);return s>100&&(s=100),s}_checkE2EDelayException(e,t){const s=e.filter(e=>e>t);if(s.length>0){const t=s.length,o=Math.min(...s),i=Math.max(...s),n=this._calcAvg(s,t),r=ut(t/e.length*100,2);if(r>50){new In("messageE2EDelayException").setMessage(`count:${t} min:${o} max:${i} avg:${n} percent:${r}`).setLevel("warning").end()}}}getStatResult(){const e=this._e2eDelayArray.length;if(0===e)return null;const t=[...this._e2eDelayArray],s=this._calcCountWithLimit({e2eDelayArray:t,min:0,max:1}),o=this._calcCountWithLimit({e2eDelayArray:t,min:1,max:3}),i=this._calcPercent(s,e),n=this._calcPercent(o,e),r=this._calcAvg(t,e);return this._checkE2EDelayException(t,3),t.length=0,this.reset(),{totalCount:e,countLessThan1Second:s,percentOfCountLessThan1Second:i,countLessThan3Second:o,percentOfCountLessThan3Second:n,avgDelay:r}}reset(){this._e2eDelayArray.length=0}}class aa{constructor(){this._n="AvgRTT",this._requestCount=0,this._rttArray=[]}addRequestCount(){this._requestCount+=1}addRTT(e){this._rttArray.push(e)}_calcTotalCount(){return this._requestCount}_calcRTTCount(e){return e.length}_calcSuccessRateOfRequest(e,t){if(0===t)return 0;let s=ut(e/t*100,2);return s>100&&(s=100),s}_calcAvg(e,t){if(0===t)return 0;let s=0;return e.forEach(e=>{s+=e}),parseInt(s/t)}_calcMax(){return Math.max(...this._rttArray)}_calcMin(){return Math.min(...this._rttArray)}getStatResult(){const e=this._calcTotalCount(),t=[...this._rttArray];if(0===e)return null;const s=this._calcRTTCount(t),o=this._calcSuccessRateOfRequest(s,e),i=this._calcAvg(t,s);return me.l(`${this._n}.getStatResult max:${this._calcMax()} min:${this._calcMin()} avg:${i}`),this.reset(),{totalCount:e,rttCount:s,successRateOfRequest:o,avgRTT:i}}reset(){this._requestCount=0,this._rttArray.length=0}}class ca{constructor(){this._map=new Map}initMap(e){e.forEach(e=>{this._map.set(e,{totalCount:0,successCount:0,failedCountOfUserSide:0,costArray:[],fileSizeArray:[]})})}addTotalCount(e){return!(Re(e)||!this._map.has(e))&&(this._map.get(e).totalCount+=1,!0)}addSuccessCount(e){return!(Re(e)||!this._map.has(e))&&(this._map.get(e).successCount+=1,!0)}addFailedCountOfUserSide(e){return!(Re(e)||!this._map.has(e))&&(this._map.get(e).failedCountOfUserSide+=1,!0)}addCost(e,t){return!(Re(e)||!this._map.has(e))&&(this._map.get(e).costArray.push(t),!0)}addFileSize(e,t){return!(Re(e)||!this._map.has(e))&&(this._map.get(e).fileSizeArray.push(t),!0)}_calcSuccessRateOfBusiness(e){if(Re(e)||!this._map.has(e))return-1;const t=this._map.get(e);let s=ut(t.successCount/t.totalCount*100,2);return s>100&&(s=100),s}_calcSuccessRateOfPlatform(e){if(Re(e)||!this._map.has(e))return-1;const t=this._map.get(e);let s=this._calcSuccessCountOfPlatform(e)/t.totalCount*100;return s=ut(s,2),s>100&&(s=100),s}_calcTotalCount(e){return Re(e)||!this._map.has(e)?-1:this._map.get(e).totalCount}_calcSuccessCountOfBusiness(e){return Re(e)||!this._map.has(e)?-1:this._map.get(e).successCount}_calcSuccessCountOfPlatform(e){if(Re(e)||!this._map.has(e))return-1;const t=this._map.get(e);return t.successCount+t.failedCountOfUserSide}_calcAvg(e){return Re(e)||!this._map.has(e)?-1:e===dn?this._calcAvgSpeed(e):this._calcAvgCost(e)}_calcAvgCost(e){const t=this._map.get(e).costArray.length;if(0===t)return 0;let s=0;return this._map.get(e).costArray.forEach(e=>{s+=e}),parseInt(s/t)}_calcAvgSpeed(e){let t=0,s=0;return this._map.get(e).costArray.forEach(e=>{t+=e}),this._map.get(e).fileSizeArray.forEach(e=>{s+=e}),parseInt(1e3*s/t)}getStatResult(e){const t=this._calcTotalCount(e);if(0===t)return null;const s=this._calcSuccessCountOfBusiness(e),o=this._calcSuccessRateOfBusiness(e),i=this._calcSuccessCountOfPlatform(e),n=this._calcSuccessRateOfPlatform(e),r=this._calcAvg(e);return this.reset(e),{totalCount:t,successCountOfBusiness:s,successRateOfBusiness:o,successCountOfPlatform:i,successRateOfPlatform:n,avgValue:r}}reset(e){Re(e)?this._map.clear():this._map.set(e,{totalCount:0,successCount:0,failedCountOfUserSide:0,costArray:[],fileSizeArray:[]})}}class ua{constructor(){this._lastMap=new Map,this._currentMap=new Map}initMap(e){e.forEach(e=>{this._lastMap.set(e,new Map),this._currentMap.set(e,new Map)})}addMessageSequence(e){const{key:s,message:o}=e;if(Re(s)||!this._lastMap.has(s)||!this._currentMap.has(s))return!1;const{conversationID:i,sequence:n}=o,r=i.replace(t.CONV_GROUP,"");if(0===this._lastMap.get(s).size)this._addCurrentMap(e);else if(this._lastMap.get(s).has(r)){const t=this._lastMap.get(s).get(r),o=t.length-1;n>t[0]&&n<t[o]?(t.push(n),t.sort(),this._lastMap.get(s).set(r,t)):this._addCurrentMap(e)}else this._addCurrentMap(e);return!0}_addCurrentMap(e){const{key:s,message:o}=e,{conversationID:i,sequence:n}=o,r=i.replace(t.CONV_GROUP,"");this._currentMap.get(s).has(r)||this._currentMap.get(s).set(r,[]),this._currentMap.get(s).get(r).push(n)}_copyData(e){if(!Re(e)){this._lastMap.set(e,new Map);let t=this._lastMap.get(e);for(const[s,o]of this._currentMap.get(e))t.set(s,o);t=null,this._currentMap.set(e,new Map)}}getStatResult(e){if(Re(this._currentMap.get(e))||Re(this._lastMap.get(e)))return null;if(0===this._lastMap.get(e).size)return this._copyData(e),null;let t=0,s=0;if(this._lastMap.get(e).forEach((e,o)=>{const i=[...e.values()],n=i.length;t+=i[n-1]-i[0]+1,s+=n}),0===t)return null;let o=ut(s/t*100,2);return o>100&&(o=100),this._copyData(e),{totalCount:t,successCountOfMessageReceived:s,successRateOfMessageReceived:o}}reset(){this._currentMap.clear(),this._lastMap.clear()}}class la extends Fs{constructor(e){super(e),this._n="QualityStatModule",this.TAG="im-ssolog-quality-stat",this.reportIndex=0,this.wholePeriod=!1,this._qualityItems=[nn,rn,an,cn,un,ln,dn,pn,hn,gn],this._messageSentItems=[an,cn,un,ln,dn],this._messageReceivedItems=[pn,hn,gn],this.REPORT_INTERVAL=120,this.REPORT_SDKAPPID_BLACKLIST=[],this.REPORT_TINYID_WHITELIST=[],this._statInfoArr=[],this._avgRTT=new aa,this._avgE2EDelay=new ra,this._rateMessageSent=new ca,this._rateMessageReceived=new ua;const t=this.getInnerEmitterInstance();t.on(Fn.A2KEY_AND_TINYID_UPDATED,this._onLoginSuccess,this),t.on(Fn.CLOUD_CONFIG_UPDATED,this._onCloudConfigUpdated,this)}_onLoginSuccess(){this._rateMessageSent.initMap(this._messageSentItems),this._rateMessageReceived.initMap(this._messageReceivedItems);const e=this.getModule(ds),t=e.getItem(this.TAG,!1);!Ct(t)&&ke(t.forEach)&&(me.l(`${this._n}._onLoginSuccess. logs count:${t.length}`),t.forEach(e=>{this._statInfoArr.push(e)}),e.removeItem(this.TAG,!1))}_onCloudConfigUpdated(){const e=this.getCloudConfig("q_rpt_interval"),t=this.getCloudConfig("q_rpt_sdkappid_bl"),s=this.getCloudConfig("q_rpt_tinyid_wl");Re(e)||(this.REPORT_INTERVAL=Number(e)),Re(t)||(this.REPORT_SDKAPPID_BLACKLIST=t.split(",").map(e=>Number(e))),Re(s)||(this.REPORT_TINYID_WHITELIST=s.split(","))}onCheckTimer(e){this.isLoggedIn()&&e%this.REPORT_INTERVAL==0&&(this.wholePeriod=!0,this._report())}addRequestCount(){this._avgRTT.addRequestCount()}addRTT(e){this._avgRTT.addRTT(e)}addMessageDelay(e){this._avgE2EDelay.addMessageDelay(e)}addTotalCount(e){this._rateMessageSent.addTotalCount(e)||me.w(this._n+".addTotalCount invalid key:",e)}addSuccessCount(e){this._rateMessageSent.addSuccessCount(e)||me.w(this._n+".addSuccessCount invalid key:",e)}addFailedCountOfUserSide(e){this._rateMessageSent.addFailedCountOfUserSide(e)||me.w(this._n+".addFailedCountOfUserSide invalid key:",e)}addCost(e,t){this._rateMessageSent.addCost(e,t)||me.w(this._n+".addCost invalid key or cost:",e,t)}addFileSize(e,t){this._rateMessageSent.addFileSize(e,t)||me.w(this._n+".addFileSize invalid key or size:",e,t)}addMessageSequence(e){this._rateMessageReceived.addMessageSequence(e)||me.w(this._n+".addMessageSequence invalid key:",e.key)}_getQualityItem(e){let t={},s=Mn[this.getNetworkType()];Re(s)&&(s=8);const o={qualityType:_n[e],timestamp:le(),networkType:s,extension:""};switch(e){case nn:t=this._avgRTT.getStatResult();break;case rn:t=this._avgE2EDelay.getStatResult();break;case an:case cn:case un:case ln:case dn:t=this._rateMessageSent.getStatResult(e);break;case pn:case hn:case gn:t=this._rateMessageReceived.getStatResult(e)}return null===t?null:{...o,...t}}_report(e){let t=[],s=null;Re(e)?this._qualityItems.forEach(e=>{s=this._getQualityItem(e),null!==s&&(s.reportIndex=this.reportIndex,s.wholePeriod=this.wholePeriod,t.push(s))}):(s=this._getQualityItem(e),null!==s&&(s.reportIndex=this.reportIndex,s.wholePeriod=this.wholePeriod,t.push(s))),me.d(this._n+"._report",t),this._statInfoArr.length>0&&(t=t.concat(this._statInfoArr),this._statInfoArr=[]);const o=this.getModule(ls),i=o.getSDKAppID(),n=o.getTinyID();lt(this.REPORT_SDKAPPID_BLACKLIST,i)&&!dt(this.REPORT_TINYID_WHITELIST,n)&&(t=[]),t.length>0&&this._doReport(t)}_doReport(e){const t={header:fr(this),quality:e};this.request({protocolName:Oi,requestData:{...t}}).then(()=>{this.reportIndex++,this.wholePeriod=!1}).catch(t=>{me.w(`${this._n}._doReport failed. networkType:${this.getNetworkType()} error:`,t),this._statInfoArr=this._statInfoArr.concat(e),this._flushAtOnce()})}_flushAtOnce(){const e=this.getModule(ds),t=e.getItem(this.TAG,!1),s=this._statInfoArr,o=this._n+"._flushAtOnce";if(Ct(t))me.l(`${o} count:${s.length}`),e.setItem(this.TAG,s,!0,!1);else{let i=s.concat(t);i.length>10&&(i=i.slice(0,10)),me.l(`${o} count:${i.length}`),e.setItem(this.TAG,i,!0,!1)}this._statInfoArr=[]}reset(){me.l(this._n+".reset"),this._report(),this.reportIndex=0,this.wholePeriod=!1,this.REPORT_SDKAPPID_BLACKLIST=[],this.REPORT_TINYID_WHITELIST=[],this._avgRTT.reset(),this._avgE2EDelay.reset(),this._rateMessageSent.reset(),this._rateMessageReceived.reset()}}class da extends Fs{constructor(e){super(e),this._n="WorkerTimerModule",this._isWorkerEnabled=!0,this._workerTimer=null,this._timerID=-1,this._init();this.getInnerEmitterInstance().on(Fn.CLOUD_CONFIG_UPDATED,this._onCloudConfigUpdated,this)}isWorkerEnabled(){return this._isWorkerEnabled&&Z}startWorkerTimer(){me.l(this._n+".startWorkerTimer"),this._workerTimer&&this._workerTimer.postMessage("start")}stopWorkerTimer(){me.l(this._n+".stopWorkerTimer"),this._workerTimer&&this._workerTimer.postMessage("stop")}_init(){if(Z){const e=URL.createObjectURL(new Blob(['let interval = -1;onmessage = function(event) {  if (event.data === "start") {    if (interval > 0) {      clearInterval(interval);    }    interval = setInterval(() => {      postMessage("");    }, 1000);    postMessage(interval);  } else if (event.data === "stop") {    clearInterval(interval);    interval = -1;  }};'],{type:"application/javascript; charset=utf-8"}));this._workerTimer=new Worker(e);const t=this;this._workerTimer.onmessage=function(e){e.data?(t._timerID=e.data,me.l(`${t._n}._init seed:${t._timerID}`)):t._m.onCheckTimer()}}}_onCloudConfigUpdated(){const e=this.getCloudConfig("enable_worker");me.l(`${this._n}._onCloudConfigUpdated enableWorker:${e}`),Re(e)||"1"===e?!this._isWorkerEnabled&&Z&&(this._isWorkerEnabled=!0,this.startWorkerTimer(),this._m.onWorkerTimerEnabled()):this._isWorkerEnabled&&Z&&(this._isWorkerEnabled=!1,this.stopWorkerTimer(),this._m.onWorkerTimerDisabled())}terminate(){me.l(this._n+".terminate"),this._workerTimer&&(this._workerTimer.terminate(),this._workerTimer=null,this._timerID=-1)}getTimerID(){return this._timerID}reset(){me.l(this._n+".reset")}}class pa{constructor(){this._n="PurchasedFeatureHandler",this._purchasedFeatureMap=new Map}isValidPurchaseBits(e){return e&&"string"==typeof e&&e.length>=1&&e.length<=64&&/[01]{1,64}/.test(e)}parsePurchaseBits(e){if(this.isValidPurchaseBits(e)){this._purchasedFeatureMap.clear();let t=null;for(let s=e.length-1,i=0;s>=0;s--,i++)t=i<32?new o(0,Math.pow(2,i)).toString():new o(Math.pow(2,i-32),0).toString(),"1"===e[s]?this._purchasedFeatureMap.set(t,!0):this._purchasedFeatureMap.set(t,!1)}else me.w(`${this._n}.parsePurchaseBits invalid purchasebits:${e}`)}hasPurchasedFeature(e){return!!this._purchasedFeatureMap.get(e)}isFeatureEnabled(e){const t=parseInt(e).toString(2);let s=void 0,i=!0;for(let n=t.length-1,r=0;n>=0;n--,r++)if("1"===t.charAt(n)&&(s=r<32?new o(0,Math.pow(2,r)).toString():new o(Math.pow(2,r-32),0).toString(),!this._purchasedFeatureMap.get(s))){i=!1;break}return me.l(`${this._n}.isFeatureEnabled decimalNumber:${e} binaryString:${t} ret:${i}`),ws({enabled:i})}clear(){this._purchasedFeatureMap.clear()}}class ha{constructor(e){this._m=e,this._n="CommercialConfigModule",this._expiredTime=0,this._isFetching=!1,this._purchasedFeatureHandler=new pa}_canFetch(){return this._getModule(ls).isLoggedIn()?!this._isFetching&&Date.now()>=this._expiredTime:(this._expiredTime=Date.now()+2e3,!1)}onCheckTimer(e){this._canFetch()&&this.fetchConfig()}fetchConfig(){const e=this._canFetch(),t=this._n+".fetchConfig";if(me.l(`${t} canFetch:${e}`),!e)return;const s=this._getModule(hs),o=new In("fetchCommercialConfig");o.setNetworkType(s.getNetworkType());const i=this._getModule(ls).getSDKAppID(),n=this._getModule(Ms);this._isFetching=!0,n.request({protocolName:xi,requestData:{SDKAppID:i}}).then(e=>{o.setMessage("purchaseBits:"+e.data.purchaseBits).end(),me.l(t+" ok."),this._parseConfig(e.data),this._isFetching=!1}).catch(e=>{s.probe().then(([t,s])=>{o.setError(e,t,s).end()}),this._isFetching=!1})}onPushedConfig(e){const t=`${this._n}.onPushedConfig data:${JSON.stringify(e)}`;me.l(""+t);new In("pushedCommercialConfig").setNetworkType(this._getModule(hs).getNetworkType()).setMessage("purchaseBits:"+e.purchaseBits).end(),this._parseConfig(e)}_parseConfig(e){const t=this._n+"._parseConfig",{errorCode:s,errorMessage:o,purchaseBits:i,expiredTime:n}=e;0===s?(this._purchasedFeatureHandler.parsePurchaseBits(i),this._expiredTime=Date.now()+1e3*n):Re(s)?(me.l(t+" failed. Invalid message format:",e),this._setExpiredTimeOnResponseError(36e5)):(me.e(`${t} errorCode:${s} errorMessage:${o}`),this._setExpiredTimeOnResponseError(12e4))}_setExpiredTimeOnResponseError(e){this._expiredTime=Date.now()+e}canIUse(e){return this._purchasedFeatureHandler.hasPurchasedFeature(e)}isFeatureEnabled(e){return this._purchasedFeatureHandler.isFeatureEnabled(e)}_getModule(e){return this._m.getModule(e)}reset(){me.l(this._n+".reset"),this._expiredTime=0,this._isFetching=!1,this._purchasedFeatureHandler.clear()}}class ga extends Fs{constructor(e){super(e),this._m=e,this._n="OfflinePushModule",this._offlinePushPlugin=void 0,this._androidPushConfig={huaweiPushBussinessId:"",xiaomiPushBussinessId:"",xiaomiPushAppId:"",xiaomiPushAppKey:"",meizuPushBussinessId:"",meizuPushAppId:"",meizuPushAppKey:"",vivoPushBussinessId:"",fcmPushBussinessId:"",oppoPushBussinessId:"",oppoPushAppKey:"",oppoPushAppSecret:"",honorPushBussinessId:""},this._deviceToken="",this._businessID=0,this._iosBusinessID=0,this._c2cUnreadCount=0,this._groupUnreadCount=0,this._isWebUniapp=0}registerPlugin(e){if(!O)return void this.outputWarning("OfflinePushInUniapp");this._offlinePushPlugin=e["tim-offline-push-plugin"];const{huaweiBusinessID:t,xiaomiBusinessID:s,xiaomiAppID:o,xiaomiAppKey:i,meizuBusinessID:n,meizuAppID:r,meizuAppKey:a,vivoBusinessID:c,oppoBusinessID:u,oppoAppKey:l,oppoAppSecret:d,honorBusinessID:p,iosBusinessID:h}=e.offlinePushConfig||{};this._androidPushConfig.huaweiPushBussinessId=t,this._androidPushConfig.xiaomiPushBussinessId=s,this._androidPushConfig.xiaomiPushAppId=o,this._androidPushConfig.xiaomiPushAppKey=i,this._androidPushConfig.meizuPushBussinessId=n,this._androidPushConfig.meizuPushAppId=r,this._androidPushConfig.meizuPushAppKey=a,this._androidPushConfig.vivoPushBussinessId=c,this._androidPushConfig.oppoPushBussinessId=u,this._androidPushConfig.oppoPushAppKey=l,this._androidPushConfig.oppoPushAppSecret=d,this._androidPushConfig.honorPushBussinessId=p;new In("registerPlugin").setMessage("tim-offline-push-plugin").setMoreMessage("isExist:"+!Re(this._offlinePushPlugin)).end(!0),me.l(`${this._n}.registerPlugin ok. offlinePushConfig:${JSON.stringify(e.offlinePushConfig)}`),this._iosBusinessID=h,this._setAppShowListener()}init(){this._isWebUniapp=this.getUniAppPlatform(),this._getDeviceToken()}_getDeviceToken(){const e=this._n+"._getDeviceToken";if(!ke(this._offlinePushPlugin.getDeviceToken))return void me.e(e+" getDeviceToken is not a function");let t=`androidPushConfig:${JSON.stringify(this._androidPushConfig)}, iosBusinessID:${this._iosBusinessID}`;me.l(`${e} start. ${t}`);new In("_getDeviceToken").setMessage(""+t).end(!0),this._offlinePushPlugin.getDeviceToken(this._androidPushConfig,s=>{const o=new In("getDeviceTokenRes"),{code:i,msg:n}=s;if(0===i){const{deviceToken:i,deviceBrand:n,deviceType:r,bussinessId:a}=s.data;this._deviceToken=i,this._businessID=a||this._iosBusinessID,t=`deviceToken:${i}, deviceBrand:${n||r}, businessID:${this._businessID}`,me.l(`${e} ok. ${t}`),o.setMessage(t).end(!0),this._setToken()}else o.setMessage(`code:${i}, msg:${n}`).end(!0),me.e(e+" failed. error:",s)})}canIUseOfflinePush(){return O&&!Re(this._offlinePushPlugin)}_setAppShowListener(){const e=this._n+"._setAppShowListener";if(Re(this._offlinePushPlugin))return void me.e(e+" offlinePushPlugin is undefined");if(!ke(this._offlinePushPlugin.setAppShowListener))return void me.e(e+" setAppShowListener is not a function");new In("_setAppShowListener").end(!0),me.l(e+" start"),this._offlinePushPlugin.setAppShowListener(t=>{const{appShow:s}=t||{};new In("setAppShowListenerRes").setMessage("appShow:"+s).end(!0),me.l(`${e} ok. appShow:${s}`),this._m.isReady()&&(0===s?(this._getConvUnreadCount(),this._onBackground()):1===s&&this._onForeground())})}getDeviceBrand(){if(!Re(this._offlinePushPlugin)&&ke(this._offlinePushPlugin.getDeviceType)){const{deviceType:e}=this._offlinePushPlugin.getDeviceType()||{};return me.l(`${this._n}.getDeviceBrand ok. deviceType:${e}`),e}}_setToken(){const e=this._n+"._setToken",t=this.getModule(ls);let s="",o=1,i="",r="";Ct(this._deviceToken)&&(o=0);const a=this.getUniAppPlatform(),c=this.getDeviceBrand();a===n.IOS||a===n.IPAD||a===n.MAC?r=this._deviceToken:a===n.ANDROID&&(i=this._deviceToken);const u=new In("offlinePushSetToken");return s=`deviceToken:${r||i}, businessID:${this._businessID}, deviceBrand:${c}, isWebUniapp:${this._isWebUniapp}, pushMsg:${o}, platform:${a}`,u.setMessage(""+s),me.l(`${e} ${s}`),this.request({protocolName:en,requestData:{tokenID:i,pushMsg:o,sdkAppID:t.getSDKAppID(),businessID:parseInt(this._businessID),deviceBrand:c,deviceToken:r,isWebUniapp:this._isWebUniapp}}).then(t=>(u.end(),me.l(e+" ok"),t)).catch(t=>(this.probeNetwork().then(([e,s])=>{u.setError(t,e,s).end()}),me.e(e+" failed. error:",t),bs(t)))}_getConvUnreadCount(){this._c2cUnreadCount=0,this._groupUnreadCount=0;this.getModule(us).getLocalConversationList().forEach(e=>{e.type===t.CONV_C2C&&(this._c2cUnreadCount+=e.unreadCount),e.type===t.CONV_GROUP&&(this._groupUnreadCount+=e.unreadCount)})}_onBackground(){const e=this._n+"._onBackground",t=new In("_onBackground");this.request({protocolName:Qi,requestData:{c2cUnreadCount:this._c2cUnreadCount,groupUnreadCount:this._groupUnreadCount,isWebUniapp:this._isWebUniapp}}).then(s=>(t.setMessage(`c2cUnreadCount: ${this._c2cUnreadCount}, groupUnreadCount: ${this._groupUnreadCount}`).end(),me.l(e+" ok"),s)).catch(s=>{this.probeNetwork().then(([e,o])=>{t.setError(s,e,o).end()}),me.e(e+" failed. error:",s)})}_onForeground(){const e=this._n+"._onForeground",t=new In("_onForeground");this.request({protocolName:Zi,requestData:{isWebUniapp:this._isWebUniapp}}).then(s=>(t.end(),me.l(e+" ok"),s)).catch(s=>{this.probeNetwork().then(([e,o])=>{t.setError(s,e,o).end()}),me.e(e+" failed. error:",s)})}getUniAppPlatform(){const e=uni.getSystemInfoSync().platform,t=this.getDeviceBrand();return"ios"===e?n.IOS:"android"===e?n.ANDROID:1002===t?n.IPAD:1001===t?n.MAC:void 0}reset(){this._deviceToken="",this._businessID=0,this._c2cUnreadCount=0,this._groupUnreadCount=0,this._isWebUniapp=0,me.l(this._n+".reset")}}class _a extends Fs{constructor(e){super(e),this._n="ProfanityFilterModule",this._plugin=null,this._filterConfigMap=new Map,this._startIndex=0,this._version=0,this._canIUseLexicon=!1,this._isFetching=!1,this._expiredTime=0}init(){const e=this.getModule(_s).getPlugin("tim-profanity-filter-plugin");e?(this._plugin=new e({logger:me,isArray:Oe,isMap:De,isDevMode:this.isDevMode()}),this._getLexicon()):this.outputWarning("ProfanityPluginNotFound")}onCheckTimer(){this._plugin&&this._canIUseLexicon&&this.isLoggedIn()&&!this._isFetching&&Date.now()>=this._expiredTime&&this._getLexicon()}filterMessage(e,s){let o=!0;if(!this._plugin||!this._canIUseLexicon)return o;if(s&&s.messageControlInfo&&!0===s.messageControlInfo.excludedFromContentModeration)return o;const{type:i,conversationType:n}=e;if(i!==t.MSG_TEXT&&i!==t.MSG_CUSTOM)return o;const r=this._n+".filterMessage";let a;if(me.l(""+r),i===t.MSG_TEXT){if(n===t.CONV_C2C?a=f:n===t.CONV_GROUP&&(a=C),!this._isConfigOn(a))return o;const{type:s,modifiedText:i}=this._plugin.filter(e.payload.text);1===s?o=!1:2===s&&(e.payload.text=i)}else if(i===t.MSG_CUSTOM){if(n===t.CONV_C2C?a=I:n===t.CONV_GROUP&&(a=y),!this._isConfigOn(a))return o;const s=this._plugin.filter(e.payload.data),i=this._plugin.filter(e.payload.description),r=this._plugin.filter(e.payload.extension);1===s.type||1===i.type||1===r.type?o=!1:(2===s.type&&(e.payload.data=s.modifiedText),2===i.type&&(e.payload.description=i.modifiedText),2===r.type&&(e.payload.extension=r.modifiedText))}return me.l(`${r} done. isAllowedToSend:${o}`),o}filterText(e,t){const s=this._n+".filterText",o={isAllowedToSend:!0,modifiedText:e};if(!this._plugin||!this._canIUseLexicon)return o;if(!this._isConfigOn(t))return o;me.l(""+s);const{type:i,modifiedText:n}=this._plugin.filter(e);return 1===i?o.isAllowedToSend=!1:2===i&&(o.modifiedText=n),me.l(s+" done. ret:",o),o}_getLexicon(){const e=new In("profanityFilter"),t=this._n+"._getLexicon";this._isFetching=!0,this.request({protocolName:tn,requestData:{startIndex:this._startIndex,version:this._version}}).then(s=>{const{errorInfo:o,filterConfig:i,lexicon:n,strToken:r,completeFlag:a,nextStartIndex:c,version:u,expiredTime:l}=s.data,{errorCode:d,errorMessage:p}=o;return 0!==d?(this._isFetching=!1,me.w(t+" failed. error:",o),void e.setCode(d).setMessage(p).end()):(this._onFilterConfig(i),this._getToken(r),1===a?(me.l(`${t} done. version:${u} expiredTime:${l}`),this._version=u,this._canIUseLexicon=!0,this._isFetching=!1,this._expiredTime=Date.now()+1e3*l,void this._plugin.onLexiconCompleted(n)):(this._startIndex=c,this._plugin.onLexiconSliced(n),void this._getLexicon()))}).catch(s=>{this.probeNetwork().then(([t,o])=>{e.setError(s,t,o).end()}),this._isFetching=!1,me.l(t+" failed. error:",s)})}_onFilterConfig(e){Ct(e)||(this._filterConfigMap.clear(),Object.keys(e).forEach(t=>{this._filterConfigMap.set(t,e[t])}),me.l(`${this._n}._onFilterConfig. keys:${Array.from(this._filterConfigMap.keys())} values:${Array.from(this._filterConfigMap.values())}`))}_isConfigOn(e){return 1===this._filterConfigMap.get(e)}_getToken(e){if(!Ee(e))return;const t=e.length;let s="";if(t%2==0)for(let o=0;o<=t-1;o+=2)s+=e[o+1],s+=e[o];else{for(let o=0;o<t-1;o+=2)s+=e[o+1],s+=e[o];s+=e[t-1]}this._plugin.onToken(s)}reset(){me.l(this._n+".reset"),this._plugin&&(this._plugin.reset(),this._plugin=null),this._filterConfigMap.clear(),this._startIndex=0,this._version=0,this._canIUseLexicon=!1,this._isFetching=!1,this._expiredTime=0}}class ma{constructor(e){this._m=e,this._n="TransCmdModule",this._TRTCCommandList=["tui_room_svr.*"];this._m.getInnerEmitterInstance().on(Fn.CLOUD_CONFIG_UPDATED,this._onCloudConfigUpdated,this)}_onCloudConfigUpdated(){let e=this._m.getModule(Cs).getCloudConfig("rtc_cmd");Re(e)||(e=JSON.parse(e),e.forEach(e=>{this._TRTCCommandList.includes(e)||this._TRTCCommandList.push(e)}))}sendTRTCCustomData(e){const{serviceCommand:t,data:s}=e;let o=m.NAME.TUIROOM_SVR+".*";return Re(t)||(o=t),this._TRTCCommandList.includes(o)?this._trans({servcmd:o,data:s}):bs({code:ks.INVALID_TRTC_CMD})}_trans(e){me.d(`${this._n}._trans. options:${JSON.stringify(e)}`);const{servcmd:t,data:s}=e;return this._m.getModule(Ms).trans({servcmd:t,data:Ee(s)?JSON.parse(s):s})}getCommandList(){return this._TRTCCommandList}reset(){me.l(this._n+".reset")}}class Ma{constructor(e){this._m=e,this._n="ErrorMessageModule",this.TIM_ERROR_ASSISTANCE="tim_error_assistance",this.STORAGE_EXPIRES_TIME=6048e5,this._map=new Map,this._init()}_init(){const e=this._getStorageModule().getItem(this.TIM_ERROR_ASSISTANCE,!1);if(!e)return void this._fetch();let t;try{t=JSON.parse(e)}catch(s){this._getStorageModule().removeItem(this.TIM_ERROR_ASSISTANCE,!1),me.w(this._n+"._init error:",s)}t&&(this._needToUpdate(t)?this._fetch():this._fillMap(t.message))}_needToUpdate(e){const{localSavedTime:t,localSavedVersion:s}=e,o=t&&(new Date).getTime()-t>=this.STORAGE_EXPIRES_TIME,i=!s||"3.1.3"!==s;return me.l(`${this._n}._needToUpdate isTimeout:${o} isDifferentVersion:${i}`),o||i}_fetch(){if(this._m.getModule(ls).isPrivateNetWork())return;const e="https://web.sdk.qcloud.com/im/download/error-message/v3/0.0.2/tim-error-message.txt",t="application/x-www-form-urlencoded;charset=UTF-8",s=this._n+"._fetch ok in",o=this;if(U)P.request({url:e,method:"GET",timeout:3e3,header:{"content-type":t},dataType:"text",success:e=>{o._fillAndSave(e.data),me.l(s+" mini program")},fail:()=>{}});else{const i=new XMLHttpRequest,n=setTimeout(()=>{i.abort()},3e3);i.onreadystatechange=function(){4===i.readyState&&(clearTimeout(n),200!==i.status&&304!==i.status||(me.l(s+" browser"),o._fillAndSave(i.responseText)))},i.open("GET",e,!0),i.setRequestHeader("Content-type",t),i.send()}}_fillAndSave(e){this._fillMap(e),this._getStorageModule().setItem(this.TIM_ERROR_ASSISTANCE,JSON.stringify({message:e,localSavedTime:(new Date).getTime(),localSavedVersion:"3.1.3"}),!0,!1)}_getStorageModule(){return this._m.getModule(ds)}_fillMap(e){this._map.clear();const t=e.split(";\n"),s=t.length;let o,i,n;const r=new RegExp(/'/g);for(let a=0;a<s;a++)if(o=t[a].indexOf(":"),i=t[a].slice(0,o),n=t[a].slice(o+1,t[a].length),!i.startsWith("//")){if(Re(n))continue;this._map.set(i,n.replace(r,""))}}get(e){const{isIntl:t,key:s,replacement1:o,replacement2:i}=e;let n=t?s+"_en":s+"_cn";!this._map.has(n)&&this._map.has(s)&&(n=s);let r="";return this._map.has(n)?(r=this._map.get(n),Re(o)||(r=r.replace("$replacement1",o)),Re(i)||(r=r.replace("$replacement2",i)),r):r}reset(){me.l(this._n+".reset")}}class fa{constructor(e){const t=new In("sdkConstruct");if(this._n="ModuleManager",this._isReady=!1,this._reason=ks.USER_NOT_LOGGED_IN,this._startLoginTs=0,this._moduleMap=new Map,this._innerEmitter=null,this._outerEmitter=null,this._checkCount=0,this._checkTimer=-1,this._moduleMap.set(ls,new pr(this,e)),this._moduleMap.set(Ds,new ha(this)),this._moduleMap.set(Cs,new ia(this)),this._moduleMap.set(ys,new da(this)),this._moduleMap.set(vs,new la(this)),this._moduleMap.set(fs,new Xr(this)),this._moduleMap.set(Ms,new sa(this)),this._moduleMap.set(ts,new gr(this)),this._moduleMap.set(ss,new Or(this)),this._moduleMap.set(os,new Rr(this)),this._moduleMap.set(As,new Ur(this)),this._moduleMap.set(is,new dr(this)),this._moduleMap.set(ns,new wn(this)),this._moduleMap.set(us,new tr(this)),this._moduleMap.set(cs,new nr(this)),this._moduleMap.set(ds,new mr(this)),this._moduleMap.set(Ls,new Ma(this)),this._moduleMap.set(ps,new Ir(this)),this._moduleMap.set(hs,new vr(this)),this._moduleMap.set(gs,new Sr(this)),this._moduleMap.set(_s,new kr(this)),this._moduleMap.set(ms,new Pr(this)),this._moduleMap.set(Is,new oa(this)),this._moduleMap.set(Ts,new na(this)),this._moduleMap.set(Ss,new ga(this)),this._moduleMap.set(Ns,new _a(this)),this._moduleMap.set(Es,new ma(this)),this._eventThrottleMap=new Map,Le(e.modules)){let t;Object.keys(e.modules).forEach(s=>{t=e.modules[s],"group-module"===s?this._moduleMap.set(rs,new t(this)):"relationship-module"===s?this._moduleMap.set(as,new t(this)):"signaling-module"===s&&this._moduleMap.set(Os,new t(this))})}const{instanceID:s,SDKAppID:o}=e,i=`instanceID:${s} SDKAppID:${o} isIntl:${this._moduleMap.get(ls).isIntl()} host:${it()} isIOSWebView:${se} inBrowser:${k} inMiniApp:${U} workerAvailable:${Z} UserAgent:${G}`;In.bindEventStatModule(this._moduleMap.get(ps)),t.setMessage(`${i} ${function(){let e="";if(U)try{const{model:t,version:s,system:o,platform:i,SDKVersion:n}=P.getSystemInfoSync();e=`model:${t} version:${s} system:${o} platform:${i} SDKVersion:${n}`}catch(t){e=""}return e}()}`).end(),me.i("SDK "+i),Us.prototype._getErrorMessage=this.getErrorMessage.bind(this),this._readyList=void 0,this._ssoLogForReady=null,this._initReadyList()}_startTimer(){const e=this._moduleMap.get(ys),t=e.isWorkerEnabled();me.l(`${this._n}.startTimer isWorkerEnabled:${t} seed:${this._checkTimer}`),t?e.startWorkerTimer():this._startMainThreadTimer()}_startMainThreadTimer(){this._checkTimer<0&&(this._checkTimer=setInterval(this.onCheckTimer.bind(this),1e3)),me.l(`${this._n}._startMainThreadTimer seed:${this._checkTimer}`)}stopTimer(){const e=this._moduleMap.get(ys),t=e.isWorkerEnabled();me.l(`${this._n}.stopTimer isWorkerEnabled:${t} seed:${this._checkTimer}`),t?e.stopWorkerTimer():this._stopMainThreadTimer()}_stopMainThreadTimer(){me.l(this._n+"._stopMainThreadTimer"),this._checkTimer>0&&(clearInterval(this._checkTimer),this._checkTimer=-1,this._checkCount=0)}_stopMainThreadSocket(){me.l(this._n+"._stopMainThreadSocket");const e=this._moduleMap.get(fs);e.setIsWorkerEnabled(!0),e.reConnect()}_startMainThreadSocket(){me.l(this._n+"._startMainThreadSocket");const e=this._moduleMap.get(fs);e.setIsWorkerEnabled(!1),e.reConnect()}onWorkerTimerEnabled(){me.l(this._n+".onWorkerTimerEnabled, disable main thread timer and socket"),this._stopMainThreadTimer(),this._stopMainThreadSocket()}onWorkerTimerDisabled(){me.l(this._n+".onWorkerTimerDisabled, enable main thread timer and socket"),this._startMainThreadTimer(),this._startMainThreadSocket()}onCheckTimer(){this._checkCount+=1;for(const[,e]of this._moduleMap)e.onCheckTimer&&e.onCheckTimer(this._checkCount)}_initReadyList(){this._readyList=[this._moduleMap.get(ts)],this._readyList.forEach(e=>{e.ready(()=>this._onModuleReady())})}_onModuleReady(){let t=!0;if(this._readyList.forEach(e=>{e.isReady()||(t=!1)}),t&&!this._isReady){this._isReady=!0,this._outerEmitter.emit(e.SDK_READY);const t=Date.now()-this._startLoginTs;me.w(`SDK is ready. cost ${t} ms`),this._startLoginTs=Date.now();const s=this._moduleMap.get(hs).getNetworkType(),o=this._ssoLogForReady.getStartTs()+ue;this._ssoLogForReady.setNetworkType(s).setMessage(t).start(o).end()}}login(){0===this._startLoginTs&&(de(),this._startLoginTs=Date.now(),this._startTimer(),this._moduleMap.get(hs).start(),this._ssoLogForReady=new In("sdkReady"),this._reason=ks.LOGGING_IN)}onLoginFailed(){this._startLoginTs=0}getOuterEmitterInstance(){return null===this._outerEmitter&&(this._outerEmitter=new Dr,Gs(this._outerEmitter),this._outerEmitter._emit=this._outerEmitter.emit,this._outerEmitter.emit=function(t,s){if(this._canIUseSignaling()){if(t===e.MESSAGE_RECEIVED){this.getModule(Os).onNewMessageList(s)}if(t===e.MESSAGE_MODIFIED){this.getModule(Os).onMessageModified(s)}}if(t===e.CONVERSATION_LIST_UPDATED||t===e.FRIEND_LIST_UPDATED||t===e.GROUP_LIST_UPDATED||t===e.TOTAL_UNREAD_MESSAGE_COUNT_UPDATED)if(this._eventThrottleMap.has(t)){const e=Date.now(),s=this._eventThrottleMap.get(t);e-s.last<=1e3?(s.timeoutID&&clearTimeout(s.timeoutID),s.timeoutID=setTimeout(()=>{s.last=Date.now(),this._outerEmitter._emit.apply(this._outerEmitter,[t,{name:t,data:this._getEventData(t)}])},1e3)):(s.last=e,this._outerEmitter._emit.apply(this._outerEmitter,[t,{name:t,data:this._getEventData(t)}]))}else this._eventThrottleMap.set(t,{last:Date.now(),timeoutID:-1}),this._outerEmitter._emit.apply(this._outerEmitter,[t,{name:t,data:this._getEventData(t)}]);else this._outerEmitter._emit.apply(this._outerEmitter,[t,{name:t,data:arguments[1]}])}.bind(this)),this._outerEmitter}_canIUseSignaling(){const e=this.getModule(Os);return!!e&&e.canIUseSignaling()}_getEventData(t){return t===e.CONVERSATION_LIST_UPDATED?this._moduleMap.get(us).getLocalConversationList():t===e.FRIEND_LIST_UPDATED?this._moduleMap.get(as).getLocalFriendList(!1):t===e.GROUP_LIST_UPDATED?this._moduleMap.get(rs).getLocalGroupList():t===e.TOTAL_UNREAD_MESSAGE_COUNT_UPDATED?this._moduleMap.get(us).getTotalUnreadMessageCount():void 0}getInnerEmitterInstance(){return null===this._innerEmitter&&(this._innerEmitter=new Dr,this._innerEmitter._emit=this._innerEmitter.emit,this._innerEmitter.emit=function(e,t){let s;s=Le(arguments[1])&&arguments[1].data?[e,{name:arguments[0],data:arguments[1].data}]:[e,{name:arguments[0],data:arguments[1]}],this._innerEmitter._emit.apply(this._innerEmitter,s)}.bind(this)),this._innerEmitter}hasModule(e){return this._moduleMap.has(e)}getModule(e){return this._moduleMap.get(e)}isReady(){return this._isReady}isIntl(){return this.getModule(ls).isIntl()}getNotReadyReason(){return this._reason}setNotReadyReason(e){this._reason=e}getErrorMessage(e,t,s){return this._moduleMap.get(Ls).get({key:e,replacement1:t,replacement2:s,isIntl:this.isIntl()})}outputWarning(e,t,s){const o=this.getErrorMessage(e,t,s);o&&me.w(o)}onError(t){const s=`code:${t.code} message:${t.message}`;me.w("Oops! "+s);new In("error").setMessage(s).setNetworkType(this.getModule(hs).getNetworkType()).setLevel("error").end(),this.getOuterEmitterInstance().emit(e.ERROR,t)}restartTimer(){me.l(this._n+".restartTimer"),this.stopTimer(),this._startTimer();const e=this.getModule(rs);e&&e.restartPolling()}getTimerID(){const e=this._moduleMap.get(ys);return e.isWorkerEnabled()?e.getTimerID():this._checkTimer}getPollingTimerID(e){return this._moduleMap.get(rs).getPollingTimerID(e)}reset(){me.l(this._n+".reset"),de();for(const[,e]of this._moduleMap)e.reset&&e.reset();this._startLoginTs=0,this._initReadyList(),this._isReady=!1,this.stopTimer(),this._outerEmitter.emit(e.SDK_NOT_READY);for(const[,e]of this._eventThrottleMap)e.timeoutID&&clearTimeout(e.timeoutID);this._eventThrottleMap.clear()}}class Ia{constructor(e){this._funcMap=new Map,this._m=e}defense(e,t,s){if("string"!=typeof e)return null;if(0===e.length)return null;if("function"!=typeof t)return null;if(this._funcMap.has(e)&&this._funcMap.get(e).has(t))return this._funcMap.get(e).get(t);this._funcMap.has(e)||this._funcMap.set(e,new Map);let o=null;return this._funcMap.get(e).has(t)?o=this._funcMap.get(e).get(t):(o=this._pack(e,t,s),this._funcMap.get(e).set(t,o)),o}defenseOnce(e,t,s){return"function"!=typeof t?null:this._pack(e,t,s)}find(e,t){return"string"!=typeof e||0===e.length||"function"!=typeof t?null:this._funcMap.has(e)&&this._funcMap.get(e).has(t)?this._funcMap.get(e).get(t):(this._m.outputWarning("ListenerFnNotFound",e),null)}delete(e,t){return"function"==typeof t&&(!!this._funcMap.has(e)&&(!!this._funcMap.get(e).has(t)&&(this._funcMap.get(e).delete(t),0===this._funcMap.get(e).size&&this._funcMap.delete(e),!0)))}_pack(t,s,o){const i=this;return function(){try{s.apply(o,Array.from(arguments))}catch(n){const s=Object.values(e).indexOf(t),o="CallbackError";if(-1!==s){const t=Object.keys(e)[s];i._m.outputWarning(o,t,n)}new In(o).setMessage("eventName:"+t).setMoreMessage(n.message).end()}}}}class Ca{constructor(e){const t={SDKAppID:e.SDKAppID,unlimitedAVChatRoom:e.unlimitedAVChatRoom||!1,scene:e.scene||"",oversea:e.oversea||!1,instanceID:ot(),devMode:e.devMode||!1,proxyServer:e.proxyServer||void 0,fileUploadProxy:e.fileUploadProxy||void 0,fileDownloadProxy:e.fileDownloadProxy||e.fileUploadProxy||void 0,modules:e.modules||void 0};this._m=new fa(t),this._vendorMap=new Map,this._safetyCallbackFactory=new Ia(this._m)}onError(e){this._m.onError(e)}login(e){this._m.login();return this._getModule(ts).login(e)}logout(){return this._getModule(ts).logout().then(e=>(this._m.reset(),e))}isReady(){return this._m.isReady()}isIntl(){return this._m.isIntl()}getNotReadyReason(){return this._m.getNotReadyReason()}getErrorMessage(e,t,s){return this._m.getErrorMessage(e,t,s)}_getModule(e){return this._m.getModule(e)}destroy(){return this.logout().finally(()=>{this._m.stopTimer();this._getModule(ys).terminate();this._getModule(fs).dealloc();const t=this._m.getOuterEmitterInstance(),s=this._getModule(ls);t.emit(e.SDK_DESTROY,{SDKAppID:s.getSDKAppID()})})}on(e,t,s){me.d("on","eventName:"+e);this._m.getOuterEmitterInstance().on(e,this._safetyCallbackFactory.defense(e,t,s),s)}once(e,t,s){me.d("once","eventName:"+e);this._m.getOuterEmitterInstance().once(e,this._safetyCallbackFactory.defenseOnce(e,t,s),s||this)}off(e,t,s,o){me.d("off","eventName:"+e);const i=this._safetyCallbackFactory.find(e,t);if(null!==i){this._m.getOuterEmitterInstance().off(e,i,s,o),this._safetyCallbackFactory.delete(e,t)}}registerPlugin(e){if(Re(e["tim-offline-push-plugin"])){this._getModule(_s).registerPlugin(e)}else{this._getModule(Ss).registerPlugin(e)}}setLogLevel(e){if(e<=0){const e=this.getErrorMessage("TIM_ASCII_ART");e&&console.log(e);const t=this.getErrorMessage("API_REFER");if(t){const e="IM SDK API ->";_t()?console.log(`%c ${e} %c`,"background:#ff9d00; padding:1px; border-radius:3px; color: #fff","background:transparent",t):console.log(e,t)}const s=this.getErrorMessage("DOCS_GUIDE");s&&console.log(s);const o=this.getErrorMessage("IOS_WEBVIEW_WARNING");se&&o&&console.warn(o)}me.setLevel(e)}createTextMessage(e){return this._getModule(ss).createTextMessage(e)}createTextAtMessage(e){return this._getModule(ss).createTextMessage(e)}createImageMessage(e){return this._getModule(ss).createImageMessage(e)}createAudioMessage(e){return this._getModule(ss).createAudioMessage(e)}createVideoMessage(e){return this._getModule(ss).createVideoMessage(e)}createCustomMessage(e){return this._getModule(ss).createCustomMessage(e)}createFaceMessage(e){return this._getModule(ss).createFaceMessage(e)}createFileMessage(e){return this._getModule(ss).createFileMessage(e)}createLocationMessage(e){return this._getModule(ss).createLocationMessage(e)}createMergerMessage(e){return this._getModule(ss).createMergerMessage(e)}downloadMergerMessage(e){if(e.type!==t.MSG_MERGER)return bs({code:ks.MSG_MERGER_TYPE_INVALID});if(Ct(e.payload.downloadKey))return bs({code:ks.MSG_MERGER_KEY_INVALID});return this._getModule(ss).downloadMergerMessage(e).catch(e=>bs({code:ks.MSG_MERGER_DOWNLOAD_FAIL}))}createForwardMessage(e){return this._getModule(ss).createForwardMessage(e)}sendMessage(e,t){if(!(e instanceof Gn))return bs({code:ks.MSG_INSTANCE_REQUIRED});return this._getModule(ss).sendMessageInstance(e,t)}searchCloudMessages(e){return this._getModule(ss).searchCloudMessages(e)}callExperimentalAPI(e,t){if("sendComboMessage"===e){return this._getModule(As).sendMessage(t)}if("handleGroupInvitation"===e){return this._getModule(rs).handleGroupInvitation(t)}if("isCommercialAbilityEnabled"===e){return this._getModule(Ds).isFeatureEnabled(t)}if("isIntl"===e)return this.isIntl();if("sendTRTCCustomData"===e){return this._getModule(Es).sendTRTCCustomData(t)}return"getTimerID"===e?this._m.getTimerID():"getPollingTimerID"===e?this._m.getPollingTimerID(t):bs({code:ks.INVALID_OPERATION})}revokeMessage(e){return this._getModule(ss).revokeMessage(e)}resendMessage(e,t){if(!(e instanceof Gn))return bs({code:ks.MSG_INSTANCE_REQUIRED});return this._getModule(ss).resendMessage(e,t)}deleteMessage(e){return this._getModule(ss).deleteMessage(e)}translateText(e){return this._getModule(ss).translateText(e)}convertVoiceToText(e){return this._getModule(ss).convertVoiceToText(e)}setMessageExtensions(e,t){return this._getModule(os).setMessageExtensions(e,t)}getMessageExtensions(e){return this._getModule(os).getMessageExtensions(e)}deleteMessageExtensions(e,t){return this._getModule(os).deleteMessageExtensions(e,t)}modifyMessage(e){return this._getModule(ss).modifyRemoteMessage(e)}getMessageList(e){return this._getModule(us).getMessageList(e)}getMessageListHopping(e){return this._getModule(us).getMessageListHopping(e)}sendMessageReadReceipt(e){return this._getModule(us).sendReadReceipt(e)}getMessageReadReceiptList(e){return this._getModule(us).getReadReceiptList(e)}getGroupMessageReadMemberList(e){const t=this._getModule(rs);return t?t.getReadReceiptDetail(e):bs({code:ks.CANNOT_FIND_MODULE})}findMessage(e){return this._getModule(us).findMessage(e)}setMessageRead(e){return this._getModule(us).setMessageRead(e)}getConversationList(e){return this._getModule(us).getConversationList(e)}getConversationProfile(e){return this._getModule(us).getConversationProfile(e)}deleteConversation(e){return this._getModule(us).deleteConversation(e)}setConversationDraft(e){return this._getModule(us).setConversationDraft(e)}clearHistoryMessage(e){return this._getModule(us).clearHistoryMessage(e)}pinConversation(e){return this._getModule(us).pinConversation(e)}setAllMessageRead(e){return this._getModule(us).setAllMessageRead(e)}setMessageRemindType(e){return this._getModule(us).setMessageRemindType(e)}getTotalUnreadMessageCount(){return this._getModule(us).getTotalUnreadMessageCount()}setConversationCustomData(e){return this._getModule(us).setConversationCustomData(e)}markConversation(e){return this._getModule(us).markConversation(e)}getConversationGroupList(){return this._getModule(us).getConversationGroupList()}createConversationGroup(e){return this._getModule(us).createConversationGroup(e)}deleteConversationGroup(e){return this._getModule(us).deleteConversationGroup(e)}renameConversationGroup(e){return this._getModule(us).renameConversationGroup(e)}addConversationsToGroup(e){return this._getModule(us).addConversationsToGroup(e)}deleteConversationsFromGroup(e){return this._getModule(us).deleteConversationsFromGroup(e)}getMyProfile(){return this._getModule(is).getMyProfile()}getUserProfile(e){return this._getModule(is).getUserProfile(e)}updateMyProfile(e){return this._getModule(is).updateMyProfile(e)}getBlacklist(){return this._getModule(is).getLocalBlacklist()}addToBlacklist(e){return this._getModule(is).addBlacklist(e)}removeFromBlacklist(e){return this._getModule(is).deleteBlacklist(e)}setSelfStatus(e){return this._getModule(is).setSelfStatus(e)}getUserStatus(e){return this._getModule(is).getUserStatus(e)}subscribeUserStatus(e){return this._getModule(is).subscribeUserStatus(e)}unsubscribeUserStatus(e){return this._getModule(is).unsubscribeUserStatus(e)}getFriendList(){const e=this._getModule(as);return e?e.getLocalFriendList():bs({code:ks.CANNOT_FIND_MODULE})}addFriend(e){const t=this._getModule(as);return t?t.addFriend(e):bs({code:ks.CANNOT_FIND_MODULE})}deleteFriend(e){const t=this._getModule(as);return t?t.deleteFriend(e):bs({code:ks.CANNOT_FIND_MODULE})}checkFriend(e){const t=this._getModule(as);return t?t.checkFriend(e):bs({code:ks.CANNOT_FIND_MODULE})}getFriendProfile(e){const t=this._getModule(as);return t?t.getFriendProfile(e):bs({code:ks.CANNOT_FIND_MODULE})}updateFriend(e){const t=this._getModule(as);return t?t.updateFriend(e):bs({code:ks.CANNOT_FIND_MODULE})}getFriendApplicationList(){const e=this._getModule(as);return e?e.getLocalFriendApplicationList():bs({code:ks.CANNOT_FIND_MODULE})}acceptFriendApplication(e){const t=this._getModule(as);return t?t.acceptFriendApplication(e):bs({code:ks.CANNOT_FIND_MODULE})}refuseFriendApplication(e){const t=this._getModule(as);return t?t.refuseFriendApplication(e):bs({code:ks.CANNOT_FIND_MODULE})}deleteFriendApplication(e){const t=this._getModule(as);return t?t.deleteFriendApplication(e):bs({code:ks.CANNOT_FIND_MODULE})}setFriendApplicationRead(){const e=this._getModule(as);return e?e.setFriendApplicationRead():bs({code:ks.CANNOT_FIND_MODULE})}getFriendGroupList(){const e=this._getModule(as);return e?e.getLocalFriendGroupList():bs({code:ks.CANNOT_FIND_MODULE})}createFriendGroup(e){const t=this._getModule(as);return t?t.createFriendGroup(e):bs({code:ks.CANNOT_FIND_MODULE})}deleteFriendGroup(e){const t=this._getModule(as);return t?t.deleteFriendGroup(e):bs({code:ks.CANNOT_FIND_MODULE})}addToFriendGroup(e){const t=this._getModule(as);return t?t.addToFriendGroup(e):bs({code:ks.CANNOT_FIND_MODULE})}removeFromFriendGroup(e){const t=this._getModule(as);return t?t.removeFromFriendGroup(e):bs({code:ks.CANNOT_FIND_MODULE})}renameFriendGroup(e){const t=this._getModule(as);return t?t.renameFriendGroup(e):bs({code:ks.CANNOT_FIND_MODULE})}getGroupList(){const e=this._getModule(rs);return e?e.getGroupList():bs({code:ks.CANNOT_FIND_MODULE})}getGroupProfile(e){const t=this._getModule(rs);return t?t.getGroupProfile(e):bs({code:ks.CANNOT_FIND_MODULE})}createGroup(e){const t=this._getModule(rs);return t?t.createGroup(e):bs({code:ks.CANNOT_FIND_MODULE})}dismissGroup(e){const t=this._getModule(rs);return t?t.dismissGroup(e):bs({code:ks.CANNOT_FIND_MODULE})}updateGroupProfile(e){const t=this._getModule(rs);return t?t.updateGroupProfile(e):bs({code:ks.CANNOT_FIND_MODULE})}joinGroup(e){const t=this._getModule(rs);return t?t.joinGroup(e):bs({code:ks.CANNOT_FIND_MODULE})}quitGroup(e){const t=this._getModule(rs);return t?t.quitGroup(e):bs({code:ks.CANNOT_FIND_MODULE})}searchGroupByID(e){const t=this._getModule(rs);return t?t.searchGroupByID(e):bs({code:ks.CANNOT_FIND_MODULE})}getGroupOnlineMemberCount(e){const t=this._getModule(rs);return t?t.getGroupOnlineMemberCount(e):bs({code:ks.CANNOT_FIND_MODULE})}changeGroupOwner(e){const t=this._getModule(rs);return t?t.changeGroupOwner(e):bs({code:ks.CANNOT_FIND_MODULE})}getGroupApplicationList(){const e=this._getModule(rs);return e?e.getGroupApplicationList():bs({code:ks.CANNOT_FIND_MODULE})}handleGroupApplication(e){const t=this._getModule(rs);return t?t.handleGroupApplication(e):bs({code:ks.CANNOT_FIND_MODULE})}initGroupAttributes(e){const t=this._getModule(rs);return t?t.initGroupAttributes(e):bs({code:ks.CANNOT_FIND_MODULE})}setGroupAttributes(e){const t=this._getModule(rs);return t?t.setGroupAttributes(e):bs({code:ks.CANNOT_FIND_MODULE})}deleteGroupAttributes(e){const t=this._getModule(rs);return t?t.deleteGroupAttributes(e):bs({code:ks.CANNOT_FIND_MODULE})}getGroupAttributes(e){const t=this._getModule(rs);return t?t.getGroupAttributes(e):bs({code:ks.CANNOT_FIND_MODULE})}setGroupCounters(e){const t=this._getModule(rs);return t?t.setGroupCounters(e):bs({code:ks.CANNOT_FIND_MODULE})}increaseGroupCounter(e){const t=this._getModule(rs);return t?t.increaseGroupCounter(e):bs({code:ks.CANNOT_FIND_MODULE})}decreaseGroupCounter(e){const t=this._getModule(rs);return t?t.decreaseGroupCounter(e):bs({code:ks.CANNOT_FIND_MODULE})}getGroupCounters(e){const t=this._getModule(rs);return t?t.getGroupCounters(e):bs({code:ks.CANNOT_FIND_MODULE})}getGroupMemberList(e){const t=this._getModule(rs);return t?t.getGroupMemberList(e):bs({code:ks.CANNOT_FIND_MODULE})}getGroupMemberProfile(e){const t=this._getModule(rs);return t?t.getGroupMemberProfile(e):bs({code:ks.CANNOT_FIND_MODULE})}addGroupMember(e){const t=this._getModule(rs);return t?t.addGroupMember(e):bs({code:ks.CANNOT_FIND_MODULE})}deleteGroupMember(e){const t=this._getModule(rs);return t?t.deleteGroupMember(e):bs({code:ks.CANNOT_FIND_MODULE})}setGroupMemberMuteTime(e){const t=this._getModule(rs);return t?t.setGroupMemberMuteTime(e):bs({code:ks.CANNOT_FIND_MODULE})}setGroupMemberRole(e){const t=this._getModule(rs);return t?t.setGroupMemberRole(e):bs({code:ks.CANNOT_FIND_MODULE})}setGroupMemberNameCard(e){const t=this._getModule(rs);return t?t.setGroupMemberNameCard(e):bs({code:ks.CANNOT_FIND_MODULE})}setGroupMemberCustomField(e){const t=this._getModule(rs);return t?t.setGroupMemberCustomField(e):bs({code:ks.CANNOT_FIND_MODULE})}markGroupMemberList(e){const t=this._getModule(rs);return t?t.markGroupMemberList(e):bs({code:ks.CANNOT_FIND_MODULE})}getJoinedCommunityList(){return this._getModule(cs).getJoinedCommunityList()}createTopicInCommunity(e){return this._getModule(cs).createTopicInCommunity(e)}deleteTopicFromCommunity(e){return this._getModule(cs).deleteTopicFromCommunity(e)}updateTopicProfile(e){return this._getModule(cs).updateTopicProfile(e)}getTopicList(e){return this._getModule(cs).getTopicList(e)}addSignalingListener(e,t,s){const o=this._getModule(Os);o&&o.addSignalingListener(e,this._safetyCallbackFactory.defense(e,t,s),s)}removeSignalingListener(e,t,s){const o=this._safetyCallbackFactory.find(e,t);if(null!==o){const i=this._getModule(Os);i&&(i.removeSignalingListener(e,o,s),this._safetyCallbackFactory.delete(e,t))}}invite(e){const t=this._getModule(Os);return t?t.invite(e):bs({code:ks.CANNOT_FIND_MODULE})}inviteSync(e,t,s){const o=this._getModule(Os);return o?o.inviteSync(e,t,s):""}inviteInGroup(e){const t=this._getModule(Os);return t?t.invite(e):bs({code:ks.CANNOT_FIND_MODULE})}inviteInGroupSync(e,t,s){const o=this._getModule(Os);return o?o.inviteSync(e,t,s):""}cancel(e){const t=this._getModule(Os);return t?t.cancel(e):bs({code:ks.CANNOT_FIND_MODULE})}accept(e){const t=this._getModule(Os);return t?t.accept(e):bs({code:ks.CANNOT_FIND_MODULE})}reject(e){const t=this._getModule(Os);return t?t.reject(e):bs({code:ks.CANNOT_FIND_MODULE})}getSignalingInfo(e){const t=this._getModule(Os);return t?t.getSignalingInfo(e):null}modifyInvitation(e){const t=this._getModule(Os);return t?t.modifyInvitation(e):bs({code:ks.CANNOT_FIND_MODULE})}}const ya={login:1,logout:1,destroy:1,on:1,off:1,ready:1,setLogLevel:1,joinGroup:1,quitGroup:1,registerPlugin:1,getGroupOnlineMemberCount:1,isReady:1,addSignalingListener:1,removeSignalingListener:1};function Ta(e,t){if(e.isReady()||1===ya[t])return!0;const s=e.getNotReadyReason(),o={code:s,message:`${e.getErrorMessage(s)} | ${t} | ${e.getErrorMessage(ks.SDK_IS_NOT_READY)}`};return e.onError(o),o}const va={},Da={};Da.create=function(t){const o="TencentCloudChat.create";let i=0;const{SDKAppID:n}=t;if(Ne(n))i=n;else if(i=parseInt(n),isNaN(n))return me.e(o+" failed. Failed to parse the SDKAppID, please check the arguments"),null;if(i&&va[i])return va[i];me.l(""+o);const r=new Ca({...t,SDKAppID:i});r.on(e.SDK_DESTROY,e=>{va[e.data.SDKAppID]=null,delete va[e.data.SDKAppID]});const a=function(e){const t=Object.create(null);return Object.keys(es).forEach(o=>{if(!e[o])return;const i=new s;t[o]=function(){const t=Array.from(arguments);return i.use((function(t,s){const i=Ta(e,o);return!0===i?s():bs(i)})).use((function(e,t){if(!0===yt(e,Zt[o],o))return t()})).use((function(t,s){return e[o].apply(e,t)})),i.run(t)}}),t}(r);return va[i]=a,Zt.hookGetAPITips(r.getErrorMessage.bind(r)),me.l(o+" ok"),a},Da.TYPES=t,Da.EVENT=e,Da.TSignaling={NEW_INVITATION_RECEIVED:"newInvitationReceived",INVITEE_ACCEPTED:"ts_invitee_accepted",INVITEE_REJECTED:"ts_invitee_rejected",INVITATION_CANCELLED:"ts_invitation_cancelled",INVITATION_TIMEOUT:"ts_invitation_timeout",INVITATION_MODIFIED:"ts_invitation_modified",ACTION_TYPE_UNKNOWN:0,ACTION_TYPE_INVITE:1,ACTION_TYPE_CANCEL_INVITE:2,ACTION_TYPE_ACCEPT_INVITE:3,ACTION_TYPE_REJECT_INVITE:4,ACTION_TYPE_INVITE_TIMEOUT:5},Da.VERSION="3.1.3",me.l("TencentCloudChat.VERSION:"+Da.VERSION);export{Da as default};
