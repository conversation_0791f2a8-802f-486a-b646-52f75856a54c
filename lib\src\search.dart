import 'package:flutter/material.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';
import 'package:tencent_cloud_chat_demo/src/tencent_page.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_conversation.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_message.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_message_list_get_option.dart';
import 'package:tencent_cloud_chat_sdk/tencent_im_sdk_plugin.dart';
import 'package:tencent_cloud_chat_uikit/data_services/core/tim_uikit_wide_modal_operation_key.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

import 'package:tencent_cloud_chat_demo/src/provider/theme.dart';
import 'package:provider/provider.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/screen_utils.dart';
import 'package:tencent_cloud_chat_uikit/ui/widgets/avatar.dart';
import 'package:tencent_cloud_chat_uikit/ui/widgets/wide_popup.dart';

class Search extends StatefulWidget {
  const Search({
    Key? key,
    this.isAutoFocus = true,
    this.conversation,
    required this.onTapConversation,
    this.initKeyword,
    this.onBack,
  }) : super(key: key);

  /// if assign a specific conversation, it will only search in it; otherwise search globally
  final V2TimConversation? conversation;

  final VoidCallback? onBack;

  /// the callback after clicking the conversation item to specific message in it
  final Function(V2TimConversation, V2TimMessage?) onTapConversation;

  /// initial keyword for detail search
  final String? initKeyword;

  final bool? isAutoFocus;

  @override
  State<Search> createState() => _SearchState();
}

class _SearchState extends State<Search> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  List<V2TimMessage> _searchResults = [];
  List<V2TimConversation> _conversationResults = [];
  // 添加消息到会话的映射，避免userID错误
  Map<String, V2TimConversation> _messageToConversationMap = {};
  bool _isLoading = false;
  String _currentKeyword = '';

  @override
  void initState() {
    super.initState();
    _searchController.text = widget.initKeyword ?? '';
    _currentKeyword = widget.initKeyword ?? '';
    if (widget.isAutoFocus == true) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _focusNode.requestFocus();
      });
    }
    if (_currentKeyword.isNotEmpty) {
      _performSearch(_currentKeyword);
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isConversation = (widget.conversation != null);
    final theme = Provider.of<DefaultThemeData>(context).theme;
    final isWideScreen =
        TUIKitScreenUtils.getFormFactor(context) == DeviceType.Desktop;

    return TencentPage(
      child: Scaffold(
        appBar: isWideScreen
            ? null
            : AppBar(
          iconTheme: const IconThemeData(
            color: Colors.black,
          ),
          elevation: 0,
          backgroundColor: Colors.white,
          surfaceTintColor: Colors.white,
          title: Text(
            isConversation
                ? (widget.conversation?.showName ??
                widget.conversation?.conversationID ??
                TIM_t("相关聊天记录"))
                : TIM_t("全局搜索"),
            style: const TextStyle(
              color: Colors.black,
              fontSize: 16,
            ),
          ),
        ),
        body: Container(
          child: isConversation
              ? _buildConversationSearch()
              : _buildGlobalSearch(theme, isWideScreen),
        ),
      ),
      name: 'search',
    );
  }

  // 会话内搜索界面
  Widget _buildConversationSearch() {
    return Column(
      children: [
        _buildSearchBar(),
        Expanded(
          child: _buildConversationSearchResults(),
        ),
      ],
    );
  }

  // 全局搜索界面
  Widget _buildGlobalSearch(dynamic theme, bool isWideScreen) {
    return Column(
      children: [
        _buildSearchBar(),
        Expanded(
          child: _buildGlobalSearchResults(theme, isWideScreen),
        ),
      ],
    );
  }

  // 搜索框
  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Color(0xFFE5E5E5), width: 0.5),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Container(
              height: 36,
              decoration: BoxDecoration(
                color: const Color(0xFFF5F5F5),
                borderRadius: BorderRadius.circular(20),
              ),
              child: TextField(
                controller: _searchController,
                focusNode: _focusNode,
                decoration: InputDecoration(
                  hintText: TIM_t("搜索聊天记录"),
                  prefixIcon: const Icon(Icons.search, size: 20),
                  contentPadding: EdgeInsets.symmetric(vertical: 0, horizontal:   0),
                  hintStyle: const TextStyle(color: Colors.grey),  // 提示文字样式

                  border: InputBorder.none,
                ),
                onChanged: (value) {
                  if (value.trim().isEmpty) {
                    setState(() {
                      _searchResults.clear();
                      _conversationResults.clear();
                      _messageToConversationMap.clear();
                      _currentKeyword = '';
                    });
                  }
                },
                onSubmitted: (value) {
                  if (value.trim().isNotEmpty) {
                    _performSearch(value.trim());
                  }
                },
              ),
            ),
          ),
          const SizedBox(width: 12),
        ],
      ),
    );
  }

  // 会话内搜索结果
  Widget _buildConversationSearchResults() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_currentKeyword.isEmpty) {
      return _buildEmptyState();
    }

    if (_searchResults.isEmpty) {
      return Center(
        child: Text(
          TIM_t("暂无搜索结果"),
          style: TextStyle(color: Colors.grey[600]),
        ),
      );
    }

    return ListView.builder(
      itemCount: _searchResults.length,
      itemBuilder: (context, index) {
        final message = _searchResults[index];
        return _buildMessageItem(message);
      },
    );
  }

  // 全局搜索结果
  Widget _buildGlobalSearchResults(dynamic theme, bool isWideScreen) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_currentKeyword.isEmpty) {
      return _buildEmptyState();
    }

    if (_conversationResults.isEmpty && _searchResults.isEmpty) {
      return Center(
        child: Text(
          TIM_t("暂无搜索结果"),
          style: TextStyle(color: Colors.grey[600]),
        ),
      );
    }

    return ListView(
      children: [
        if (_conversationResults.isNotEmpty) ...[
          _buildSectionHeader(TIM_t("相关会话")),
          ..._conversationResults.map((conv) =>
              _buildConversationItem(conv, theme, isWideScreen)),
        ],
        if (_searchResults.isNotEmpty) ...[
          _buildSectionHeader(TIM_t("相关消息")),
          ..._searchResults.map((msg) => _buildMessageItem(msg)),
        ],
      ],
    );
  }

  // 空状态
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            TIM_t("输入关键词搜索聊天记录"),
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  // 分组标题
  Widget _buildSectionHeader(String title) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: const Color(0xFFF5F5F5),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 14,
          color: Color(0xFF666666),
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  // 会话项
  Widget _buildConversationItem(V2TimConversation conversation, dynamic theme, bool isWideScreen) {
    final displayName = conversation.showName ?? conversation.faceUrl ?? conversation.conversationID ?? "";
    return ListTile(
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: const Color(0xFF147AFF),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Center(
          child: Text(
            displayName.isNotEmpty ? displayName.substring(0, 1) : "?",
            style: const TextStyle(color: Colors.white, fontSize: 16),
          ),
        ),
      ),
      title: Text(
        displayName,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      subtitle: Text(
        _getConversationTypeText(conversation),
        style: const TextStyle(fontSize: 12, color: Color(0xFF999999)),
      ),
      onTap: () {
        // 保持原有的逻辑：进入会话内搜索
        if (isWideScreen) {
          TUIKitWidePopup.showPopupWindow(
            operationKey: TUIKitWideModalOperationKey.chatHistory,
            context: context,
            width: MediaQuery.of(context).size.width * 0.5,
            height: MediaQuery.of(context).size.width * 0.5,
            child: (onClose) => Search(
              conversation: conversation,
              initKeyword: _currentKeyword,
              onTapConversation: (V2TimConversation conversation, V2TimMessage? message) {},
            ),
            theme: theme,
          );
        } else {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => Search(
                onTapConversation: widget.onTapConversation,
                conversation: conversation,
                initKeyword: _currentKeyword,
              ),
            ),
          );
        }
      },
    );
  }
  int _getConversationTypeFromMessage(V2TimMessage message) {
    // 根据你的业务逻辑，可能需要检查message的属性来确定是群聊还是单聊
    // 这里提供一个示例实现
    if (message.groupID != null && message.groupID!.isNotEmpty) {
      return 2; // 群聊
    } else {
      return 1; // 单聊
    }
  }
  // 消息项
  Widget _buildMessageItem(V2TimMessage message) {
    print('message.faceUrl! ${message.faceUrl}');
    return ListTile(
      leading: Container(
        width: 40,
        height: 40,
        child: Avatar(
            faceUrl:message.faceUrl ?? '', // 从message获取头像URL
            showName: message.nickName ?? '', // 优先使用nickName，fallback到sender
            type: _getConversationTypeFromMessage(message), // 从message推断会话类型
            borderRadius: BorderRadius.circular(999)),
      ),
      title: Text(
         message.nickName ?? TIM_t("未知用户"),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            _getMessageContent(message),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 4),
          Text(
            _formatTime(message.timestamp),
            style: const TextStyle(fontSize: 12, color: Color(0xFF999999)),
          ),
        ],
      ),
      onTap: () {
        // 使用映射表获取正确的会话信息，避免userID错误
        final messageKey = "${message.msgID}_${message.timestamp}";
        final conversation = _messageToConversationMap[messageKey] ?? _getConversationFromMessage(message);

        widget.onTapConversation(conversation, message);
      },
    );
  }

  // 获取消息内容
  String _getMessageContent(V2TimMessage message) {
    switch (message.elemType) {
      case 1: // 文本消息
        return message.textElem?.text ?? "";
      case 3: // 图片消息
        return TIM_t("[图片]");
      case 4: // 语音消息
        return TIM_t("[语音]");
      case 5: // 视频消息
        return TIM_t("[视频]");
      case 6: // 文件消息
        return TIM_t("[文件]");
      default:
        return TIM_t("[其他消息]");
    }
  }

  // 根据消息获取会话
  V2TimConversation _getConversationFromMessage(V2TimMessage message) {
    return V2TimConversation(
      conversationID: message.groupID != null
          ? "group_${message.groupID}"
          : "c2c_${message.userID}",
      type: message.groupID != null ? 2 : 1,
    );
  }

  // 获取会话类型文本
  String _getConversationTypeText(V2TimConversation conversation) {
    switch (conversation.type) {
      case 1:
        return TIM_t("单聊");
      case 2:
        return TIM_t("群聊");
      default:
        return TIM_t("未知类型");
    }
  }

  // 格式化时间
  String _formatTime(int? timestamp) {
    if (timestamp == null) return "";

    final date = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
    final now = DateTime.now();
    final diff = now.difference(date);

    if (diff.inDays > 0) {
      return "${date.month}-${date.day}";
    } else if (diff.inHours > 0) {
      return "${diff.inHours}小时前";
    } else if (diff.inMinutes > 0) {
      return "${diff.inMinutes}分钟前";
    } else {
      return "刚刚";
    }
  }

  // 执行搜索
  Future<void> _performSearch(String keyword) async {
    if (keyword.trim().isEmpty) return;

    setState(() {
      _isLoading = true;
      _currentKeyword = keyword;
      // 清空之前的映射关系
      _messageToConversationMap.clear();
    });

    try {
      if (widget.conversation != null) {
        // 在特定会话中搜索消息
        await _searchInConversation(keyword);
      } else {
        // 全局搜索
        await _searchGlobally(keyword);
      }
    } catch (e) {
      print("搜索出错: $e");
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // 在特定会话中搜索
  Future<void> _searchInConversation(String keyword) async {
    List<V2TimMessage> allMessages = [];
    V2TimMessage? lastMsg;

    // 分页获取聊天记录
    for (int i = 0; i < 10; i++) { // 最多获取10页
      // 创建消息获取选项
      final option = V2TIMMessageListGetOption();
      option.count = 20;
      option.lastMsg = lastMsg;

      // 根据会话类型设置参数
      if (widget.conversation!.type == 1) {
        // 单聊：从 conversationID 中提取 userID
        final conversationId = widget.conversation!.conversationID!;
        if (conversationId.startsWith('c2c_')) {
          option.userID = conversationId.substring(4); // 移除 'c2c_' 前缀
        }
      } else if (widget.conversation!.type == 2) {
        // 群聊：从 conversationID 中提取 groupID
        final conversationId = widget.conversation!.conversationID!;
        if (conversationId.startsWith('group_')) {
          option.groupID = conversationId.substring(6); // 移除 'group_' 前缀
        }
      }

      final result = await TencentImSDKPlugin.v2TIMManager
          .getMessageManager()
          .getHistoryMessageList(
        count: option.count!,
        groupID: option.groupID,
        userID: option.userID,
        lastMsg: option.lastMsg,
      );

      if (result.code != 0 || result.data == null) break;

      final messages = result.data!;
      if (messages.isEmpty) break;

      allMessages.addAll(messages);

      // 如果消息数量少于请求数量，说明已经到底了
      if (messages.length < 20) break;

      lastMsg = messages.last;
    }

    // 过滤包含关键词的消息
    final filteredMessages = allMessages.where((message) {
      if (message.elemType == 1) { // 文本消息
        final text = message.textElem?.text ?? "";
        return text.toLowerCase().contains(keyword.toLowerCase());
      }
      return false;
    }).toList();

    // 为每个消息创建会话映射
    for (final message in filteredMessages) {
      final messageKey = "${message.msgID}_${message.timestamp}";
      _messageToConversationMap[messageKey] = widget.conversation!;
    }

    if (mounted) {
      setState(() {
        _searchResults = filteredMessages;
      });
    }
  }

  // 全局搜索
  Future<void> _searchGlobally(String keyword) async {
    // 先搜索会话列表
    final convResult = await TencentImSDKPlugin.v2TIMManager
        .getConversationManager()
        .getConversationList(count: 100, nextSeq: "0");

    List<V2TimConversation> matchedConversations = [];
    List<V2TimMessage> matchedMessages = [];

    if (convResult.code == 0 && convResult.data != null) {
      // 过滤会话名称包含关键词的会话
      matchedConversations = convResult.data!.conversationList!
          .where((conv) =>
      (conv.showName?.toLowerCase().contains(keyword.toLowerCase()) ?? false))
          .toList();

      // 在每个会话中搜索消息（限制搜索范围以提高性能）
      for (final conversation in convResult.data!.conversationList!.take(20)) {
        // 创建消息获取选项
        final option = V2TIMMessageListGetOption();
        option.count = 20;

        // 根据会话类型设置参数
        if (conversation.type == 1) {
          // 单聊：从 conversationID 中提取 userID
          final conversationId = conversation.conversationID!;
          if (conversationId.startsWith('c2c_')) {
            option.userID = conversationId.substring(4); // 移除 'c2c_' 前缀
          }
        } else if (conversation.type == 2) {
          // 群聊：从 conversationID 中提取 groupID
          final conversationId = conversation.conversationID!;
          if (conversationId.startsWith('group_')) {
            option.groupID = conversationId.substring(6); // 移除 'group_' 前缀
          }
        }

        final msgResult = await TencentImSDKPlugin.v2TIMManager
            .getMessageManager()
            .getHistoryMessageList(
          count: option.count!,
          groupID: option.groupID,
          userID: option.userID,
        );

        if (msgResult.code == 0 && msgResult.data != null) {
          final messages = msgResult.data!.where((message) {
            if (message.elemType == 1) {
              final text = message.textElem?.text ?? "";
              return text.toLowerCase().contains(keyword.toLowerCase());
            }
            return false;
          }).toList();

          // 为每个消息创建会话映射
          for (final message in messages) {
            final messageKey = "${message.msgID}_${message.timestamp}";
            _messageToConversationMap[messageKey] = conversation;
          }

          matchedMessages.addAll(messages);
        }
      }
    }

    if (mounted) {
      setState(() {
        _conversationResults = matchedConversations;
        _searchResults = matchedMessages.take(50).toList(); // 限制消息结果数量
      });
    }
  }
}