class PacketDetailResponse {
  int? code;
  String? level;
  String? msg;
  bool? ok;
  DetailData? data;
  int? dataType;

  PacketDetailResponse({this.code, this.level, this.msg, this.ok, this.data, this.dataType});

  PacketDetailResponse.fromJson(Map<String, dynamic> json) {
    if(json["code"] is int) {
      code = json["code"];
    }
    if(json["level"] is String) {
      level = json["level"];
    }
    if(json["msg"] is String) {
      msg = json["msg"];
    }
    if(json["ok"] is bool) {
      ok = json["ok"];
    }
    if(json["data"] is Map) {
      data = json["data"] == null ? null : DetailData.fromJson(json["data"]);
    }
    if(json["dataType"] is int) {
      dataType = json["dataType"];
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> _data = <String, dynamic>{};
    _data["code"] = code;
    _data["level"] = level;
    _data["msg"] = msg;
    _data["ok"] = ok;
    if(data != null) {
      _data["data"] = data?.toJson();
    }
    _data["dataType"] = dataType;
    return _data;
  }
}

class DetailData {
  int? id;
  int? coverId;
  int? senderId;
  String? chatType;
  int? chatId;
  String? type;
  String? totalAmount;
  int? totalCount;
  String? status;
  String? createTime;
  List<DetailItem>? list;

  DetailData({
    this.id,
    this.coverId,
    this.senderId,
    this.chatType,
    this.chatId,
    this.type,
    this.totalAmount,
    this.totalCount,
    this.status,
    this.createTime,
    this.list
  });

  DetailData.fromJson(Map<String, dynamic> json) {
    if(json["id"] is int) {
      id = json["id"];
    }
    if(json["coverId"] is int) {
      coverId = json["coverId"];
    }
    if(json["senderId"] is int) {
      senderId = json["senderId"];
    }
    if(json["chatType"] is String) {
      chatType = json["chatType"];
    }
    if(json["chatId"] is int) {
      chatId = json["chatId"];
    }
    if(json["type"] is String) {
      type = json["type"];
    }
    if(json["totalAmount"] != null) {
      if(json["totalAmount"] is int) {
        totalAmount = json["totalAmount"].toString();
      } else if(json["totalAmount"] is double) {
        totalAmount = json["totalAmount"].toString();
      } else if(json["totalAmount"] is String) {
        totalAmount = json["totalAmount"];
      }
    }
    if(json["totalCount"] is int) {
      totalCount = json["totalCount"];
    }
    if(json["status"] is String) {
      status = json["status"];
    }
    if(json["createTime"] is String) {
      createTime = json["createTime"];
    }
    if(json["list"] is List) {
      list = json["list"] == null ? null : (json["list"] as List).map((e) => DetailItem.fromJson(e)).toList();
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> _data = <String, dynamic>{};
    _data["id"] = id;
    _data["coverId"] = coverId;
    _data["senderId"] = senderId;
    _data["chatType"] = chatType;
    _data["chatId"] = chatId;
    _data["type"] = type;
    _data["totalAmount"] = totalAmount;
    _data["totalCount"] = totalCount;
    _data["status"] = status;
    _data["createTime"] = createTime;
    if(list != null) {
      _data["list"] = list?.map((e) => e.toJson()).toList();
    }
    return _data;
  }
}

class DetailItem {
  String? nick;
  String? phone;
  String? amount;
  String? snatchTime;
  String? userId;

  DetailItem({this.nick, this.phone, this.amount, this.snatchTime, this.userId});

  DetailItem.fromJson(Map<String, dynamic> json) {
    if(json["nick"] is String) {
      nick = json["nick"];
    }
    if(json["phone"] is String) {
      phone = json["phone"];
    }
    if(json["amount"] != null) {
      if(json["amount"] is int) {
        amount = json["amount"].toString();
      } else if(json["amount"] is double) {
        amount = json["amount"].toString();
      } else if(json["amount"] is String) {
        amount = json["amount"];
      }
    }
    if(json["snatchTime"] is String) {
      snatchTime = json["snatchTime"];
    }
    if(json["userId"] is String) {
      userId = json["userId"];
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> _data = <String, dynamic>{};
    _data["nick"] = nick;
    _data["phone"] = phone;
    _data["amount"] = amount;
    _data["snatchTime"] = snatchTime;
    _data["userId"] = userId;
    return _data;
  }
}