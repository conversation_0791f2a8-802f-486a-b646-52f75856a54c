import 'dart:io';
import 'dart:typed_data';
import 'package:video_player/video_player.dart';
import 'package:fc_native_video_thumbnail/fc_native_video_thumbnail.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

class VideoThumbnailHelper {
  static final _plugin = FcNativeVideoThumbnail();

  /// 生成视频缩略图
  static Future<Uint8List?> generateThumbnail(String videoPath) async {
    try {
      // 获取临时目录
      final tempDir = await getTemporaryDirectory();
      final fileName = path.basenameWithoutExtension(videoPath);
      final thumbnailPath = path.join(tempDir.path, '${fileName}_thumbnail.jpg');

      // 生成缩略图
      final success = await _plugin.getVideoThumbnail(
        srcFile: videoPath,
        destFile: thumbnailPath,
        width: 300,
        height: 300,
        format: 'jpeg',
        quality: 90,
      );

      if (success) {
        // 读取生成的缩略图文件
        final thumbnailFile = File(thumbnailPath);
        if (await thumbnailFile.exists()) {
          final bytes = await thumbnailFile.readAsBytes();
          // 清理临时文件
          try {
            await thumbnailFile.delete();
          } catch (e) {
            // 忽略删除错误
          }
          return bytes;
        }
      }

      return null;
    } catch (e) {
      print('生成视频缩略图失败: $e');
      return null;
    }
  }
  
  /// 检查文件是否为视频
  static bool isVideoFile(String filePath) {
    final String fileName = filePath.toLowerCase();
    return fileName.endsWith('.mp4') || 
           fileName.endsWith('.mov') ||
           fileName.endsWith('.avi') || 
           fileName.endsWith('.mkv') ||
           fileName.endsWith('.3gp') || 
           fileName.endsWith('.webm') ||
           fileName.endsWith('.m4v') ||
           fileName.endsWith('.flv');
  }
  
  /// 获取视频时长
  static Future<Duration?> getVideoDuration(String videoPath) async {
    try {
      VideoPlayerController controller;
      
      if (videoPath.startsWith('http')) {
        controller = VideoPlayerController.networkUrl(Uri.parse(videoPath));
      } else {
        controller = VideoPlayerController.file(File(videoPath));
      }
      
      await controller.initialize();
      final duration = controller.value.duration;
      await controller.dispose();
      
      return duration;
    } catch (e) {
      print('获取视频时长失败: $e');
      return null;
    }
  }
  
  /// 格式化时长显示
  static String formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, "0");
    String twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
    String twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60));
    
    if (duration.inHours > 0) {
      return "${twoDigits(duration.inHours)}:$twoDigitMinutes:$twoDigitSeconds";
    } else {
      return "$twoDigitMinutes:$twoDigitSeconds";
    }
  }
}
