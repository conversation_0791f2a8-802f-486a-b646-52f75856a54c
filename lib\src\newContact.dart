// ignore_for_file: file_names

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

import 'package:tencent_cloud_chat_demo/src/provider/theme.dart';
import 'package:tencent_cloud_chat_uikit/theme/color.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/screen_utils.dart';
import '../../widgets/avatar.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_friend_application.dart'
    if (dart.library.html) 'package:tencent_cloud_chat_sdk/web/compatible_models/v2_tim_friend_application.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/view_models/tui_friendship_view_model.dart';
import 'package:tencent_cloud_chat_uikit/data_services/services_locatar.dart';
import 'package:tencent_cloud_chat_uikit/theme/tui_theme_view_model.dart';
import './widgets/empty.dart';

class NewContact extends StatelessWidget {
  const NewContact({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Provider.of<DefaultThemeData>(context).theme;

    late TUIFriendShipViewModel model =
        serviceLocator<TUIFriendShipViewModel>();

    _getShowName(V2TimFriendApplication item) {
      final nickName = item.nickname ?? "";
      final userID = item.userID;
      return nickName != "" ? nickName : userID;
    }

    Widget newContactWidget() {
      return TIMUIKitNewContact(
        emptyBuilder: (c) {
          return Center(
            child: Text(TIM_t("暂无新联系人")),
          );
        },
      );
    }

    Widget _itemBuilder(
        BuildContext context, V2TimFriendApplication applicationInfo) {
      final theme = Provider.of<TUIThemeViewModel>(context).theme;
      final showName = _getShowName(applicationInfo);
      final faceUrl = applicationInfo.faceUrl ?? '';
      return Container(
        padding: const EdgeInsets.only(top: 10, left: 16),
        color: Colors.white,
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.only(bottom: 12),
              margin: const EdgeInsets.only(right: 12),
              child: Avatar(avatarUrl: faceUrl, size: 46, radius: 23,),
            ),
            Expanded(
                child: Container(
              padding: const EdgeInsets.only(top: 10, bottom: 20),
              decoration: BoxDecoration(
                  border: Border(
                      bottom: BorderSide(
                          color: theme.weakDividerColor ??
                              CommonColor.weakDividerColor))),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    showName,
                    style: const TextStyle(color: Colors.black, fontSize: 14),
                  ),
                  Expanded(child: Container()),
                  Container(
                    margin: const EdgeInsets.only(right: 8),
                    child: InkWell(
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 14, vertical: 6),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            color: const Color(0xFF0072FC)),
                        child: Text(
                          TIM_t("同意"),
                          style: const TextStyle(
                            color: Colors.white,
                          ),
                        ),
                      ),
                      onTap: () async {
                        await model.acceptFriendApplication(
                          applicationInfo.userID,
                          applicationInfo.type,
                        );
                        model.loadData();
                      },
                    ),
                  ),
                  Container(
                      margin: const EdgeInsets.only(right: 8),
                      child: InkWell(
                        child: Container(
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8),
                              color: const Color(0xFFE9E9E9)),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 14, vertical: 6),
                          child: Text(
                            TIM_t("拒绝"),
                            style: const TextStyle(
                              color: Color(0xFF666666),
                            ),
                          ),
                        ),
                        onTap: () async {
                          await model.refuseFriendApplication(
                            applicationInfo.userID,
                            applicationInfo.type,
                          );
                          model.loadData();
                        },
                      ))
                ],
              ),
            ))
          ],
        ),
      );
    }

    Widget _newUsers() {
      return TIMUIKitNewContact(
        emptyBuilder: (c) {
          return const EmptyStateWidget(type: EmptyStateType.newFriend);
        },
        itemBuilder: _itemBuilder,
      );
    }

    Widget _body() {
      return Container(
        color: const Color(0xFFF9F9F9),
        child: Column(
          children: [
            const SizedBox(height: 16),
            _newUsers()
          ],
        ),
      );
    }

    return TUIKitScreenUtils.getDeviceWidget(
        context: context,
        desktopWidget: newContactWidget(),
        defaultWidget: Scaffold(
          appBar: AppBar(
            title: Text(
              TIM_t("新的朋友"),
              style: const TextStyle(color: Color(0xFF333333), fontSize: 17),
            ),
            backgroundColor: const Color(0xFFFFFFFF),
          ),
          body: _body(),
        ));
  }
}
