class LoginResponse {
  final int code;
  final String? level;
  final String? msg;
  final bool ok;
  final LoginData? data;
  final int? dataType;

  LoginResponse({
    required this.code,
    this.level,
    this.msg,
    required this.ok,
    this.data,
    this.dataType,
  });

  factory LoginResponse.fromJson(Map<String, dynamic> json) {
    return LoginResponse(
      code: json['code'] ?? 0,
      level: json['level'],
      msg: json['msg'],
      ok: json['ok'] ?? false,
      data: json['data'] != null ? LoginData.fromJson(json['data']) : null,
      dataType: json['dataType'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'level': level,
      'msg': msg,
      'ok': ok,
      'data': data?.toJson(),
      'dataType': dataType,
    };
  }
}

class LoginData {
  final String userId;
  final String? userType;
  final String loginName;
  final String? faceUrl;
  final String nick;
  final int? gender;
  final String? phone;
  final String? email;
  final bool? disabledFlag;
  final String? remark;
  final String? ip;
  final String? birthday;
  final String? location;
  final String? selfSignature;
  final int? allowType;
  final int? adminForbidType;
  final String? language;
  final String? userAgent;
  final String token;
  final String genUserSig;
  final String? userName;
  final int? appId;

  LoginData({
    required this.userId,
    this.userType,
    required this.loginName,
    this.faceUrl,
    required this.nick,
    this.gender,
    this.phone,
    this.email,
    this.disabledFlag,
    this.remark,
    this.ip,
    this.birthday,
    this.location,
    this.selfSignature,
    this.allowType,
    this.adminForbidType,
    this.language,
    this.userAgent,
    required this.token,
    required this.genUserSig,
    this.userName,
    this.appId,
  });

  factory LoginData.fromJson(Map<String, dynamic> json) {
    return LoginData(
      userId: json['userId']?.toString() ?? '',
      userType: json['userType'],
      loginName: json['loginName'] ?? '',
      faceUrl: json['faceUrl'],
      nick: json['nick'] ?? '',
      gender: json['gender'],
      phone: json['phone'],
      email: json['email'],
      disabledFlag: json['disabledFlag'],
      remark: json['remark'],
      ip: json['ip'],
      birthday: json['birthday'],
      location: json['location'],
      selfSignature: json['selfSignature'],
      allowType: json['allowType'],
      adminForbidType: json['adminForbidType'],
      language: json['language'],
      userAgent: json['userAgent'],
      token: json['token'] ?? '',
      genUserSig: json['genUserSig'] ?? '',
      userName: json['userName'],
      appId: json['appId'] != null ? int.tryParse(json['appId'].toString()) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'userType': userType,
      'loginName': loginName,
      'faceUrl': faceUrl,
      'nick': nick,
      'gender': gender,
      'phone': phone,
      'email': email,
      'disabledFlag': disabledFlag,
      'remark': remark,
      'ip': ip,
      'birthday': birthday,
      'location': location,
      'selfSignature': selfSignature,
      'allowType': allowType,
      'adminForbidType': adminForbidType,
      'language': language,
      'userAgent': userAgent,
      'token': token,
      'genUserSig': genUserSig,
      'userName': userName,
      'appId': appId,
    };
  }
}
