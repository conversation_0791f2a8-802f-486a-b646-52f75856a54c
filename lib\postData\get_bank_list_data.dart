
class BankListParams {
  int? pageNum;
  int? pageSize;
  bool? searchCount;
  List<SortItemList>? sortItemList;

  BankListParams({this.pageNum, this.pageSize, this.searchCount, this.sortItemList});

  BankListParams.fromJson(Map<String, dynamic> json) {
    if(json["pageNum"] is int) {
      pageNum = json["pageNum"];
    }
    if(json["pageSize"] is int) {
      pageSize = json["pageSize"];
    }
    if(json["searchCount"] is bool) {
      searchCount = json["searchCount"];
    }
    if(json["sortItemList"] is List) {
      sortItemList = json["sortItemList"] == null ? null : (json["sortItemList"] as List).map((e) => SortItemList.fromJson(e)).toList();
    }
  }

  static List<BankListParams> fromList(List<Map<String, dynamic>> list) {
    return list.map(BankListParams.fromJson).toList();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> _data = <String, dynamic>{};
    _data["pageNum"] = pageNum;
    _data["pageSize"] = pageSize;
    _data["searchCount"] = searchCount;
    if(sortItemList != null) {
      _data["sortItemList"] = sortItemList?.map((e) => e.toJson()).toList();
    }
    return _data;
  }
}

class SortItemList {
  bool? isAsc;
  String? column;

  SortItemList({this.isAsc, this.column});

  SortItemList.fromJson(Map<String, dynamic> json) {
    if(json["isAsc"] is bool) {
      isAsc = json["isAsc"];
    }
    if(json["column"] is String) {
      column = json["column"];
    }
  }

  static List<SortItemList> fromList(List<Map<String, dynamic>> list) {
    return list.map(SortItemList.fromJson).toList();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> _data = <String, dynamic>{};
    _data["isAsc"] = isAsc;
    _data["column"] = column;
    return _data;
  }
}