import 'package:flutter/cupertino.dart';
import 'package:tencent_calls_uikit/tencent_calls_uikit.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';
import 'package:tencent_cloud_chat_demo/src/pages/cross_platform/wide_screen/home_page.dart';
import 'package:tencent_cloud_chat_demo/src/pages/home_page.dart';
import 'package:tencent_cloud_chat_demo/src/pages/login.dart';
import 'package:tencent_cloud_chat_demo/src/provider/custom_sticker_package.dart';
import 'package:tencent_cloud_chat_demo/src/provider/theme.dart';
import 'package:tencent_cloud_chat_demo/utils/constant.dart';
import 'package:tencent_cloud_chat_demo/utils/theme.dart';
import 'package:tencent_cloud_chat_demo/utils/toast.dart';
import 'package:tencent_cloud_chat_demo/utils/user_info_local.dart';
import 'package:provider/provider.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/screen_utils.dart';

class InitStep {
  static setTheme(String themeTypeString, BuildContext context) {
    final CoreServicesImpl _coreInstance = TIMUIKitCore.getInstance();
    ThemeType themeType = DefTheme.themeTypeFromString(themeTypeString);
    Provider.of<DefaultThemeData>(context, listen: false).currentThemeType =
        themeType;
    Provider.of<DefaultThemeData>(context, listen: false).theme =
        DefTheme.defaultTheme[themeType]!;
    _coreInstance.setTheme(theme: DefTheme.defaultTheme[themeType]!);
  }

  static setCustomSticker(BuildContext context) async {
    // 添加自定义表情包
    List<CustomStickerPackage> customStickerPackageList = [];

    customStickerPackageList.addAll(Const.emojiList.map((customEmojiPackage) {
      return CustomStickerPackage(
          name: customEmojiPackage.name,
          baseUrl: "assets/custom_face_resource/${customEmojiPackage.name}",
          isEmoji: customEmojiPackage.isEmoji,
          stickerList: customEmojiPackage.list
              .asMap()
              .keys
              .map((idx) =>
                  CustomSticker(index: idx, name: customEmojiPackage.list[idx]))
              .toList(),
          menuItem: CustomSticker(
            index: 0,
            name: customEmojiPackage.icon,
          ));
    }).toList());

    Provider.of<CustomStickerPackageData>(context, listen: false)
        .customStickerPackageList = customStickerPackageList;
  }

  static void removeLocalSetting() async {
    // 清除本地存储的登录信息
    await UserInfoLocal.clearLoginInfo();
    // 清除红包封面类型
    await RedEnvelopeLocal.clearRedEnvelopeType();
  }

  static directToLogin(BuildContext context,
      [Function? initIMSDKAndAddIMListeners]) {
    Navigator.of(context).pushAndRemoveUntil(
      PageRouteBuilder(
        transitionDuration: const Duration(milliseconds: 500),
        pageBuilder: (BuildContext context, Animation<double> animation,
            Animation secondaryAnimation) {
          return FadeTransition(
            opacity: animation,
            child: LoginPage(initIMSDK: initIMSDKAndAddIMListeners),
          );
        },
      ),
      (route) => false, // 清除所有路由历史
    );
  }

  static directToHomePage(BuildContext context) {
    final isWideScreen =
        TUIKitScreenUtils.getFormFactor(context) == DeviceType.Desktop;
    Navigator.of(context).pushAndRemoveUntil(
      PageRouteBuilder(
          transitionDuration: const Duration(milliseconds: 500),
          pageBuilder: (BuildContext context, Animation<double> animation,
              Animation secondaryAnimation) {
            return FadeTransition(
              opacity: animation,
              child:
                  isWideScreen ? const HomePageWideScreen() : const HomePage(),
            );
          },
          settings: const RouteSettings(name: '/homePage')),
      (route) => false, // 清除所有路由历史
    );
  }
  static userTuiLogin(int sdkAppId, String userID, String userSig) async {
    // 音视频登录
    await TUICallKit.instance.login(sdkAppId, userID, userSig);
    final CoreServicesImpl coreInstance = TIMUIKitCore.getInstance();
    // 即时通讯登录
    var data = await coreInstance.login(
      userID: userID,
      userSig: userSig,
    );

    if (data.code != 0) {
      debugPrint('即时通讯登录失败: ${data.desc}');
      final option1 = data.desc;
      ToastUtils.toast(TIM_t_para("登录失败{{option1}}", "登录失败$option1")(option1: option1));
      await UserInfoLocal.clearLoginInfo();
      await RedEnvelopeLocal.clearRedEnvelopeType();
      return;
    }else{
      debugPrint('即时通讯登录成功: ${data.desc}');

    }
  }
  static  checkLogin(BuildContext context, initIMSDKAndAddIMListeners) async {
    // 初始化IM SDK
    await initIMSDKAndAddIMListeners();
    debugPrint('检查登录');
    // 检查本地是否有登录信息
    final userInfo = await UserInfoLocal.getUserInfo();
    final CoreServicesImpl coreInstance = TIMUIKitCore.getInstance();
    if (userInfo != null) {
      debugPrint('有登录信息');
      // 如果有登录信息，直接登录
      final appId = userInfo.appId ?? 0;
      final userId = userInfo.userId;
      final userSig = userInfo.genUserSig;

      if (appId != 0 && userId.isNotEmpty && userSig.isNotEmpty) {
        debugPrint('登录信息完整');
        await TIMUIKitCore.getSDKInstance().login(userID: userId, userSig: userSig);

        var data = await coreInstance.login(
          userID: userId,
          userSig: userSig,
        );
        if (data.code == 0) {
          debugPrint('登录成功: ${data.desc}');
         await InitStep.userTuiLogin(appId, userId, userSig);
          // directToHomePage(context);
          setCustomSticker(context);
          return;
        } else {
          debugPrint('登录失败: ${data.desc}');
          // 登录失败时清除本地登录信息
          await UserInfoLocal.clearLoginInfo();
        }

        // 调用登录方法
        // final res = await TIMUIKitCore.getSDKInstance()
        //     .login(userID: userId, userSig: userSig);

        // if (res.code == 0) {
        //   // 登录成功，跳转到主页
        //   directToHomePage(context);
        //   setCustomSticker(context);
        //   return;
        // }
        // directToHomePage(context);
        // setCustomSticker(context);
        // return;
      }
    }
    debugPrint('跳转登录页');
    // 如果没有登录信息或登录失败，跳转到登录页
    Future.delayed(const Duration(seconds: 1), () {
      directToLogin(context);
      setCustomSticker(context);
    });
  }
}
