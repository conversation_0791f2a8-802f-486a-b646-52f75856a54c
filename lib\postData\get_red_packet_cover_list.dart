// {
//   "pageNum": 1,
//   "pageSize": 10,
//   "searchCount": true,
//   "sortItemList": [
//     {
//       "isAsc": true,
//       "column": ""
//     }
//   ],
//   "language": ""
// }

// 定义语言枚举
enum RedPacketLanguage {
  zh,
  en,
}

// 语言枚举扩展方法
extension RedPacketLanguageExtension on RedPacketLanguage {
  String get value {
    switch (this) {
      case RedPacketLanguage.zh:
        return 'zh';
      case RedPacketLanguage.en:
        return 'en';
    }
  }
}

class GetRedPacketCoverListData {
  final int pageNum;
  final int pageSize;
  final bool searchCount;
  final List<Map<String, dynamic>> sortItemList;
  final RedPacketLanguage language;

  GetRedPacketCoverListData({
    this.pageNum = 1,
    this.pageSize = 10,
    this.searchCount = true,
    this.sortItemList = const [],
    this.language = RedPacketLanguage.zh,
  });

  Map<String, dynamic> toJson() {
    return {
      'pageNum': pageNum,
      'pageSize': pageSize,
      'searchCount': searchCount,
      'sortItemList': sortItemList,
      'language': language.value,
    };
  }
}