import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_conversation.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_group_info.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

import 'package:tencent_cloud_chat_demo/src/chat.dart';

import 'package:tencent_cloud_chat_demo/src/provider/theme.dart';
import 'package:tencent_cloud_chat_uikit/theme/color.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/screen_utils.dart';
import './widgets/empty.dart';

class GroupList extends StatelessWidget {
  final sdkInstance = TIMUIKitCore.getSDKInstance();
  final void Function(V2TimGroupInfo groupInfo, V2TimConversation conversation)?
      onTapItem;

  GroupList({Key? key, this.onTapItem}) : super(key: key);

 _jumpToChatPage(BuildContext context, String groupId) async {
    final res = await sdkInstance
        .getConversationManager()
        .getConversation(conversationID: "group_$groupId");
    if (res.code == 0) {
      final conversation = res.data;
      if (conversation != null) {
        Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => Chat(selectedConversation: conversation),
            ));
      }
    }
  }

  Widget _itemBuilder(BuildContext context, V2TimGroupInfo groupInfo) {
    final showName = groupInfo.groupName ?? groupInfo.groupID;
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.only(top: 0, left: 16, bottom: 0),
      child:  InkWell(
        onTap: () {
             final groupID = groupInfo.groupID;
          _jumpToChatPage(context, groupID);
        },
        child:       Row(
        children: [
          Container(
            height: 46,
            width: 46,
            margin: const EdgeInsets.only(right: 12),
            child: Image.asset('assets/group_cover.png'),
          ),
          Expanded(
            child: Container(
              height: 66,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    showName,
                    style: TextStyle(
                        color: hexToColor("111111"),
                        fontSize: 14),
                  ),
                  Expanded(child: Container()),
                  const TIMUIKitUnreadCount(),
                  Container(
                    color: hexToColor("000000"),
                    margin: const EdgeInsets.only(right: 16),
                  )
                ],
              ),
            ),
          )
        ],
      ),
      )

    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Provider.of<DefaultThemeData>(context).theme;

    Widget groupList() {
      return TIMUIKitGroup(
        
        onTapItem: (groupInfo, conversation) {
           final groupID = groupInfo.groupID;
          _jumpToChatPage(context, groupID);
        },
        emptyBuilder: (_) {
          return const EmptyStateWidget(type: EmptyStateType.group);
        },
        groupCollector: (groupInfo) {
          final groupID = groupInfo?.groupID ?? "";
          return !groupID.contains("im_discuss_");
        },
        itemBuilder: _itemBuilder,
      );
    }

    return TUIKitScreenUtils.getDeviceWidget(
        context: context,
        desktopWidget: groupList(),
        defaultWidget: Scaffold(
          appBar: AppBar(
            title: Text(
              TIM_t("群聊"),
              style: const TextStyle(color: Color(0xFF333333), fontSize: 16),
            ),
            backgroundColor: const Color(0xFFFFFFFF),
            surfaceTintColor: const Color(0xFFFFFFFF),
          ),
          body: Container(
            color: const Color(0xFFF9F9F9),
            child: groupList(),
          ),
        ));
  }
}
