class BankListData {
  int? code;
  String? level;
  String? msg;
  bool? ok;
  Data? data;
  int? dataType;

  BankListData({this.code, this.level, this.msg, this.ok, this.data, this.dataType});

  BankListData.fromJson(Map<String, dynamic> json) {
    if(json["code"] is int) {
      code = json["code"];
    }
    if(json["level"] is String) {
      level = json["level"];
    }
    if(json["msg"] is String) {
      msg = json["msg"];
    }
    if(json["ok"] is bool) {
      ok = json["ok"];
    }
    if(json["data"] is Map) {
      data = json["data"] == null ? null : Data.fromJson(json["data"]);
    }
    if(json["dataType"] is int) {
      dataType = json["dataType"];
    }
  }

  static List<BankListData> fromList(List<Map<String, dynamic>> list) {
    return list.map(BankListData.fromJson).toList();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> _data = <String, dynamic>{};
    _data["code"] = code;
    _data["level"] = level;
    _data["msg"] = msg;
    _data["ok"] = ok;
    if(data != null) {
      _data["data"] = data?.toJson();
    }
    _data["dataType"] = dataType;
    return _data;
  }
}

class Data {
  int? pageNum;
  int? pageSize;
  int? total;
  int? pages;
  List<BankCard>? list;
  bool? emptyFlag;

  Data({this.pageNum, this.pageSize, this.total, this.pages, this.list, this.emptyFlag});

  Data.fromJson(Map<String, dynamic> json) {
    if(json["pageNum"] is int) {
      pageNum = json["pageNum"];
    }
    if(json["pageSize"] is int) {
      pageSize = json["pageSize"];
    }
    if(json["total"] is int) {
      total = json["total"];
    }
    if(json["pages"] is int) {
      pages = json["pages"];
    }
    if(json["list"] is List) {
      list = json["list"] == null ? null : (json["list"] as List).map((e) => BankCard.fromJson(e)).toList();
    }
    if(json["emptyFlag"] is bool) {
      emptyFlag = json["emptyFlag"];
    }
  }

  static List<Data> fromList(List<Map<String, dynamic>> list) {
    return list.map(Data.fromJson).toList();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> _data = <String, dynamic>{};
    _data["pageNum"] = pageNum;
    _data["pageSize"] = pageSize;
    _data["total"] = total;
    _data["pages"] = pages;
    if(list != null) {
      _data["list"] = list?.map((e) => e.toJson()).toList();
    }
    _data["emptyFlag"] = emptyFlag;
    return _data;
  }
}

class BankCard {
  int? id;
  int? userId;
  String? bankName;
  String? cardNo;
  String? cardHolder;
  int? isDefault;
  int? status;
  String? auditRemark;
  int? disabledFlag;

  BankCard({this.id, this.userId, this.cardNo, this.cardHolder, this.isDefault, this.status, this.auditRemark, this.disabledFlag});

  BankCard.fromJson(Map<String, dynamic> json) {

    if (json["id"] != null) {
      if (json["id"] is int) {
        id = json["id"];
      } else if (json["id"] is double) {
        id = json["id"].toInt();
      } else if (json["id"] is String) {
        id = int.tryParse(json["id"]);
      }
    }

    if(json["userId"] is int) {
      userId = json["userId"];
    }
    if(json["bankName"] is String) {
      bankName = json["bankName"];
    }
    if(json["cardNo"] is String) {
      cardNo = json["cardNo"];
    }
    if(json["cardHolder"] is String) {
      cardHolder = json["cardHolder"];
    }
    if(json["isDefault"] is int) {
      isDefault = json["isDefault"];
    }
    if(json["status"] is int) {
      status = json["status"];
    }
    if(json["auditRemark"] is String) {
      auditRemark = json["auditRemark"];
    }
    if(json["disabledFlag"] is int) {
      disabledFlag = json["disabledFlag"];
    }
  }

  static List<BankCard> fromList(List<Map<String, dynamic>> list) {
    return list.map(BankCard.fromJson).toList();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> _data = <String, dynamic>{};
    _data["id"] = id;
    _data["userId"] = userId;
    _data["cardNo"] = cardNo;
    _data["cardHolder"] = cardHolder;
    _data["isDefault"] = isDefault;
    _data["status"] = status;
    _data["auditRemark"] = auditRemark;
    _data["disabledFlag"] = disabledFlag;
    return _data;
  }
}