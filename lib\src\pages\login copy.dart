// ignore_for_file: unused_import, avoid_print

import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tencent_calls_uikit/tencent_calls_uikit.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';
import 'package:tencent_cloud_chat_demo/config.dart';
import 'package:tencent_cloud_chat_demo/src/pages/home_page.dart';
import 'package:tencent_cloud_chat_demo/src/pages/privacy/privacy_webview.dart';
import 'package:tencent_cloud_chat_demo/src/provider/theme.dart';
import 'package:tencent_cloud_chat_demo/src/routes.dart';
import 'package:tencent_cloud_chat_demo/utils/GenerateTestUserSig.dart';
import 'package:tencent_cloud_chat_demo/utils/commonUtils.dart';
import 'package:tencent_cloud_chat_demo/utils/toast.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_uikit/theme/color.dart';

class LoginPage extends StatelessWidget {
  final Function? initIMSDK;
  const LoginPage({Key? key, this.initIMSDK}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () async {
          return false;
        },
        child: Scaffold(
          body: AppLayout(initIMSDK: initIMSDK),
          resizeToAvoidBottomInset: false,
        ));
  }
}

class AppLayout extends StatelessWidget {
  final Function? initIMSDK;
  const AppLayout({Key? key, this.initIMSDK}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        SystemChannels.textInput.invokeMethod('TextInput.hide');
      },
      child: Stack(
        children: [
          const AppLogo(),
          LoginForm(
            initIMSDK: initIMSDK,
          ),
        ],
      ),
    );
  }
}

class AppLogo extends StatelessWidget {
  const AppLogo({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final double height = MediaQuery.of(context).size.height;
    final theme = Provider.of<DefaultThemeData>(context).theme;
    return Stack(
      alignment: Alignment.topCenter,
      children: [
        Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(begin: Alignment.topCenter, end: Alignment.bottomCenter, colors: [theme.lightPrimaryColor ?? CommonColor.lightPrimaryColor, theme.primaryColor ?? CommonColor.primaryColor]),
            ),
            child: Image.asset("assets/hero_image.png", color: Colors.transparent, width: MediaQuery.of(context).size.width, height: 500)),
        Positioned(
          child: Container(
            padding: EdgeInsets.only(top: height / 30, left: 24),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                SizedBox(
                  height: CommonUtils.adaptWidth(380),
                  width: CommonUtils.adaptWidth(140),
                  child: const Image(
                    image: AssetImage("assets/logo_transparent.png"),
                  ),
                ),
                Expanded(
                    child: Container(
                  margin: const EdgeInsets.only(right: 5),
                  height: CommonUtils.adaptHeight(180),
                  padding: const EdgeInsets.only(top: 10, left: 12, right: 15),
                  child: Column(
                    children: <Widget>[
                      Text(
                        TIM_t("腾讯云即时通信IM"),
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: CommonUtils.adaptFontSize(58),
                        ),
                      ),
                      Text(
                        TIM_t("欢迎使用本 APP 体验Phichat 产品服务"),
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: CommonUtils.adaptFontSize(26),
                        ),
                      ),
                    ],
                    crossAxisAlignment: CrossAxisAlignment.start,
                  ),
                )),
              ],
            ),
          ),
        )
      ],
    );
  }
}

class LoginForm extends StatefulWidget {
  final Function? initIMSDK;
  const LoginForm({Key? key, required this.initIMSDK}) : super(key: key);

  @override
  _LoginFormState createState() => _LoginFormState();
}

class _LoginFormState extends State<LoginForm> {
  final CoreServicesImpl coreInstance = TIMUIKitCore.getInstance();

  String userID = '';

  @override
  initState() {
    super.initState();
    checkFirstEnter();
    if (widget.initIMSDK != null) {
      widget.initIMSDK!();
    }
  }

  TextSpan webViewLink(String title, String url) {
    return TextSpan(
      text: TIM_t(title),
      style: const TextStyle(
        color: Color.fromRGBO(0, 110, 253, 1),
      ),
      recognizer: TapGestureRecognizer()
        ..onTap = () {
          Navigator.push(context, MaterialPageRoute(builder: (context) => PrivacyDocument(title: title, url: url)));
        },
    );
  }

  void checkFirstEnter() async {
    Future<SharedPreferences> _prefs = SharedPreferences.getInstance();
    SharedPreferences prefs = await _prefs;
    String? firstTime = prefs.getString("firstTime");
    if (firstTime != null && firstTime == "true") {
      return;
    }
    showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return CupertinoAlertDialog(
          content: Text.rich(
            TextSpan(style: const TextStyle(fontSize: 14, color: Colors.black, height: 2.0), children: [
              TextSpan(
                text: TIM_t("欢迎使用腾讯云即时通信 IM，为保护您的个人信息安全，我们更新了《隐私政策》，主要完善了收集用户信息的具体内容和目的、增加了第三方SDK使用等方面的内容。"),
              ),
              const TextSpan(
                text: "\n",
              ),
              TextSpan(
                text: TIM_t("请您点击"),
              ),
              webViewLink("《用户协议》", 'https://web.sdk.qcloud.com/document/Tencent-IM-User-Agreement.html'),
              TextSpan(
                text: TIM_t(", "),
              ),
              webViewLink("《隐私协议》", 'https://privacy.qq.com/document/preview/1cfe904fb7004b8ab1193a55857f7272'),
              TextSpan(
                text: TIM_t(", "),
              ),
              webViewLink("《信息收集清单》", 'https://privacy.qq.com/document/preview/45ba982a1ce6493597a00f8c86b52a1e'),
              TextSpan(
                text: TIM_t("和"),
              ),
              webViewLink("《信息共享清单》", 'https://privacy.qq.com/document/preview/dea84ac4bb88454794928b77126e9246'),
              TextSpan(text: TIM_t("并仔细阅读，如您同意以上内容，请点击“同意并继续”，开始使用我们的产品与服务！")),
            ]),
            overflow: TextOverflow.clip,
          ),
          actions: [
            CupertinoButton(
                child: Text(TIM_t("同意并继续"), style: const TextStyle(color: Colors.blue, fontSize: 16)),
              onPressed: () {
                prefs.setString("firstTime", "true");
                Navigator.of(context).pop(true);
              },
            ),
            CupertinoButton(
              child: Text(TIM_t("不同意并退出"), style: const TextStyle(color: Colors.grey, fontSize: 16)),
              onPressed: () {
                exit(0);
              },
            ),
          ],
        );
      },
    );
  }

  directToHomePage() {
    Routes().directToHomePage();
  }

  userLogin() async {
    if (userID.trim() == '') {
      ToastUtils.toast(TIM_t("请输入用户名"));
      return;
    }

    String key = IMDemoConfig.key;
    int sdkAppId = IMDemoConfig.sdkAppID;
    if (key == "" || sdkAppId == 0) {
      ToastUtils.toast(TIM_t("请在 config.dart 中输入 sdkAppID 和 key"));
      return;
    }
    GenerateTestUserSig generateTestUserSig = GenerateTestUserSig(
      sdkappid: sdkAppId,
      key: key,
    );

    String userSig = generateTestUserSig.genSig(identifier: userID, expire: 99999);
    // 音视频登录
    await TUICallKit.instance.login(sdkAppId, userID, userSig);
    // 即时通讯登录
    var data = await coreInstance.login(
      userID: userID,
      userSig: userSig,
    );
    if (data.code != 0) {
      final option1 = data.desc;
      ToastUtils.toast(TIM_t_para("登录失败{{option1}}", "登录失败$option1")(option1: option1));
      return;
    }

    directToHomePage();
  }
  jumpToRegister() {
    Navigator.pushNamed(context, '/register');
  }
  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(
      context,
      designSize: const Size(750, 1624),
      minTextAdapt: true,
    );
    return Stack(
      children: [
        Positioned(
            bottom: CommonUtils.adaptHeight(200),
            child: Container(
              padding: const EdgeInsets.fromLTRB(16, 40, 16, 0),
              decoration: const BoxDecoration(
                //背景
                color: Colors.white,

                borderRadius: BorderRadius.only(topLeft: Radius.circular(30.0), topRight: Radius.circular(30.0), bottomLeft: Radius.circular(30.0), bottomRight: Radius.circular(30.0)),
                //设置四周边框
              ),
              // color: Colors.white,
              height: MediaQuery.of(context).size.height - CommonUtils.adaptHeight(600),

              width: MediaQuery.of(context).size.width,
              child: Form(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: EdgeInsets.only(top: CommonUtils.adaptFontSize(34)),
                      child: Text(
                        TIM_t("用户名"),
                        style: TextStyle(
                          fontWeight: FontWeight.w700,
                          fontSize: CommonUtils.adaptFontSize(34),
                        ),
                      ),
                    ),
                    TextField(
                      autofocus: false,
                      decoration: InputDecoration(
                        contentPadding: EdgeInsets.only(left: CommonUtils.adaptWidth(14)),
                        hintText: TIM_t("请输入用户名"),
                        hintStyle: TextStyle(fontSize: CommonUtils.adaptFontSize(32)),
                        //
                      ),
                      keyboardType: TextInputType.text,
                      onChanged: (v) {
                        setState(() {
                          userID = v;
                        });
                      },
                    ),
                    Container(
                      margin: EdgeInsets.only(
                        top: CommonUtils.adaptHeight(46),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: ElevatedButton(
                              child: Text(TIM_t("登录")),
                              onPressed: userLogin,
                            ),
                            
                          )
                        ],
                      ),
                    ),
                    Container(
                      margin: EdgeInsets.only(
                        top: CommonUtils.adaptHeight(46),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: ElevatedButton(
                              child: Text(TIM_t("注册")),
                              onPressed: jumpToRegister,
                            ),
                            
                          )
                        ],
                      ),
                    ),
                    Expanded(
                      child: Container(),
                    )
                  ],
                ),
              ),
            ))
      ],
    );
  }
}
