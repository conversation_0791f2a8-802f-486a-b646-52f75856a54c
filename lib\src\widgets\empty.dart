import 'package:flutter/material.dart';
import 'package:tencent_chat_i18n_tool/language_json/strings.g.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';

/// 空状态类型枚举
enum EmptyStateType {
  /// 暂无会话
  conversation,
  /// 暂无群聊
  group,
  /// 暂无新朋友
  newFriend,
  /// 暂无黑名单
  blacklist,
  // 暂无红包记录
  redEnvelopeRecord,
}

/// 空状态组件
/// 用于显示不同类型的空状态页面
class EmptyStateWidget extends StatelessWidget {
  /// 空状态类型
  final EmptyStateType type;
  
  /// 顶部内边距，默认为100
  final double topPadding;
  
  /// 图片宽度，默认为112
  final double imageWidth;
  
  /// 图片高度，默认为112
  final double imageHeight;
  
  /// 图片与文字的间距，默认为8
  final double spacing;
  
  /// 自定义文本，如果提供则覆盖默认文本
  final String? customText;
  
  /// 文本样式
  final TextStyle textStyle;

  const EmptyStateWidget({
    Key? key,
    required this.type,
    this.topPadding = 100,
    this.imageWidth = 112,
    this.imageHeight = 112,
    this.spacing = 8,
    this.customText,
    this.textStyle = const TextStyle(
      color: Color(0xFFBBBBBB),
      fontSize: 12,
      fontWeight: FontWeight.w400
    ),
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 根据类型获取对应的图片路径和文本
    String imagePath;
    String text;
    
    switch (type) {
      case EmptyStateType.conversation:
        imagePath = "assets/empty/empty_conversation.png";
        text = (TIM_t("暂无会话"));
        break;
      case EmptyStateType.group:
        imagePath = "assets/empty/empty_group.png";
        text = (TIM_t("暂无群聊"));
        break;
      case EmptyStateType.newFriend:
        imagePath = "assets/empty/empty_new.png";
        text = (TIM_t("暂无新朋友"));
        break;
      case EmptyStateType.blacklist:
        imagePath = "assets/empty/empty_black.png";
        text = (TIM_t("暂无黑名单"));
        break;
      case EmptyStateType.redEnvelopeRecord:
        imagePath = "assets/empty/empty_red.png";
        text = (TIM_t("暂无红包记录"));
        break;
    }
    
    // 如果提供了自定义文本，则使用自定义文本
    final displayText = customText ?? text;
    
    return Container(
      padding: EdgeInsets.only(top: topPadding),
      child: Center(
        child: Column(
          children: [
            Image.asset(
              imagePath,
              width: imageWidth,
              height: imageHeight,
            ),
            SizedBox(height: spacing),
            Text(
              displayText,
              style: textStyle,
            ),
          ],
        ),
      ),
    );
  }
}