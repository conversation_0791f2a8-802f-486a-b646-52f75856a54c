<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/BPMDetect.h</key>
		<data>
		Y8ZlWrIfJX8rFu9TZtdCyWhGQsQ=
		</data>
		<key>Headers/FIFOSampleBuffer.h</key>
		<data>
		1n1MbPfnpt1eDsMQnTmcEHELNH4=
		</data>
		<key>Headers/FIFOSamplePipe.h</key>
		<data>
		Ri8aov9gF3VltOQS3BJqnkiDY/U=
		</data>
		<key>Headers/STTypes.h</key>
		<data>
		hZyePvhM4LiBd64cbLb/9Z1k/Nw=
		</data>
		<key>Headers/SoundTouch.h</key>
		<data>
		tXM/yrLck8k0XmR4wXsQ0wGh2TM=
		</data>
		<key>Headers/TXSoundTouch.h</key>
		<data>
		KlOJXIl1torOHEJLskFpNxXFARg=
		</data>
		<key>Headers/soundtouch_config.h</key>
		<data>
		u/SRoueMf2z/pUiQIIHIwNLOmJI=
		</data>
		<key>Info.plist</key>
		<data>
		sdeDzer72WRcISTPA8wa9OIMiC8=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		swiQ6wOQI8GPPCUUnC+qMiEuDYk=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/BPMDetect.h</key>
		<dict>
			<key>hash</key>
			<data>
			Y8ZlWrIfJX8rFu9TZtdCyWhGQsQ=
			</data>
			<key>hash2</key>
			<data>
			IlC5Kcrqt2gvypyZT9Q5emYQsqY4AQivwSlLE/na9NU=
			</data>
		</dict>
		<key>Headers/FIFOSampleBuffer.h</key>
		<dict>
			<key>hash</key>
			<data>
			1n1MbPfnpt1eDsMQnTmcEHELNH4=
			</data>
			<key>hash2</key>
			<data>
			x7kcr/SdqKk+U0xcQ/Zdf6SDQkLJdAUdgH2rkLR9skg=
			</data>
		</dict>
		<key>Headers/FIFOSamplePipe.h</key>
		<dict>
			<key>hash</key>
			<data>
			Ri8aov9gF3VltOQS3BJqnkiDY/U=
			</data>
			<key>hash2</key>
			<data>
			fAgNtdbpjmeA1tNa7jNAF+DiH+O5wc1jm/BQdUN9X9o=
			</data>
		</dict>
		<key>Headers/STTypes.h</key>
		<dict>
			<key>hash</key>
			<data>
			hZyePvhM4LiBd64cbLb/9Z1k/Nw=
			</data>
			<key>hash2</key>
			<data>
			m0HRMn4OBstHaI26oib25pCagKDdX82Sy10NGtq5amU=
			</data>
		</dict>
		<key>Headers/SoundTouch.h</key>
		<dict>
			<key>hash</key>
			<data>
			tXM/yrLck8k0XmR4wXsQ0wGh2TM=
			</data>
			<key>hash2</key>
			<data>
			ti8wNitWe/qUgtxY1jjjgTnOVZ7g7LFKeiUdNrCrjdg=
			</data>
		</dict>
		<key>Headers/TXSoundTouch.h</key>
		<dict>
			<key>hash</key>
			<data>
			KlOJXIl1torOHEJLskFpNxXFARg=
			</data>
			<key>hash2</key>
			<data>
			MsSs4N75PCwCQw+mo5pqITrh7r5JE0I4HyEBpj1IGPw=
			</data>
		</dict>
		<key>Headers/soundtouch_config.h</key>
		<dict>
			<key>hash</key>
			<data>
			u/SRoueMf2z/pUiQIIHIwNLOmJI=
			</data>
			<key>hash2</key>
			<data>
			h9GFPBZQwSrxA4Ye3VdZ60R5i4i+l3wxaqRKVL6g2BA=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			swiQ6wOQI8GPPCUUnC+qMiEuDYk=
			</data>
			<key>hash2</key>
			<data>
			m9duYxe73qIPAbsyr272zwThI4zTg1MZvqJrq3utYD0=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
