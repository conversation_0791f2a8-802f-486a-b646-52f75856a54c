import 'package:flutter/material.dart';
import 'package:tencent_cloud_chat_demo/src/widgets/bottom_sheet_picker.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../../../../../apis/tuils_api.dart';
import '../permission_type.dart';
import '../../../../../utils/toast.dart';
import 'package:dio/dio.dart';
import '../../../../../models/upload_response.dart';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';

class ChooseCoverPage extends StatefulWidget {
  const ChooseCoverPage({super.key});
  @override
  State<StatefulWidget> createState() => _ChooseCoverPageState();
}

class _ChooseCoverPageState extends State<ChooseCoverPage> {
  TextEditingController _controller = TextEditingController();
  bool _isPublishing = false;
  String permissionType = 'public';
  List<String> visibleUsers = [];
  List<Map<String, String>> visibleUsersInfo = [];
  List<String> invisibleUsers = [];
  List<Map<String, String>> invisibleUsersInfo = [];
  final ImagePicker _picker = ImagePicker();
  List<XFile> _imageFiles = [];
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  Future<void> _takePhoto() async {
    try {
      final XFile? photo = await _picker.pickImage(source: ImageSource.camera, imageQuality: 10);
      if (photo != null) {
        setState(() {
          _imageFiles.add(photo);
        });
        List<Map<String, Object>> imageUrls = await _uploadImages();
        if (imageUrls.isEmpty) {
          ToastUtils.toast(TIM_t("上传图片失败, 请重试"));
          return;
        }
        var data = {
          "momentCoverUrl": imageUrls[0]['mediaUrl'],
        };
        var res = await Api.instance.updateCover(data);
        if (res.code == 0 && res.ok!) {
          ToastUtils.toast(TIM_t("修改封面成功"));
          Navigator.pop(context);
        }
      }
    } catch (e) {
      print("Camera error: $e");
    }
  }

  Future<void> _pickImage() async {
    try {
      final List<XFile>? images = await _picker.pickMultiImage();
      if (images != null && images.isNotEmpty) {
        setState(() {
          _imageFiles.addAll(images);
        });
        List<Map<String, Object>> imageUrls = await _uploadImages();
        if (imageUrls.isEmpty) {
          ToastUtils.toast(TIM_t("上传图片失败, 请重试"));
          return;
        }
        var data = {
          "momentCoverUrl": imageUrls[0]['mediaUrl'],
        };
        var res = await Api.instance.updateCover(data);
        if (res.code == 0 && res.ok!) {
          ToastUtils.toast(TIM_t("修改封面成功"));
          Navigator.pop(context);
        }
      }
    } catch (e) {
      ToastUtils.toast(TIM_t("选择图片出错"));
    }
  }


  Future<List<Map<String, Object>>> _uploadImages() async {
    List<Map<String, Object>> uploadedUrls = [];

    try {
      for (XFile image in _imageFiles) {
        FormData formData = FormData.fromMap({
          "file": await MultipartFile.fromFile(
            image.path,
            filename: image.name,
          ),
          "folder": 1
        });
        var result = await Api.instance.upload(formData);
        if (result is UploadResponse &&
            result.code == 0 &&
            result.data != null) {
          String? url = result.data?.fileUrl;
          if (url != null && url.isNotEmpty) {
            var obj = {
              "mediaType": "image",
              "mediaUrl": url,
              "sortOrder": 0,
            };
            uploadedUrls.add(obj);
          }
        }
      }

      return uploadedUrls;
    } catch (e) {
      ToastUtils.toast(TIM_t("上传图片失败"));
      return [];
    }
  }


  List<String> _getUserIds(List<String> userIds) {
    return userIds;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(TIM_t('更换朋友圈封面'), style: const TextStyle(fontSize: 16)),
          leading: Padding(
            padding: const EdgeInsets.only(left: 1),
            child: GestureDetector(
              onTap: () {
                Navigator.pop(context);
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                margin: const EdgeInsets.symmetric(vertical: 8),
                child: Image.asset('assets/moments/icon_back_b.png', width: 16, height: 16,)
              ),
            ),

          ),
           backgroundColor: Colors.white,
        ),
        body: Container(
          color: const Color(0XFFF6F6F6),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                color: Colors.white,
                child: InkWell(
                    onTap: () => {
                      _pickImage()
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        border: Border(
                          bottom: BorderSide(
                            color: Color(0XFFE9E9E9),
                            width: 1,
                          ),
                        ),
                      ),
                      child: Row(
                        children: [
                          Text(TIM_t('从手机相册选择'),
                              style: const TextStyle(
                                  fontSize: 14, color: Color(0XFF333333))),
                          const Spacer(),
                          Image.asset(
                            'assets/moments/icon_right.png',
                            width: 16,
                            height: 16,
                          )
                        ],
                      ),
                    )),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                color: Colors.white,
                child: InkWell(
                    onTap: () => {
                      _takePhoto()
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        border: Border(
                          bottom: BorderSide(
                            color: Color(0XFFE9E9E9),
                            width: 1,
                          ),
                        ),
                      ),
                      child: Row(
                        children: [
                          Text(TIM_t('拍一个'),
                              style: const TextStyle(
                                  fontSize: 14, color: Color(0XFF333333))),
                          const Spacer(),
                          Image.asset(
                            'assets/moments/icon_right.png',
                            width: 16,
                            height: 16,
                          )
                        ],
                      ),
                    )),
              )
            ],
          ),
        ));
  }
}
