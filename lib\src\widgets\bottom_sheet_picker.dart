import 'package:flutter/material.dart';

class BottomSheetOption {
  final String title;
  final Function onTap;
  final bool isSelected;

  BottomSheetOption({
    required this.title,
    required this.onTap,
    this.isSelected = false,
  });
}

class BottomSheetPicker {
  static Future<T?> show<T>({
    required BuildContext context,
    required String title,
    required List<BottomSheetOption> options,
    String cancelText = "取消",
  }) {
    return showModalBottomSheet<T>(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return Container(
          decoration: const BoxDecoration(
            color: Colors.transparent,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 选项组
              Container(
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(10),
                    topRight: Radius.circular(10),
                  ),
                ),
                child: Column(
                  children: [
                    if(title.isNotEmpty)
                    // 标题
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      child: Text(
                        title,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Color(0xFF333333),
                        ),
                      ),
                    ),
                    if(title.isNotEmpty)
                    // 分割线
                    Container(
                      height: 0.5,
                      color: const Color(0xFFEEEEEE),
                    ),
                    // 选项列表
                    ...options
                        .map((option) => Column(
                              children: [
                                InkWell(
                                  onTap: () {
                                    Navigator.pop(context);
                                    option.onTap();
                                  },
                                  child: Container(
                                    width: double.infinity,
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 16),
                                    child: Center(
                                      child: Text(
                                        option.title,
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: option.isSelected
                                              ? const Color(0xFF0072FC)
                                              : const Color(0xFF333333),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                                if (options.indexOf(option) <
                                    options.length - 1)
                                  Container(
                                    height: 0.5,
                                    color: const Color(0xFFEEEEEE),
                                  ),
                              ],
                            ))
                        .toList(),
                  ],
                ),
              ),
              // 取消按钮
              Container(
                  decoration: const BoxDecoration(color: Colors.white),
                  width: double.infinity,
                  child: Column(
                    children: [
                      SizedBox(height: 10, child: Container(color: const Color(0xFFF4F7F8))),
                      InkWell(
                        onTap: () => Navigator.pop(context),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          child: Center(
                            child: Text(
                              cancelText,
                              style: const TextStyle(
                                fontSize: 14,
                                color: Color(0xFF333333),
                              ),
                            ),
                          ),
                        ),
                      ),
                      // 底部安全区域
                      const SizedBox(
                          height: 8,
                          child: SafeArea(child: SizedBox(height: 8))),
                    ],
                  )),
            ],
          ),
        );
      },
    );
  }
}
