import 'package:flutter/material.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_user_full_info.dart';
import '../../../../apis/tuils_api.dart';
import '../../../../models/moments_list_response.dart';
import '../../../../utils/database_helper.dart';
import '../../../../utils/moment_item_extension.dart';
import 'package:tencent_cloud_chat_sdk/tencent_im_sdk_plugin.dart';

class MomentsVm with ChangeNotifier {
  List<MomentItem> momentsList = [];
  int pages = 1;
  int pageNo = 1;
  bool isLoading = false;
  bool hasError = false;
  final DatabaseHelper _dbHelper = DatabaseHelper();

  // 获取朋友圈list
  Future<bool> getMomentsList(String type, {String? userId}) async {
    await _loadFromCache();
    isLoading = true;
    notifyListeners();
    try {
      // 如果是刷新，先尝试从缓存加载数据
      if (type == "refresh") {
        await _loadFromCache();
      }

      final params = {"pageSize": 10, "searchCount": true, "phone": ""};

      // 如果传入了userId，则添加到参数中
      if (userId != null) {
        params["userId"] = userId;
      }
      if (type == "refresh") {
        pageNo = 1;
        params["pageNum"] = pageNo;
      } else {
        // 检查是否已经到最后一页
        if (pageNo >= pages) {
          isLoading = false;
          notifyListeners();
          return false;
        }
        // 页码递增
        pageNo = pageNo + 1;
        params["pageNum"] = pageNo;
      }

      MomentsListResponse list = await Api.instance.getMomentsList(params);
      pages = list.data?.pages ?? 1;

      // 拿到用户列表的userid
      var ids = list.data?.list?.map((e) => e.userId).toList();
      final data = await TencentImSDKPlugin.v2TIMManager
          .getUsersInfo(userIDList: ids!.map((e) => e.toString()).toList());

      // 创建用户ID到用户信息的映射
      Map<String, V2TimUserFullInfo> userInfoMap = {};
      for (var userInfo in data.data ?? []) {
        if (userInfo != null) {
          userInfoMap[userInfo.userID] = userInfo;
        }
      }

      // 使用映射直接查找用户信息，避免嵌套循环
      for (int i = 0; i < (list.data?.list?.length ?? 0); i++) {
        final element = list.data?.list?[i];
        if (element != null) {
          final userInfo = userInfoMap[element.userId.toString()];
          if (userInfo != null) {
            list.data?.list?[i].name = userInfo.nickName;
            list.data?.list?[i].avatar = userInfo.faceUrl;
          }
        }
      }
      if (type == "refresh") {
        momentsList = list.data?.list ?? [];

      } else {
        momentsList.addAll(list.data?.list ?? []);
      }
      // 保存到缓存
      if (momentsList.isNotEmpty) {
        await _saveToCache(momentsList);
      }
      hasError = false;
      return true;
    } catch (e) {
      debugPrint("获取朋友圈列表失败: $e");
      hasError = true;

      // 如果是刷新但失败了，尝试从缓存加载
      if (type == "refresh" && momentsList.isEmpty) {
        await _loadFromCache();
      }

      return false;
    } finally {
      isLoading = false;
      notifyListeners();
    }
  }

  // 从缓存加载数据
  Future<void> _loadFromCache() async {
    try {
      final cachedMoments = await _dbHelper.getMoments(pageSize: 10, pageNum: 1);
      if (cachedMoments.isNotEmpty) {
        momentsList = cachedMoments;
        debugPrint("从缓存加载了 ${cachedMoments.length} 条朋友圈");
      }
    } catch (e) {
      debugPrint("从缓存加载朋友圈失败: $e");
    }
  }

  // 保存数据到缓存
  Future<void> _saveToCache(List<MomentItem> moments) async {
    try {
      await _dbHelper.saveMoments(moments);
      debugPrint("保存了 ${moments.length} 条朋友圈到缓存");
    } catch (e) {
      debugPrint("保存朋友圈到缓存失败: $e");
    }
  }

  // 点赞
  Future<bool> likeMoment(String momentId) async {
    try {
      final params = {
        "momentId": momentId,
      };
      debugPrint("点赞参数:${params.toString()}");
      await Api.instance.like(params);
      return true;
    } catch (e) {
      debugPrint("点赞失败: $e");
      return false;
    } finally {
      notifyListeners();
    }

  }

  // 取消点赞
  Future<bool> cancelLike(String momentId) async {
    try {
      final params = {
        "momentId": momentId,
      };
      debugPrint("取消点赞参数:${params.toString()}");
      await Api.instance.cancelLike(params);
      return true;
    } catch (e) {
      debugPrint("取消点赞失败: $e");
      return false;
    }finally {
      notifyListeners();
    }
  }

  // 在 moments_vm.dart 中
  Future<bool> toggleLike(String momentId, String userId) async {
    try {
      // 找到对应的 moment
      final momentIndex =
          momentsList.indexWhere((m) => m.id.toString() == momentId);
      if (momentIndex == -1) return false;

      final moment = momentsList[momentIndex];
      final isLiked = moment.likeUserList?.contains(userId) ?? false;

      if (isLiked) {
        // 取消点赞
        final params = {"momentId": momentId};
        await Api.instance.cancelLike(params);
        moment.likeUserList!.remove(userId);
      } else {
        // 点赞
        final params = {"momentId": momentId};
        await Api.instance.like(params);
        moment.likeUserList ??= [];
        // moment.likeUserList!.add(userId);
      }

      // 更新缓存
      await _saveToCache(momentsList);

      // 通知监听者
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint("点赞操作失败: $e");
      return false;
    }
  }

  // 评论
  Future<bool> comment(
      String momentId, String comment, String? replyCommentId) async {
    try {
      print('momentId $momentId');
      final params = {
        "momentId": momentId,
        "content": comment,
      };
      print('replyCommentId $replyCommentId');
      if (replyCommentId != null) {

        params['replyCommentId'] = replyCommentId;
      }
      debugPrint("评论参数:${params.toString()}");
      await Api.instance.comment(params);
      debugPrint("评论成功");

      // 评论成功后刷新列表
      await getMomentsList("refresh");

      return true;
    } catch (e) {
      debugPrint("评论失败: $e");
      return false;
    }
  }
}
