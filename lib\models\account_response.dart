class AccountData {
  int? code;
  String? level;
  String? msg;
  bool? ok;
  Data? data;
  int? dataType;

  AccountData({this.code, this.level, this.msg, this.ok, this.data, this.dataType});

  AccountData.fromJson(Map<String, dynamic> json) {
    if(json["code"] is int) {
      code = json["code"];
    }
    if(json["level"] is String) {
      level = json["level"];
    }
    if(json["msg"] is String) {
      msg = json["msg"];
    }
    if(json["ok"] is bool) {
      ok = json["ok"];
    }
    if(json["data"] is Map) {
      data = json["data"] == null ? null : Data.fromJson(json["data"]);
    }
    if(json["dataType"] is int) {
      dataType = json["dataType"];
    }
  }

  static List<AccountData> fromList(List<Map<String, dynamic>> list) {
    return list.map(AccountData.fromJson).toList();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> _data = <String, dynamic>{};
    _data["code"] = code;
    _data["level"] = level;
    _data["msg"] = msg;
    _data["ok"] = ok;
    if(data != null) {
      _data["data"] = data?.toJson();
    }
    _data["dataType"] = dataType;
    return _data;
  }
}

class Data {
  int? balance;
  int? userId;
  int? status;
  String? createTime;

  Data({this.balance, this.userId, this.status, this.createTime});

  Data.fromJson(Map<String, dynamic> json) {
    // 处理 balance 字段，支持多种类型转换
    if (json["balance"] != null) {
      if (json["balance"] is int) {
        balance = json["balance"];
      } else if (json["balance"] is double) {
        balance = json["balance"].toInt();
      } else if (json["balance"] is String) {
        balance = int.tryParse(json["balance"]);
      }
    }
    
    // 处理 userId 字段，支持多种类型转换
    if (json["userId"] != null) {
      if (json["userId"] is int) {
        userId = json["userId"];
      } else if (json["userId"] is double) {
        userId = json["userId"].toInt();
      } else if (json["userId"] is String) {
        userId = int.tryParse(json["userId"]);
      }
    }
    
    if(json["status"] is int) {
      status = json["status"];
    }
    if(json["createTime"] is String) {
      createTime = json["createTime"];
    }
  }

  static List<Data> fromList(List<Map<String, dynamic>> list) {
    return list.map(Data.fromJson).toList();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> _data = <String, dynamic>{};
    _data["balance"] = balance;
    _data["userId"] = userId;
    _data["status"] = status;
    _data["createTime"] = createTime;
    return _data;
  }
}