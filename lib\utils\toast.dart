import 'package:flutter/material.dart';
import 'package:flutter_styled_toast/flutter_styled_toast.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';
import 'package:tencent_cloud_chat_demo/config.dart';


class ToastUtils {
  // 用于控制loading的显示和隐藏
  static OverlayEntry? _loadingEntry;

  static void toast(String msg) {
    showToast(
    msg,
    context: IMDemoConfig.navigatorKey.currentContext!,
    animation: StyledToastAnimation.fade,
    duration: const Duration(seconds: 2),
    position: StyledToastPosition.center,
    borderRadius: BorderRadius.circular(8.0), // 自定义圆角大小
    backgroundColor: Colors.black45,
    textStyle: const TextStyle(color: Colors.white, fontSize: 16.0),
    );
  }

  static void toastTop(String msg) {
    showToast(
    msg,
    context: IMDemoConfig.navigatorKey.currentContext!,
    animation: StyledToastAnimation.fade,
    duration: const Duration(seconds: 2),
    position: const StyledToastPosition(align: Alignment.topCenter, offset: 120.0),
    borderRadius: BorderRadius.circular(8.0), // 自定义圆角大小
    backgroundColor: Colors.black45,
    textStyle: const TextStyle(color: Colors.white, fontSize: 16.0),
    );
  }

  static void toastError(int code, String desc) {
    showToast(  
      "code:$code,desc:$desc",
      context: IMDemoConfig.navigatorKey.currentContext!,
      animation: StyledToastAnimation.fade,
      duration: const Duration(seconds: 2),
      position: StyledToastPosition.center,
      borderRadius: BorderRadius.circular(8.0), // 自定义圆角大小
      backgroundColor: Colors.black45,
      textStyle: const TextStyle(color: Colors.white, fontSize: 16.0),
    );
  }

  // 显示加载动画
  static void showLoading({String? message}) {
    final msg = message ?? TIM_t("正在加载...");
    hideLoading(); // 先隐藏可能存在的loading
    
    final BuildContext context = IMDemoConfig.navigatorKey.currentContext!;
    final OverlayState overlayState = Overlay.of(context);
    
    _loadingEntry = OverlayEntry(
      builder: (context) => Material(
        color: Colors.transparent,
        child: Center(
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 35, horizontal: 25),
            decoration: BoxDecoration(
              color: const Color(0xff434343),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min, 
              children: [
                const CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
                if (msg != null) ...[
                  const SizedBox(height: 20),
                  Text(
                    msg,
                    style: const TextStyle(color: Colors.white, fontSize: 16),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
    
    overlayState.insert(_loadingEntry!);

    // 超过5秒自动隐藏
    Future.delayed(const Duration(seconds: 5), () {
      hideLoading();
    });
  }

  // 隐藏加载动画
  static void hideLoading() {
    _loadingEntry?.remove();
    _loadingEntry = null;
  }

  static void log(Object? data) {
    bool prod = const bool.fromEnvironment('ISPRODUCT_ENV', defaultValue: false);
    if (!prod) {
      // ignore: avoid_print
      print("===================================");
      // ignore: avoid_print
      print("IM_DEMO_PRINT:$data");
      // ignore: avoid_print
      print("===================================");
    } else {}
  }
}