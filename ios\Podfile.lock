PODS:
  - audio_session (0.0.1):
    - Flutter
  - better_player_plus (1.0.0):
    - <PERSON><PERSON> (~> 6.0.0)
    - Flutter
    - GCDWebServer
    - HLSCachingReverseProxyServer
    - PINCache
  - Cache (6.0.0)
  - camera_avfoundation (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - DKImagePickerController/Core (4.3.9):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.9)
  - DKImagePickerController/PhotoGallery (4.3.9):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.9)
  - DKPhotoGallery (0.0.19):
    - DKPhotoGallery/Core (= 0.0.19)
    - DKPhotoGallery/Model (= 0.0.19)
    - DKPhotoGallery/Preview (= 0.0.19)
    - DKPhotoGallery/Resource (= 0.0.19)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.19):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.19):
    - SDWebImage
    - SwiftyGif
  - FBAEMKit (17.0.3):
    - FBSDKCoreKit_Basics (= 17.0.3)
  - FBSDKCoreKit (17.0.3):
    - FBAEMKit (= 17.0.3)
    - FBSDKCoreKit_Basics (= 17.0.3)
  - FBSDKCoreKit_Basics (17.0.3)
  - FBSDKLoginKit (17.0.3):
    - FBSDKCoreKit (= 17.0.3)
  - fc_native_video_thumbnail (0.0.1):
    - Flutter
    - FlutterMacOS
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - Flutter (1.0.0)
  - flutter_facebook_auth (7.0.1):
    - FBSDKLoginKit (~> 17.0.2)
    - Flutter
  - flutter_image_compress_common (1.0.0):
    - Flutter
    - Mantle
    - SDWebImage
    - SDWebImageWebPCoder
  - flutter_native_splash (2.4.3):
    - Flutter
  - flutter_plugin_record_plus (0.0.1):
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
    - Toast
  - GCDWebServer (3.5.4):
    - GCDWebServer/Core (= 3.5.4)
  - GCDWebServer/Core (3.5.4)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleMLKit/BarcodeScanning (6.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitBarcodeScanning (~> 5.0.0)
  - GoogleMLKit/MLKitCore (6.0.0):
    - MLKitCommon (~> 11.0.0)
  - GoogleToolboxForMac/Defines (4.2.1)
  - GoogleToolboxForMac/Logger (4.2.1):
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - "GoogleToolboxForMac/NSData+zlib (4.2.1)":
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilitiesComponents (1.1.0):
    - GoogleUtilities/Logger
  - GTMSessionFetcher/Core (3.5.0)
  - HLSCachingReverseProxyServer (0.1.0):
    - GCDWebServer (~> 3.5)
    - PINCache (>= 3.0.1-beta.3)
  - HydraAsync (2.0.6)
  - image_cropper (0.0.4):
    - Flutter
    - TOCropViewController (~> 2.7.4)
  - image_gallery_saver (2.0.2):
    - Flutter
  - image_gallery_saver_plus (0.0.1):
    - Flutter
  - image_picker_ios (0.0.1):
    - Flutter
  - just_audio (0.0.1):
    - Flutter
    - FlutterMacOS
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - Mantle (2.2.0):
    - Mantle/extobjc (= 2.2.0)
  - Mantle/extobjc (2.2.0)
  - MLImage (1.0.0-beta5)
  - MLKitBarcodeScanning (5.0.0):
    - MLKitCommon (~> 11.0)
    - MLKitVision (~> 7.0)
  - MLKitCommon (11.0.0):
    - GoogleDataTransport (< 10.0, >= 9.4.1)
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GoogleUtilities/UserDefaults (< 8.0, >= 7.13.0)
    - GoogleUtilitiesComponents (~> 1.0)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
  - MLKitVision (7.0.0):
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
    - MLImage (= 1.0.0-beta5)
    - MLKitCommon (~> 11.0)
  - mobile_scanner (5.2.3):
    - Flutter
    - GoogleMLKit/BarcodeScanning (~> 6.0.0)
  - nanopb (2.30910.0):
    - nanopb/decode (= 2.30910.0)
    - nanopb/encode (= 2.30910.0)
  - nanopb/decode (2.30910.0)
  - nanopb/encode (2.30910.0)
  - open_file_ios (0.0.1):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - pasteboard (0.0.1):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.1.1):
    - Flutter
  - photo_manager (3.7.1):
    - Flutter
    - FlutterMacOS
  - PINCache (3.0.4):
    - PINCache/Arc-exception-safe (= 3.0.4)
    - PINCache/Core (= 3.0.4)
  - PINCache/Arc-exception-safe (3.0.4):
    - PINCache/Core
  - PINCache/Core (3.0.4):
    - PINOperation (~> 1.2.3)
  - PINOperation (1.2.3)
  - PromisesObjC (2.4.0)
  - RTCRoomEngine/Professional (3.2.2):
    - TXIMSDK_Plus_iOS_XCFramework (>= 8.4.6676)
    - TXLiteAVSDK_Professional (>= 12.2.16956)
  - SDWebImage (5.21.1):
    - SDWebImage/Core (= 5.21.1)
  - SDWebImage/Core (5.21.1)
  - SDWebImageWebPCoder (0.14.6):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.17)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - SnapKit (5.7.1)
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - SwiftyGif (5.4.5)
  - tencent_calls_uikit (0.0.1):
    - Flutter
    - RTCRoomEngine/Professional (~> 3.2.0)
    - SnapKit
    - TUICore (>= 8.6.7019)
  - tencent_cloud_chat_push (8.6.7019):
    - Flutter
    - TIMPush (= 8.6.7019)
    - TXIMSDK_Plus_iOS_XCFramework
  - tencent_cloud_chat_sdk (8.0.0):
    - Flutter
    - HydraAsync
    - TXIMSDK_Plus_iOS_XCFramework (= 8.6.7019)
  - tencent_cloud_uikit_core (0.0.1):
    - Flutter
  - TIMPush (8.6.7019):
    - TXIMSDK_Plus_iOS_XCFramework (>= 8.6.7019)
  - Toast (4.1.1)
  - TOCropViewController (2.7.4)
  - TUICore (8.6.7019):
    - SDWebImage
    - TUICore/ImSDK_Plus (= 8.6.7019)
  - TUICore/Base (8.6.7019):
    - SDWebImage
  - TUICore/ImSDK_Plus (8.6.7019):
    - SDWebImage
    - TUICore/Base
    - TXIMSDK_Plus_iOS_XCFramework
  - TXIMSDK_Plus_iOS_XCFramework (8.6.7019)
  - TXLiteAVSDK_Professional (12.7.19324):
    - TXLiteAVSDK_Professional/Professional (= 12.7.19324)
  - TXLiteAVSDK_Professional/Professional (12.7.19324)
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - wakelock_plus (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter

DEPENDENCIES:
  - audio_session (from `.symlinks/plugins/audio_session/ios`)
  - better_player_plus (from `.symlinks/plugins/better_player_plus/ios`)
  - camera_avfoundation (from `.symlinks/plugins/camera_avfoundation/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - fc_native_video_thumbnail (from `.symlinks/plugins/fc_native_video_thumbnail/darwin`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - Flutter (from `Flutter`)
  - flutter_facebook_auth (from `.symlinks/plugins/flutter_facebook_auth/ios`)
  - flutter_image_compress_common (from `.symlinks/plugins/flutter_image_compress_common/ios`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - flutter_plugin_record_plus (from `.symlinks/plugins/flutter_plugin_record_plus/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - image_cropper (from `.symlinks/plugins/image_cropper/ios`)
  - image_gallery_saver (from `.symlinks/plugins/image_gallery_saver/ios`)
  - image_gallery_saver_plus (from `.symlinks/plugins/image_gallery_saver_plus/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - just_audio (from `.symlinks/plugins/just_audio/darwin`)
  - mobile_scanner (from `.symlinks/plugins/mobile_scanner/ios`)
  - open_file_ios (from `.symlinks/plugins/open_file_ios/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - pasteboard (from `.symlinks/plugins/pasteboard/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - photo_manager (from `.symlinks/plugins/photo_manager/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - tencent_calls_uikit (from `.symlinks/plugins/tencent_calls_uikit/ios`)
  - tencent_cloud_chat_push (from `.symlinks/plugins/tencent_cloud_chat_push/ios`)
  - tencent_cloud_chat_sdk (from `.symlinks/plugins/tencent_cloud_chat_sdk/ios`)
  - tencent_cloud_uikit_core (from `.symlinks/plugins/tencent_cloud_uikit_core/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/ios`)

SPEC REPOS:
  trunk:
    - Cache
    - DKImagePickerController
    - DKPhotoGallery
    - FBAEMKit
    - FBSDKCoreKit
    - FBSDKCoreKit_Basics
    - FBSDKLoginKit
    - GCDWebServer
    - GoogleDataTransport
    - GoogleMLKit
    - GoogleToolboxForMac
    - GoogleUtilities
    - GoogleUtilitiesComponents
    - GTMSessionFetcher
    - HLSCachingReverseProxyServer
    - HydraAsync
    - libwebp
    - Mantle
    - MLImage
    - MLKitBarcodeScanning
    - MLKitCommon
    - MLKitVision
    - nanopb
    - PINCache
    - PINOperation
    - PromisesObjC
    - RTCRoomEngine
    - SDWebImage
    - SDWebImageWebPCoder
    - SnapKit
    - SwiftyGif
    - TIMPush
    - Toast
    - TOCropViewController
    - TUICore
    - TXIMSDK_Plus_iOS_XCFramework
    - TXLiteAVSDK_Professional

EXTERNAL SOURCES:
  audio_session:
    :path: ".symlinks/plugins/audio_session/ios"
  better_player_plus:
    :path: ".symlinks/plugins/better_player_plus/ios"
  camera_avfoundation:
    :path: ".symlinks/plugins/camera_avfoundation/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  fc_native_video_thumbnail:
    :path: ".symlinks/plugins/fc_native_video_thumbnail/darwin"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  Flutter:
    :path: Flutter
  flutter_facebook_auth:
    :path: ".symlinks/plugins/flutter_facebook_auth/ios"
  flutter_image_compress_common:
    :path: ".symlinks/plugins/flutter_image_compress_common/ios"
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  flutter_plugin_record_plus:
    :path: ".symlinks/plugins/flutter_plugin_record_plus/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  image_cropper:
    :path: ".symlinks/plugins/image_cropper/ios"
  image_gallery_saver:
    :path: ".symlinks/plugins/image_gallery_saver/ios"
  image_gallery_saver_plus:
    :path: ".symlinks/plugins/image_gallery_saver_plus/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  just_audio:
    :path: ".symlinks/plugins/just_audio/darwin"
  mobile_scanner:
    :path: ".symlinks/plugins/mobile_scanner/ios"
  open_file_ios:
    :path: ".symlinks/plugins/open_file_ios/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  pasteboard:
    :path: ".symlinks/plugins/pasteboard/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  photo_manager:
    :path: ".symlinks/plugins/photo_manager/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  tencent_calls_uikit:
    :path: ".symlinks/plugins/tencent_calls_uikit/ios"
  tencent_cloud_chat_push:
    :path: ".symlinks/plugins/tencent_cloud_chat_push/ios"
  tencent_cloud_chat_sdk:
    :path: ".symlinks/plugins/tencent_cloud_chat_sdk/ios"
  tencent_cloud_uikit_core:
    :path: ".symlinks/plugins/tencent_cloud_uikit_core/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/ios"

SPEC CHECKSUMS:
  audio_session: 9bb7f6c970f21241b19f5a3658097ae459681ba0
  better_player_plus: 10794c0ed1b3b4ae058939e22a6172f850a2039b
  Cache: 4ca7e00363fca5455f26534e5607634c820ffc2d
  camera_avfoundation: be3be85408cd4126f250386828e9b1dfa40ab436
  device_info_plus: 71ffc6ab7634ade6267c7a93088ed7e4f74e5896
  DKImagePickerController: 946cec48c7873164274ecc4624d19e3da4c1ef3c
  DKPhotoGallery: b3834fecb755ee09a593d7c9e389d8b5d6deed60
  FBAEMKit: 9900b2edd99a2d21629a6277e6166f14c6215799
  FBSDKCoreKit: 0791f8f68a8630931a4c12aa23a56cc021551596
  FBSDKCoreKit_Basics: 46d6b472c0dd0a5a7e972c025033d1c567f54eb4
  FBSDKLoginKit: b4a4eba1d62eb452544411824f41689adabd5bd2
  fc_native_video_thumbnail: b511cec81fad66be9b28dd54b9adb39d40fcd6cc
  file_picker: 5f42b9d5580e30b57b4863f9d94b448016b702e5
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_facebook_auth: 8ae86c1cf564cad8bbdead592c9ebb4e300f1e3b
  flutter_image_compress_common: 1697a328fd72bfb335507c6bca1a65fa5ad87df1
  flutter_native_splash: 6cad9122ea0fad137d23137dd14b937f3e90b145
  flutter_plugin_record_plus: 7dc36c7574e26041729728fdaaf7eeba30d90ec0
  flutter_secure_storage: 1ed9476fba7e7a782b22888f956cce43e2c62f13
  fluttertoast: 76fea30fcf04176325f6864c87306927bd7d2038
  GCDWebServer: 2c156a56c8226e2d5c0c3f208a3621ccffbe3ce4
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleMLKit: 97ac7af399057e99182ee8edfa8249e3226a4065
  GoogleToolboxForMac: d1a2cbf009c453f4d6ded37c105e2f67a32206d8
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  GoogleUtilitiesComponents: 679b2c881db3b615a2777504623df6122dd20afe
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  HLSCachingReverseProxyServer: 59935e1e0244ad7f3375d75b5ef46e8eb26ab181
  HydraAsync: 8d589bd725b0224f899afafc9a396327405f8063
  image_cropper: c4326ea50132b1e1564499e5d32a84f01fb03537
  image_gallery_saver: 14711d79da40581063e8842a11acf1969d781ed7
  image_gallery_saver_plus: e597bf65a7846979417a3eae0763b71b6dfec6c3
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  just_audio: 4e391f57b79cad2b0674030a00453ca5ce817eed
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  Mantle: c5aa8794a29a022dfbbfc9799af95f477a69b62d
  MLImage: 1824212150da33ef225fbd3dc49f184cf611046c
  MLKitBarcodeScanning: 10ca0845a6d15f2f6e911f682a1998b68b973e8b
  MLKitCommon: afec63980417d29ffbb4790529a1b0a2291699e1
  MLKitVision: e858c5f125ecc288e4a31127928301eaba9ae0c1
  mobile_scanner: 92e8812bf22a8f84131e2a7f9d0f44dad1a4742b
  nanopb: 438bc412db1928dac798aa6fd75726007be04262
  open_file_ios: 5ff7526df64e4394b4fe207636b67a95e83078bb
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  pasteboard: 49088aeb6119d51f976a421db60d8e1ab079b63c
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 3787117e48f80715ff04a3830ca039283d6a4f29
  photo_manager: 1d80ae07a89a67dfbcae95953a1e5a24af7c3e62
  PINCache: d9a87a0ff397acffe9e2f0db972ac14680441158
  PINOperation: fb563bcc9c32c26d6c78aaff967d405aa2ee74a7
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  RTCRoomEngine: 6a4fc015118e2b868325598cba5e1ae1aa9f1cf4
  SDWebImage: f29024626962457f3470184232766516dee8dfea
  SDWebImageWebPCoder: e38c0a70396191361d60c092933e22c20d5b1380
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  SnapKit: d612e99e678a2d3b95bf60b0705ed0a35c03484a
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  SwiftyGif: 706c60cf65fa2bc5ee0313beece843c8eb8194d4
  tencent_calls_uikit: 1bfab6433b694c2dc050276317a197273b1c2f13
  tencent_cloud_chat_push: f87ae58098c2062b06e81f39fc53afc528395916
  tencent_cloud_chat_sdk: 2797f99ea8ee1cd9933b9ac866a42575a85fac7a
  tencent_cloud_uikit_core: 137e8ae40882b1929508e688182b2818708cc078
  TIMPush: d0dfe96355ee413a7cacb2576f8aaa66f6073ab2
  Toast: 1f5ea13423a1e6674c4abdac5be53587ae481c4e
  TOCropViewController: 80b8985ad794298fb69d3341de183f33d1853654
  TUICore: 7991cb71071c1c75360d58f92250fa4db2313a76
  TXIMSDK_Plus_iOS_XCFramework: cb54f7de6e30e1368c6831c6eff31c25393bbb98
  TXLiteAVSDK_Professional: f4f5382ac77edbd6fc37fd9f0479b371beab8cbd
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  video_player_avfoundation: 2cef49524dd1f16c5300b9cd6efd9611ce03639b
  wakelock_plus: e29112ab3ef0b318e58cfa5c32326458be66b556
  webview_flutter_wkwebview: 6e6160e04b1e85872253adc5322afe416d9cdddc

PODFILE CHECKSUM: 9b5fec7154ddb2ccddc6154ed5da4b8dfb10878f

COCOAPODS: 1.16.2
