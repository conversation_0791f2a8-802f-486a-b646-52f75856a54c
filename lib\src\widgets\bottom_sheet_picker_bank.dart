import 'package:flutter/material.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';
import '../pages/services/bank_add_page.dart';

class BottomSheetOption {
  final String title;
  final Function onTap;
  final bool isSelected;

  BottomSheetOption({
    required this.title,
    required this.onTap,
    this.isSelected = false,
  });
}

class BottomSheetPicker {
  static Future<T?> show<T>({
    required BuildContext context,
    required String title,
    required List<BottomSheetOption> options,
    String cancelText = "取消",
  }) {
    jumpAddBankPage() {
      Navigator.pop(context);
      Navigator.push(context, MaterialPageRoute(builder: (context) => const BankAddPage()));
    }
    return showModalBottomSheet<T>(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return Container(
          decoration: const BoxDecoration(
            color: Colors.transparent,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 选项组
              Container(
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(10),
                    topRight: Radius.circular(10),
                  ),
                ),
                child: Column(
                  children: [
                    if(title.isNotEmpty)
                    // 标题
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      child: Text(
                        title,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Color(0xFF333333),
                        ),
                      ),
                    ),
                    if(title.isNotEmpty)
                    // 选项列表
Container(
  // 设置最大高度，防止列表过长
  constraints: BoxConstraints(
    maxHeight: MediaQuery.of(context).size.height * 0.22, // 屏幕高度的40%
  ),
  child: ListView(
    // 禁用ListView的内边距
    padding: EdgeInsets.zero,
    // 禁用ListView的弹性效果
    physics: const ClampingScrollPhysics(),
    shrinkWrap: true,
    children: [
      // 选项列表
      ...options.map((option) => Column(
        children: [
          InkWell(
            onTap: () {
              Navigator.pop(context);
              option.onTap();
            },
            child: Container(
              alignment: Alignment.centerLeft,
              width: double.infinity,
              margin: const EdgeInsets.only(left: 21, right: 16),
              padding: const EdgeInsets.only(top: 12, bottom: 12),
              decoration: BoxDecoration(
                border: options.indexOf(option) < options.length - 1
                    ? const Border(
                        bottom: BorderSide(
                          color: Color(0xFFE9E9E9),
                          width: 1,
                        ))
                    : null,
              ),
              child: Row(
                children: [
                  const SizedBox(width: 5,),
                  Image.asset('assets/serveice/bank_picker_icon.png', width: 20, height: 20,),
                  const SizedBox(width: 12,),
                  Text(
                    option.title,
                    style: TextStyle(
                      fontSize: 14,
                      color: option.isSelected
                          ? const Color(0xFF0072FC)
                          : const Color(0xFF333333),
                    ),
                  ),
                  const Spacer(),
                  if(option.isSelected)
                    Image.asset('assets/serveice/item_select_icon.png', width: 16, height: 16,),
                ],
              ),
            ),
          ),
        ],
      )).toList(),
    ],
  ),
),
                    Container(
                      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                      margin: const EdgeInsets.only(left: 16, right: 16),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color: const Color(0xFFE9E9E9),
                      ),
                      child: InkWell(
                        onTap: () {
                          jumpAddBankPage();
                        },
                        child: Row(
                          children: [
                            Image.asset('assets/serveice/add_icon.png', width: 20, height: 20,),
                            const SizedBox(width: 12,),
                            Text(TIM_t('绑定新卡'), style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500, color: Color(0xFF333333))),
                          ],
                        ),
                      )
                    ),
                  ],
                ),
              ),
              // 取消按钮
              Container(
                  decoration: const BoxDecoration(color: Colors.white),
                  width: double.infinity,
                  child: Column(
                    children: [
                      SizedBox(height: 10, child: Container(color: const Color(0xFFF4F7F8))),
                      InkWell(
                        onTap: () => Navigator.pop(context),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          child: Center(
                            child: Text(
                              cancelText,
                              style: const TextStyle(
                                fontSize: 14,
                                color: Color(0xFF333333),
                              ),
                            ),
                          ),
                        ),
                      ),
                      // 底部安全区域
                      const SizedBox(
                          height: 8,
                          child: SafeArea(child: SizedBox(height: 8))),
                    ],
                  )),
            ],
          ),
        );
      },
    );
  }
}
