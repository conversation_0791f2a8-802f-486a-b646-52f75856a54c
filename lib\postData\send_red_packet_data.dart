// 发送红包的数据类型
class SendRedPacketData {
  final String chat;      // chat: 单聊对方用户ID或群组ID
  final String amount;        // totalAmount: 红包总金额
  final String remark;        // 备注/祝福语
  final String coverId;       // 封面ID
  final String chatType;      // 单聊/群聊 private/group
  final String type;          // normal/lucky (普通/拼手气)
  final int totalCount;       // 红包个数 (拼手气时需要)

  SendRedPacketData({
    required this.chat,
    required this.amount,
    required this.remark,
    required this.coverId,
    this.chatType = 'private',
    this.type = 'normal',
    this.totalCount = 1,
  });

  Map<String, dynamic> toJson() {
    return {
      'chat': chat,
      'totalAmount': amount,
      'remark': remark,
      'coverId': coverId,
      'chatType': chatType,
      'type': type,
      'totalCount': totalCount,
    };
  }
}