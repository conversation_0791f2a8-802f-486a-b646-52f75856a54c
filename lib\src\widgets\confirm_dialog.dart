import 'package:flutter/material.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';

/// 通用确认对话框
///
/// 用于显示确认/取消操作的对话框，支持自定义标题、内容和按钮文本
class ConfirmDialog {
  /// 显示确认对话框
  ///
  /// [context] - 上下文
  /// [title] - 对话框标题
  /// [content] - 对话框内容（可选）
  /// [confirmText] - 确认按钮文本
  /// [cancelText] - 取消按钮文本
  /// 返回用户选择结果：true表示确认，false表示取消，null表示对话框被关闭
  static Future<bool?> show({
    required BuildContext context,
    required String title,
    String? content,
    String? confirmText,
    String? cancelText,
  }) async {
    confirmText = confirmText ?? TIM_t("确定");
    cancelText = cancelText ?? TIM_t("取消");
    return await showDialog<bool>(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: Colors.white,
          actionsPadding:
              const EdgeInsets.only(left: 50, right: 50, bottom: 24),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          title: Text(
            title,
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
            textAlign: TextAlign.center,
          ),
          content: content != null
              ? Text(
                  content,
                  style: const TextStyle(fontSize: 14),
                  textAlign: TextAlign.center,
                )
              : null,
          actions: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                GestureDetector(
                  onTap: () {
                    Navigator.pop(context, false);
                  },
                  child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 4),
                      margin: const EdgeInsets.only(right: 16),
                      // width: 64,
                      height: 32,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color: const Color(0xFFE9E9E9),
                      ),
                      child: Center(
                        child: Text(cancelText!,
                            style: const TextStyle(color: Color(0XFF666666))),
                      )),
                ),
                GestureDetector(
                  onTap: () {
                    Navigator.pop(context, true);
                  },
                  child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 4),
                      width: 64,
                      height: 32,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color: const Color(0xFF0072FC),
                      ),
                      child: Center(
                        child: Text(confirmText!,
                            style: const TextStyle(color: Color(0XFFFFFFFF))),
                      )),
                ),
              ],
            )
          ],
        );
      },
    );
  }
}
