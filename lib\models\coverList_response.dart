class CoverList {
  int? code;
  String? level;
  String? msg;
  bool? ok;
  Data? data;
  int? dataType;

  CoverList({this.code, this.level, this.msg, this.ok, this.data, this.dataType});

  CoverList.fromJson(Map<String, dynamic> json) {
    if(json["code"] is int) {
      code = json["code"];
    }
    if(json["level"] is String) {
      level = json["level"];
    }
    if(json["msg"] is String) {
      msg = json["msg"];
    }
    if(json["ok"] is bool) {
      ok = json["ok"];
    }
    if(json["data"] is Map) {
      data = json["data"] == null ? null : Data.fromJson(json["data"]);
    }
    if(json["dataType"] is int) {
      dataType = json["dataType"];
    }
  }

  static List<CoverList> fromList(List<Map<String, dynamic>> list) {
    return list.map(CoverList.fromJson).toList();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> _data = <String, dynamic>{};
    _data["code"] = code;
    _data["level"] = level;
    _data["msg"] = msg;
    _data["ok"] = ok;
    if(data != null) {
      _data["data"] = data?.toJson();
    }
    _data["dataType"] = dataType;
    return _data;
  }
}

class Data {
  int? pageNum;
  int? pageSize;
  int? total;
  int? pages;
  List<CoverItem>? list;  // 修改这里，使用CoverItem替代List
  bool? emptyFlag;

  Data({this.pageNum, this.pageSize, this.total, this.pages, this.list, this.emptyFlag});

  Data.fromJson(Map<String, dynamic> json) {
    if(json["pageNum"] is int) {
      pageNum = json["pageNum"];
    }
    if(json["pageSize"] is int) {
      pageSize = json["pageSize"];
    }
    if(json["total"] is int) {
      total = json["total"];
    }
    if(json["pages"] is int) {
      pages = json["pages"];
    }
    if(json["list"] is List) {
      list = json["list"] == null ? null : (json["list"] as List).map((e) => CoverItem.fromJson(e)).toList();  // 修改这里
    }
    if(json["emptyFlag"] is bool) {
      emptyFlag = json["emptyFlag"];
    }
  }

  static List<Data> fromList(List<Map<String, dynamic>> list) {
    return list.map(Data.fromJson).toList();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> _data = <String, dynamic>{};
    _data["pageNum"] = pageNum;
    _data["pageSize"] = pageSize;
    _data["total"] = total;
    _data["pages"] = pages;
    if(list != null) {
      _data["list"] = list?.map((e) => e.toJson()).toList();
    }
    _data["emptyFlag"] = emptyFlag;
    return _data;
  }
}

// 将List类重命名为CoverItem
class CoverItem {
  int? id;
  String? type;
  String? title;
  String? url;
  String? language;
  int? sort;

  CoverItem({this.id, this.type, this.title, this.url, this.language, this.sort});

  CoverItem.fromJson(Map<String, dynamic> json) {
    if(json["id"] is int) {
      id = json["id"];
    }
    if(json["type"] is String) {
      type = json["type"];
    }
    if(json["title"] is String) {
      title = json["title"];
    }
    if(json["url"] is String) {
      url = json["url"];
    }
    if(json["language"] is String) {
      language = json["language"];
    }
    if(json["sort"] is int) {
      sort = json["sort"];
    }
  }

  static List<CoverItem> fromList(List<Map<String, dynamic>> list) {  // 修改这里
    return list.map(CoverItem.fromJson).toList();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> _data = <String, dynamic>{};
    _data["id"] = id;
    _data["type"] = type;
    _data["title"] = title;
    _data["url"] = url;
    _data["language"] = language;
    _data["sort"] = sort;
    return _data;
  }
}