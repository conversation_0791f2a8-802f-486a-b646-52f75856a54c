import 'package:flutter/material.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';
import 'package:tencent_cloud_chat_demo/src/pages/home_page.dart';
import 'package:tencent_cloud_chat_demo/src/tencent_page.dart';
import 'package:tencent_cloud_chat_demo/utils/toast.dart';
import 'package:tencent_cloud_chat_sdk/enum/group_member_filter_enum.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_conversation.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_group_info.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_group_member_full_info.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_message.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_user_full_info.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/life_cycle/group_profile_life_cycle.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/separate_models/tui_group_profile_model.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/view_models/tui_friendship_view_model.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/view_models/tui_self_info_view_model.dart';
import 'package:tencent_cloud_chat_uikit/data_services/services_locatar.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_uikit/theme/color.dart';

import 'package:tencent_cloud_chat_uikit/ui/utils/platform.dart';
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitAddFriend/tim_uikit_send_application.dart';
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitGroupProfile/group_profile_widget.dart';
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitGroupProfile/widgets/tim_ui_group_profile_widget.dart';

import 'package:tencent_cloud_chat_demo/src/chat.dart';
import 'package:tencent_cloud_chat_demo/src/search.dart';
import 'package:tencent_cloud_chat_demo/src/user_profile.dart';
import 'package:tencent_cloud_chat_sdk/enum/group_member_role.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../widgets/avatar.dart';
import 'tui_add_group_member.dart';
import 'tui_delete_group_member.dart';
import 'package:tencent_cloud_chat_sdk/enum/receive_message_opt_enum.dart';
import './tim_uikit_group_motification.dart';
import 'package:tencent_cloud_chat_uikit/ui/controller/tim_uikit_chat_controller.dart';

import 'package:tencent_cloud_chat_uikit/data_services/group/group_services.dart';
import './widgets/confirm_dialog.dart';
import './widgets/input_dialog.dart';

class GroupProfilePage extends StatefulWidget {
  final String groupID;
  final sdkInstance = TIMUIKitCore.getSDKInstance();
  final coreInstance = TIMUIKitCore.getInstance();
  final TUIGroupProfileModel _groupProfileModel;

  final TUISelfInfoViewModel _selfInfoViewModel =
      serviceLocator<TUISelfInfoViewModel>();
  final TUIFriendShipViewModel _friendShipViewModel =
      serviceLocator<TUIFriendShipViewModel>();

  GroupProfilePage({Key? key, required this.groupID})
      : _groupProfileModel = TUIGroupProfileModel(),
        super(key: key) {
    _groupProfileModel.groupID = groupID;
    _groupProfileModel.loadData(groupID);
  }

  @override
  State<GroupProfilePage> createState() => _GroupProfilePageState();
}

class _GroupProfilePageState extends State<GroupProfilePage> {
  bool isPined = false;
  bool isDisturb = false;
  List<V2TimGroupMemberFullInfo>? memberList = [];
  final TIMUIKitChatController _timuiKitChatController =
      TIMUIKitChatController();

  @override
  void initState() {
    super.initState();
    _loadConversationStatus();
    _getGroupMemberList();
  }

  Future<void> _loadConversationStatus() async {
    try {
      // 获取会话信息
      final conversationID = "group_${widget.groupID}";
      debugPrint('正在获取会话信息: $conversationID');

      final result = await widget.sdkInstance
          .getConversationManager()
          .getConversation(conversationID: conversationID);

      if (result.code == 0 && result.data != null) {
        final conversation = result.data;
        setState(() {
          isPined = conversation?.isPinned ?? false;
          isDisturb = conversation?.recvOpt != 0; // 0表示正常接收消息
        });
        debugPrint(
            '会话信息: isPinned: ${conversation?.isPinned}, recvOpt: ${conversation?.recvOpt}');
      } else {
        debugPrint('获取会话信息失败: ${result.code}, ${result.desc}');
      }
    } catch (e) {
      debugPrint('获取会话状态异常: $e');
    }
  }

  Future<void> _itemClick(
      String action, BuildContext context, V2TimGroupInfo groupInfo) async {
    final conversationID = "group_${widget.groupID}";

    if (action == 'pinedConversation') {
      // 切换置顶状态
      bool oldIsAdd = isPined;

      final newPinStatus = !isPined;

      debugPrint('设置会话置顶: $newPinStatus');
      setState(() {
        isPined = newPinStatus;
      });
      try {
        final result = await widget.sdkInstance
            .getConversationManager()
            .pinConversation(
                conversationID: conversationID, isPinned: newPinStatus);
      } catch (e) {
        setState(() {
          isPined = oldIsAdd;
        });
      }
    } else if (action == 'setMessageDisturb') {
      // 切换免打扰状态
      bool oldIsAdd = isDisturb;
      final newDisturbStatus = !isDisturb;
      final recvOpt = newDisturbStatus
          ? ReceiveMsgOptEnum.V2TIM_RECEIVE_NOT_NOTIFY_MESSAGE
          : ReceiveMsgOptEnum.V2TIM_RECEIVE_MESSAGE;

      debugPrint('设置消息免打扰: $newDisturbStatus, recvOpt: ${recvOpt.index}');
      setState(() {
        isDisturb = newDisturbStatus;
      });
      // 使用正确的方法：setGroupReceiveMessageOpt
      try {
        final result = await widget.sdkInstance
            .getMessageManager()
            .setGroupReceiveMessageOpt(groupID: widget.groupID, opt: recvOpt);
      } catch (e) {
        setState(() {
          isDisturb = oldIsAdd;
        });
      }
    }
  }

  // 修改群昵称
  Future<void> _updateGroupNickname(String groupID) async {
    debugPrint('修改群昵称: $groupID');
    // 获取当前用户在群组中的昵称
    final String? currentUserID =
        TIMUIKitCore.getInstance().loginUserInfo?.userID;
    String currentNameCard = _getCurrentUserNameCard();
    // 从群成员列表中查找当前用户的信息
    if (currentUserID != null &&
        widget._groupProfileModel.groupMemberList != null) {
      for (var member in widget._groupProfileModel.groupMemberList!) {
        if (member?.userID == currentUserID) {
          currentNameCard = member?.nameCard ?? '';
          break;
        }
      }
    }
    final String? newNickname = await InputDialog.show(
      context: context,
      title: TIM_t("修改我的群昵称"),
      hintText: TIM_t("请输入群昵称"),
      initialValue: currentNameCard,
    );
    if (newNickname != null && newNickname.isNotEmpty) {
      // 调用腾讯云IM SDK修改群昵称
      await widget.sdkInstance.getGroupManager().setGroupMemberInfo(
            groupID: groupID,
            userID: currentUserID ?? '',
            nameCard: newNickname,
          );

      // 刷新群组信息
      await widget._groupProfileModel.loadGroupMemberList(groupID: groupID);

      if (mounted) {
        // 显示修改成功提示
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(TIM_t("群昵称修改成功")),
            duration: const Duration(seconds: 1),
          ),
        );

        // 刷新页面
        setState(() {});
      }
    }
  }

  // 获取当前用户的群昵称
  String _getCurrentUserNameCard() {
    final String? currentUserID =
        TIMUIKitCore.getInstance().loginUserInfo?.userID;
    String nameCard = '';

    if (currentUserID != null &&
        widget._groupProfileModel.groupMemberList.isNotEmpty) {
      for (var member in widget._groupProfileModel.groupMemberList) {
        if (member?.userID == currentUserID) {
          nameCard = member?.nameCard ?? '';
          break;
        }
      }
    }

    return nameCard;
  }

// 清空聊天记录
  Future<void> clearGroupMessage(String groupID) async {
    final bool? confirm = await ConfirmDialog.show(
      context: context,
      title: TIM_t("清空聊天记录") + ' ？',
    );

    if (confirm == true) {
      // 调用SDK清空聊天记录
      final res =
          await widget.sdkInstance.getMessageManager().clearGroupHistoryMessage(
                groupID: groupID,
              );

      if (res.code == 0) {
        // 清空成功
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(TIM_t("聊天记录已清空")),
            duration: const Duration(seconds: 1),
          ),
        );

        // 刷新聊天页面
        _timuiKitChatController.clearHistory(groupID);
      } else {
        // 清空失败
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(TIM_t("清空聊天记录失败")),
            duration: const Duration(seconds: 1),
          ),
        );
      }
    }
  }

// 获取群成员列表
  Future<void> _getGroupMemberList() async {
    // 使用serviceLocator获取GroupServices的具体实现实例
    final groupServices = serviceLocator<GroupServices>();
    final res = await groupServices.getGroupMemberList(
      groupID: widget.groupID,
      filter: GroupMemberFilterTypeEnum.V2TIM_GROUP_MEMBER_FILTER_ALL,
      nextSeq: "0",
      count: 100,
    );

    if (res.code == 0 && res.data != null) {
      memberList = res.data!.memberInfoList;
      setState(() {});
      debugPrint('群成员数量: ${memberList?.length}');
    }
  }

// 退出群组
  Future<void> _exitGroup() async {
    final bool? confirm = await ConfirmDialog.show(
      context: context,
      title: TIM_t("是否退出群聊？"),
      content: TIM_t("退出群聊后，群聊信息将被删除，群聊成员将无法再收到群聊消息。"),
    );

    if (confirm == true) {
      // 调用SDK退出群组
      final res = await widget.sdkInstance.quitGroup(groupID: widget.groupID);
      debugPrint('退出群组结果: ${res.toJson()}');
      if (res.code == 0) {
        // 退出成功
        await widget.sdkInstance
            .getConversationManager()
            .deleteConversation(conversationID: "group_${widget.groupID}");
        // 返回home页
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(
              builder: (context) => const HomePage()), // 替换为您的首页组件
          (route) => false, // 移除所有路由
        );
        ToastUtils.toast('退出群组成功');
      } else {
        // 退出失败
        ToastUtils.toast('退出群组失败');
      }
    }
  }

  // 转让群组
  _transmitOwner(BuildContext context, String groupID) async {
    List<V2TimGroupMemberFullInfo>? selectedMember = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SelectNewGroupOwner(
          model: widget._groupProfileModel,
          groupID: groupID,
        ),
      ),
    );
    if (selectedMember != null) {
      final userID = selectedMember.first.userID;
      await widget.sdkInstance
          .getGroupManager()
          .transferGroupOwner(groupID: groupID, userID: userID);
    }
  }

// 修改群名称
  Future<void> setGroupName() async {
    final String? newGroupName = await InputDialog.show(
      context: context,
      title: "修改群名称",
      hintText: "请输入群名称",
      initialValue: widget._groupProfileModel.groupInfo!.groupName.toString(),
    );
    if (newGroupName != null && newGroupName.isNotEmpty) {
      // 调用腾讯云IM SDK修改群名称
      await widget.sdkInstance.getGroupManager().setGroupInfo(
            info: V2TimGroupInfo(
                groupName: newGroupName,
                groupID: widget.groupID,
                groupType: widget._groupProfileModel.groupInfo!.groupType),
          );

      // 刷新群组信息
      await widget._groupProfileModel
          .loadGroupMemberList(groupID: widget.groupID);

      if (mounted) {
        // 显示修改成功提示
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(TIM_t("群名称修改成功")),
            duration: const Duration(seconds: 1),
          ),
        );

        // 刷新页面
        setState(() {});
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(
      context,
      designSize: const Size(375, 812),
      minTextAdapt: true,
    );
    return TencentPage(
        child: Scaffold(
            appBar: AppBar(
                title: Text(
                  TIM_t("群聊"),
                  style: TextStyle(color: hexToColor("1f2329"), fontSize: 16),
                ),
                shadowColor: Colors.white,
                backgroundColor: Colors.white,
                surfaceTintColor: Colors.white,
                leading: IconButton(
                  padding: const EdgeInsets.only(left: 16),
                  icon: Icon(
                    Icons.arrow_back_ios,
                    color: hexToColor("2a2e35"),
                    size: 20,
                  ),
                  onPressed: () {
                    Navigator.pop(context);
                  },
                )),
            body: SafeArea(
                child: Container(
              color: const Color(0xFFF9F9F9),
              child: TIMUIKitGroupProfile(
                backGroundColor: const Color(0xFFF9F9F9),
                lifeCycle: GroupProfileLifeCycle(didLeaveGroup: () async {
                  // Shows navigating back to the home page.
                  // You can customize the reaction here.
                  if (PlatformUtils().isWeb) {
                    Navigator.of(context).pop();
                    Navigator.of(context).pop();
                  } else {
                    Navigator.of(context)
                        .popUntil(ModalRoute.withName("/homePage"));
                  }
                }),
                groupID: widget.groupID,
                builder: (context, groupInfo, groupMemberList) =>
                    _builder(context, groupInfo, groupMemberList),
                onClickUser: (V2TimGroupMemberFullInfo memberInfo, _) {
                  if (memberInfo.userID !=
                      widget._selfInfoViewModel.loginInfo?.userID) {
                    widget._friendShipViewModel
                        .isFriend(memberInfo.userID)
                        .then((isFriend) {
                      if (!isFriend) {
                        V2TimUserFullInfo friendInfo = V2TimUserFullInfo(
                            userID: memberInfo.userID,
                            nickName: memberInfo.nickName,
                            faceUrl: memberInfo.faceUrl);
                        Navigator.pushReplacement(
                            context,
                            MaterialPageRoute(
                                builder: (context) => SendApplication(
                                    friendInfo: friendInfo,
                                    model: widget._selfInfoViewModel)));
                      } else {
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  UserProfile(userID: memberInfo.userID),
                            ));
                      }
                    });
                  }
                },
                profileWidgetBuilder:
                    GroupProfileWidgetBuilder(searchMessage: () {
                  return TIMUIKitGroupProfileWidget.searchMessage(
                      (V2TimConversation? conversation) {
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => Search(
                            onTapConversation: (V2TimConversation conversation,
                                V2TimMessage? targetMsg) {
                              Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => Chat(
                                      selectedConversation: conversation,
                                      initFindingMsg: targetMsg,
                                    ),
                                  ));
                            },
                            conversation: conversation,
                          ),
                        ));
                  });
                }),
              ),
            ))),
        name: 'groupProfile');
  }

  Widget _builder(ontext, groupInfo, groupMemberList) {
    final bool isAdmin = groupInfo?.role ==
            GroupMemberRoleType.V2TIM_GROUP_MEMBER_ROLE_OWNER ||
        groupInfo?.role == GroupMemberRoleType.V2TIM_GROUP_MEMBER_ROLE_ADMIN;
    final bool isOwner =
        groupInfo?.role == GroupMemberRoleType.V2TIM_GROUP_MEMBER_ROLE_OWNER;
    num moreCount = isAdmin ? 2 : 1;
    final option1 = memberList!.length;
    return Container(
        child: Column(
      children: [
        GestureDetector(
          onTap: () {
            setGroupName();
          },
          child: Container(
              color: Colors.white,
              margin: const EdgeInsets.only(top: 12),
              padding: const EdgeInsets.only(
                  top: 16, bottom: 16, left: 16, right: 16),
              child: Row(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(23),
                    child: Image.asset(
                      'assets/group_cover.png',
                      width: 46,
                      height: 46,
                      fit: BoxFit.cover,
                    ),
                  ),
                  const SizedBox(
                    width: 16,
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        groupInfo?.groupName ?? "",
                        style: TextStyle(
                            fontSize: 14.sp, fontWeight: FontWeight.w500),
                      ),
                      const SizedBox(
                        height: 8,
                      ),
                      Text(
                        groupInfo?.groupID ?? "",
                        style: const TextStyle(
                            fontSize: 12, color: Color(0xFF666666)),
                      ),
                    ],
                  )
                ],
              )),
        ),
        Container(
          color: Colors.white,
          margin: const EdgeInsets.only(top: 12),
          padding:
              const EdgeInsets.only(top: 16, bottom: 16, left: 32, right: 32),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.only(bottom: 16),
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color: hexToColor("E5E5E5"),
                    ),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      TIM_t("群成员"),
                      style: const TextStyle(
                        fontSize: 14,
                        color: Color(0xFF666666),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          TIM_t_para("{{option1}}人", "$option1人")(
                              option1: option1),
                          style: const TextStyle(
                            fontSize: 14,
                            color: Color(0xFF666666),
                          ),
                        ),
                        const SizedBox(
                          width: 8,
                        ),
                        Image.asset('assets/icon_right.png',
                            width: 16, height: 16)
                      ],
                    )
                  ],
                ),
              ),
              const SizedBox(height: 16),
              GridView.builder(
                key: UniqueKey(),
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 5,
                  mainAxisSpacing: 8,
                  crossAxisSpacing: 8,
                  mainAxisExtent: 82,
                ),
                padding: EdgeInsets.zero,
                itemCount: memberList!.length + moreCount.toInt(),
                // 添加邀请和删除按钮
                itemBuilder: (context, index) {
                  if (index < memberList!.length) {
                    final member = memberList![index];
                    return GestureDetector(
                      onTap: () async {
                        if (member.userID !=
                            widget._selfInfoViewModel.loginInfo?.userID) {
                          widget._friendShipViewModel
                              .isFriend(member.userID)
                              .then((isFriend) {
                            if (!isFriend) {
                              V2TimUserFullInfo friendInfo = V2TimUserFullInfo(
                                  userID: member.userID,
                                  nickName: member.nickName,
                                  faceUrl: member.faceUrl);
                              Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) => SendApplication(
                                          friendInfo: friendInfo,
                                          model: widget._selfInfoViewModel)));
                            } else {
                              Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) =>
                                        UserProfile(userID: member.userID),
                                  ));
                            }
                          });
                        }
                      },
                      child: Column(
                        children: [
                          Avatar(
                            avatarUrl: member.faceUrl ?? '',
                            size: 46,
                            radius: 23,
                          ),
                          SizedBox(height: 8.h),
                          Text(
                            member.nameCard?.isNotEmpty == true
                                ? member.nameCard!
                                : (member.friendRemark?.isNotEmpty == true
                                    ? member.friendRemark!
                                    : (member.nickName?.isNotEmpty == true
                                        ? member.nickName!
                                        : member.userID)),
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: const Color(0xFF333333),
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    );
                  } else if (index == memberList!.length) {
                    // 邀请按钮
                    return GestureDetector(
                      onTap: () async {
                        final result = await Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) => AddGroupMemberPage(
                                    model: widget._groupProfileModel)));
                        if (result == true) {
                          _getGroupMemberList(); // 如果添加了成员，刷新数据
                        }
                      },
                      child: Column(children: [
                        Image.asset('assets/group_add.png',
                            width: 46, height: 46, fit: BoxFit.contain),
                      ]),
                    );
                  } else {
                    // 只有群主或管理员才显示删除按钮
                    final selfMember =
                        widget._groupProfileModel.groupMemberList?.firstWhere(
                      (member) =>
                          member?.userID ==
                          widget._selfInfoViewModel.loginInfo?.userID,
                      orElse: () => null,
                    );
                    final selfRole = selfMember?.role ??
                        GroupMemberRoleType.V2TIM_GROUP_MEMBER_ROLE_MEMBER;
                    final isAdmin = selfRole ==
                        GroupMemberRoleType.V2TIM_GROUP_MEMBER_ROLE_ADMIN;
                    final isOwner = selfRole ==
                        GroupMemberRoleType.V2TIM_GROUP_MEMBER_ROLE_OWNER;

                    if (isAdmin || isOwner) {
                      return GestureDetector(
                        onTap: () async {
                          final result = await Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => DeleteGroupMemberPage(
                                      model: widget._groupProfileModel)));
                          if (result) {
                            debugPrint('收到返回数据: ${result.toString()}');

                            setState(() {
                              _getGroupMemberList();
                            });
                          }
                        },
                        child: Column(children: [
                          Image.asset('assets/group_delete.png',
                              width: 46, height: 46, fit: BoxFit.contain),
                        ]),
                      );
                    } else {
                      return const SizedBox(); // 非管理员不显示删除按钮
                    }
                  }
                },
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),
        Container(
            color: Colors.white,
            padding:
                const EdgeInsets.only(top: 16, bottom: 16, left: 32, right: 32),
            child: InkWell(
                onTap: () async {
                  // 使用正确的方式获取会话
                  final result = await widget.sdkInstance
                      .getConversationManager()
                      .getConversation(
                          conversationID: "group_${widget.groupID}");
                  debugPrint('获取会话信息: ${result.data}');
                  if (result.data != null) {
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => Search(
                            onTapConversation: (V2TimConversation conversation,
                                V2TimMessage? targetMsg) {
                              Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => Chat(
                                      selectedConversation: conversation,
                                      initFindingMsg: targetMsg,
                                    ),
                                  ));
                            },
                            conversation: result.data,
                          ),
                        ));
                  }
                },
                child: Container(
                  width: double.infinity,
                  child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(TIM_t("查找聊天内容"),
                            style: TextStyle(
                                fontSize: 14.sp,
                                fontWeight: FontWeight.w500,
                                color: const Color(0xFF666666))),
                        Image.asset('assets/icon_right.png',
                            width: 16, height: 16)
                      ]),
                ))),
        const SizedBox(
          height: 12,
        ),
        Container(
            color: Colors.white,
            padding:
                const EdgeInsets.only(top: 16, bottom: 16, left: 32, right: 32),
            child: InkWell(
                onTap: () {
                  Navigator.push(context, MaterialPageRoute(builder: (context) {
                    final model = widget._groupProfileModel;
                    final String notification =
                        (model.groupInfo?.notification != null &&
                                model.groupInfo!.notification!.isNotEmpty)
                            ? model.groupInfo!.notification!
                            : '';

                    final bool isAdmin = model.groupInfo?.role ==
                            GroupMemberRoleType.V2TIM_GROUP_MEMBER_ROLE_OWNER ||
                        model.groupInfo?.role ==
                            GroupMemberRoleType.V2TIM_GROUP_MEMBER_ROLE_ADMIN;

                    if (isAdmin) {
                      // 管理员和群主跳转到编辑页面
                      return GroupProfileNotificationPage(
                          model: model, notification: notification);
                    } else {
                      // 普通用户跳转到查看页面
                      return Scaffold(
                        appBar: AppBar(
                          title: Text(
                            TIM_t("群公告"),
                            style: const TextStyle(
                                color: Color(0xFF333333), fontSize: 16),
                          ),
                        ),
                        body: SingleChildScrollView(
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: SelectableText(
                              notification,
                              style: const TextStyle(fontSize: 16),
                            ),
                          ),
                        ),
                      );
                    }
                  }));
                },
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(TIM_t("群公告")),
                    SizedBox(
                      height: 4.h,
                    ),
                    Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          (widget._groupProfileModel.groupInfo?.notification !=
                                      null &&
                                  widget._groupProfileModel.groupInfo!
                                      .notification!.isNotEmpty)
                              ? Expanded(
                                  child: Text(
                                  widget._groupProfileModel.groupInfo
                                          ?.notification ??
                                      '',
                                  style: TextStyle(
                                      fontSize: 12.sp,
                                      fontWeight: FontWeight.w500,
                                      color: const Color(0xFF999999)),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                  softWrap: true,
                                ))
                              : Text(TIM_t("暂无群公告"),
                                  style: TextStyle(
                                      fontSize: 12.sp,
                                      fontWeight: FontWeight.w500,
                                      color: const Color(0xFFBBBBBB))),
                          if (widget._groupProfileModel.groupInfo
                                      ?.notification !=
                                  null &&
                              widget._groupProfileModel.groupInfo!.notification!
                                  .isNotEmpty)
                            Padding(
                              padding: const EdgeInsets.only(left: 15),
                              child: Image.asset('assets/icon_right.png',
                                  width: 16, height: 16),
                            )
                        ])
                  ],
                ))),
        const SizedBox(height: 12),
        Container(
          color: Colors.white,
          padding: const EdgeInsets.only(left: 32, right: 32),
          child: Column(
            children: [
              Container(
                decoration: const BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color: Color(0xFFEEEEEE),
                      width: 1,
                    ),
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(TIM_t("置顶聊天"),
                          style: const TextStyle(
                            fontSize: 14,
                            color: Color(0xFF666666),
                            fontWeight: FontWeight.w500,
                          )),
                      SizedBox(
                        width: 52,
                        height: 26,
                        child: FittedBox(
                          fit: BoxFit.contain,
                          child: Switch(
                            value: isPined,
                            onChanged: (bool value) {
                              if (widget._groupProfileModel.groupInfo != null) {
                                _itemClick('pinedConversation', context,
                                    widget._groupProfileModel.groupInfo!);
                              } else {
                                debugPrint('会话数据为空');
                              }
                            },
                            activeColor: Colors.white,
                            activeTrackColor: const Color(0xff0072FC),
                            inactiveThumbColor: Colors.white,
                            inactiveTrackColor: Colors.grey[300],
                            materialTapTargetSize:
                                MaterialTapTargetSize.shrinkWrap,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Container(
                decoration: const BoxDecoration(),
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(TIM_t("消息免打扰"),
                          style: const TextStyle(
                            fontSize: 14,
                            color: Color(0xFF666666),
                            fontWeight: FontWeight.w500,
                          )),
                      SizedBox(
                        width: 52,
                        height: 26,
                        child: FittedBox(
                          fit: BoxFit.contain,
                          child: Switch(
                            value: isDisturb,
                            onChanged: (bool value) {
                              if (widget._groupProfileModel.groupInfo != null) {
                                _itemClick('setMessageDisturb', context,
                                    widget._groupProfileModel.groupInfo!);
                              } else {
                                debugPrint('会话数据为空');
                              }
                            },
                            activeColor: Colors.white,
                            activeTrackColor: const Color(0xff0072FC),
                            inactiveThumbColor: Colors.white,
                            inactiveTrackColor: Colors.grey[300],
                            materialTapTargetSize:
                                MaterialTapTargetSize.shrinkWrap,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),
        Container(
            color: Colors.white,
            padding:
                const EdgeInsets.only(top: 16, bottom: 16, left: 32, right: 32),
            child: GestureDetector(
                onTap: () {
                  _updateGroupNickname(widget.groupID);
                  // Navigator.push(context, MaterialPageRoute(builder: (context) => const SearchChatContentPage()));
                },
                child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(TIM_t("我的群昵称"),
                          style: TextStyle(
                              fontSize: 14.sp,
                              fontWeight: FontWeight.w500,
                              color: const Color(0xFF666666))),
                      Expanded(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Text(_getCurrentUserNameCard(),
                                style: TextStyle(
                                    fontSize: 14.sp,
                                    fontWeight: FontWeight.w500,
                                    color: const Color(0xFF666666))),
                            Image.asset('assets/remark_name.png',
                                width: 16, height: 16)
                          ],
                        ),
                      )
                    ]))),
        const SizedBox(height: 12),
        Container(
            padding: const EdgeInsets.only(left: 32, right: 32),
            color: Colors.white,
            child: Column(
              children: [
                Container(
                  decoration: const BoxDecoration(
                    border: Border(
                      bottom: BorderSide(
                        color: Color(0xFFEEEEEE),
                        width: 1,
                      ),
                    ),
                  ),
                  child: InkWell(
                    onTap: () async {
                      clearGroupMessage(widget.groupID);
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Center(
                        child: Text(
                          TIM_t("清空消息"),
                          style: const TextStyle(
                            fontSize: 16,
                            color: Color(0xffFF7677),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                Container(
                  decoration: const BoxDecoration(
                    border: Border(
                      bottom: BorderSide(
                        color: Color(0xFFEEEEEE),
                        width: 1,
                      ),
                    ),
                  ),
                  child: InkWell(
                    onTap: () async {
                      _exitGroup();
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Center(
                        child: Text(
                          TIM_t("退出群组"),
                          style: const TextStyle(
                            fontSize: 16,
                            color: Color(0xffFF7677),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                if (isOwner)
                  Container(
                    decoration: const BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          color: Color(0xFFEEEEEE),
                          width: 1,
                        ),
                      ),
                    ),
                    child: InkWell(
                      onTap: () async {
                        _transmitOwner(context, widget.groupID);
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Center(
                          child: Text(
                            TIM_t('转让群主'),
                            style: const TextStyle(
                              fontSize: 16,
                              color: Color(0xffFF7677),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
              ],
            )),
      ],
    ));
  }
}
