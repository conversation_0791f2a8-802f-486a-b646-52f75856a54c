import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';
import 'package:tencent_cloud_chat_sdk/enum/group_member_role.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_group_member_full_info.dart'
    if (dart.library.html) 'package:tencent_cloud_chat_sdk/web/compatible_models/v2_tim_group_member_full_info.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_group_member_search_param.dart'
    if (dart.library.html) 'package:tencent_cloud_chat_sdk/web/compatible_models/v2_tim_group_member_search_param.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_base.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_state.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/separate_models/tui_group_profile_model.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/screen_utils.dart';
import 'package:tencent_cloud_chat_uikit/ui/widgets/group_member_list.dart';
import 'package:tencent_cloud_chat_uikit/theme/tui_theme.dart';

GlobalKey<_DeleteGroupMemberPageState> deleteGroupMemberKey = GlobalKey();

class DeleteGroupMemberPage extends StatefulWidget {
  final TUIGroupProfileModel model;
  final VoidCallback? onClose;

  const DeleteGroupMemberPage({Key? key, required this.model, this.onClose})
      : super(key: key);

  @override
  State<StatefulWidget> createState() => _DeleteGroupMemberPageState();
}

class _DeleteGroupMemberPageState extends TIMUIKitState<DeleteGroupMemberPage> {
  List<V2TimGroupMemberFullInfo> selectedGroupMember = [];
  List<V2TimGroupMemberFullInfo?>? searchMemberList;

  bool isSearchTextExist(String? searchText) {
    return searchText != null && searchText != "";
  }

  handleSearchGroupMembers(String searchText, context) async {
    searchText = searchText;
    List<V2TimGroupMemberFullInfo?> currentGroupMember =
        Provider.of<TUIGroupProfileModel>(context, listen: false)
            .groupMemberList;
    final res =
        await widget.model.searchGroupMember(V2TimGroupMemberSearchParam(
      keywordList: [searchText],
      groupIDList: [widget.model.groupInfo!.groupID],
    ));

    if (res.code == 0) {
      List<V2TimGroupMemberFullInfo?> list = [];
      final searchResult = res.data!.groupMemberSearchResultItems!;
      searchResult.forEach((key, value) {
        if (value is List) {
          for (V2TimGroupMemberFullInfo item in value) {
            list.add(item);
          }
        }
      });

      currentGroupMember = list;
    } else {
      currentGroupMember = [];
    }
    setState(() {
      searchMemberList =
          isSearchTextExist(searchText) ? currentGroupMember : null;
    });
  }

  handleRole(groupMemberList) {
    return groupMemberList
            ?.where((value) =>
                value?.role ==
                GroupMemberRoleType.V2TIM_GROUP_MEMBER_ROLE_MEMBER)
            .toList() ??
        [];
  }

  void submitDelete() async {
    if (selectedGroupMember.isNotEmpty) {
      final userIDs = selectedGroupMember.map((e) => e.userID).toList();
      final groupID = widget.model.groupInfo?.groupID;

      print('准备删除成员: ${userIDs.join(", ")}');
      print('当前群ID: ${groupID ?? "未知"}');

      if (groupID != null) {
        await widget.model.kickOffMember(userIDs);
        print('删除成员完成，准备刷新列表');
        await widget.model.loadGroupMemberList(groupID: groupID);
        print('列表刷新完成，成员数量: ${widget.model.groupMemberList?.length ?? 0}');

        if (mounted) {
          Navigator.of(context).pop(true);
          print('返回上一页，并传递更新标志');
        }
      }
    }
  }

  @override
  Widget tuiBuild(BuildContext context, TUIKitBuildValue value) {
    final TUITheme theme = value.theme;

    return TUIKitScreenUtils.getDeviceWidget(
        context: context,
        desktopWidget: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: GroupProfileMemberList(
            memberList:
                handleRole(searchMemberList ?? widget.model.groupMemberList),
            canSelectMember: true,
            canSlideDelete: false,
            onSelectedMemberChange: (selectedMember) {
              selectedGroupMember = selectedMember;
            },
            touchBottomCallBack: () {},
          ),
        ),
        defaultWidget: Scaffold(
          appBar: AppBar(
              title: Text(
                TIM_t("删除群成员"),
                style: TextStyle(color: theme.appbarTextColor, fontSize: 17),
              ),
              actions: [
                // TextButton(
                //   onPressed: submitDelete,
                //   child: Text(
                //     TIM_t("确定"),
                //     style: TextStyle(
                //       color: theme.appbarTextColor,
                //       fontSize: 16,
                //     ),
                //   ),
                // )
                GestureDetector(
                  onTap: () {
                    submitDelete();
                  },
                  child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 4),
                      margin: const EdgeInsets.only(right: 16),
                      width: 64,
                      height: 32,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color: const Color(0xFF007AFF),
                      ),
                      child: Center(
                        child:
                            Text(TIM_t('确定'), style: const TextStyle(color: Colors.white)),
                      )),
                )
              ],
              shadowColor: theme.weakBackgroundColor,
              backgroundColor: Colors.white,
              surfaceTintColor: Colors.white,
              iconTheme: IconThemeData(
                color: theme.appbarTextColor,
              )),
          body: Container(
            color: const Color(0xFFF9F9F9),
            child: GroupProfileMemberList(
              memberList:
                  handleRole(searchMemberList ?? widget.model.groupMemberList),
              canSelectMember: true,
              canSlideDelete: false,
              onSelectedMemberChange: (selectedMember) {
                selectedGroupMember = selectedMember;
              },
              touchBottomCallBack: () {},
            ),
          ),
        ));
  }
}
