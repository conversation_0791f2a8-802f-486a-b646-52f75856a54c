'use strict';const e=2,t=4,o=10,s=11,r=12,i=14,n=15,u=20,a=22,p=23,l=26,c=27,h=29,g="onMessageReceived",d="onMessageModified",m="onGroupListUpdated",_="groupAttributesUpdated",M="onGroupCounterUpdated",f="onTopicUpdated",I="error";class y{constructor(e=0,t=0){this.high=e,this.low=t}equal(e){return null!==e&&(this.low===e.low&&this.high===e.high)}toString(){const e=Number(this.high).toString(16);let t=Number(this.low).toString(16);if(t.length<8){let e=8-t.length;for(;e;)t="0"+t,e--}return e+t}}const D={TEST:{CHINA:{DEFAULT:"wss://wss-dev.tim.qq.com"},OVERSEA:{DEFAULT:"wss://wss-dev.tim.qq.com"},SINGAPORE:{DEFAULT:"wss://wsssgp-dev.im.qcloud.com"},KOREA:{DEFAULT:"wss://wsskr-dev.im.qcloud.com"},GERMANY:{DEFAULT:"wss://wssger-dev.im.qcloud.com"},IND:{DEFAULT:"wss://wssind-dev.im.qcloud.com"},JPN:{DEFAULT:"wss://wssjpn-dev.im.qcloud.com"},USA:{DEFAULT:"wss://wssusa-dev.im.qcloud.com"}},PRODUCTION:{CHINA:{DEFAULT:"wss://wss.im.qcloud.com",BACKUP:"wss://wss.tim.qq.com",STAT:"https://events.im.qcloud.com",ANYCAST:"wss://162.14.13.203"},OVERSEA:{DEFAULT:"wss://wss.im.qcloud.com",BACKUP:"wss://wss.my-imcloud.com",STAT:"https://api.my-imcloud.com"},SINGAPORE:{DEFAULT:"wss://wsssgp.im.qcloud.com",BACKUP:"wss://wsssgp.my-imcloud.com",STAT:"https://apisgp.my-imcloud.com"},KOREA:{DEFAULT:"wss://wsskr.im.qcloud.com",BACKUP:"wss://wsskr.my-imcloud.com",STAT:"https://apikr.my-imcloud.com"},GERMANY:{DEFAULT:"wss://wssger.im.qcloud.com",BACKUP:"wss://wssger.my-imcloud.com",STAT:"https://apiger.my-imcloud.com"},IND:{DEFAULT:"wss://wssind.my-imcloud.com",BACKUP:"wss://wssind.im.qcloud.com",STAT:"https://apiind.my-imcloud.com"},JPN:{DEFAULT:"wss://wssjpn.im.qcloud.com",BACKUP:"wss://wssjpn.my-imcloud.com",STAT:"https://apijpn.my-imcloud.com"},USA:{DEFAULT:"wss://wssusa.im.qcloud.com",BACKUP:"wss://wssusa.my-imcloud.com",STAT:"https://apiusa.my-imcloud.com"}}},L={ANDROID:2,IOS:3,MAC:4,WEB:7,WX_MP:8,QQ_MP:9,TT_MP:10,BAIDU_MP:11,ALI_MP:12,IPAD:13,UNI_NATIVE_APP:15},G="CHINA",C={HOST:{CURRENT:{DEFAULT:"wss://wss.im.qcloud.com",STAT:"https://events.im.qcloud.com"},setCurrent(e=G){this.CURRENT=D.PRODUCTION[e]}},NAME:{OPEN_IM:"openim",OPEN_IM_MSG_EXT:"openim_msg_ext_http_svc",GROUP:"group_open_http_svc",GROUP_AVCHATROOM:"group_open_avchatroom_http_svc",GROUP_COMMUNITY:"million_group_open_http_svc",GROUP_ATTR:"group_open_attr_http_svc",FRIEND:"sns",PROFILE:"profile",RECENT_CONTACT:"recentcontact",PIC:"openpic",BIG_GROUP_NO_AUTH:"group_open_http_noauth_svc",BIG_GROUP_LONG_POLLING:"group_open_long_polling_http_svc",BIG_GROUP_LONG_POLLING_NO_AUTH:"group_open_long_polling_http_noauth_svc",IM_OPEN_STAT:"imopenstat",WEB_IM:"webim",IM_COS_SIGN:"im_cos_sign_svr",CUSTOM_UPLOAD:"im_cos_msg",HEARTBEAT:"heartbeat",IM_OPEN_PUSH:"im_open_push",IM_OPEN_STATUS:"im_open_status",IM_LONG_MESSAGE:"im_long_msg",IM_CONFIG_MANAGER:"im_sdk_config_mgr",STAT_SERVICE:"StatSvc",OVERLOAD_PUSH:"OverLoadPush",IM_MSG_AUDIT_MGR:"im_msg_audit_mgr",TUIROOM_SVR:"tui_room_svr",IM_OPEN_TRANSLATE:"im_open_translate",IM_OPEN_SPEECH:"im_open_speech",MESSAGE_SEARCH:"message_search"},CHANNEL:{SOCKET:1,XHR:2,AUTO:0},NAME_VERSION:{openim:"v4",group_open_http_svc:"v4",sns:"v4",profile:"v4",recentcontact:"v4",openpic:"v4",group_open_http_noauth_svc:"v4",group_open_long_polling_http_svc:"v4",group_open_long_polling_http_noauth_svc:"v4",imopenstat:"v4",im_cos_sign_svr:"v4",im_cos_msg:"v4",webim:"v4",im_open_push:"v4",im_open_status:"v4"}},b={SEARCH_MSG:new y(0,Math.pow(2,0)).toString(),SEARCH_GRP_SNS:new y(0,Math.pow(2,1)).toString(),AVCHATROOM_HISTORY_MSG:new y(0,Math.pow(2,2)).toString(),GRP_COMMUNITY:new y(0,Math.pow(2,3)).toString(),MSG_TO_SPECIFIED_GRP_MBR:new y(0,Math.pow(2,4)).toString(),AVCHATROOM_MBR_LIST:new y(0,Math.pow(2,6)).toString(),USER_STATUS:new y(0,Math.pow(2,7)).toString(),CONV_MARK:new y(0,Math.pow(2,9)).toString(),CONV_GROUP:new y(0,Math.pow(2,10)).toString(),AVCHATROOM_BAN_MBR:new y(0,Math.pow(2,11)).toString(),MSG_EXT:new y(0,Math.pow(2,13)).toString(),GRP_COUNTER:new y(0,Math.pow(2,15)).toString()},T="group_profile",A="group_member_profile";C.HOST.setCurrent(G);const S="undefined"!=typeof wx&&"function"==typeof wx.getSystemInfoSync&&Boolean(wx.getSystemInfoSync().fontSizeSetting),v="undefined"!=typeof qq&&"function"==typeof qq.getSystemInfoSync&&Boolean(qq.getSystemInfoSync().fontSizeSetting),w="undefined"!=typeof tt&&"function"==typeof tt.getSystemInfoSync&&Boolean(tt.getSystemInfoSync().fontSizeSetting),N="undefined"!=typeof swan&&"function"==typeof swan.getSystemInfoSync&&Boolean(swan.getSystemInfoSync().fontSizeSetting),R="undefined"!=typeof my&&"function"==typeof my.getSystemInfoSync&&Boolean(my.getSystemInfoSync().fontSizeSetting),k="undefined"!=typeof jd&&"function"==typeof jd.getSystemInfoSync,E="undefined"!=typeof uni&&"undefined"==typeof window&&"function"==typeof uni.requireNativePlugin,$=S||v||w||N||R||E||k,P=("undefined"!=typeof uni||"undefined"!=typeof window)&&!$;v?qq:w?tt:N?swan:R?my:S?wx:E?uni:!k||jd;const q=P&&window&&window.navigator&&window.navigator.userAgent||"",U=/(micromessenger|webbrowser)/i.test(q),O=/AppleWebKit\/([\d.]+)/i.exec(q);O&&parseFloat(O.pop());const F=function(){let e="WEB";return U?e="WEB":v?e="QQ_MP":w?e="TT_MP":N?e="BAIDU_MP":R?e="ALI_MP":S?e="WX_MP":E&&(e="UNI_NATIVE_APP"),L[e]}();!function(){const e=q.match(/OS (\d+)_/i);e&&e[1]&&e[1]}(),function(){const e=q.match(/Android (\d+)(?:\.(\d+))?(?:\.(\d+))*/i);if(!e)return null;const t=e[1]&&parseFloat(e[1]),o=e[2]&&parseFloat(e[2]);t&&o&&parseFloat(e[1]+"."+e[2])}(),function(){const e=q.match(/Chrome\/(\d+)/);e&&e[1]&&parseFloat(e[1])}();const x=/MSIE/.test(q)||q.indexOf("Trident")>-1&&q.indexOf("rv:11.0")>-1;!function(){const e=/MSIE\s(\d+)\.\d/.exec(q);let t=e&&parseFloat(e[1]);!t&&/Trident\/7.0/i.test(q)&&/rv:11.0/.test(q)&&(t=11)}(),function(){const e=q.match(/TBS\/(\d+)/i);if(e&&e[1])e[1]}();const V="TIMTextElem",H="TIMImageElem",j="TIMSoundElem",B="TIMFileElem",K="TIMFaceElem",J="TIMVideoFileElem",W="TIMLocationElem",z="TIMGroupTipElem",X="TIMGroupSystemNoticeElem",Y="TIMCustomElem",Q="TIMRelayElem",Z="High",ee="Normal",te="Low",oe="Lowest",se="C2C",re="GROUP",ie="TOPIC",ne="@TIM#SYSTEM",ue="Private",ae="Public",pe="ChatRoom",le="AVChatRoom",ce="Community",he="Owner",ge="Admin",de="Member",me="Custom",_e=1,Me=3,fe=4,Ie=5,ye="AcceptAndNotify",De="AlreadyInGroup",Le="__kImSDK_MesssageAtALL__";let Ge,Ce;Ge="undefined"!=typeof console?console:"undefined"!=typeof global&&global.console?global.console:"undefined"!=typeof window&&window.console?window.console:{};const be=function(){},Te=["assert","clear","count","debug","dir","dirxml","error","group","groupCollapsed","groupEnd","info","log","profile","profileEnd","table","time","timeEnd","timeStamp","trace","warn"];let Ae=Te.length;for(;Ae--;)Ce=Te[Ae],console[Ce]||(Ge[Ce]=be);var Se=Ge;const ve=function(){return(new Date).getTime()+0};let we=0;function Ne(){return nt()?"%c Chat %c":"Chat"}function Re(){const e=function(){const e=new Date;return e.setTime(ve()),e}();return e.toLocaleTimeString("en-US",{hour12:!1})+"."+function(e){let t;switch(e.toString().length){case 1:t="00"+e;break;case 2:t="0"+e;break;default:t=e}return t}(e.getMilliseconds())}const ke={arguments2String(e){let t="";if(1===e.length)t=e[0];else for(let o=0,s=e.length;o<s;o++)Ke(e[o])?Je(e[o])?t+=Xe(e[o]):t+=JSON.stringify(e[o]):t+=e[o],t+=" ";return t},_exec(e,t){nt()?Se[e](Ne(),"background:#0abf5b; padding:1px; border-radius:3px; color: #fff","background:transparent",Re(),t):Se[e](`${Ne()} ${Re()} ${t}`)},d:function(){if(we<=-1){const e=this.arguments2String(arguments);this._exec("debug",e)}},l:function(){if(we<=0){const e=this.arguments2String(arguments);this._exec("log",e)}},log:function(){if(we<=0){const e=this.arguments2String(arguments);this._exec("log",e)}},i:function(){if(we<=1){const e=this.arguments2String(arguments);this._exec("info",e)}},w:function(){if(we<=2){const e=this.arguments2String(arguments);this._exec("warn",e)}},e:function(){if(we<=3){const e=this.arguments2String(arguments);this._exec("error",e)}},setLevel:function(e){e<4&&this._exec("log","set level from "+we+" to "+e),we=e},getLevel:function(){return we}},Ee={JPG:1,JPEG:1,GIF:2,PNG:3,BMP:4,UNKNOWN:255},$e="Tag_Profile_IM_Nick",Pe="Tag_Profile_IM_Image",qe="JoinedSuccess",Ue="WaitAdminApproval",Oe="@TGS#_",Fe="@TOPIC#_",xe=function(e){return null!==e&&("number"==typeof e&&!isNaN(e-0)||"object"==typeof e&&e.constructor===Number)},Ve=function(e){return"string"==typeof e},He=function(e){if("object"!=typeof e||null===e)return!1;const t=Object.getPrototypeOf(e);if(null===t)return!0;let o=t;for(;null!==Object.getPrototypeOf(o);)o=Object.getPrototypeOf(o);return t===o},je=function(e){return"function"==typeof Array.isArray?Array.isArray(e):"array"===We(e)},Be=function(e){return void 0===e},Ke=function(e){return je(e)||function(e){return null!==e&&"object"==typeof e}(e)},Je=function(e){return e instanceof Error},We=function(e){return Object.prototype.toString.call(e).match(/^\[object (.*)\]$/)[1].toLowerCase()};Date.now||(Date.now=function(){return(new Date).getTime()});const ze=function(e,t,o,s){if(!Ke(e)||!Ke(t))return 0;let r=0;const i=Object.keys(t);let n;for(let u=0,a=i.length;u<a;u++)if(n=i[u],!(Be(t[n])||o&&o.includes(n)))if(Ke(e[n])&&Ke(t[n]))r+=ze(e[n],t[n],o,s);else{if(s&&s.includes(t[n]))continue;e[n]!==t[n]&&(e[n]=t[n],r+=1)}return r},Xe=function(e){return JSON.stringify(e,["message","code"])},Ye=function(e){const t=e||99999999;return Math.round(Math.random()*t)},Qe={},Ze=function(e){if(0===Object.getOwnPropertyNames(e).length)return Object.create(null);const t=Array.isArray(e)?[]:Object.create(null);let o="";for(const s in e)null!==e[s]?void 0!==e[s]?(o=typeof e[s],["string","number","function","boolean"].indexOf(o)>=0?t[s]=e[s]:t[s]=Ze(e[s])):t[s]=void 0:t[s]=null;return t};function et(e,t){if(!je(e)||!je(t))return!1;let o=!1;return t.forEach(({key:t,value:s})=>{const r=e.find(e=>e.key===t);r?r.value!==s&&(r.value=s,o=!0):(e.push({key:t,value:s}),o=!0)}),o}const ot=e=>e===le,st=({type:e,groupID:t})=>e===ce||(""+t).startsWith(Oe)&&!(""+t).includes(Fe),rt=e=>(""+e).startsWith(Oe)&&(""+e).includes(Fe);function it(e){return e.split(Fe)[0]}function nt(){return!x&&!$}function ut(e,t){if(!e)return;let o=e;return t&&(e.startsWith("http://")?o=e.replace(/^http:\/\/[^/]+/,t):e.startsWith("https://")&&(o=e.replace(/^https:\/\/[^/]+/,t))),o}const at=Object.prototype.hasOwnProperty;function pt(e){if(null==e)return!0;if("boolean"==typeof e)return!1;if("number"==typeof e)return 0===e;if("string"==typeof e)return 0===e.length;if("function"==typeof e)return 0===e.length;if(Array.isArray(e))return 0===e.length;if(e instanceof Error)return""===e.message;if(He(e)){for(const t in e)if(at.call(e,t))return!1;return!0}return!("map"!==We(e)&&!function(e){return"set"===We(e)}(e)&&!function(e){return"file"===We(e)}(e))&&0===e.size}const lt=function(e){return{code:0,data:e||{}}};class ct extends Error{constructor(e){super();const{code:t,message:o,data:s}=e;this.code=t,this.message=o||this._getErrorMessage(this.code),this.data=s||{}}}const ht=2101,gt=2114,dt=2600,mt=2601,_t=2602,Mt=2603,ft=2620,It=2621,yt=2622,Dt=2623,Lt=2660,Gt=2661,Ct=2662,bt=2681,Tt=2682,At=2683,St=2684,vt=2685,wt=2686,Nt=2687,Rt=2805,kt=2903,Et=3122,$t=3123;let Pt=null;const qt=function(e){return Promise.resolve(lt(e))},Ut=function(e,t=!1){if(e instanceof ct)return t&&null!==Pt&&Pt.emit(I,e),Promise.reject(e);if(e instanceof Error){const e=new ct({code:kt});return t&&null!==Pt&&Pt.emit(I,e),Promise.reject(e)}if(Be(e)||Be(e.code))return Promise.reject(new ct({code:kt}));const o=new ct(e);return t&&null!==Pt&&Pt.emit(I,o),Promise.reject(o)};const Ot={A2KEY_AND_TINYID_UPDATED:"_inner1",CLOUD_CONFIG_UPDATED:"_inner2",PROFILE_UPDATED:"_inner3",CONV_SYNC_COMPLETED:"_inner4",C2C_UNREAD_HANDLE_COMPLETED:"_inner5"},Ft="messageReceivedGroup",xt="messageReceivedGroupAVPush",Vt="messageReceivedGroupAVPull",Ht={info:4,warning:5,error:6},jt={wifi:1,"2g":2,"3g":3,"4g":4,"5g":5,unknown:6,none:7,online:8},Bt={login:4};class Kt{constructor(e){this._n="SSOLogData",this.eventType=Bt[e]||0,this.timestamp=0,this.networkType=8,this.code=0,this.message="",this.moreMessage="",this.extension=e,this.costTime=0,this.duplicate=!1,this.level=4,this.uiPlatform=void 0,this._sentFlag=!1,this._startts=ve()}static bindEventStatModule(e){Kt.prototype._eventStatModule=e}updateTimeStamp(){this.timestamp=ve()}start(e){return this._startts=e,this}end(e=!1){if(this._sentFlag)return;const t=ve();0===this.costTime&&(this.costTime=t-this._startts),this.setMoreMessage(`startts:${this._startts} endts:${t}`),e?(this._sentFlag=!0,this._eventStatModule&&this._eventStatModule.pushIn(this)):setTimeout(()=>{this._sentFlag=!0,this._eventStatModule&&this._eventStatModule.pushIn(this)},0)}setError(e,t,o){if(!(e instanceof Error))return ke.w(this._n+".setError value not instanceof Error, please check!"),this;if(this._sentFlag)return this;if(this.setNetworkType(o),t)e.code&&this.setCode(e.code),e.message&&this.setMoreMessage(e.message);else{const e=Rt;this.setCode(e)}return this.setLevel("error"),this}setCode(e){return Be(e)||this._sentFlag||("ECONNABORTED"===e&&(this.code=103),xe(e)?this.code=e:ke.w(this._n+".setCode value not a number, please check!",e,typeof e)),this}setMessage(e){return Be(e)||this._sentFlag||(xe(e)&&(this.message=e.toString()),Ve(e)&&(this.message=e)),this}setCostTime(e){return this.costTime=e,this}setLevel(e){return Be(e)||this._sentFlag||(this.level=Ht[e]),this}setMoreMessage(e){return pt(this.moreMessage)?this.moreMessage=""+e:this.moreMessage+=" "+e,this}setNetworkType(e){if(Be(e))ke.w(this._n+".setNetworkType value is undefined, please check!");else{const t=jt[e.toLowerCase()];Be(t)||(this.networkType=t)}return this}getStartTs(){return this._startts}setUIPlatform(e){this.uiPlatform=e}}const Jt="send_group_msg",Wt="get_joined_group_list",zt="get_group_self_member_info",Xt="create_group",Yt="destroy_group",Qt="modify_group_base_info",Zt="apply_join_group",eo="apply_join_group_noauth",to="quit_group",oo="get_group_public_info",so="change_group_owner",ro="handle_apply_join_group",io="handle_invite_join_permission_group",no="handle_invite_join_group",uo="group_msg_recall",ao="msg_read_report",po="group_msg_get",lo="get_group_msg_receipt",co="group_msg_receipt",ho="get_group_msg_receipt_detail",go="get_pendency",mo="deletemsg",_o="get_msg",Mo="get_msg_noauth",fo="get_online_member_num",Io="delete_group_ramble_msg_by_seq",yo="modify_group_msg",Do="set_group_attr",Lo="modify_group_attr",Go="delete_group_attr",Co="clear_group_attr",bo="get_group_attr",To="group_set_key_values",Ao="group_get_key_values",So="batch_get_group_notify",vo="update_group_counter",wo="get_group_counter",No="get_group_member_info",Ro="get_members",ko="get_specified_group_member_info",Eo="add_group_member",$o="delete_group_member",Po="ban_group_member",qo="modify_group_member_info",Uo="modify_user_info",Oo={UNSEND:"unSend",SUCCESS:"success",FAIL:"fail"},Fo={NOT_START:"notStart",PENDING:"pending",RESOLVED:"resolved",REJECTED:"rejected"};class xo{constructor(e){this.type=V,this.content={text:e.text||""}}setText(e){this.content.text=e}sendable(){return 0!==this.content.text.length}}class Vo{constructor(e,t){this._imageMemoryURL="",this._fileDownloadProxy=t,$?this.createImageDataASURLInWXMiniApp(e.file):this.createImageDataASURLInWeb(e.file),this._initImageInfoModel(),this.type=H,this._percent=0,this.content={imageFormat:e.imageFormat||Ee.UNKNOWN,uuid:e.uuid,imageInfoArray:[]},this.initImageInfoArray(e.imageInfoArray),this._autoFixUrl()}_initImageInfoModel(){const e=this;this._ImageInfoModel=function(t){this.instanceID=Ye(9999999),this.sizeType=t.type||0,this.type=0,this.size=t.size||0,this.width=t.width||0,this.height=t.height||0,this.imageUrl=t.url||"",this.url=ut(t.url||e._imageMemoryURL,e._fileDownloadProxy)},this._ImageInfoModel.prototype={setSizeType(e){this.sizeType=e},setType(e){this.type=e},setImageUrl(e){e&&(this.imageUrl=e)},getImageUrl(){return this.imageUrl}}}initImageInfoArray(e){let t=0,o=null,s=null;for(;t<=2;)s=Be(e)||Be(e[t])?{type:0,size:0,width:0,height:0,url:""}:e[t],o=new this._ImageInfoModel(s),o.setSizeType(t+1),o.setType(t),this.addImageInfo(o),t++;this.updateAccessSideImageInfoArray()}updateImageInfoArray(e){const t=this.content.imageInfoArray.length;let o;for(let s=0;s<t;s++)o=this.content.imageInfoArray[s],e[s].size&&(o.size=e[s].size),e[s].url&&o.setImageUrl(e[s].url),e[s].width&&(o.width=e[s].width),e[s].height&&(o.height=e[s].height)}_autoFixUrl(){const e=this.content.imageInfoArray.length;let t="",o="";const s=["http","https"];let r=null;for(let i=0;i<e;i++)this.content.imageInfoArray[i].url&&(r=this.content.imageInfoArray[i],""!==r.imageUrl&&(o=r.imageUrl.slice(0,r.imageUrl.indexOf("://")+1),t=r.imageUrl.slice(r.imageUrl.indexOf("://")+1),s.indexOf(o)<0&&(o="https:"),this.content.imageInfoArray[i].setImageUrl([o,t].join(""))))}updatePercent(e){this._percent=e,this._percent>1&&(this._percent=1)}updateImageFormat(e){this.content.imageFormat=Ee[e.toUpperCase()]||Ee.UNKNOWN}createImageDataASURLInWeb(e){void 0!==e&&e.files.length>0&&(this._imageMemoryURL=window.URL.createObjectURL(e.files[0]))}createImageDataASURLInWXMiniApp(e){e&&e.url&&(this._imageMemoryURL=e.url)}replaceImageInfo(e,t){this.content.imageInfoArray[t]instanceof this._ImageInfoModel||(this.content.imageInfoArray[t]=e)}addImageInfo(e){this.content.imageInfoArray.length>=3||this.content.imageInfoArray.push(e)}updateAccessSideImageInfoArray(){const e=this.content.imageInfoArray,{width:t=0,height:o=0}=e[0];0!==t&&0!==o&&(!function(e){const t=e[2];e[2]=e[1],e[1]=t;for(let o=0;o<e.length;o++)e[o].setType(o)}(e),Object.assign(e[2],function(e){const{originUrl:t,originWidth:o,originHeight:s,min:r=198}=e,i=parseInt(o),n=parseInt(s),u={url:void 0,width:0,height:0};if((i<=n?i:n)<=r)u.url=t,u.width=i,u.height=n;else{n<=i?(u.width=Math.ceil(i*r/n),u.height=r):(u.width=r,u.height=Math.ceil(n*r/i));const e=t&&t.indexOf("?")>-1?t+"&":t+"?";u.url=198===r?e+"imageView2/3/w/198/h/198":e+"imageView2/3/w/720/h/720"}if(Be(t)){const{url:e,...t}=u;return t}return u}({originWidth:t,originHeight:o,min:720})))}sendable(){return 0!==this.content.imageInfoArray.length&&(""!==this.content.imageInfoArray[0].imageUrl&&0!==this.content.imageInfoArray[0].size)}}class Ho{constructor(e){this.type=K,this.content=e||null}sendable(){return null!==this.content}}class jo{constructor(e,t){this.type=j,this._percent=0,this.content={downloadFlag:2,second:e.second,size:e.size,url:ut(e.url,t),remoteAudioUrl:e.url||"",uuid:e.uuid}}updatePercent(e){this._percent=e,this._percent>1&&(this._percent=1)}updateAudioUrl(e){this.content.remoteAudioUrl=e}sendable(){return""!==this.content.remoteAudioUrl}}const Bo={from:!0,groupID:!0,groupName:!0,to:!0};class Ko{constructor(e){this.type=z,this.content={},this._initContent(e)}_initContent(e){Object.keys(e).forEach(t=>{switch(t){case"remarkInfo":break;case"groupProfile":this.content.groupProfile={},this._initGroupProfile(e[t]);break;case"operatorInfo":break;case"memberInfoList":case"msgMemberInfo":this._updateMemberList(e[t]);break;case"memberExtraInfo":case"onlineMemberInfo":break;case"memberNum":this.content[t]=e[t],this.content.memberCount=e[t];break;case"newGroupProfile":this.content.newGroupProfile={},this._initNewGroupProfile(e[t]);break;default:this.content[t]=e[t]}}),this.content.userIDList||(this.content.userIDList=[this.content.operatorID])}_initGroupProfile(e){const t=Object.keys(e);for(let o=0;o<t.length;o++){const s=t[o];Bo[s]&&(this.content.groupProfile[s]=e[s])}}_updateMemberList(e){pt(this.content.memberList)?this.content.memberList=e:this.content.memberList.forEach(t=>{e.forEach(e=>{t.userID===e.userID&&Object.assign(t,e)})})}_initNewGroupProfile(e){const t=Object.keys(e);for(let o=0;o<t.length;o++){const s=t[o];"muteAllMembers"!==s?this.content.newGroupProfile[s]=e[s]:this.content.newGroupProfile[s]=1===e[s]}}}const Jo={from:!0,groupID:!0,groupName:!0,to:!0};class Wo{constructor(e){this.type=X,this.content={},this._initContent(e)}_initContent(e){Object.keys(e).forEach(t=>{switch(t){case"memberInfoList":break;case"remarkInfo":this.content.handleMessage=e[t];break;case"groupProfile":this.content.groupProfile={},this._initGroupProfile(e[t]);break;default:this.content[t]=e[t]}})}_initGroupProfile(e){const t=Object.keys(e);for(let o=0;o<t.length;o++){const s=t[o];Jo[s]&&("groupName"===s?this.content.groupProfile.name=e[s]:this.content.groupProfile[s]=e[s])}}}class zo{constructor(e,t){this.type=B,this._percent=0;const o=this._getFileInfo(e);this.content={downloadFlag:2,fileUrl:ut(e.url,t)||"",uuid:e.uuid,fileName:o.name||"",fileSize:o.size||0}}_getFileInfo(e){if(!Be(e.fileName)&&!Be(e.fileSize))return{size:e.fileSize,name:e.fileName};const t=e.file.files[0];if(E){if(t.path&&-1!==t.path.indexOf(".")){const e=t.path.slice(t.path.lastIndexOf(".")+1).toLowerCase();t.type=e,t.name||(t.name=`${Ye(999999)}.${e}`)}t.name||(t.type="",t.name=t.path.slice(t.path.lastIndexOf("/")+1).toLowerCase()),t.suffix&&(t.type=t.suffix),t.url||(t.url=t.path)}return{size:t.size,name:t.name}}updatePercent(e){this._percent=e,this._percent>1&&(this._percent=1)}updateFileUrl(e){this.content.fileUrl=e}sendable(){return""!==this.content.fileUrl&&(""!==this.content.fileName&&0!==this.content.fileSize)}}class Xo{constructor(e){this.type=Y,this.content={data:e.data||"",description:e.description||"",extension:e.extension||""}}setData(e){return this.content.data=e,this}setDescription(e){return this.content.description=e,this}setExtension(e){return this.content.extension=e,this}sendable(){return 0!==this.content.data.length||0!==this.content.description.length||0!==this.content.extension.length}}class Yo{constructor(e,t){this.type=J,this._percent=0,this.content={remoteVideoUrl:e.remoteVideoUrl||e.videoUrl||"",videoFormat:e.videoFormat,videoSecond:parseInt(e.videoSecond,10),videoSize:e.videoSize,videoUrl:ut(e.videoUrl,t),videoDownloadFlag:2,videoUUID:e.videoUUID,thumbUUID:e.thumbUUID,thumbFormat:e.thumbFormat,thumbWidth:e.thumbWidth,snapshotWidth:e.thumbWidth,thumbHeight:e.thumbHeight,snapshotHeight:e.thumbHeight,thumbSize:e.thumbSize,snapshotSize:e.thumbSize,thumbDownloadFlag:2,thumbUrl:ut(e.thumbUrl,t),snapshotUrl:ut(e.thumbUrl,t)}}updatePercent(e){this._percent=e,this._percent>1&&(this._percent=1)}updateVideoUrl(e){e&&(this.content.remoteVideoUrl=e)}updateSnapshotInfo(e){const{snapshotUrl:t,snapshotWidth:o,snapshotHeight:s}=e;pt(t)||(this.content.thumbUrl=this.content.snapshotUrl=t),pt(o)||(this.content.thumbWidth=this.content.snapshotWidth=Number(o)),pt(s)||(this.content.thumbHeight=this.content.snapshotHeight=Number(s))}sendable(){return""!==this.content.remoteVideoUrl}}class Qo{constructor(e){this.type=W;const{description:t,longitude:o,latitude:s}=e;this.content={description:t,longitude:o,latitude:s}}sendable(){return!0}}class Zo{constructor(e,t){if(this.from=e.from,this.messageSender=e.from,this.time=e.time,this.messageSequence=e.sequence,this.clientSequence=e.clientSequence||e.sequence,this.messageRandom=e.random,this.cloudCustomData=e.cloudCustomData||"",this.clientTime=e.clientTime||void 0,e.ID)this.ID=e.ID||"",this.nick=e.nick||"",this.avatar=e.avatar||"",this.messageBody=[{type:e.type,payload:e.payload}],e.conversationType.startsWith(se)?this.receiverUserID=e.to:e.conversationType.startsWith(re)&&(this.receiverGroupID=e.to),this.messageReceiver=e.to;else{this.nick=e.nick||"",this.avatar=e.avatar||"",this.messageBody=[];const o=e.elements[0].type,s=e.elements[0].content;this._patchRichMediaPayload(o,s),this._updateRichMediaDownloadUrl(o,s,t),o===Q?this.messageBody.push({type:o,payload:new es(s).content}):this.messageBody.push({type:o,payload:s}),e.groupID&&(this.receiverGroupID=e.groupID,this.messageReceiver=e.groupID),e.to&&(this.receiverUserID=e.to,this.messageReceiver=e.to),this.ID=`${e.tinyID}-${e.clientTime}-${e.random}`}}_patchRichMediaPayload(e,t){e===H?t.imageInfoArray.forEach(e=>{!e.imageUrl&&e.url&&(e.imageUrl=e.url,e.sizeType=e.type,1===e.type?e.type=0:3===e.type&&(e.type=1))}):e===J?!t.remoteVideoUrl&&t.videoUrl&&(t.remoteVideoUrl=t.videoUrl):e===j?!t.remoteAudioUrl&&t.url&&(t.remoteAudioUrl=t.url):e===B&&!t.fileUrl&&t.url&&(t.fileUrl=t.url,t.url=void 0)}_updateRichMediaDownloadUrl(e,t,o){o&&(e===H?t.imageInfoArray.forEach(e=>{e.url=ut(e.url,o)}):e===J?(t.videoUrl=ut(t.videoUrl,o),t.snapshotUrl=ut(t.thumbUrl,o),t.snapshotHeight=t.thumbHeight,t.snapshotWidth=t.thumbWidth):e===j?t.url=ut(t.url,o):e===B&&(t.fileUrl=ut(t.fileUrl,o)))}}var es=class{constructor(e,t){if(this.type=Q,this.content={downloadKey:"",pbDownloadKey:"",messageList:[],title:"",abstractList:[],compatibleText:"",version:0,layersOverLimit:!1},e.downloadKey){const{downloadKey:t,pbDownloadKey:o,title:s,abstractList:r,compatibleText:i,version:n}=e;this.content.downloadKey=t,this.content.pbDownloadKey=o,this.content.title=s,this.content.abstractList=r,this.content.compatibleText=i,this.content.version=n||0}else if(pt(e.messageList))1===e.layersOverLimit&&(this.content.layersOverLimit=!0);else{const{messageList:o,title:s,abstractList:r,compatibleText:i,version:n}=e,u=[];o.forEach(e=>{if(!pt(e)){const o=new Zo(e,t);u.push(o)}}),this.content.messageList=u,this.content.title=s,this.content.abstractList=r,this.content.compatibleText=i,this.content.version=n||0}}sendable(){return!pt(this.content.messageList)||!pt(this.content.downloadKey)}};const ts={1:Z,2:ee,3:te,4:oe};class os{constructor(e){this.ID="",this.conversationID=e.conversationID||null,this.conversationType=e.conversationType||se,this.conversationSubType=e.conversationSubType,this.time=e.time||Math.ceil(Date.now()/1e3),this.sequence=e.sequence||0,this.clientSequence=e.clientSequence||e.sequence||0,this.random=e.random||0===e.random?e.random:Ye(),this.priority=this._computePriority(e.priority),this.nick=e.nick||"",this.avatar=e.avatar||"",this.isPeerRead=!1,this.nameCard="",this.hasRiskContent=function(e){let t=!1;return e&&e>1&&(t=!0),t}(e.checkResult),this._elements=[],this.isPlaceMessage=e.isPlaceMessage||0,this.isRevoked=2===e.isPlaceMessage||8===e.msgFlagBits,this.from=e.from||null,this.to=e.to||null,this.flow="",this.isSystemMessage=e.isSystemMessage||!1,this.protocol=e.protocol||"JSON",this.isResend=!1,this.isRead=!1,this.status=e.status||Oo.SUCCESS,this._onlineOnlyFlag=!1,this._groupAtInfoList=[],this._relayFlag=!1,this.atUserList=[],this.cloudCustomData=e.cloudCustomData||"",this.isDeleted=!1,this.isModified=!1,this._isExcludedFromUnreadCount=!(!e.messageControlInfo||1!==e.messageControlInfo.excludedFromUnreadCount),this._isExcludedFromLastMessage=!(!e.messageControlInfo||1!==e.messageControlInfo.excludedFromLastMessage),this.clientTime=e.clientTime||Math.floor(ve()/1e3)||0,this.senderTinyID=e.senderTinyID||e.tinyID||"",this.readReceiptInfo=e.readReceiptInfo||{readCount:void 0,unreadCount:void 0,isPeerRead:void 0},this.needReadReceipt=!0===e.needReadReceipt||1===e.needReadReceipt,this.version=e.messageVersion||0,this.isBroadcastMessage=e.isBroadcastMessage||!1,this._receiverList=e.receiverList||void 0,this.isSupportExtension=!0===e.isSupportExtension||1===e.isSupportExtension,this.revoker=e.revokerInfo&&e.revokerInfo.revoker||"",this.revokerInfo=e.revokerInfo||{userID:"",nick:"",avatar:""},this.revokeReason=e.revokeReason||"",this.reInitialize(e.currentUser),this.extractGroupInfo(e.groupProfile||null),this.handleGroupAtInfo(e),this.initC2CReadReceiptInfo(e.readReceiptSentByPeer)}get elements(){return this._elements}getElements(){return this._elements}extractGroupInfo(e){if(null===e)return;Ve(e.nick)&&(this.nick=e.nick),Ve(e.avatar)&&(this.avatar=e.avatar);const{messageFromAccountExtraInformation:t}=e;He(t)&&Ve(t.nameCard)&&(this.nameCard=t.nameCard)}handleGroupAtInfo(e){e.payload&&e.payload.atUserList&&e.payload.atUserList.forEach(e=>{e!==Le?(this._groupAtInfoList.push({groupAtAllFlag:0,groupAtUserID:e}),this.atUserList.push(e)):(this._groupAtInfoList.push({groupAtAllFlag:1}),this.atUserList.push(Le))}),je(e.groupAtInfo)&&e.groupAtInfo.forEach(e=>{0===e.groupAtAllFlag?this.atUserList.push(e.groupAtUserID):1===e.groupAtAllFlag&&this.atUserList.push(Le)})}getGroupAtInfoList(){return this._groupAtInfoList}_initProxy(){this._elements[0]&&(this.payload=this._elements[0].content,this.type=this._elements[0].type)}reInitialize(e){e&&(this.status=this.from?Oo.SUCCESS:Oo.UNSEND,!this.from&&(this.from=e)),this._initFlow(e),this._initSequence(e),this._concatConversationID(e),this.generateMessageID()}isSendable(){return 0!==this._elements.length&&("function"==typeof this._elements[0].sendable&&this._elements[0].sendable())}_initTo(e){this.conversationType===re&&(this.to=e.groupID)}_initSequence(e){0===this.clientSequence&&e&&(this.clientSequence=function(e){if(!e)return!1;if(void 0===Qe[e]){const t=new Date;let o=("3"+t.getHours()).slice(-2),s=("0"+t.getMinutes()).slice(-2),r=("0"+t.getSeconds()).slice(-2);Qe[e]=parseInt([o,s,r,"0001"].join("")),o=null,s=null,r=null,ke.l("autoIncrementIndex start index:"+Qe[e])}return Qe[e]++}(e)),0===this.sequence&&this.conversationType===se&&(this.sequence=this.clientSequence)}generateMessageID(){this.from===ne&&(this.senderTinyID="144115198244471703"),this.ID=`${this.senderTinyID}-${this.clientTime}-${this.random}`}_initFlow(e){""!==e&&(e===this.from?(this.flow="out",this.isRead=!0):this.flow="in")}_concatConversationID(e){const{to:t}=this;let o="";const s=this.conversationType;s!==ne?(o=s===se?e===this.from?t:this.from:this.to,this.conversationID=o?`${s}${o}`:null):this.conversationID=ne}isElement(e){return e instanceof xo||e instanceof Vo||e instanceof Ho||e instanceof jo||e instanceof zo||e instanceof Yo||e instanceof Ko||e instanceof Wo||e instanceof Xo||e instanceof Qo||e instanceof es}setElement(e,t){if(this.isElement(e))return this._elements=[e],void this._initProxy();const o=e=>{if(e.type&&e.content)switch(e.type){case V:this.setTextElement(e.content);break;case H:this.setImageElement(e.content,t);break;case j:this.setAudioElement(e.content,t);break;case B:this.setFileElement(e.content,t);break;case J:this.setVideoElement(e.content,t);break;case Y:this.setCustomElement(e.content);break;case W:this.setLocationElement(e.content);break;case z:this.setGroupTipElement(e.content);break;case X:this.setGroupSystemNoticeElement(e.content);break;case K:this.setFaceElement(e.content);break;case Q:this.setMergerElement(e.content,t)}};if(je(e))for(let s=0;s<e.length;s++)o(e[s]);else o(e);this._initProxy()}clearElement(){this._elements.length=0}setTextElement(e){const t="string"==typeof e?e:e.text,o=new xo({text:t});this._elements.push(o)}setImageElement(e,t){const o=new Vo(e,t);this._elements.push(o)}setAudioElement(e,t){const o=new jo(e,t);this._elements.push(o)}setFileElement(e,t){const o=new zo(e,t);this._elements.push(o)}setVideoElement(e,t){const o=new Yo(e,t);this._elements.push(o)}setLocationElement(e){const t=new Qo(e);this._elements.push(t)}setCustomElement(e){const t=new Xo(e);this._elements.push(t)}setGroupTipElement(e){let t={};const o=e.operationType;if(pt(e.memberInfoList)?e.operatorInfo&&(t=e.operatorInfo):o!==_e&&o!==Me&&o!==fe&&o!==Ie||(t=e.memberInfoList[0]),!pt(e.memberExtraInfo)){const{reason:t}=e.memberExtraInfo;e.msgMemberInfo.forEach(e=>{e.reason=t})}const{nick:s,avatar:r}=t;Ve(s)&&(this.nick=s),Ve(r)&&(this.avatar=r);const i=new Ko(e);this._elements.push(i)}setGroupSystemNoticeElement(e){const t=new Wo(e);this._elements.push(t)}setFaceElement(e){const t=new Ho(e);this._elements.push(t)}setMergerElement(e,t){const o=new es(e,t);this._elements.push(o)}setIsRead(e){this.isRead=e}setRelayFlag(e){this._relayFlag=e}getRelayFlag(){return this._relayFlag}_computePriority(e){if(Be(e))return ee;if(Ve(e)&&-1!==Object.values(ts).indexOf(e))return e;if(xe(e)){const t=""+e;if(-1!==Object.keys(ts).indexOf(t))return ts[t]}return ee}setNickAndAvatar(e){const{nick:t,avatar:o}=e;Ve(t)&&(this.nick=t),Ve(o)&&(this.avatar=o)}setNameCard(e){Ve(e)&&(this.nameCard=e)}initC2CReadReceiptInfo(e){this.conversationType===se&&!0===this.needReadReceipt&&(this.readReceiptInfo.isPeerRead=1===e)}}class ss{constructor(e){this._groupModule=e,this._n="GroupTipsHandler",this._cachedGroupTipsMap=new Map,this._checkCountMap=new Map,this.MAX_CHECK_COUNT=4,this._getTopicPendingMap=new Map}onCheckTimer(e){e%1==0&&this._cachedGroupTipsMap.size>0&&this._checkCachedGroupTips()}_checkCachedGroupTips(){this._cachedGroupTipsMap.forEach((e,t)=>{let o=this._checkCountMap.get(t);const s=this._groupModule.hasLocalGroup(t);ke.l(`${this._n}._checkCachedGroupTips groupID:${t} hasLocalGroup:${s} checkCount:${o}`),s?(this._notifyCachedGroupTips(t),this._checkCountMap.delete(t),this._groupModule.deleteUnjoinedAVChatRoom(t)):o>=this.MAX_CHECK_COUNT?(this._deleteCachedGroupTips(t),this._checkCountMap.delete(t)):(o++,this._checkCountMap.set(t,o))})}onNewGroupTips(e){ke.d(`${this._n}.onReceiveGroupTips count:${e.dataList.length}`);const{eventDataList:t,result:o,AVChatRoomMessageList:r}=this.newGroupTipsStoredAndSummary(e);if(r.length>0&&this._groupModule.onAVChatRoomMessage(r),t.length>0){this._groupModule.updateNextMessageSeq(t);this._groupModule.getModule(s).onNewMessage({conversationOptionsList:t,isInstantMessage:!0})}o.length>0&&(this._groupModule.emitOuterEvent(g,o),this.handleMessageList(o))}newGroupTipsStoredAndSummary(e){const{event:t,dataList:r}=e;let i=null;const n=[],u=[],a={},p=[];for(let l=0,c=r.length;l<c;l++){const e=Ze(r[l]);if(6===t){if(this._groupModule.isGroupAttributesUpdatedNotice(e))continue;if(this._groupModule.isGroupCountersNotice(e))continue}const{groupProfile:{groupID:c,communityType:h=0,topicID:g,invisible:d}}=e;let m=void 0;const _=this._groupModule.isMessageFromTopic(h,g);if(_){m=ie,e.to=g;const t=this._groupModule.getModule(o);t.hasLocalTopic(c,g)||this._getTopicPendingMap.has(g)||(this._getTopicPendingMap.set(g,1),t.getTopicList({groupID:c,topicIDList:[g]}).finally(()=>{this._getTopicPendingMap.delete(g)}))}const M=this._groupModule.hasLocalGroup(c);if(!M&&this._groupModule.isUnjoinedAVChatRoom(c))continue;if(!M&&!_){this._cacheGroupTipsAndProbe({groupID:c,event:t,item:e});continue}if(this._groupModule.isMessageFromOrToAVChatroom(c)){e.event=t,p.push(e);continue}if(e.currentUser=this._groupModule.getMyUserID(),e.conversationType=re,i=new os(e),i.setElement({type:z,content:{...e.elements,groupProfile:e.groupProfile}}),i.isSystemMessage=!1,1===d){this._qualityStat(i);continue}const f=this._groupModule.getModule(s),{conversationID:I,sequence:y}=i;if(6===t)i._onlineOnlyFlag=!0,u.push(i);else{if(!f.pushIntoNoticeResult(u,i))continue}if(this._groupModule.isMessageFromCommunityOfTopic(h,g))continue;if(6===t&&f.getLocalConversation(I))continue;6!==t&&this._qualityStat(i);const D=f.isRemoteRead({conversationID:I,sequence:y});if(Be(a[I])){let e=0;"in"===i.flow&&(i._isExcludedFromUnreadCount||i._onlineOnlyFlag||D||(e=1)),a[I]=n.push({conversationID:I,unreadCount:e,type:Be(m)?i.conversationType:m,subType:i.conversationSubType,lastMessage:i._isExcludedFromLastMessage?"":i})-1}else{const e=a[I];n[e].type=i.conversationType,n[e].subType=i.conversationSubType,n[e].lastMessage=i._isExcludedFromLastMessage?"":i,"in"===i.flow&&(i._isExcludedFromUnreadCount||i._onlineOnlyFlag||D||n[e].unreadCount++)}}return{eventDataList:n,result:u,AVChatRoomMessageList:p}}_qualityStat(e){this._groupModule.getModule(l).addMessageSequence({key:Ft,message:e})}handleMessageList(e){e.forEach(e=>{switch(e.payload.operationType){case 1:this._onNewMemberComeIn(e);break;case 2:this._onMemberQuit(e);break;case 3:this._onMemberKickedOut(e);break;case 4:this._onMemberSetAdmin(e);break;case 5:this._onMemberCancelledAdmin(e);break;case 6:this._onGroupProfileModified(e);break;case 7:this._onMemberInfoModified(e);break;case 8:this._onTopicProfileUpdated(e);break;default:ke.w(`${this._n}.handleMessageList unknown operationType:${e.payload.operationType}`)}})}_onNewMemberComeIn(e){const{memberNum:t,groupProfile:{groupID:o}}=e.payload,s=this._groupModule.getLocalGroupProfile(o);s&&xe(t)&&s.memberCount!==t&&(s.memberCount=t,this._updateConversationGroupProfile(s))}_onMemberQuit(e){const{memberNum:t,groupProfile:{groupID:o}}=e.payload,s=this._groupModule.getLocalGroupProfile(o);s&&xe(t)&&s.memberCount!==t&&(s.memberCount=t,this._updateConversationGroupProfile(s));this._groupModule.getGroupMemberHandler().deleteLocalGroupMembers(o,e.payload.userIDList)}_onMemberKickedOut(e){const{memberNum:t,groupProfile:{groupID:o}}=e.payload,s=this._groupModule.getLocalGroupProfile(o);s&&xe(t)&&s.memberCount!==t&&(s.memberCount=t,this._updateConversationGroupProfile(s));this._groupModule.getGroupMemberHandler().deleteLocalGroupMembers(o,e.payload.userIDList)}_updateConversationGroupProfile(e){this._groupModule.getModule(s).updateConversationGroupProfile([e])}_onMemberSetAdmin(e){const t=e.payload.groupProfile.groupID,o=e.payload.userIDList,s=this._groupModule.getGroupMemberHandler();o.forEach(e=>{const o=s.getLocalGroupMemberInfo(t,e);o&&o.updateRole(ge)})}_onMemberCancelledAdmin(e){const t=e.payload.groupProfile.groupID,o=e.payload.userIDList,s=this._groupModule.getGroupMemberHandler();o.forEach(e=>{const o=s.getLocalGroupMemberInfo(t,e);o&&o.updateRole(de)})}_onGroupProfileModified(e){const{newGroupProfile:t,groupProfile:o}=e.payload,{groupID:s}=o,r=this._groupModule.getLocalGroupProfile(s);Object.keys(t).forEach(e=>{switch(e){case"ownerID":this._ownerChanged(r,t);break;case"groupName":r.name=t[e];break;default:r[e]=t[e]}});const i=!r.isSupportTopic;this._groupModule.emitGroupListUpdate(!0,i)}_ownerChanged({groupID:e},t){const o=this._groupModule.getLocalGroupProfile(e),s=this._groupModule.getMyUserID();if(s===t.ownerID){o.updateGroup({selfInfo:{role:he}});const t=this._groupModule.getGroupMemberHandler(),r=t.getLocalGroupMemberInfo(e,s),i=this._groupModule.getLocalGroupProfile(e).ownerID,n=t.getLocalGroupMemberInfo(e,i);r&&r.updateRole(he),n&&n.updateRole(de)}}_onMemberInfoModified(e){const{to:t,payload:{groupProfile:o,memberList:s}}=e,r=o.groupID;rt(t)&&this._updateTopicMuteTime(e);const i=this._groupModule.getGroupMemberHandler();s.forEach(e=>{const t=i.getLocalGroupMemberInfo(r,e.userID);t&&xe(e.muteTime)&&t.updateMuteUntil(e.muteTime)})}_updateTopicMuteTime(e){const{to:t,payload:{groupProfile:s,memberList:r=[]}}=e,i=this._groupModule.getModule(o),{groupID:n}=s,u=i.getLocalTopic(n,t);if(u){let e=!1;for(let t=0;t<r.length;t++){const o=r[t];if(o.userID===this._groupModule.getMyUserID()&&o.muteTime>=0){u.updateSelfInfo({muteTime:o.muteTime}),e=!0;break}}e&&this._groupModule.emitOuterEvent(f,{groupID:n,topic:u})}}_onTopicProfileUpdated(e){const{groupProfile:{groupID:t},newTopicInfo:s}=e.payload;this._groupModule.getModule(o).onTopicProfileUpdated({groupID:t,topicID:e.to,...s})}_cacheGroupTips(e,t){this._cachedGroupTipsMap.has(e)||this._cachedGroupTipsMap.set(e,[]),this._cachedGroupTipsMap.get(e).push(t)}_deleteCachedGroupTips(e){this._cachedGroupTipsMap.has(e)&&this._cachedGroupTipsMap.delete(e)}_notifyCachedGroupTips(e){const t=this._cachedGroupTipsMap.get(e)||[];t.forEach(e=>{this.onNewGroupTips(e)}),this._deleteCachedGroupTips(e),ke.l(`${this._n}._notifyCachedGroupTips groupID:${e} count:${t.length}`)}_cacheGroupTipsAndProbe(e){const{groupID:t,event:o,item:s}=e;this._cacheGroupTips(t,{event:o,dataList:[s]}),this._groupModule.getGroupSimplifiedInfo(t).then(e=>{const{type:o}=e;o===le?this._groupModule.hasLocalGroup(t)?this._notifyCachedGroupTips(t):this._groupModule.setUnjoinedAVChatRoom(t):(this._groupModule.updateGroupMap([e]),this._notifyCachedGroupTips(t))}),this._checkCountMap.has(t)||this._checkCountMap.set(t,0),ke.l(`${this._n}._cacheGroupTipsAndProbe groupID:${t}`)}reset(){this._cachedGroupTipsMap.clear(),this._checkCountMap.clear(),this._getTopicPendingMap.clear()}}class rs{constructor(e){this._groupModule=e,this._n="CommonGroupHandler",this.tempConversationList=null,this._cachedGroupMessageMap=new Map,this._checkCountMap=new Map,this.MAX_CHECK_COUNT=4,this.PAGING_GRP_COUNT_LIMIT=200,this._getTopicPendingMap=new Map,this._pagingStatus=Fo.NOT_START,this._pagingGetCostList=[];e.getInnerEmitterInstance().on(Ot.A2KEY_AND_TINYID_UPDATED,this.syncGroupList,this)}onCheckTimer(e){e%1==0&&this._cachedGroupMessageMap.size>0&&this._checkCachedGroupMessage()}_checkCachedGroupMessage(){this._cachedGroupMessageMap.forEach((e,t)=>{let o=this._checkCountMap.get(t);const s=this._groupModule.hasLocalGroup(t);ke.l(`${this._n}._checkCachedGroupMessage groupID:${t} hasLocalGroup:${s} checkCount:${o}`),s?(this._notifyCachedGroupMessage(t),this._checkCountMap.delete(t),this._groupModule.deleteUnjoinedAVChatRoom(t)):o>=this.MAX_CHECK_COUNT?(this._deleteCachedGroupMessage(t),this._checkCountMap.delete(t)):(o++,this._checkCountMap.set(t,o))})}handleUpdateGroupLastMessage(e){const t=this._n+".handleUpdateGroupLastMessage";if(ke.l(`${t} conversation count:${e.length}, local group count:${this._groupModule.getLocalGroupList().length}`),0===this._groupModule.getGroupMap().size)return void(this.tempConversationList=e);let o,s,r,i=!1;for(let n=0,u=e.length;n<u;n++)o=e[n],o.type===re&&(s=o.conversationID.split(/^GROUP/)[1],r=this._groupModule.getLocalGroupProfile(s),r&&(r.lastMessage={...o.lastMessage},i=!0));ke.l(`${t} groupUpdated:${i}`),i&&(this._groupModule.sortLocalGroupList(),this._groupModule.emitGroupListUpdate(!0,!1))}onNewGroupMessage(e){ke.d(`${this._n}.onNewGroupMessage count:${e.dataList.length}`);const{conversationOptionsList:t,messageList:o,AVChatRoomMessageList:r}=this._newGroupMessageStoredAndSummary(e);if(r.length>0&&this._groupModule.onAVChatRoomMessage(r),this._groupModule.filterModifiedMessage(o),t.length>0){this._groupModule.updateNextMessageSeq(t);this._groupModule.getModule(s).onNewMessage({conversationOptionsList:t,isInstantMessage:e.isInstantMessage||!0,updateUnreadCount:e.updateUnreadCount||!0})}const i=this._groupModule.filterUnmodifiedMessage(o);i.length>0&&this._groupModule.emitOuterEvent(g,i),o.length=0}_newGroupMessageStoredAndSummary(e){const{dataList:r,event:i,isInstantMessage:n}=e;let u=null;const a=[],p=[],l=[],c={},h=this._groupModule.getFileDownloadProxy(),g=r.length;g>1&&r.sort((e,t)=>e.sequence-t.sequence);const d=this._groupModule.getModule(s),m=this._groupModule.getModule(t);for(let t=0;t<g;t++){const e=Ze(r[t]),{groupProfile:{groupID:s,communityType:g=0,topicID:_,invisible:M}}=e;let f=void 0;const I=this._groupModule.isMessageFromTopic(g,_);if(I){f=ie,e.to=_;const t=this._groupModule.getModule(o);t.hasLocalTopic(s,_)||this._getTopicPendingMap.has(_)||(this._getTopicPendingMap.set(_,1),t.getTopicList({groupID:s,topicIDList:[_]}).finally(()=>{this._getTopicPendingMap.delete(_)}))}const y=this._groupModule.hasLocalGroup(s);if(!y&&this._groupModule.isUnjoinedAVChatRoom(s))continue;if(!y&&!I){this._cacheGroupMessageAndProbe({groupID:s,event:i,item:e});continue}if(this._groupModule.isMessageFromOrToAVChatroom(s)){e.event=i,l.push(e);continue}if(e.currentUser=this._groupModule.getMyUserID(),e.conversationType=re,e.isSystemMessage=!!e.isSystemMessage,u=new os(e),u.setElement(e.elements,h),1===M){this._qualityStat(n,u);continue}let D=1===r[t].isModified;if(d.isMessageSentByCurrentInstance(u)?u.isModified=D:D=!1,1===e.onlineOnlyFlag)u._onlineOnlyFlag=!0,d.isMessageSentByCurrentInstance(u)||p.push(u);else{if(this._groupModule.isMessageFromCommunityOfTopic(g,_)){p.push(u);continue}if(u.from===this._groupModule.getMyUserID()){const t=d.getLatestMessageSentByMe(u.conversationID);if(t){const{nick:o,avatar:s}=t;o===u.nick&&s===u.avatar||(d.modifyMessageSentByMe({conversationID:e,latestNick:u.nick,latestAvatar:u.avatar}),m.mockOnNickAvatarModified(u.nick,u.avatar))}}if(!d.pushIntoMessageList(p,u,D))continue;this._qualityStat(n,u);const{conversationID:e,sequence:t}=u,o=d.isRemoteRead({conversationID:e,sequence:t});if(Be(c[e])){let t=0;"in"===u.flow&&(u._isExcludedFromUnreadCount||o||(t=1)),c[e]=a.push({conversationID:e,unreadCount:t,type:Be(f)?u.conversationType:f,subType:u.conversationSubType,lastMessage:u._isExcludedFromLastMessage?"":u})-1}else{const t=c[e];a[t].type=Be(f)?u.conversationType:f,a[t].subType=u.conversationSubType,a[t].lastMessage=u._isExcludedFromLastMessage?"":u,"in"===u.flow&&(u._isExcludedFromUnreadCount||o||a[t].unreadCount++)}}}return{conversationOptionsList:a,messageList:p,AVChatRoomMessageList:l}}_qualityStat(e,t){const o=this._groupModule.getModule(l);o.addMessageSequence({key:Ft,message:t}),e&&t.clientTime>0&&o.addMessageDelay(t.clientTime)}onGroupMessageRevoked(e){const t=this._groupModule.getModule(s),o=[];let r=null,i=!0;e.dataList.forEach(e=>{const{revokedInfos:s}=e.elements,{revokerInfo:n}=e;Be(s)||s.forEach(e=>{const s=pt(e.topicID)?"GROUP"+e.groupID:"GROUP"+e.topicID;r=t.revoke(s,e.sequence,e.random);const u=e.revokerInfo&&e.revokerInfo.revoker||n&&n.revoker,a=n&&n.reason;if(r)r.revoker=u,r.revokeReason=a||"",o.push(r);else{const r={conversationID:s,to:e.topicID||"",sequence:e.sequence,time:e.time,revoker:u};t.isLastMessageRevoked(r)&&(o.push(r),i=!1)}})}),0!==o.length&&(t.onMessageRevoked(o),!0===i&&(ke.l(`${this._n}.onGroupMessageRevoked count:${o.length}`),t.emitMessageRevokedEvent(o)))}_groupListTreeShaking(e){const t=new Map([...this._groupModule.getGroupMap()]);for(let s=0,r=e.length;s<r;s++)t.delete(e[s].groupID);if(this._groupModule.hasJoinedAVChatRoom()){this._groupModule.getJoinedAVChatRoom().forEach(e=>{t.delete(e)})}this._groupModule.getGroupMap().forEach((e,o)=>{e.isSupportTopic&&t.delete(o)});const o=[...t.keys()];for(let s=0,r=o.length;s<r;s++)this._groupModule.deleteGroup(o[s])}syncGroupList(e=!1){this._pagingStatus===Fo.NOT_START&&this._groupModule.clearGroupMap();const t=["Type","Name","FaceUrl","NextMsgSeq","LastMsgTime","AtInfoList","LastRecallTime"],o=this.PAGING_GRP_COUNT_LIMIT,s=[];if(!0===e)return this._pagingGetGroupListWithTopic({limit:o,offset:0,groupBaseInfoFilter:t,groupList:s});const r=this._n+".syncGroupList",i=new Kt("syncGroupList");return this._pagingGetGroupList({limit:o,offset:0,groupBaseInfoFilter:t,groupList:s}).then(()=>{const e=function(e){if(!je(e)||0===e.length)return;let t=0;return e.forEach(e=>{t+=e}),(t/e.length).toFixed(0)}(this._pagingGetCostList),t=function(e){if(!je(e)||0===e.length)return;let t=0;return e.forEach(e=>{t+=e}),t.toFixed(0)}(this._pagingGetCostList);this._pagingGetCostList.length=0,this._pagingStatus=Fo.RESOLVED,this._groupListTreeShaking(s),this._groupModule.updateGroupMap(s);const o=`count:${this._groupModule.getLocalGroupList().length} sum:${t} avg:${e}`;return ke.l(`${r} ok. ${o}`),i.setNetworkType(this._groupModule.getNetworkType()).setMessage(o).end(),this.tempConversationList&&(this.handleUpdateGroupLastMessage(this.tempConversationList),this.tempConversationList=null),this._groupModule.emitGroupListUpdate(!0,!0),lt({groupList:this._groupModule.getLocalGroupList()})}).catch(e=>(this._pagingStatus=Fo.REJECTED,this._groupModule.probeNetwork().then(([t,o])=>{i.setError(e,t,o).end()}),ke.e(r+" failed. error:",e),Ut(e)))}getGroupList(){const e=this._n+".getGroupList";if(ke.l(""+e),this._pagingStatus===Fo.REJECTED)return this.syncGroupList().then(()=>{const e=this._groupModule.getLocalGroupList();return lt({groupList:e,isSyncCompleted:this.isPagingGetCompleted()})}).catch(t=>(ke.e(e+" failed. error:",t),Ut(t)));const t=this._groupModule.getLocalGroupList();return ke.l(`${e}. returned group count:${t.length}`),qt({groupList:t,isSyncCompleted:this.isPagingGetCompleted()})}isPagingGetCompleted(){return this._pagingStatus===Fo.RESOLVED}_pagingGetGroupList(e){const t=this._n+"._pagingGetGroupList";let{isCommunityRelay:o=!1,limit:s,offset:r,groupBaseInfoFilter:i,groupList:n}=e;const u=Date.now();return this._groupModule.request({protocolName:Wt,requestData:{type:o?ce:void 0,memberAccount:this._groupModule.getMyUserID(),limit:s,offset:r,responseFilter:{groupBaseInfoFilter:i,selfInfoFilter:["Role","JoinTime","MsgFlag","MsgSeq"]}}}).then(e=>{const{groups:a=[],totalCount:p}=e.data;n.push(...a),this._handleGroupAtInfoWithoutTopic(o,a);const l=r+s,c=!(p>l),h=`offset:${r} limit:${s} totalCount:${p} isCompleted:${c} currentCount:${n.length} isCommunityRelay:${o}`,g=Date.now()-u;return this._pagingGetCostList.push(g),ke.l(`${t} ok. ${h} cost ${g} ms`),o||c?!o&&c?(ke.l(t+" start to get community list"),r=0,this._pagingGetGroupList({limit:s,offset:r,groupBaseInfoFilter:i,groupList:n,isCommunityRelay:!0})):o&&!c?(r=l,this._pagingGetGroupList({limit:s,offset:r,groupBaseInfoFilter:i,groupList:n,isCommunityRelay:!0})):lt({groupList:n}):(r=l,this._pagingGetGroupList({limit:s,offset:r,groupBaseInfoFilter:i,groupList:n}))}).catch(e=>10018===e.code?(ke.w(`${this.logPrefix} response size exceeds the limit, request count:${s}`),s=50,this._pagingGetGroupList({limit:s,offset:r,groupBaseInfoFilter:i,groupList:n,isCommunityRelay:o})):o?(11e3===e.code&&ke.l(t+" ok. community unavailable"),qt({groupList:n})):Ut(e))}_pagingGetGroupListWithTopic(e){const t=this._n+"._pagingGetGroupListWithTopic";let{limit:o,offset:s,groupBaseInfoFilter:r,groupList:i}=e;const n=Date.now();return this._groupModule.request({protocolName:Wt,requestData:{type:ce,memberAccount:this._groupModule.getMyUserID(),limit:o,offset:s,responseFilter:{groupBaseInfoFilter:r,selfInfoFilter:["Role","JoinTime","MsgFlag","MsgSeq"]},isSupportTopic:1}}).then(e=>{const{groups:u=[],totalCount:a}=e.data;i.push(...u);const p=s+o,l=!(a>p);if(ke.l(`${t} ok. offset:${s} limit:${o} totalCount:${a} isCompleted:${l} currentCount:${i.length} cost ${Date.now()-n} ms`),!l)return s=p,this._pagingGetGroupListWithTopic({limit:o,offset:s,groupBaseInfoFilter:r,groupList:i});this._groupModule.updateGroupMap(i),this._groupModule.emitGroupListUpdate(!0,!1);const c=this._groupModule.getLocalGroupList().filter(e=>!0===e.isSupportTopic);return lt({groupList:c})}).catch(e=>10018===e.code?(ke.w(`${this.logPrefix} response size exceeds the limit, request count:${o}`),o=50,this._pagingGetGroupListWithTopic({limit:o,offset:s,groupBaseInfoFilter:r,groupList:i})):Ut(e))}_cacheGroupMessage(e,t){this._cachedGroupMessageMap.has(e)||this._cachedGroupMessageMap.set(e,[]),this._cachedGroupMessageMap.get(e).push(t)}_deleteCachedGroupMessage(e){this._cachedGroupMessageMap.has(e)&&this._cachedGroupMessageMap.delete(e)}_notifyCachedGroupMessage(e){const t=this._cachedGroupMessageMap.get(e)||[];t.forEach(e=>{this.onNewGroupMessage(e)}),this._deleteCachedGroupMessage(e),ke.l(`${this._n}._notifyCachedGroupMessage groupID:${e} count:${t.length}`)}_cacheGroupMessageAndProbe(e){const{groupID:t,event:o,item:s}=e;this._cacheGroupMessage(t,{event:o,dataList:[s]}),this._groupModule.getGroupSimplifiedInfo(t).then(e=>{const{type:o}=e;o===le?this._groupModule.hasLocalGroup(t)?this._notifyCachedGroupMessage(t):this._groupModule.setUnjoinedAVChatRoom(t):(this._groupModule.updateGroupMap([e]),this._notifyCachedGroupMessage(t))}),this._checkCountMap.has(t)||this._checkCountMap.set(t,0),ke.l(`${this._n}._cacheGroupMessageAndProbe groupID:${t}`)}_handleGroupAtInfoWithoutTopic(e,t){e&&0!==t.length&&t.forEach(e=>{const{groupID:t,groupAtInfoList:o}=e,r=[];if(!Be(o)){o.forEach(e=>{r.push({...e,groupID:t})});this._groupModule.getModule(s).onNewGroupAtTips({dataList:r})}})}setPagingGroupCount(e){Be(e)||(this.PAGING_GRP_COUNT_LIMIT=parseInt(e,10))}reset(){this.PAGING_GRP_COUNT_LIMIT=200,this._cachedGroupMessageMap.clear(),this._checkCountMap.clear(),this._getTopicPendingMap.clear(),this._pagingStatus=Fo.NOT_START,this._pagingGetCostList=[]}}const is=1,ns=2,us=3,as=4,ps=5;class ls{constructor(e){this._groupModule=e,this._n="GroupAttributesHandler",this._groupAttributesMap=new Map,this._groupAttributesCopy={},this.CACHE_EXPIRE_TIME=3e4;this._groupModule.getInnerEmitterInstance().on(Ot.CLOUD_CONFIG_UPDATED,this._onCloudConfigUpdated,this)}_onCloudConfigUpdated(){const e=this._groupModule.getCloudConfig("grp_attr_cache_time");Be(e)||(this.CACHE_EXPIRE_TIME=Number(e))}updateLocalMainSequenceOnReconnected(){this._groupAttributesMap.forEach(e=>{e.localMainSequence=0})}isGroupAttributesUpdatedNotice(e){const{to:t,elements:{newGroupProfile:o}}=e,s=!Be(o)&&!pt(o.groupAttributeOption);return s&&this._onGroupAttributesUpdated({groupID:t,groupAttributeOption:o.groupAttributeOption}),s}_onGroupAttributesUpdated(e){const{groupID:t,groupAttributeOption:o}=e,{mainSequence:s,isWithChangedAttributeInfo:r,groupAttributeList:i=[],operationType:n}=o;if(ke.l(this._n+".onGroupAttributesUpdated. "+`groupID:${t} isWithChangedAttributeInfo:${r} operationType:${n}`),Be(n))return;this._groupAttributesCopy=this._getCachedAttributes({groupID:t});const{localMainSequence:u}=this._getLocalGroupAttributes(t),a=s-u;if(0!==a){if(1===r&&1===a)return this._refreshCachedGroupAttributes({groupID:t,remoteMainSequence:s,groupAttributeList:i,operationType:n}),void this._emitGroupAttributesUpdated(t);if(this._hasLocalGroupAttributes(t)){const{avChatRoomKey:e}=this._getLocalGroupAttributes(t);this._getGroupAttributes({groupID:t,avChatRoomKey:e}).then(()=>{this._emitGroupAttributesUpdated(t)})}}}initGroupAttributesCache(e){const{groupID:t,avChatRoomKey:o}=e;this._groupAttributesMap.set(t,{lastUpdateTime:0,localMainSequence:0,remoteMainSequence:0,attributes:new Map,avChatRoomKey:o}),ke.l(`${this._n}.initGroupAttributesCache groupID:${t} avChatRoomKey:${o}`)}initGroupAttributes(e){const{groupID:t,groupAttributes:o}=e,{remoteMainSequence:s,avChatRoomKey:r}=this._getLocalGroupAttributes(t),i=new Kt("initGroupAttributes");return i.setMessage(`groupID:${t} avChatRoomKey:${r} mainSequence:${s}`),this._groupModule.request({protocolName:Do,requestData:{groupID:t,avChatRoomKey:r,mainSequence:s,groupAttributeList:this._transformGroupAttributes(o)}}).then(e=>{ke.l(`${this._n}.initGroupAttributes ok. groupID:${t}`);const{mainSequence:s,groupAttributeList:r}=e.data,n=[...r];return n.forEach(e=>{e.value=o[e.key]}),this._groupAttributesCopy=this._getCachedAttributes({groupID:t}),this._refreshCachedGroupAttributes({groupID:t,remoteMainSequence:s,groupAttributeList:n,operationType:is}),this._emitGroupAttributesUpdated(t),i.setNetworkType(this._groupModule.getNetworkType()).end(),lt({groupAttributes:o})}).catch(e=>(this._groupModule.probeNetwork().then(([t,o])=>{i.setError(e,t,o).end()}),Ut(e)))}setGroupAttributes(e){const t=this._n+".setGroupAttributes",{groupID:o,groupAttributes:s}=e,{remoteMainSequence:r,avChatRoomKey:i,attributes:n}=this._getLocalGroupAttributes(o),u=this._transformGroupAttributes(s);u.forEach(e=>{const{key:t}=e;e.sequence=0,n.has(t)&&(e.sequence=n.get(t).sequence)});const a=new Kt("setGroupAttributes");return a.setMessage(`groupID:${o} groupAttributes:${JSON.stringify(s)}`),ke.l(`${t}. groupID:${o} mainSequence:${r}`),this._groupModule.request({protocolName:Lo,requestData:{groupID:o,avChatRoomKey:i,mainSequence:r,groupAttributeList:u}}).then(e=>{ke.l(t+" ok.");const{mainSequence:r,groupAttributeList:i}=e.data,n=[...i];return n.forEach(e=>{e.value=s[e.key]}),this._groupAttributesCopy=this._getCachedAttributes({groupID:o}),this._refreshCachedGroupAttributes({groupID:o,remoteMainSequence:r,groupAttributeList:n,operationType:ns}),this._emitGroupAttributesUpdated(o),a.setNetworkType(this._groupModule.getNetworkType()).end(),lt({groupAttributes:s})}).catch(e=>(this._groupModule.probeNetwork().then(([t,o])=>{a.setError(e,t,o).end()}),Ut(e)))}deleteGroupAttributes(e){const{groupID:t,keyList:o=[]}=e,{remoteMainSequence:s,avChatRoomKey:r,attributes:i}=this._getLocalGroupAttributes(t);let n=[...i.keys()],u=Co,a=us;const p={groupID:t,avChatRoomKey:r,mainSequence:s},l=[];o.length>0&&(n=[],u=Go,a=as,o.forEach(e=>{let t=0;i.has(e)&&(t=i.get(e).sequence,n.push(e)),l.push({key:e,sequence:t})}),p.groupAttributeList=l);const c=new Kt("deleteGroupAttributes");return c.setMessage(`groupID:${t} mainSequence:${s} keyList:${o} protocolName:${u}`),this._groupModule.request({protocolName:u,requestData:p}).then(e=>{ke.l(`${this._n}.deleteGroupAttributes ok. groupID:${t}`);const{mainSequence:o}=e.data;return this._groupAttributesCopy=this._getCachedAttributes({groupID:t}),this._refreshCachedGroupAttributes({groupID:t,remoteMainSequence:o,groupAttributeList:l,operationType:a}),this._emitGroupAttributesUpdated(t),c.setNetworkType(this._groupModule.getNetworkType()).end(),lt({keyList:n})}).catch(e=>(this._groupModule.probeNetwork().then(([t,o])=>{c.setError(e,t,o).end()}),Ut(e)))}getGroupAttributes(e){const t=this._n+".getGroupAttributes",{groupID:o}=e,{avChatRoomKey:s,lastUpdateTime:r,localMainSequence:i,remoteMainSequence:n}=this._getLocalGroupAttributes(o),u=new Kt("getGroupAttributes");if(u.setMessage(`groupID:${o} localMainSequence:${i} remoteMainSequence:${n} keyList:${e.keyList}`),Date.now()-r>=this.CACHE_EXPIRE_TIME||i<n)return this._getGroupAttributes({groupID:o,avChatRoomKey:s}).then(s=>{u.setMoreMessage("get attributes from remote. count:"+s.length).setNetworkType(this._groupModule.getNetworkType()).end(),ke.l(`${t} from remote. groupID:${o}`);const r=this._getCachedAttributes(e);return lt({groupAttributes:r})}).catch(e=>(this._groupModule.probeNetwork().then(([t,o])=>{u.setError(e,t,o).end()}),Ut(e)));u.setMoreMessage("get attributes from cache").setNetworkType(this._groupModule.getNetworkType()).end(),ke.l(`${t} from cache. groupID:${o}`);const a=this._getCachedAttributes(e);return qt({groupAttributes:a})}_getGroupAttributes(e){let t=0;return Be(e.avChatRoomKey)||(t=1),this._groupModule.request({protocolName:bo,requestData:{...e,groupType:t}}).then(t=>{ke.l(`${this._n}._getGroupAttributes ok. groupID:${e.groupID}`);const{mainSequence:o,groupAttributeList:s}=t.data,r=[...s];return Be(o)||this._refreshCachedGroupAttributes({groupID:e.groupID,remoteMainSequence:o,groupAttributeList:r,operationType:ps}),s}).catch(e=>Ut(e))}_refreshCachedGroupAttributes(e){const{groupID:t,remoteMainSequence:o,groupAttributeList:s,operationType:r}=e;if(this._hasLocalGroupAttributes(t)){const e=this._getLocalGroupAttributes(t),{localMainSequence:i}=e;if(r===ps||o-i==1)e.remoteMainSequence=o,e.localMainSequence=o,e.lastUpdateTime=Date.now(),this._updateCachedAttributes({groupAttributes:e,groupAttributeList:s,operationType:r});else{if(i===o)return;e.remoteMainSequence=o}this._groupAttributesMap.set(t,e);const n=`operationType:${r} localMainSequence:${i} remoteMainSequence:${o}`;ke.l(`${this._n}._refreshCachedGroupAttributes. ${n}`)}}_getCachedAttributes(e){const{groupID:t,keyList:o=[]}=e,s={};if(this._hasLocalGroupAttributes(t)){const{attributes:e}=this._getLocalGroupAttributes(t);if(o.length>0)o.forEach(t=>{e.has(t)&&(s[t]=e.get(t).value)});else for(const t of e.keys())s[t]=e.get(t).value}return s}_updateCachedAttributes(e){const{groupAttributes:t,groupAttributeList:o,operationType:s}=e;s!==us?s!==as?(s===is&&t.attributes.clear(),o.forEach(e=>{const{key:o,value:s,sequence:r}=e;t.attributes.set(o,{value:s,sequence:r})})):o.forEach(e=>{t.attributes.delete(e.key)}):t.attributes.clear()}_hasLocalGroupAttributes(e){return this._groupAttributesMap.has(e)}_getLocalGroupAttributes(e){return this._hasLocalGroupAttributes(e)||this.initGroupAttributesCache({groupID:e}),this._groupAttributesMap.get(e)}_transformGroupAttributes(e){const t=[];return Object.keys(e).forEach(o=>{t.push({key:o,value:e[o]})}),t}_emitGroupAttributesUpdated(e){const t=this._getCachedAttributes({groupID:e}),{updatedKeyList:o,deletedKeyList:s}=this._computeAttrChangedInfo(t);ke.l(`${this._n}._emitGroupAttributesUpdated update:${o.length}, delete:${s.length}`),0===o.length&&0===s.length||this._groupModule.emitOuterEvent(_,{groupID:e,groupAttributes:t,updatedKeyList:o,deletedKeyList:s})}_computeAttrChangedInfo(e){const t=[],o=[];return Object.keys(e).forEach(o=>{e[o]!==this._groupAttributesCopy[o]&&t.push(o)}),Object.keys(this._groupAttributesCopy).forEach(t=>{Be(e[t])&&o.push(t)}),this._groupAttributesCopy={},{updatedKeyList:t,deletedKeyList:o}}deleteLocalGroupAttributes(e){this._hasLocalGroupAttributes(e)&&this._groupAttributesMap.delete(e)}reset(){this._groupAttributesMap.clear(),this._groupAttributesCopy={},this.CACHE_EXPIRE_TIME=3e4}}const cs="Set",hs="Increase",gs="Decrease";class ds{constructor(e){this._groupModule=e,this._n="GroupCountersHandler",this._groupCountersMap=new Map,this.EXPIRE_TIME=3e4;this._groupModule.getInnerEmitterInstance().on(Ot.CLOUD_CONFIG_UPDATED,this._onCloudConfigUpdated,this)}_onCloudConfigUpdated(){const e=this._groupModule.getCloudConfig("grp_counter_expire_time");Be(e)||(this.EXPIRE_TIME=Number(e))}isGroupCountersNotice(e){const{to:t,elements:{groupCounterInfo:o}}=e;let s=!1;return pt(o)||(this._onGroupCountersUpdated({groupID:t,groupCounterInfo:o}),s=!0),s}_onGroupCountersUpdated(e){const{groupID:t,groupCounterInfo:o}=e;o.forEach(e=>{const{type:o,groupCounterSeq:s,counterList:r=[]}=e;0!==o&&2!==o||(this._updateLocalGroupCounters({groupID:t,groupCounterSeq:s,counterList:r}),r.forEach(e=>{this._groupModule.emitOuterEvent(M,{groupID:t,key:e.key,value:e.value})})),1===o&&this._deleteLocalGroupCounters({groupID:t,groupCounterSeq:s,counterList:r})}),ke.l(`${this._n}._onGroupCountersUpdated groupID:${t}`)}initGroupCountersCache(e){const{groupID:t,avChatRoomKey:o}=e;this._groupCountersMap.set(t,{lastUpdateTime:0,groupCounterSeq:0,counters:new Map,avChatRoomKey:o}),ke.l(`${this._n}.initGroupCountersCache groupID:${t} avChatRoomKey:${o}`)}setGroupCounters(e){if(!this._groupModule.canIUse(b.GRP_COUNTER))return this._groupModule.cannotUseCommercialAbility("setGroupCounters");const t=this._n+".setGroupCounters",{groupID:o,counters:s}=e,r=this._convertObjectToList(s),{avChatRoomKey:i}=this._getLocalGroupCounters(o),n=`groupID:${o} count:${r.length}`,u=new Kt("setGroupCounters");return u.setMessage(""+n),ke.l(`${t}. ${n}`),this._updateGroupCounters({groupID:o,counterList:r,avChatRoomKey:i,mode:cs}).then(e=>(u.end(),ke.l(t+" ok."),lt({counters:e}))).catch(e=>(this._groupModule.probeNetwork().then(([t,o])=>{u.setError(e,t,o).end()}),ke.e(t+" failed. error:",e),Ut(e)))}increaseGroupCounter(e){const t="increaseGroupCounter";if(!this._groupModule.canIUse(b.GRP_COUNTER))return this._groupModule.cannotUseCommercialAbility(t);const o=`${this._n}.${t}`,{groupID:s,key:r,value:i}=e,{avChatRoomKey:n}=this._getLocalGroupCounters(s),u=`groupID:${s} key:${r} value:${i}`,a=new Kt(t);a.setMessage(""+u),ke.l(`${o}. ${u}`);const p=[{key:r,value:i}];return this._updateGroupCounters({groupID:s,counterList:p,avChatRoomKey:n,mode:hs}).then(e=>(a.end(),ke.l(o+" ok."),lt({counters:e}))).catch(e=>(this._groupModule.probeNetwork().then(([t,o])=>{a.setError(e,t,o).end()}),ke.e(o+" failed. error:",e),Ut(e)))}decreaseGroupCounter(e){const t="decreaseGroupCounter";if(!this._groupModule.canIUse(b.GRP_COUNTER))return this._groupModule.cannotUseCommercialAbility(t);const o=`${this._n}.${t}`,{groupID:s,key:r,value:i}=e,{avChatRoomKey:n}=this._getLocalGroupCounters(s),u=`groupID:${s} key:${r} value:${i}`,a=new Kt(t);a.setMessage(""+u),ke.l(`${o}. ${u}`);const p=[{key:r,value:i}];return this._updateGroupCounters({groupID:s,counterList:p,avChatRoomKey:n,mode:gs}).then(e=>(a.end(),ke.l(o+" ok."),lt({counters:e}))).catch(e=>(this._groupModule.probeNetwork().then(([t,o])=>{a.setError(e,t,o).end()}),ke.e(o+" failed. error:",e),Ut(e)))}getGroupCounters(e){if(!this._groupModule.canIUse(b.GRP_COUNTER))return this._groupModule.cannotUseCommercialAbility("getGroupCounters");const t=this._n+".getGroupCounters",{groupID:o,keyList:s=[]}=e,{avChatRoomKey:r,lastUpdateTime:i}=this._getLocalGroupCounters(o),n=new Kt("getGroupCounters");if(n.setMessage("groupID:"+o),Date.now()-i>=this.EXPIRE_TIME)return this._getRemoteGroupCounters({groupID:o,avChatRoomKey:r}).then(e=>{n.setMoreMessage("from remote. count:"+e.length).end(),ke.l(`${t} from remote. groupID:${o}`);const r=this._getLocalCounters(o,s);return lt({counters:r})}).catch(e=>(this._groupModule.probeNetwork().then(([t,o])=>{n.setError(e,t,o).end()}),Ut(e)));n.setMoreMessage("from cache").end(),ke.l(`${t} from cache. groupID:${o}`);const u=this._getLocalCounters(o,s);return qt({counters:u})}_getRemoteGroupCounters(e){return this._groupModule.request({protocolName:wo,requestData:{...e}}).then(t=>{const{counterList:o=[],groupCounterSeq:s}=t.data;return this._updateLocalGroupCounters({groupID:e.groupID,counterList:o,groupCounterSeq:s}),ke.l(`${this._n}._getRemoteGroupCounters ok. groupID:${e.groupID}`),o}).catch(e=>Ut(e))}_convertObjectToList(e){const t=[];return Object.keys(e).forEach(o=>{t.push({key:o,value:e[o]})}),t}_updateGroupCounters(e){const t=this._n+"._updateGroupCounters",{groupID:o,avChatRoomKey:s,mode:r}=e;return ke.l(`${t}. groupID:${o} avChatRoomKey:${s} mode:${r}`),this._groupModule.request({protocolName:vo,requestData:{...e}}).then(e=>{ke.l(t+" ok.");const{counterList:o=[]}=e.data,s={};return o.forEach(e=>{const{key:t,value:o}=e;s[t]=o}),s}).catch(e=>Ut(e))}_hasLocalGroupCounters(e){return this._groupCountersMap.has(e)}_getLocalGroupCounters(e){return this._hasLocalGroupCounters(e)||this.initGroupCountersCache({groupID:e}),this._groupCountersMap.get(e)}_updateLocalGroupCounters(e){const{groupID:t,counterList:o=[],groupCounterSeq:s}=e;if(this._hasLocalGroupCounters(t)){const{counters:e,avChatRoomKey:r,groupCounterSeq:i}=this._getLocalGroupCounters(t);if(s>0&&s<i)return;o.forEach(t=>{const{key:o,value:s}=t;e.set(o,s)}),this._groupCountersMap.set(t,{lastUpdateTime:Date.now(),groupCounterSeq:s,counters:e,avChatRoomKey:r})}}_deleteLocalGroupCounters(e){const{groupID:t,counterList:o=[],groupCounterSeq:s}=e;if(this._hasLocalGroupCounters(t)){const{counters:e,avChatRoomKey:r}=this._getLocalGroupCounters(t);o.forEach(t=>{e.delete(t.key)}),this._groupCountersMap.set(t,{lastUpdateTime:Date.now(),groupCounterSeq:s,counters:e,avChatRoomKey:r})}}_getLocalCounters(e,t){const o={};if(!this._hasLocalGroupCounters(e))return o;const{counters:s}=this._getLocalGroupCounters(e);if(t.length>0)t.forEach(e=>{s.has(e)&&(o[e]=s.get(e))});else for(const r of s.keys())o[r]=s.get(r);return o}reset(){this._groupCountersMap.clear(),this.EXPIRE_TIME=3e4}}class ms{constructor(e){const{manager:t,groupID:o,onInit:s,onSuccess:r,onFail:i}=e;this._n="Polling",this._manager=t,this._groupModule=t._groupModule,this._onInit=s,this._onSuccess=r,this._onFail=i,this._groupID=o,this._timeoutID=-1,this._isRunning=!1,this._protocolName=_o}start(){const e=this._groupModule.isLoggedIn();e||(this._protocolName=Mo),ke.l(`${this._n}.start pollingInterval:${this._manager.getPollingInterval()} isLoggedIn:${e}`),this._isRunning=!0,this._request()}isRunning(){return this._isRunning}_request(){const e=this._onInit(this._groupID);this._groupModule.request({protocolName:this._protocolName,requestData:e}).then(e=>{this._onSuccess(this._groupID,e),this.isRunning()&&(clearTimeout(this._timeoutID),this._timeoutID=setTimeout(this._request.bind(this),this._manager.getPollingInterval()))}).catch(e=>{this._onFail(this._groupID,e),this.isRunning()&&(clearTimeout(this._timeoutID),this._timeoutID=setTimeout(this._request.bind(this),this._manager.MAX_POLLING_INTERVAL))})}stop(){ke.l(this._n+".stop"),this._timeoutID>0&&(clearTimeout(this._timeoutID),this._timeoutID=-1),this._isRunning=!1}getPollingTimerID(){return this._timeoutID}}class _s{constructor(e){this.value=e,this.next=null}}class Ms{constructor(e){this.MAX_LENGTH=e,this.pTail=null,this.pNodeToDel=null,this.map=new Map}set(e){const t=new _s(e);if(this.map.size<this.MAX_LENGTH)null===this.pTail?(this.pTail=t,this.pNodeToDel=t):(this.pTail.next=t,this.pTail=t),this.map.set(e,1);else{let o=this.pNodeToDel;this.pNodeToDel=this.pNodeToDel.next,this.map.delete(o.value),o.next=null,o=null,this.pTail.next=t,this.pTail=t,this.map.set(e,1)}}has(e){return this.map.has(e)}delete(e){this.has(e)&&this.map.delete(e)}tail(){return this.pTail}size(){return this.map.size}data(){return Array.from(this.map.keys())}reset(){let e;for(;null!==this.pNodeToDel;)e=this.pNodeToDel,this.pNodeToDel=this.pNodeToDel.next,e.next=null,e=null;this.pTail=null,this.map.clear()}}const fs=["groupID","name","avatar","type","introduction","notification","ownerID","selfInfo","createTime","infoSequence","lastInfoTime","lastMessage","nextMessageSeq","memberNum","maxMemberNum","memberList","joinOption","groupCustomField","muteAllMembers","isSupportTopic","inviteOption","_lastRevokedTime"];class Is{constructor(e){this.groupID="",this.name="",this.avatar="",this.type="",this.introduction="",this.notification="",this.ownerID="",this.createTime="",this.infoSequence="",this.lastInfoTime="",this.selfInfo={messageRemindType:"",joinTime:"",nameCard:"",role:"",userID:"",memberCustomField:void 0,readedSequence:0,excludedUnreadSequenceList:void 0},this.lastMessage={lastTime:"",lastSequence:"",fromAccount:"",messageForShow:""},this.nextMessageSeq="",this.memberNum="",this.memberCount="",this.maxMemberNum="",this.maxMemberCount="",this.joinOption="",this.inviteOption="",this.groupCustomField=[],this.muteAllMembers=!1,this.isSupportTopic=!1,this._lastRevokedTime=0,this._initGroup(e)}set memberNum(e){}set maxMemberNum(e){}get memberNum(){return this.memberCount}get maxMemberNum(){return this.maxMemberCount}_initGroup(e){for(const t in e)fs.indexOf(t)<0||("selfInfo"!==t?("memberNum"===t&&(this.memberCount=e[t]),"maxMemberNum"===t&&(this.maxMemberCount=e[t]),"isSupportTopic"!==t?this[t]=e[t]:this.isSupportTopic=1===e[t]):this.updateSelfInfo(e[t]))}updateGroup(e){e.appid=void 0,e.grossTopicNextMsgSeq=void 0,e.selfInfo&&(e.selfInfo.grossTopicReadSeq=void 0);const t=JSON.parse(JSON.stringify(e));t.lastMsgTime&&(this.lastMessage.lastTime=t.lastMsgTime),Be(t.muteAllMembers)||("On"===t.muteAllMembers?t.muteAllMembers=!0:t.muteAllMembers=!1),t.groupCustomField&&et(this.groupCustomField,t.groupCustomField),Be(t.memberNum)||(this.memberCount=t.memberNum),Be(t.maxMemberNum)||(this.maxMemberCount=t.maxMemberNum),Be(t.isSupportTopic)||(this.isSupportTopic=xe(t.isSupportTopic)?1===t.isSupportTopic:t.isSupportTopic),ze(this,t,["members","errorCode","lastMsgTime","groupCustomField","memberNum","maxMemberNum","isSupportTopic"]),je(t.members)&&t.members.length>0&&t.members.forEach(e=>{e.userID===this.selfInfo.userID&&ze(this.selfInfo,e,["sequence"])})}updateSelfInfo({nameCard:e,joinTime:t,role:o,messageRemindType:s,readedSequence:r,excludedUnreadSequenceList:i}){const n={nameCard:e,joinTime:t,role:o,messageRemindType:s,readedSequence:r,excludedUnreadSequenceList:i};ze(this.selfInfo,{...n},[],["",null,void 0,0,NaN])}setSelfNameCard(e){this.selfInfo.nameCard=e}}const ys={3:!0,4:!0,5:!0,6:!0,17:!0,20:!0};class Ds{constructor(e){this._groupModule=e,this._n="AVChatRoomHandler",this._joinedGroupMap=new Map,this._pollingRequestInfoMap=new Map,this._pollingInstanceMap=new Map,this.sequencesLinkedList=new Ms(200),this.messageIDLinkedList=new Ms(100),this.receivedMessageCount=0,this._reportMessageStackedCount=0,this._onlineMemberCountMap=new Map,this.DEFAULT_EXPIRE_TIME=60,this.DEFAULT_POLLING_INTERVAL=300,this.MAX_POLLING_INTERVAL=2e3,this._pollingInterval=this.DEFAULT_POLLING_INTERVAL,this.DEFAULT_POLLING_NO_MESSAGE_COUNT=20,this.DEFAULT_POLLING_INTERVAL_PLUS=2e3,this._pollingNoMessageCount=0,this._startBroadcastSeq=1,this._broadcastMessageIDMap=new Map,this.DEFAULT_POLLING_SIMPLIFIED_MSG=0}hasJoinedAVChatRoom(){return this._joinedGroupMap.size>0}checkJoinedAVChatRoomByID(e){return this._joinedGroupMap.has(e)}getJoinedAVChatRoom(){return this._joinedGroupMap.size>0?[...this._joinedGroupMap.keys()]:[]}_updateRequestData(e){const t=this._pollingRequestInfoMap.get(e);return e===[...this._pollingInstanceMap.keys()][0]?{...t,startBroadcastSeq:this._startBroadcastSeq,simplifiedMessage:this.DEFAULT_POLLING_SIMPLIFIED_MSG}:{...t,simplifiedMessage:this.DEFAULT_POLLING_SIMPLIFIED_MSG}}_handleSuccess(e,t){const{key:o,nextSeq:s,rspMsgList:r,errorCode:i,nextBroadcastSeq:n,broadcastMessageList:u}=t.data;if(0!==i){const o=this._pollingRequestInfoMap.get(e),s=new Kt("longPollingAVError"),r=o?`${o.key}-${o.startSeq}`:"requestInfo is undefined";s.setMessage(`${e}-${r}-${t.errorInfo}`).setCode(t.errorCode).setNetworkType(this._groupModule.getNetworkType()).end(!0)}else{if(!this.checkJoinedAVChatRoomByID(e))return;Ve(o)&&xe(s)&&this._pollingRequestInfoMap.set(e,{key:o,startSeq:s}),xe(n)&&n>this._startBroadcastSeq&&(this._startBroadcastSeq=n),je(r)&&r.length>0?(r.forEach(e=>{e.to=e.groupID}),this.onMessage(r)):(this._pollingNoMessageCount+=1,this._pollingNoMessageCount===this.DEFAULT_POLLING_NO_MESSAGE_COUNT&&(this._pollingInterval=this.DEFAULT_POLLING_INTERVAL+this.DEFAULT_POLLING_INTERVAL_PLUS)),this._onBroadcastMessage(u)}}_handleFailure(e,t){}onMessage(e){if(!je(e)||0===e.length)return;0!==this._pollingNoMessageCount&&(this._pollingNoMessageCount=0,this._pollingInterval=this.DEFAULT_POLLING_INTERVAL);let t=null;const o=[],i=this._getModule(s),n=this._getModule(l),u=e.length;u>1&&e.sort((e,t)=>e.sequence-t.sequence);const p=this._getModule(r);for(let s=0;s<u;s++){const r=this.restoreMessageFromSimplified(e[s]);if(!ys[r.event]){ke.w(`${this._n}.onMessage unknown event:${r.event}`);continue}if(6===r.event){if(this._groupModule.isGroupAttributesUpdatedNotice(r))continue;if(this._groupModule.isGroupCountersNotice(r))continue}if(20===r.event){this.handleMessageRevokedNotice(r);continue}this.receivedMessageCount+=1,t=this.packMessage(r,r.event);const u=1===r.isModified,l=1===r.isHistoryMessage;if(!p.isUnlimitedAVChatRoom()&&this.sequencesLinkedList.has(t.sequence))continue;if(this.messageIDLinkedList.has(t.ID))continue;const{conversationID:c}=t;if(this.receivedMessageCount%50==0?this._getModule(a).detectFirstRound(c,this.sequencesLinkedList.data()):this.receivedMessageCount%80==0&&this._getModule(a).detectSecondRound(c,this.sequencesLinkedList.data()),null!==this.sequencesLinkedList.tail()){const e=this.sequencesLinkedList.tail().value,o=t.sequence-e;o>1&&o<=20?this._getModule(a).onMessageMaybeLost(c,e+1,o-1):o<-1&&o>=-20&&this._getModule(a).onMessageMaybeLost(c,t.sequence+1,Math.abs(o)-1)}this.sequencesLinkedList.set(t.sequence),this.messageIDLinkedList.set(t.ID);let h=!1;if(this._isMessageSentByCurrentInstance(t)?u&&(h=!0,t.isModified=u,i.updateMessageIsModifiedProperty(t)):h=!0,h){if(t.conversationType===ne&&5===t.payload.operationType&&this._onGroupDismissed(t.payload.groupProfile.groupID),!l&&t.conversationType!==ne){const e=t.conversationID.replace(re,"");this._pollingInstanceMap.has(e)?this._groupModule.isLoggedIn()&&n.addMessageSequence({key:Vt,message:t}):(t.type!==z&&t.clientTime>0&&n.addMessageDelay(t.clientTime),n.addMessageSequence({key:xt,message:t}))}o.push(t)}}if(0===o.length)return;this._groupModule.filterModifiedMessage(o);const c=this.packConversationOption(o);c.length>0&&i.onNewMessage({conversationOptionsList:c,isInstantMessage:!0}),ke.d(`${this._n}.onMessage count:${o.length}`),this._checkMessageStacked(o);const h=this._groupModule.filterUnmodifiedMessage(o);h.length>0&&this._groupModule.emitOuterEvent(g,h),o.length=0}handleMessageRevokedNotice(e){const{groupID:t,elements:{revokeMsgList:o},revokerInfo:r}=e,i=[];if(o.forEach(e=>{const{tinyID:o,clientTime:s,random:n}=e,u={conversationID:`${re}${t}`,ID:`${o}-${s}-${n}`,revoker:r.revoker,revokeReason:r.reason||""};i.push(u)}),0===i.length)return;this._getModule(s).emitMessageRevokedEvent(i)}isBroadcastOrNormal(e){return 3===e||17===e}isGroupTip(e){return 4===e||6===e}isGroupSystemNotice(e){return 5===e}restoreGroupTipElements(e={}){const{operatorInfo:t={},operatorID:o,userIDList:s=[],operationType:r}=e;xe(e.groupJoinType)||1!==r&&2!==r||(e.groupJoinType=2===r?0:1);const{userID:i=o,avatar:n="",nick:u=""}=t;e.operatorInfo={userID:i,avatar:n,nick:u};const a=s.map(e=>({userID:e}));return e.memberInfoList=e.memberInfoList||a,e}restoreMessageFromSimplified(e){const{event:t}=e;if(this.isBroadcastOrNormal(t)&&(e.cloudCustomData=e.cloudCustomData||"",e.elements=e.elements.map(e=>{if(e.type===Y){const{content:t={}}=e;e.content={data:"",description:"",extension:"",...t}}return e})),(this.isGroupTip(t)||this.isGroupSystemNotice(t))&&(e.from=e.from||"@TIM#SYSTEM"),this.isGroupTip(t)){e.elements=this.restoreGroupTipElements(e.elements);const{elements:t={}}=e,{operationType:o,operatorInfo:s={}}=t;if(1===o){const e=[{userID:s.userID}];t.memberInfoList=t.memberInfoList||e}}if(this.isGroupSystemNotice(t)){let{memberInfoList:t,operatorInfo:o={}}=e.elements;t||(t=o),e.elements.memberInfoList={userID:e.elements.operatorID,avatar:"",nick:"",...t},e.elements={authentication:"",remarkInfo:"",messageKey:1e3*e.time,...e.elements};const s=Object.keys(e.elements).filter(e=>"operatorInfo"!==e).reduce((t,o)=>({...t,[o]:e.elements[o]}),{});e.elements=s}return e}_onGroupDismissed(e){ke.l(`${this._n}._onGroupDismissed groupID:${e}`),this._groupModule.deleteLocalGroupAndConversation(e),this.reset(e)}_checkMessageStacked(e){const t="MessageStacked",o=e.length;if(o>=100&&(this._groupModule.outputWarning(t,o),this._reportMessageStackedCount<5)){new Kt(t).setNetworkType(this._groupModule.getNetworkType()).setMessage(`count:${o} groupID:${[...this._joinedGroupMap.keys()]}`).setLevel("warning").end(),this._reportMessageStackedCount+=1}}_isMessageSentByCurrentInstance(e){return!!this._getModule(s).isMessageSentByCurrentInstance(e)}packMessage(e,t){e.currentUser=this._groupModule.getMyUserID(),e.conversationType=5===t?ne:re,e.isSystemMessage=!!e.isSystemMessage;const o=new os(e),s=this.packElements(e,t);return o.setElement(s,this._groupModule.getFileDownloadProxy()),o}packElements(e,t){return 4===t||6===t?(this._updateMemberCountByGroupTips(e),{type:z,content:{...e.elements,groupProfile:e.groupProfile}}):5===t?{type:X,content:{...e.elements,groupProfile:{...e.groupProfile,groupID:e.groupID}}}:e.elements}packConversationOption(e){const t=new Map;for(let o=0;o<e.length;o++){const s=e[o],r=s.conversationID;if(t.has(r)){const e=t.get(r);e.lastMessage=s,"in"===s.flow&&e.unreadCount++}else t.set(r,{conversationID:s.conversationID,unreadCount:"out"===s.flow?0:1,type:s.conversationType,subType:s.conversationSubType,lastMessage:s})}return[...t.values()]}_updateMemberCountByGroupTips(e){const{groupProfile:{groupID:t},elements:{onlineMemberInfo:o}}=e;if(pt(o))return;const{onlineMemberNum:s=0,expireTime:r=this.DEFAULT_EXPIRE_TIME}=o,i=this._onlineMemberCountMap.get(t)||{},n=Date.now();pt(i)?Object.assign(i,{lastReqTime:0,lastSyncTime:0,latestUpdateTime:n,memberCount:s,expireTime:r}):(i.latestUpdateTime=n,i.memberCount=s),ke.d(this._n+"._updateMemberCountByGroupTips info:",i),this._onlineMemberCountMap.set(t,i)}_onBroadcastMessage(e){if(pt(e))return;const t=[],o=e.length;let s=null;for(let r=0;r<o;r++){const o=this.restoreMessageFromSimplified(e[r]);ys[o.event]?(s=this.packMessage(o,o.event),s.isBroadcastMessage=!0,this._broadcastMessageIDMap.has(s.ID)||(t.push(s),this._broadcastMessageIDMap.set(s.ID,1))):ke.w(`${this._n}._onBroadcastMessage unknown event:${o.event}`)}t.length>0&&this._groupModule.emitOuterEvent(g,t)}start(e){if(this._pollingInstanceMap.has(e)){const t=this._pollingInstanceMap.get(e);return void(t.isRunning()||t.start())}const t=new ms({manager:this,groupID:e,onInit:this._updateRequestData.bind(this),onSuccess:this._handleSuccess.bind(this),onFail:this._handleFailure.bind(this)});t.start(),this._pollingInstanceMap.set(e,t),ke.l(`${this._n}.start groupID:${e}`)}handleJoinResult(e){return this._preCheck().then(()=>{const{longPollingKey:t,group:o}=e,s=o.groupID;return this._joinedGroupMap.set(s,o),this._groupModule.updateGroupMap([o]),this._groupModule.deleteUnjoinedAVChatRoom(s),this._groupModule.emitGroupListUpdate(!0,!1),Be(t)?qt({status:qe,group:o}):Promise.resolve()})}startRunLoop(e){return this.handleJoinResult(e).then(()=>{const{longPollingKey:t,group:o,startSeq:s=0}=e,r=o.groupID;return this._pollingRequestInfoMap.set(r,{key:t,startSeq:s}),this.start(r),this._groupModule.isLoggedIn()?qt({status:qe,group:o}):qt({status:qe})})}_preCheck(){if(this._getModule(r).isUnlimitedAVChatRoom())return Promise.resolve();if(!this.hasJoinedAVChatRoom())return Promise.resolve();const[e,t]=this._joinedGroupMap.entries().next().value;if(this._groupModule.isLoggedIn()){if(!(t.selfInfo.role===he||t.ownerID===this._groupModule.getMyUserID()))return this._groupModule.quitGroup(e);this._groupModule.deleteLocalGroupAndConversation(e)}else this._groupModule.deleteLocalGroupAndConversation(e);return this.reset(e),Promise.resolve()}joinWithoutAuth(e){const{groupID:t}=e,o=this._n+".joinWithoutAuth",r=new Kt("joinWithoutAuth");return this._groupModule.request({protocolName:eo,requestData:e}).then(({data:{longPollingKey:e}})=>{if(this._groupModule.probeNetwork().then(([o,s])=>{r.setNetworkType(s).setMessage(`groupID:${t} longPollingKey:${e}`).end(!0)}),Be(e))return Ut({code:Ct});ke.l(`${o} ok. groupID:${t}`);this._getModule(s).setCompleted(`${re}${t}`);const i=new Is({groupID:t});return this.startRunLoop({group:i,longPollingKey:e}),lt({status:qe})}).catch(e=>(ke.e(`${o} failed. groupID:${t} error:`,e),this._groupModule.probeNetwork().then(([o,s])=>{r.setError(e,o,s).setMessage("groupID:"+t).end(!0)}),Ut(e))).finally(()=>{this._groupModule.getModule(i).reportAtOnce()})}getGroupOnlineMemberCount(e){const t=this._onlineMemberCountMap.get(e)||{},o=Date.now();return pt(t)||o-t.lastSyncTime>1e3*t.expireTime&&o-t.latestUpdateTime>1e4&&o-t.lastReqTime>3e3?(t.lastReqTime=o,this._onlineMemberCountMap.set(e,t),this._getGroupOnlineMemberCount(e).then(e=>lt({memberCount:e.memberCount})).catch(e=>Ut(e))):qt({memberCount:t.memberCount})}_getGroupOnlineMemberCount(e){const t=this._n+"._getGroupOnlineMemberCount";return this._groupModule.request({protocolName:fo,requestData:{groupID:e}}).then(o=>{const s=this._onlineMemberCountMap.get(e)||{},{data:{onlineMemberNum:r=0,expireTime:i=this.DEFAULT_EXPIRE_TIME}}=o;ke.l(`${t} ok. groupID:${e} memberCount:${r} expireTime:${i}`);const n=Date.now();return pt(s)&&(s.lastReqTime=n),this._onlineMemberCountMap.set(e,Object.assign(s,{lastSyncTime:n,latestUpdateTime:n,memberCount:r,expireTime:i})),{memberCount:r}}).catch(o=>{ke.w(t+" failed. error:",o);return new Kt("_getGroupOnlineMemberCount").setCode(o.code).setMessage(`groupID:${e} error:${JSON.stringify(o)}`).setNetworkType(this._groupModule.getNetworkType()).end(),Promise.reject(o)})}_getModule(e){return this._groupModule.getModule(e)}setPollingInterval(e){Be(e)||(xe(e)?this._pollingInterval=this.DEFAULT_POLLING_INTERVAL=e:this._pollingInterval=this.DEFAULT_POLLING_INTERVAL=parseInt(e,10))}setPollingIntervalPlus(e){Be(e)||(xe(e)?this.DEFAULT_POLLING_INTERVAL_PLUS=e:this.DEFAULT_POLLING_INTERVAL_PLUS=parseInt(e,10))}setPollingNoMessageCount(e){Be(e)||(xe(e)?this.DEFAULT_POLLING_NO_MESSAGE_COUNT=e:this.DEFAULT_POLLING_NO_MESSAGE_COUNT=parseInt(e,10))}setPollingSimplifiedMessage(e){Be(e)||"0"!==e&&"1"!==e||(this.DEFAULT_POLLING_SIMPLIFIED_MSG=parseInt(e,10))}getPollingInterval(){return this._pollingInterval}onAVChatRoomMemberBanned(e){const{groupID:t}=e.payload.groupProfile;ke.l(`${this._n}.onAVChatRoomMemberBanned groupID:${t}`),this._groupModule.deleteLocalGroupAndConversation(t),this.reset(t)}restartPolling(){ke.l(`${this._n}.restartPolling count:${this._pollingInstanceMap.size}`);for(const e of this._pollingInstanceMap.values())e.stop(),e.start()}getPollingTimerID(e){if(!this._pollingInstanceMap.has(e))return-1;const t=this._pollingInstanceMap.get(e).getPollingTimerID();return ke.l(`${this._n}.getPollingTimerID groupID:${e} timerID:${t}`),t}reset(e){if(e){ke.l(`${this._n}.reset groupID:${e}`);const t=this._pollingInstanceMap.get(e);t&&t.stop(),this._pollingInstanceMap.delete(e),this._joinedGroupMap.delete(e),this._pollingRequestInfoMap.delete(e),this._onlineMemberCountMap.delete(e)}else{ke.l(this._n+".reset all");for(const e of this._pollingInstanceMap.values())e.stop();this._pollingInstanceMap.clear(),this._joinedGroupMap.clear(),this._pollingRequestInfoMap.clear(),this._onlineMemberCountMap.clear(),this._broadcastMessageIDMap.clear()}this.sequencesLinkedList.reset(),this.messageIDLinkedList.reset(),this.receivedMessageCount=0,this._reportMessageStackedCount=0,this._pollingInterval=this.DEFAULT_POLLING_INTERVAL=300,this.DEFAULT_POLLING_NO_MESSAGE_COUNT=20,this.DEFAULT_POLLING_INTERVAL_PLUS=2e3,this._pollingNoMessageCount=0}}class Ls{constructor(e){this.userID="",this.avatar="",this.nick="",this.role="",this.joinTime="",this.lastSendMsgTime="",this.nameCard="",this.muteUntil=0,this.memberCustomField=[],this._initMember(e)}_initMember(e){this.updateMember(e)}updateMember(e){const t=[null,void 0,"",0,NaN];e.memberCustomField&&et(this.memberCustomField,e.memberCustomField),ze(this,e,["memberCustomField","marks"],t)}updateRole(e){["Owner","Admin","Member"].indexOf(e)<0||(this.role=e)}updateMuteUntil(e){Be(e)||(this.muteUntil=Math.floor((Date.now()+1e3*e)/1e3))}updateNameCard(e){Be(e)||(this.nameCard=e)}updateMemberCustomField(e){e&&et(this.memberCustomField,e)}}class Gs{constructor(e){this._groupModule=e,this._n="GroupMemberHandler",this.groupMemberListMap=new Map,this._groupModule.getInnerEmitterInstance().on(Ot.PROFILE_UPDATED,this._onProfileUpdated,this)}_onProfileUpdated({data:e}){for(let t=0;t<e.length;t++){const o=e[t];this.groupMemberListMap.forEach(e=>{e.has(o.userID)&&e.get(o.userID).updateMember({nick:o.nick,avatar:o.avatar})})}}deleteGroupMemberList(e){this.groupMemberListMap.delete(e)}getGroupMemberList({groupID:e,role:o,offset:s=0,count:r=15,filter:i}){const n=this._n+".getGroupMemberList",u=this._groupModule.hasLocalGroup(e);if(ke.l(`${n} groupID:${e} role:${o} offset:${s} count:${r} hasLocalGroup:${u}`),!u)return qt({memberList:[],offset:0});if(this._groupModule.getLocalGroupProfile(e).type===le){if(this._groupModule.canIUse(b.AVCHATROOM_MBR_LIST))return this._getAVChatRoomMemberList({groupID:e,offset:s,filter:i});this._groupModule.outputWarning("LiveOnlineMember")}let a;o!==ge&&o!==he&&o!==de||(a=o);const p=new Kt("getGroupMemberList");let l=0;const c={groupID:e,limit:r>100?100:r,memberRoleFilter:a?[a]:void 0};st({groupID:e})?c.next=""+s:(c.offset=s,l=s+r);let h=[];return this._groupModule.request({protocolName:No,requestData:c}).then(({data:{members:o,memberNum:s,next:r}})=>{if(Be(r)||(l=pt(r)?0:r),!je(o)||0===o.length)return l=0,Promise.resolve([]);this._groupModule.hasLocalGroup(e)&&(this._groupModule.getLocalGroupProfile(e).memberNum=s),h=this._updateLocalGroupMemberMap(e,o);return this._groupModule.getModule(t).getUserProfile({userIDList:o.map(e=>e.userID),tagList:[$e,Pe]})}).then(t=>{const{data:o}=t;if(!je(o)||0===o.length)return qt({memberList:[],offset:l});const i=o.map(e=>({userID:e.userID,nick:e.nick,avatar:e.avatar}));return this._updateLocalGroupMemberMap(e,i),h.length<r&&(l=0),p.setNetworkType(this._groupModule.getNetworkType()).setMessage(`groupID:${e} offset:${s} count:${r}`).end(),ke.l(n+" ok."),lt({memberList:h,offset:l})}).catch(e=>(this._groupModule.probeNetwork().then(([t,o])=>{p.setError(e,t,o).end()}),ke.e(n+" failed. error:",e),Ut(e)))}_getAVChatRoomMemberList({groupID:e,offset:t,filter:o}){const s=this._n+"._getAVChatRoomMemberList",r=new Kt("_getAVChatRoomMemberList");return r.setMessage(`groupID:${e} offset:${t} filter:${o}`),this._groupModule.request({protocolName:Ro,requestData:{groupID:e,offset:t,filter:o}}).then(t=>{const{memberList:o=[],offset:i=0}=t.data;r.setNetworkType(this._groupModule.getNetworkType()).end(),ke.l(`${s} ok. member count:${o.length}, next request timestamp:${i}`);const n=this._updateLocalGroupMemberMap(e,o);return lt({memberList:n,offset:i})}).catch(e=>(this._groupModule.probeNetwork().then(([t,o])=>{r.setError(e,t,o).end()}),ke.e(s+" failed. error:",e),Ut(e)))}getGroupMemberProfile(e){const o="getGroupMemberProfile",s=`${this._n}.${o}`;let r="groupID:"+e.groupID;e.userIDList.length>5?r+=" userIDList.length:"+e.userIDList.length:r+=" userIDList:"+e.userIDList,ke.l(`${s} ${r}`),e.userIDList.length>50&&(e.userIDList=e.userIDList.slice(0,50));const{groupID:i,userIDList:n}=e,u=this._groupModule.getLocalGroupProfile(i);if(u&&ot(u.type)){const e=Nt;return Ut({code:e,message:this._groupModule.getErrorMessage(e,o)})}const a=new Kt(o);return a.setMessage(r),this._getGroupMemberProfileAdvance({...e,userIDList:n}).then(e=>{const{members:o}=e.data;if(!je(o)||0===o.length)return qt([]);this._updateLocalGroupMemberMap(i,o);return this._groupModule.getModule(t).getUserProfile({userIDList:o.map(({userID:e})=>e),tagList:[$e,Pe]})}).then(e=>{const t=e.data.map(({userID:e,nick:t,avatar:o})=>({userID:e,nick:t,avatar:o}));this._updateLocalGroupMemberMap(i,t);const o=n.filter(e=>this.hasLocalGroupMember(i,e)).map(e=>this.getLocalGroupMemberInfo(i,e));return a.setNetworkType(this._groupModule.getNetworkType()).end(),lt({memberList:o})})}addGroupMember(e){const t=this._n+".addGroupMember",{groupID:o}=e,s=this._groupModule.getLocalGroupProfile(o),{type:r}=s,i=new Kt("addGroupMember");if(i.setMessage(`groupID:${o} groupType:${r}`),ot(r)){const e=new ct({code:Gt});return i.setError(e,!0,this._groupModule.getNetworkType()).end(),Ut(e)}return e.userIDList=e.userIDList.map(e=>({userID:e})),ke.l(`${t} groupID:${o}`),this._groupModule.request({protocolName:Eo,requestData:e}).then(({data:{members:o}})=>{ke.l(t+" ok");const r=o.filter(e=>1===e.result).map(e=>e.userID),n=o.filter(e=>0===e.result).map(e=>e.userID),u=o.filter(e=>2===e.result).map(e=>e.userID),a=o.filter(e=>4===e.result).map(e=>e.userID),p=`groupID:${e.groupID}, successUserIDList:${r}, failureUserIDList:${n}, existedUserIDList:${u}, overLimitUserIDList:`+a;return i.setNetworkType(this._groupModule.getNetworkType()).setMoreMessage(p).end(),0===r.length?lt({successUserIDList:r,failureUserIDList:n,existedUserIDList:u,overLimitUserIDList:a}):(s.memberCount+=r.length,this._updateConversationGroupProfile(s),lt({successUserIDList:r,failureUserIDList:n,existedUserIDList:u,overLimitUserIDList:a,group:s}))}).catch(e=>(this._groupModule.probeNetwork().then(([t,o])=>{i.setError(e,t,o).end()}),ke.e(t+" failed. error:",e),Ut(e)))}deleteGroupMember(e){const t=this._n+".deleteGroupMember",{groupID:o,userIDList:s}=e,r=this._groupModule.getLocalGroupProfile(o);if(Be(r))return Ut({code:Mt});if(ot(r.type)){return this._groupModule.canIUse(b.AVCHATROOM_BAN_MBR)?this._banAVChatRoomMember(e):this._groupModule.cannotUseCommercialAbility("deleteGroupMember")}const i=`groupID:${o} ${s.length>5?"userIDList.length:"+s.length:"userIDList:"+s}`;ke.l(`${t} groupID:${o} userIDList:`,s);const n=new Kt("deleteGroupMember");return n.setMessage(i),this._groupModule.request({protocolName:$o,requestData:e}).then(()=>(n.setNetworkType(this._groupModule.getNetworkType()).end(),ke.l(t+" ok"),r.memberCount-=1,this._updateConversationGroupProfile(r),this.deleteLocalGroupMembers(o,s),lt({group:r,userIDList:s}))).catch(e=>(this._groupModule.probeNetwork().then(([t,o])=>{n.setError(e,t,o).end()}),ke.e(t+" failed. error:",e),Ut(e)))}_updateConversationGroupProfile(e){this._groupModule.getModule(s).updateConversationGroupProfile([e])}_banAVChatRoomMember(e){const t=this._n+"._banAVChatRoomMember",{groupID:o,userIDList:s}=e,r=`groupID:${o} ${s.length>5?"userIDList.length:"+s.length:"userIDList:"+s}`,i=new Kt("_banAVChatRoomMember");i.setMessage(r),ke.l(`${t} groupID:${o} userIDList:`,s);const n=this._groupModule.getLocalGroupProfile(o);return Be(e.duration)||0===e.duration?Ut({code:wt}):this._groupModule.request({protocolName:Po,requestData:e}).then(()=>(i.setNetworkType(this._groupModule.getNetworkType()).end(),ke.l(t+" ok"),this.deleteLocalGroupMembers(o,s),lt({group:n,userIDList:s}))).catch(e=>(this._groupModule.probeNetwork().then(([t,o])=>{i.setError(e,t,o).end()}),ke.e(t+" failed. error:",e),Ut(e)))}setGroupMemberMuteTime(e){const{groupID:t,userID:o,muteTime:s}=e,r=this._n+".setGroupMemberMuteTime";if(o===this._groupModule.getMyUserID())return Ut({code:vt});const i=`groupID:${t} userID:${o} muteTime:${s}`;ke.l(`${r} ${i}`);const n=new Kt("setGroupMemberMuteTime");return n.setMessage(i),this.modifyGroupMemberInfo({groupID:t,userID:o,muteTime:s}).then(e=>{n.setNetworkType(this._groupModule.getNetworkType()).end(),ke.l(r+" ok");const o=this._groupModule.getLocalGroupProfile(t);return lt({group:o,member:e})}).catch(e=>(this._groupModule.probeNetwork().then(([t,o])=>{n.setError(e,t,o).end()}),ke.e(r+" failed. error:",e),Ut(e)))}setGroupMemberRole(e){const t=this._n+".setGroupMemberRole",{groupID:o,userID:s,role:r}=e,i=`groupID:${o} userID:${s} role:${r}`,n=this._groupModule.getLocalGroupProfile(o);if(!n||n.type===ue||n.type===le)return Ut({code:Tt});if(n&&n.selfInfo.role!==he)return Ut({code:bt});const u=[ge,de];if(st({groupID:o})&&u.push(me),u.indexOf(r)<0)return Ut({code:At});if(s===this._groupModule.getMyUserID())return Ut({code:St});const a=new Kt("setGroupMemberRole");return a.setMessage(i),ke.l(`${t} ${i}`),this.modifyGroupMemberInfo({groupID:o,userID:s,role:r}).then(e=>(a.setNetworkType(this._groupModule.getNetworkType()).end(),ke.l(t+" ok"),lt({group:n,member:e}))).catch(e=>(this._groupModule.probeNetwork().then(([t,o])=>{a.setError(e,t,o).end()}),ke.e(t+" failed. error:",e),Ut(e)))}_filterProfanity(e,t){const o=this._groupModule.getModule(h);if(!o)return!0;const{isAllowedToSend:s,modifiedText:r}=o.filterText(t[e],A);return!0===s&&(t[e]=r,!0)}setGroupMemberNameCard(e){const t="setGroupMemberNameCard",o=`${this._n}.${t}`;if(e.nameCard&&!1===this._filterProfanity("nameCard",e))return Ut({code:$t});const{groupID:s,userID:r=this._groupModule.getMyUserID(),nameCard:i}=e,n=`groupID:${s} userID:${r} nameCard:${i}`;ke.l(`${o} ${n}`);const u=this._groupModule.getLocalGroupProfile(s);if(u&&ot(u.type)){const e=Nt;return Ut({code:e,message:this._groupModule.getErrorMessage(e,t)})}const a=new Kt(t);return a.setMessage(n),this.modifyGroupMemberInfo({groupID:s,userID:r,nameCard:i}).then(e=>{ke.l(o+" ok"),a.setNetworkType(this._groupModule.getNetworkType()).end();const t=this._groupModule.getLocalGroupProfile(s);return r===this._groupModule.getMyUserID()&&t&&t.setSelfNameCard(i),lt({group:t,member:e})}).catch(e=>(this._groupModule.probeNetwork().then(([t,o])=>{a.setError(e,t,o).end()}),ke.e(o+" failed. error:",e),Ut(e)))}setGroupMemberCustomField(e){const t="setGroupMemberCustomField",o=`${this._n}.${t}`,{groupID:s,userID:r=this._groupModule.getMyUserID(),memberCustomField:i}=e,n=`groupID:${s} userID:${r} memberCustomField:${JSON.stringify(i)}`;ke.l(`${o} ${n}`);const u=this._groupModule.getLocalGroupProfile(s);if(u&&ot(u.type)){const e=Nt;return Ut({code:e,message:this._groupModule.getErrorMessage(e,t)})}const a=new Kt(t);return a.setMessage(n),this.modifyGroupMemberInfo({groupID:s,userID:r,memberCustomField:i}).then(e=>{a.setNetworkType(this._groupModule.getNetworkType()).end(),ke.l(o+" ok");const t=this._groupModule.getLocalGroupProfile(s);return lt({group:t,member:e})}).catch(e=>(this._groupModule.probeNetwork().then(([t,o])=>{a.setError(e,t,o).end()}),ke.e(o+" failed. error:",e),Ut(e)))}modifyGroupMemberInfo(e){let{groupID:t,userID:o}=e,s=void 0;return rt(t)&&(s=t,t=it(s)),this._groupModule.request({protocolName:qo,requestData:{...e,groupID:t,topicID:s}}).then(()=>{if(this.hasLocalGroupMember(t,o)){const s=this.getLocalGroupMemberInfo(t,o);return Be(e.muteTime)||s.updateMuteUntil(e.muteTime),Be(e.role)||s.updateRole(e.role),Be(e.nameCard)||s.updateNameCard(e.nameCard),Be(e.memberCustomField)||s.updateMemberCustomField(e.memberCustomField),s}const s=this._groupModule.getLocalGroupProfile(t);if(s&&!ot(s.type))return this.getGroupMemberProfile({groupID:t,userIDList:[o]}).then(({data:{memberList:[e]}})=>e)})}markGroupMemberList(e){const t=this._n+".markGroupMemberList",{groupID:o,markType:s,enableMark:r,userIDList:i=[]}=e,n=`groupID:${o} markType:${s} enableMark:${r} userIDList count:${i.length}`;ke.l(`${t} ${n}`);let u=2;const a=[];!0===r&&(u=1);let p=[...i];var l;i.length>500&&(p=i.slice(0,500),ke.w(`${t} ${l=500,"the length of userIDList cannot exceed "+l}`)),p.forEach(e=>{a.push({userID:e,markType:[s]})}),p=null;const c=new Kt("markGroupMemberList");return c.setMessage(n),this._groupModule.request({protocolName:Uo,requestData:{groupID:o,operationType:u,memberList:a}}).then(e=>{const{memberList:o=[]}=e.data,s=[],r=[];o.length===i.length?s.push(...i):(o.forEach(e=>{s.push(e.userID)}),i.forEach(e=>{s.includes(e)||r.push(e)}));const n=`success count:${s.length} fail count:${r.length}`;return c.setNetworkType(this._groupModule.getNetworkType()).setMessage(n).end(),ke.l(`${t} ok. ${n}`),lt({successUserIDList:s,failureUserIDList:r})}).catch(e=>(this._groupModule.probeNetwork().then(([t,o])=>{c.setError(e,t,o).end()}),ke.e(t+" failed. error:",e),Ut(e)))}_getGroupMemberProfileAdvance(e){return this._groupModule.request({protocolName:ko,requestData:{...e,memberInfoFilter:e.memberInfoFilter?e.memberInfoFilter:["Role","JoinTime","NameCard","ShutUpUntil"]}})}_updateLocalGroupMemberMap(e,t){if(!je(t)||0===t.length)return[];return t.map(t=>(this.hasLocalGroupMember(e,t.userID)?this.getLocalGroupMemberInfo(e,t.userID).updateMember(t):this.setLocalGroupMember(e,new Ls(t)),this.getLocalGroupMemberInfo(e,t.userID)))}deleteLocalGroupMembers(e,t){const o=this.groupMemberListMap.get(e);o&&t.forEach(e=>{o.delete(e)})}getLocalGroupMemberInfo(e,t){return this.groupMemberListMap.has(e)?this.groupMemberListMap.get(e).get(t):null}setLocalGroupMember(e,t){if(this.groupMemberListMap.has(e))this.groupMemberListMap.get(e).set(t.userID,t);else{const o=(new Map).set(t.userID,t);this.groupMemberListMap.set(e,o)}}getLocalGroupMemberList(e){return this.groupMemberListMap.get(e)}hasLocalGroupMember(e,t){return this.groupMemberListMap.has(e)&&this.groupMemberListMap.get(e).has(t)}hasLocalGroupMemberMap(e){return this.groupMemberListMap.has(e)}reset(){this.groupMemberListMap.clear()}}const Cs=1,bs=15;class Ts{constructor(e){this._groupModule=e,this._n="GroupSystemNoticeHandler",this.pendencyMap=new Map}onNewGroupSystemNotice(e){const{dataList:t,isSyncingEnded:o,isInstantMessage:r}=e;ke.d(`${this._n}.onReceiveSystemNotice count:${t.length}`);const{eventDataList:i,result:n}=this.newSystemNoticeStoredAndSummary({notifiesList:t,isInstantMessage:r});if(i.length>0){this._groupModule.getModule(s).onNewMessage({conversationOptionsList:i,isInstantMessage:r}),this._onReceivedGroupSystemNotice({result:n,isInstantMessage:r})}r?n.length>0&&this._groupModule.emitOuterEvent(g,n):!0===o&&this._clearGroupSystemNotice()}newSystemNoticeStoredAndSummary({notifiesList:e,isInstantMessage:t}){let o=null;const r=e.length;let i=0;const n=[],u={conversationID:ne,unreadCount:0,type:ne,subType:null,lastMessage:null};for(i=0;i<r;i++){const r=e[i],{groupProfile:{communityType:a=0,topicID:p},elements:{topicIDList:l,operationType:c}}=r;if(!(2!==a||pt(p)&&pt(l))){if([17,18,20].includes(c)){this._handleTopicSystemNotice(r);continue}pt(p)||(r.to=p)}if(r.elements.operationType===bs)continue;r.currentUser=this._groupModule.getMyUserID(),r.conversationType=ne,r.conversationID=ne,o=new os(r),o.setElement({type:X,content:{...r.elements,groupProfile:{...r.groupProfile}}}),o.isSystemMessage=!0,(1===o.sequence&&1===o.random||2===o.sequence&&2===o.random)&&(o.sequence=Ye(),o.random=Ye(),o.generateMessageID(),ke.l(`${this._n}.newSystemNoticeStoredAndSummary sequence and random maybe duplicated, regenerate. ID:${o.ID}`));this._groupModule.getModule(s).pushIntoNoticeResult(n,o)&&(t?u.unreadCount++:o.setIsRead(!0),u.subType=o.conversationSubType)}return u.lastMessage=n[n.length-1],{eventDataList:n.length>0?[u]:[],result:n}}_clearGroupSystemNotice(){this._getPendencyList().then(e=>{e.forEach(e=>{this.pendencyMap.set(`${e.from}_${e.groupID}_${e.to}`,e)});const t=this._groupModule.getModule(s).getLocalMessageList(ne),o=[];t.forEach(e=>{const{operatorID:t,operationType:s,groupProfile:r}=e.payload;if(s===Cs){const s=`${t}_${r.groupID}_${r.to}`,i=this.pendencyMap.get(s);i&&xe(i.handled)&&0!==i.handled&&o.push(e)}}),this.deleteGroupSystemNotice({messageList:o})})}deleteGroupSystemNotice(e){const t=this._n+".deleteGroupSystemNotice";return je(e.messageList)&&0!==e.messageList.length?(ke.l(t+" "+e.messageList.map(e=>e.ID)),this._groupModule.request({protocolName:mo,requestData:{messageListToDelete:e.messageList.map(e=>({from:ne,messageSeq:e.clientSequence,messageRandom:e.random}))}}).then(()=>{ke.l(t+" ok");const o=this._groupModule.getModule(s);return e.messageList.forEach(e=>{o.deleteLocalMessage(e)}),lt()}).catch(e=>(ke.e(t+" error:",e),Ut(e)))):qt()}_getPendencyList(e={}){const{type:t,startTime:o=0,limit:s=20}=e;return this._groupModule.request({protocolName:go,requestData:{type:t,startTime:o,limit:s,handleAccount:this._groupModule.getMyUserID()}}).then(e=>{const t=e.data.pendencyList;return 0!==e.data.nextStartTime?this._getPendencyList({startTime:e.data.nextStartTime}).then(e=>[...t,...e]):t})}getGroupApplicationList(){return this._getPendencyList().then(e=>this._getPendencyList({type:ce}).then(t=>(e.push(...t),this._handlePendencyResult(e))).catch(t=>this._handlePendencyResult(e)))}_handlePendencyResult(e){const t=[];return e.forEach(e=>{this.pendencyMap.set(`${e.from}_${e.groupID}_${e.to}`,e),0===e.handled&&t.push({applicant:e.from,applicantNick:e.fromUserNickName,groupName:e.groupName,groupID:e.groupID,authentication:e.authentication,messageKey:e.time,applicationType:e.applicationType,userID:e.userID,note:e.note})}),qt({applicationList:t})}_onReceivedGroupSystemNotice({result:e,isInstantMessage:t}){t&&e.forEach(e=>{switch(e.payload.operationType){case 1:break;case 2:this._onApplyGroupRequestAgreed(e);break;case 3:break;case 4:this._onMemberKicked(e);break;case 5:this._onGroupDismissed(e);break;case 6:break;case 7:this._onInviteGroup(e);break;case 8:this._onQuitGroup(e);break;case 9:this._onSetManager(e);break;case 10:this._onDeleteManager(e);break;case 11:case 12:case 15:break;case 20:this._onMessageRemindTypeSynced(e);break;case 21:this._groupModule.onAVChatRoomMemberBanned(e)}})}_onApplyGroupRequestAgreed(e){const t=e.payload.groupProfile.groupID;this._groupModule.hasLocalGroup(t)||this._groupModule.getGroupProfile({groupID:t}).then(({data:{group:e}})=>{if(e){this._groupModule.updateGroupMap([e]);const t=!e.isSupportTopic;this._groupModule.emitGroupListUpdate(!0,t)}})}_onMemberKicked(e){const t=e.payload.groupProfile.groupID;this._groupModule.hasLocalGroup(t)&&this._groupModule.deleteLocalGroupAndConversation(t)}_onGroupDismissed(e){const t=e.payload.groupProfile.groupID;this._groupModule.hasLocalGroup(t)&&this._groupModule.deleteLocalGroupAndConversation(t);const{_AVChatRoomHandler:o}=this._groupModule;o&&o.checkJoinedAVChatRoomByID(t)&&o.reset(t)}_onInviteGroup(e){const t=e.payload.groupProfile.groupID;this._groupModule.hasLocalGroup(t)||this._groupModule.getGroupProfile({groupID:t}).then(({data:{group:e}})=>{e&&(this._groupModule.updateGroupMap([e]),this._groupModule.emitGroupListUpdate())})}_onQuitGroup(e){const t=e.payload.groupProfile.groupID;this._groupModule.hasLocalGroup(t)&&this._groupModule.deleteLocalGroupAndConversation(t)}_onSetManager(e){const{to:t,groupID:o}=e.payload.groupProfile,s=this._groupModule.getGroupMemberHandler().getLocalGroupMemberInfo(o,t);s&&s.updateRole(ge)}_onDeleteManager(e){const{to:t,groupID:o}=e.payload.groupProfile,s=this._groupModule.getGroupMemberHandler().getLocalGroupMemberInfo(o,t);s&&s.updateRole(de)}_onMessageRemindTypeSynced(e){const{groupID:t}=e.payload.groupProfile,o=e.payload.messageRemindType;this._groupModule.getModule(s).onGroupMessageRemindTypeUpdated({groupID:t,messageRemindType:o})}_handleTopicSystemNotice(e){const{groupProfile:{groupID:t,topicID:s},elements:{operationType:r,topicIDList:i,messageRemindType:n}}=e,u=this._groupModule.getModule(o);17===r?u.onTopicCreated({groupID:t,topicID:s}):18===r?u.onTopicDeleted({groupID:t,topicIDList:i}):20===r&&u.onTopicMessageRemindTypeUpdated({groupID:t,topicID:s,messageRemindType:n})}reset(){this.pendencyMap.clear()}}class As extends class{constructor(e){this._m=e,this._n=""}isLoggedIn(){return this._m.getModule(r).isLoggedIn()}isOversea(){return this._m.getModule(r).isOversea()}isPrivateNetWork(){return this._m.getModule(r).isPrivateNetWork()}getFileDownloadProxy(){return this._m.getModule(r).getFileDownloadProxy()}getMyUserID(){return this._m.getModule(r).getUserID()}getMyTinyID(){return this._m.getModule(r).getTinyID()}getSDKAppID(){return this._m.getModule(r).getSDKAppID()}isIntl(){return this._m.getModule(r).isIntl()}isDevMode(){return this._m.getModule(r).isDevMode()}getModule(e){return this._m.getModule(e)}getPlatform(){return F}getNetworkType(){return this._m.getModule(n).getNetworkType()}probeNetwork(e){return this._m.getModule(n).probe(e)}getCloudConfig(e){return this._m.getModule(p).getCloudConfig(e)}emitOuterEvent(e,t){this._m.getOuterEmitterInstance().emit(e,t)}emitInnerEvent(e,t){this._m.getInnerEmitterInstance().emit(e,t)}getInnerEmitterInstance(){return this._m.getInnerEmitterInstance()}generateTjgID(e){return this._m.getModule(r).getTinyID()+"-"+e.random}filterModifiedMessage(e){if(pt(e))return;const t=e.filter(e=>!0===e.isModified);t.length>0&&this.emitOuterEvent(d,t)}filterUnmodifiedMessage(e){if(pt(e))return[];return e.filter(e=>!1===e.isModified)}request(e){return this._m.getModule(u).request(e)}canIUse(e){return this._m.getModule(c).canIUse(e)}getErrorMessage(e,t,o){return this._m.getErrorMessage(e,t,o)}outputWarning(e,t,o){const s=this.getErrorMessage(e,t,o);s&&ke.w(s)}cannotUseCommercialAbility(e){const t=Et;return Ut({code:t,message:this.getErrorMessage(t,e)})}}{constructor(e){super(e),this._n="GroupModule",this._commonGroupHandler=new rs(this),this._groupAttributesHandler=new ls(this),this._groupCountersHandler=new ds(this),this._AVChatRoomHandler=new Ds(this),this._groupTipsHandler=new ss(this),this._groupSystemNoticeHandler=new Ts(this),this._groupMemberHandler=new Gs(this),this.groupMap=new Map,this._unjoinedAVChatRoomList=new Map,this._receiptDetailCompleteMap=new Map;this.getInnerEmitterInstance().on(Ot.CLOUD_CONFIG_UPDATED,this._onCloudConfigUpdated,this)}_onCloudConfigUpdated(){const e=this.getCloudConfig("polling_interval"),t=this.getCloudConfig("polling_interval_plus"),o=this.getCloudConfig("polling_no_msg_count"),s=this.getCloudConfig("polling_simplified_msg"),r=this.getCloudConfig("paging_grp_count");ke.l(`${this._n}._onCloudConfigUpdated pollingInterval:${e} pollingIntervalPlus:${t} pollingNoMessageCount:${o} pollingSimplifiedMessage:${s} pagingGroupCount:${r}`),this._AVChatRoomHandler.setPollingInterval(e),this._AVChatRoomHandler.setPollingIntervalPlus(t),this._AVChatRoomHandler.setPollingNoMessageCount(o),this._AVChatRoomHandler.setPollingSimplifiedMessage(s),this._commonGroupHandler.setPagingGroupCount(r)}onCheckTimer(e){this.isLoggedIn()&&(this._commonGroupHandler.onCheckTimer(e),this._groupTipsHandler.onCheckTimer(e))}guardForAVChatRoom(e){if(e.conversationType===re){const t=rt(e.to)?it(e.to):e.to;return this.hasLocalGroup(t)?qt():this.getGroupProfile({groupID:t}).then(o=>{const s=o.data.group.type;if(ke.l(`${this._n}.guardForAVChatRoom. groupID:${t} type:${s}`),s===le){const o=ht;return Ut(new ct({code:o,message:this.getErrorMessage(o,e.from,t),data:{message:e}}))}return qt()})}return qt()}checkJoinedAVChatRoomByID(e){return this._AVChatRoomHandler.checkJoinedAVChatRoomByID(e)}onNewGroupMessage(e){this._commonGroupHandler.onNewGroupMessage(e)}updateNextMessageSeq(e){if(je(e)){const t=this.getModule(o);e.forEach(e=>{const o=e.conversationID.replace(re,"");rt(o)&&t.updateLastMessage(o,e.lastMessage),this.groupMap.has(o)&&(this.groupMap.get(o).nextMessageSeq=e.lastMessage.sequence+1)})}}onNewGroupTips(e){this._groupTipsHandler.onNewGroupTips(e)}onGroupMessageRevoked(e){this._commonGroupHandler.onGroupMessageRevoked(e)}onNewGroupSystemNotice(e){this._groupSystemNoticeHandler.onNewGroupSystemNotice(e)}onGroupMessageReadNotice(e){e.dataList.forEach(e=>{const{groupMessageReadNotice:t}=e.elements;if(!Be(t)){const e=this.getModule(s);t.forEach(t=>{const{groupID:o,topicID:s,lastMessageSeq:r}=t;ke.d(`${this._n}.onGroupMessageReadNotice groupID:${o} lastMessageSeq:${r}`);let i=`${re}${o}`,n=!0;pt(s)||(i=`${re}${s}`,n=!1),e.updateIsReadAfterReadReport({conversationID:i,lastMessageSeq:r}),e.updateUnreadCount(i,n),e.clearGroupAtInfoList(i,n)})}})}onReadReceiptList(e){ke.d(this._n+".onReadReceiptList options:",JSON.stringify(e)),e.dataList.forEach(e=>{const{groupProfile:t,elements:o}=e,{groupID:r}=t,i=this.getModule(s),{readReceiptList:n}=o;i.updateReadReceiptInfo({groupID:r,readReceiptList:n})})}onGroupMessageModified(e){ke.d(this._n+".onGroupMessageModified options:",JSON.stringify(e));const t=this.getModule(s);e.dataList.forEach(e=>{t.onMessageModified({...e,conversationType:re,to:e.topicID?e.topicID:e.groupID})})}deleteGroupSystemNotice(e){this._groupSystemNoticeHandler.deleteGroupSystemNotice(e)}initGroupMap(e){this.groupMap.set(e.groupID,new Is(e))}clearGroupMap(){this.groupMap.clear()}deleteGroup(e){this.groupMap.delete(e)}updateGroupMap(e){const t=this.getModule(s);let o;e.forEach(e=>{o=e.groupID,this.groupMap.has(o)?this.groupMap.get(o).updateGroup(e):(this.groupMap.set(o,new Is(e)),t.deleteGroupRomaingMessageInfo(o))});const r=this.getMyUserID();for(const[,s]of this.groupMap)s.selfInfo.userID=r,"Owner"===s.selfInfo.role&&(s.ownerID=r)}getGroupMap(){return this.groupMap}getLocalGroupList(){return[...this.groupMap.values()]}getLocalGroupProfile(e){return this.groupMap.get(e)}sortLocalGroupList(){const e=[...this.groupMap].filter(([e,t])=>!pt(t.lastMessage));e.sort((e,t)=>t[1].lastMessage.lastTime-e[1].lastMessage.lastTime),this.groupMap=new Map([...e])}updateGroupLastMessage(e){this._commonGroupHandler.handleUpdateGroupLastMessage(e)}emitGroupListUpdate(e=!0,t=!0){const o=this.getLocalGroupList();if(e&&this.emitOuterEvent(m),t){const e=JSON.parse(JSON.stringify(o));this.getModule(s).updateConversationGroupProfile(e)}}getMyNameCardByGroupID(e){const t=this.getLocalGroupProfile(e);return t?t.selfInfo.nameCard:""}isPagingGetCompleted(){return this._commonGroupHandler.isPagingGetCompleted()}getMessageRemindType(e){je(e)&&0!==e.length&&(ke.l(`${this._n}.getMessageRemindType groupIDList:${e}`),this.getGroupProfileAdvance({groupIDList:e,responseFilter:{memberInfoFilter:["MsgFlag"]}}).then(e=>{const{successGroupList:t}=e.data,o=this.getModule(s);t.forEach(e=>{o.onGroupMessageRemindTypeUpdated({groupID:e.groupID,messageRemindType:je(e.members)?e.members[0].messageRemindType:""})})}))}getGroupList(){return this._commonGroupHandler.getGroupList()}syncCommunityWithTopic(){return this._commonGroupHandler.syncGroupList(!0)}getGroupProfile(e){const t=this._n+".getGroupProfile",o=new Kt("getGroupProfile"),{groupID:r,groupCustomFieldFilter:i}=e;ke.l(`${t} groupID:${r}`);const n={groupIDList:[r],responseFilter:{groupBaseInfoFilter:["Type","Name","Introduction","Notification","FaceUrl","Owner_Account","CreateTime","InfoSeq","LastInfoTime","LastMsgTime","MemberNum","MaxMemberNum","ApplyJoinOption","NextMsgSeq","ShutUpAllMember","InviteJoinOption"],groupCustomFieldFilter:i,memberInfoFilter:["Role","JoinTime","MsgSeq","MsgFlag","NameCard"]}};return this.getGroupProfileAdvance(n).then(({data:{successGroupList:e,failureGroupList:i}})=>{if(ke.l(t+" ok"),i.length>0)return Ut(i[0]);let n;if(ot(e[0].type)&&!this.hasLocalGroup(r)?n=new Is(e[0]):(this.updateGroupMap(e),n=this.getLocalGroupProfile(r)),!n.isSupportTopic){this.getModule(s).updateConversationGroupProfile([n])}return o.setNetworkType(this.getNetworkType()).setMessage(`groupID:${r} type:${n.type} muteAllMembers:${n.muteAllMembers} ownerID:${n.ownerID}`).end(),lt({group:n})}).catch(s=>(this.probeNetwork().then(([t,r])=>{o.setError(s,t,r).setMessage("groupID:"+e.groupID).end()}),ke.e(t+" failed. error:",s),Ut(s)))}getGroupProfileAdvance(e){const t=this._n+".getGroupProfileAdvance",{groupIDList:o}=e;je(o)&&o.length>50&&(this.outputWarning("GetGroupProfileLimit"),o.length=50);const s=[],r=[];o.forEach(e=>{st({groupID:e})?r.push(e):s.push(e)});const i=[];if(s.length>0){const t=this._getGroupProfileAdvance({...e,groupIDList:s});i.push(t)}if(r.length>0){const t=this._getGroupProfileAdvance({...e,groupIDList:r,relayFlag:s.length>0});i.push(t)}return Promise.all(i).then(e=>{const t=[],o=[];return e.forEach(e=>{t.push(...e.successGroupList),o.push(...e.failureGroupList)}),lt({successGroupList:t,failureGroupList:o})}).catch(e=>(ke.e(t+" failed. error:",e),Ut(e)))}_getGroupProfileAdvance(e){const{relayFlag:t=!1,...o}=e;return this.request({protocolName:zt,requestData:o}).then(e=>{ke.l(this._n+"._getGroupProfileAdvance ok.");const{groups:t}=e.data;return{successGroupList:t.filter(e=>Be(e.errorCode)||0===e.errorCode),failureGroupList:t.filter(e=>e.errorCode&&0!==e.errorCode).map(e=>new ct({code:e.errorCode,message:e.errorInfo,data:{groupID:e.groupID}}))}}).catch(o=>t&&st({groupID:e.groupIDList[0]})?{successGroupList:[],failureGroupList:[]}:Ut(o))}createGroup(o){const s=[ae,ue,pe,le,ce],r=this._n+".createGroup",{type:i,groupID:n}=o;if(o.name&&!1===this._filterProfanity("name",o))return Ut({code:$t});if(o.introduction&&!1===this._filterProfanity("introduction",o))return Ut({code:$t});if(o.notification&&!1===this._filterProfanity("notification",o))return Ut({code:$t});if(!s.includes(i))return Ut({code:dt});if(!st({type:i})){if(!pt(n)&&st({groupID:n}))return Ut({code:_t});o.isSupportTopic=void 0}if(ot(i)&&!Be(o.memberList)&&o.memberList.length>0&&(o.memberList=void 0),this._canIUseJoinOption(i)||Be(o.joinOption)||(o.joinOption=void 0),st({type:i})){if(!pt(n)&&!st({groupID:n}))return Ut({code:_t});o.isSupportTopic=!0===o.isSupportTopic?1:0}const u=new Kt("createGroup");ke.l(r+" options:",o);let a=null,p=[];return this.request({protocolName:Xt,requestData:{...o,ownerID:this.getMyUserID(),webPushFlag:1}}).then(s=>{const{groupID:i,overLimitUserIDList:n=[]}=s.data;a=i,p=n;const l=`groupType:${o.type} groupID:${i} overLimitUserIDList:${n}`;if(u.setNetworkType(this.getNetworkType()).setMessage(l).end(),ke.l(`${r} ok. ${l}`),o.type===le)return this.getGroupProfile({groupID:i});if(o.type===ce&&1===o.isSupportTopic)return this.getGroupProfile({groupID:i});pt(o.memberList)||pt(n)||(o.memberList=o.memberList.filter(e=>-1===n.indexOf(e.userID))),this.updateGroupMap([{...o,groupID:i}]);const c=this.getModule(e);let h="",g=0;o.type===ce?(h=this.isIntl()?"Create Community":"创建社群",g=1):h=this.isIntl()?"Create Group":"创建群组";const d=this.getModule(t).getMyNick(),m=c.createCustomMessage({to:i,conversationType:re,payload:{data:JSON.stringify({businessID:"group_create",content:h,cmd:g,opUser:d||this.getMyUserID(),version:4})}});return c.sendMessageInstance(m),this.emitGroupListUpdate(),this.getGroupProfile({groupID:i})}).then(e=>{const{group:t}=e.data,{nameCard:o,joinTime:s}=t.selfInfo;return t.updateSelfInfo({nameCard:o,joinTime:s,messageRemindType:ye,role:he}),lt({group:t,overLimitUserIDList:p})}).catch(e=>{if(u.setMessage("groupType:"+o.type),this.probeNetwork().then(([t,o])=>{u.setError(e,t,o).end()}),10010===e.code||10007===e.code){this.updateGroupMap([{...o,groupID:a}]);const e=this.getLocalGroupProfile(a);return e.selfInfo.role=he,ke.l(r+" success, but failed to get group profile."),lt({group:e,overLimitUserIDList:p})}return ke.e(r+" failed. error:",e),Ut(e)})}dismissGroup(e){const t=this._n+".dismissGroup";if(this.hasLocalGroup(e)&&this.getLocalGroupProfile(e).type===ue)return Ut(new ct({code:yt}));const o=new Kt("dismissGroup");return o.setMessage("groupID:"+e),ke.l(`${t} groupID:${e}`),this.request({protocolName:Yt,requestData:{groupID:e}}).then(()=>(o.setNetworkType(this.getNetworkType()).end(),ke.l(t+" ok"),this.deleteLocalGroupAndConversation(e),this.checkJoinedAVChatRoomByID(e)&&this._AVChatRoomHandler.reset(e),this._groupAttributesHandler.deleteLocalGroupAttributes(e),lt({groupID:e}))).catch(e=>(this.probeNetwork().then(([t,s])=>{o.setError(e,t,s).end()}),ke.e(t+" failed. error:",e),Ut(e)))}updateGroupProfile(e){const t=this._n+".updateGroupProfile";if(this.hasLocalGroup(e.groupID)){const o=this.getLocalGroupProfile(e.groupID).type;this._canIUseJoinOption(o)||Be(e.joinOption)||(ke.w(t+" joinOption is unavailable for Work/Meeting/AVChatRoom"),e.joinOption=void 0)}if(Be(e.muteAllMembers)||(e.muteAllMembers?e.muteAllMembers="On":e.muteAllMembers="Off"),e.name&&!1===this._filterProfanity("name",e))return Ut({code:$t});if(e.introduction&&!1===this._filterProfanity("introduction",e))return Ut({code:$t});if(e.notification&&!1===this._filterProfanity("notification",e))return Ut({code:$t});const o=new Kt("updateGroupProfile");return o.setMessage(JSON.stringify(e)),ke.l(`${t} groupID:${e.groupID}`),this.request({protocolName:Qt,requestData:e}).then(()=>{if(o.setNetworkType(this.getNetworkType()).end(),ke.l(t+" ok"),this.hasLocalGroup(e.groupID)){this.groupMap.get(e.groupID).updateGroup(e)}return lt({group:this.groupMap.get(e.groupID)})}).catch(e=>(this.probeNetwork().then(([t,s])=>{o.setError(e,t,s).end()}),ke.l(t+" failed. error:",e),Ut(e)))}_filterProfanity(e,t){const o=this.getModule(h);if(!o)return!0;const{isAllowedToSend:s,modifiedText:r}=o.filterText(t[e],T);return!0===s&&(t[e]=r,!0)}joinGroup(e){const{groupID:t,type:o}=e,s=this._n+".joinGroup";if(o===ue)return Ut({code:mt});if(this.deleteUnjoinedAVChatRoom(t),this.hasLocalGroup(t)){if(!this.isLoggedIn())return qt({status:De});const o=new Kt("applyJoinGroup");return this.getGroupProfile({groupID:t}).then(()=>(o.setNetworkType(this.getNetworkType()).setMessage(`groupID:${t} joinedStatus:${De}`).end(),qt({status:De}))).catch(r=>(o.setNetworkType(this.getNetworkType()).setMessage(`groupID:${t} unjoined`).end(),ke.w(`${s} ${t} was unjoined, now join!`),this.groupMap.delete(t),this.applyJoinGroup(e)))}return ke.l(`${s} groupID:${t}`),this.isLoggedIn()?this.applyJoinGroup(e):this._AVChatRoomHandler.joinWithoutAuth(e)}applyJoinGroup(e){const t=this._n+".applyJoinGroup",{groupID:o,applyMessage:r}=e;if(!pt(r)&&!1===this._filterProfanity("applyMessage",e))return Ut({code:$t});const i=new Kt("applyJoinGroup"),n={...e},u=this.canIUse(b.AVCHATROOM_HISTORY_MSG);u&&(n.historyMessageFlag=1);return this.getModule(s).deleteTopicRoamingMessageInfo(o),this.request({protocolName:Zt,requestData:n}).then(({data:{joinedStatus:e,longPollingKey:s,startSeq:r,avChatRoomFlag:n,avChatRoomKey:a,messageList:p}})=>{const l=`groupID:${o} joinedStatus:${e} longPollingKey:${s} startSeq:${r} avChatRoomFlag:${n} canGetAVChatRoomHistoryMessage:${u}, history message count:`+(pt(p)?0:p.length);switch(i.setNetworkType(this.getNetworkType()).setMessage(""+l).end(),ke.l(`${t} ok. ${l}`),e){case Ue:return lt({status:Ue});case qe:return this.getGroupProfile({groupID:o}).then(({data:{group:e}})=>this._handleJoinResult({group:e,avChatRoomFlag:n,longPollingKey:s,startSeq:r,avChatRoomKey:a,messageList:p})).catch(()=>{const e=new Is({groupID:o});return this._handleJoinResult({group:e,avChatRoomFlag:n,longPollingKey:s,startSeq:r,avChatRoomKey:a,messageList:p})});default:{const e=new ct({code:Lt});return ke.e(t+" failed. error:",e),Ut(e)}}}).catch(e=>(i.setMessage("groupID:"+o),this.probeNetwork().then(([t,o])=>{i.setError(e,t,o).end()}),ke.e(t+" failed. error:",e),Ut(e)))}_handleJoinResult(e){const{group:t,avChatRoomFlag:o,longPollingKey:r,startSeq:i,avChatRoomKey:n,messageList:u}=e,{groupID:a}=t;if(1===o){let e;return this.getModule(s).setCompleted(`${re}${a}`),this._groupAttributesHandler.initGroupAttributesCache({groupID:a,avChatRoomKey:n}),this._groupCountersHandler.initGroupCountersCache({groupID:a,avChatRoomKey:n}),e=Be(r)?this._AVChatRoomHandler.handleJoinResult({group:t}):this._AVChatRoomHandler.startRunLoop({group:t,longPollingKey:r,startSeq:i}),e.then(()=>{this._onAVChatRoomHistoryMessage(u)}),e}return this.emitGroupListUpdate(!0,!1),lt({status:qe,group:t})}quitGroup(e){const t=this._n+".quitGroup";ke.l(`${t} groupID:${e}`);const o=this.checkJoinedAVChatRoomByID(e);if(!o&&!this.hasLocalGroup(e))return Ut({code:Dt});if(o&&!this.isLoggedIn())return ke.l(`${t} anonymously ok. groupID:${e}`),this.deleteLocalGroupAndConversation(e),this._AVChatRoomHandler.reset(e),qt({groupID:e});const s=new Kt("quitGroup");return s.setMessage("groupID:"+e),this.request({protocolName:to,requestData:{groupID:e}}).then(()=>(s.setNetworkType(this.getNetworkType()).end(),ke.l(t+" ok"),this.deleteLocalGroupAndConversation(e),o&&this._AVChatRoomHandler.reset(e),this._groupAttributesHandler.deleteLocalGroupAttributes(e),lt({groupID:e}))).catch(e=>(this.probeNetwork().then(([t,o])=>{s.setError(e,t,o).end()}),ke.e(t+" failed. error:",e),Ut(e)))}searchGroupByID(e){const t=this._n+".searchGroupByID",o={groupIDList:[e]},s=new Kt("searchGroupByID");return s.setMessage("groupID:"+e),ke.l(`${t} groupID:${e}`),this.request({protocolName:oo,requestData:o}).then(({data:{groupProfile:e}})=>{if(0!==e[0].errorCode)throw new ct({code:e[0].errorCode,message:e[0].errorInfo});return s.setNetworkType(this.getNetworkType()).end(),ke.l(t+" ok"),lt({group:new Is(e[0])})}).catch(e=>(this.probeNetwork().then(([t,o])=>{s.setError(e,t,o).end()}),ke.w(t+" failed. error:",e),Ut(e)))}changeGroupOwner(e){const t=this._n+".changeGroupOwner";if(this.hasLocalGroup(e.groupID)&&this.getLocalGroupProfile(e.groupID).type===le)return Ut({code:ft});if(e.newOwnerID===this.getMyUserID())return Ut({code:It});const o=new Kt("changeGroupOwner");return o.setMessage(`groupID:${e.groupID} newOwnerID:${e.newOwnerID}`),ke.l(`${t} groupID:${e.groupID}`),this.request({protocolName:so,requestData:e}).then(()=>{o.setNetworkType(this.getNetworkType()).end(),ke.l(t+" ok");const{groupID:s,newOwnerID:r}=e;this.groupMap.get(s).ownerID=r;const i=this._groupMemberHandler.getLocalGroupMemberList(s);if(i instanceof Map){const e=i.get(this.getMyUserID());Be(e)||(e.updateRole("Member"),this.groupMap.get(s).selfInfo.role="Member");const t=i.get(r);Be(t)||t.updateRole("Owner")}return this.emitGroupListUpdate(!0,!1),lt({group:this.groupMap.get(s)})}).catch(e=>(this.probeNetwork().then(([t,s])=>{o.setError(e,t,s).end()}),ke.e(t+" failed. error:",e),Ut(e)))}getGroupApplicationList(){return this._groupSystemNoticeHandler.getGroupApplicationList()}handleGroupApplication(e){const t=this._n+".handleGroupApplication",{handleAction:o,handleMessage:s,message:r,application:i}=e;let n,u,a,p,l;r?(n=r.payload.operatorID,u=r.payload.groupProfile.groupID,a=r.payload.authentication,p=r.payload.messageKey):i&&(n=i.applicant,u=i.groupID,a=i.authentication,p=i.messageKey);let c=ro;i&&2===i.applicationType&&(c=io,l=i.userID);const h=new Kt("handleGroupApplication");return h.setMessage("groupID:"+u),ke.l(`${t} groupID:${u}`),this.request({protocolName:c,requestData:{handleAction:o,handleMessage:s,applicant:n,invitee:l,groupID:u,authentication:a,messageKey:p}}).then(()=>(h.setNetworkType(this.getNetworkType()).end(),ke.l(t+" ok"),r&&this._groupSystemNoticeHandler.deleteGroupSystemNotice({messageList:[e.message]}),lt({group:this.getLocalGroupProfile(u)}))).catch(e=>(this.probeNetwork().then(([t,o])=>{h.setError(e,t,o).end()}),ke.e(t+" failed. error",e),Ut(e)))}handleGroupInvitation(e){const t=this._n+".handleGroupInvitation",{groupProfile:{groupID:o},authentication:s,messageKey:r,operatorID:i}=e.message.payload,{handleAction:n}=e,u=new Kt("handleGroupInvitation");return u.setMessage(`groupID:${o} inviter:${i} handleAction:${n}`),ke.l(`${t} groupID:${o} inviter:${i} handleAction:${n}`),this.request({protocolName:no,requestData:{...e,inviter:i,groupID:o,authentication:s,messageKey:r}}).then(()=>(u.setNetworkType(this.getNetworkType()).end(),ke.l(t+" ok"),this._groupSystemNoticeHandler.deleteGroupSystemNotice({messageList:[e.message]}),lt({group:this.getLocalGroupProfile(o)}))).catch(e=>(this.probeNetwork().then(([t,o])=>{u.setError(e,t,o).end()}),ke.e(t+" failed. error",e),Ut(e)))}getGroupOnlineMemberCount(e){return this._AVChatRoomHandler.checkJoinedAVChatRoomByID(e)?this._AVChatRoomHandler.getGroupOnlineMemberCount(e):qt({memberCount:0})}hasLocalGroup(e){return this.groupMap.has(e)}deleteLocalGroupAndConversation(e){const t=this.checkJoinedAVChatRoomByID(e);if(ke.l(`${this._n}.deleteLocalGroupAndConversation isJoinedAVChatRoom:${t}`),t){this.getModule(s).deleteLocalConversation(`${re}${e}`)}if(st({groupID:e})){const t=this.getLocalGroupProfile(e);if(t&&!0===t.isSupportTopic){this.getModule(o).deleteTopicListInCommunity(e)}}this._deleteLocalGroup(e),this.emitGroupListUpdate(!0,!1)}_deleteLocalGroup(e){this.groupMap.delete(e),this._groupMemberHandler.deleteGroupMemberList(e)}sendMessage(e,t){if(je(e._receiverList)&&e._receiverList.length>0){if(!this.canIUse(b.MSG_TO_SPECIFIED_GRP_MBR))return this.cannotUseCommercialAbility("group direct messages")}const o=this.createGroupMessagePack(e,t);return this.request(o)}createGroupMessagePack(e,t){let o=null;t&&t.offlinePushInfo&&(o=t.offlinePushInfo);let s="";Ve(e.cloudCustomData)&&e.cloudCustomData.length>0&&(s=e.cloudCustomData);const r=[];if(He(t)&&He(t.messageControlInfo)){const{excludedFromUnreadCount:e,excludedFromLastMessage:o,excludedFromContentModeration:s}=t.messageControlInfo;!0===e&&r.push("NoUnread"),!0===o&&r.push("NoLastMsg"),!0===s&&r.push("NoMsgCheck")}let i=void 0;je(e._receiverList)&&e._receiverList.length>0&&(i=e._receiverList,e._receiverList.length>50&&(i=e._receiverList.slice(0,50),this.outputWarning("ReceiverListLimit")));const n=this.isOnlineMessage(e,t)?1:0,u=e.getGroupAtInfoList(),a={fromAccount:this.getMyUserID(),groupID:e.to,msgBody:e.getElements(),cloudCustomData:s,random:e.random,priority:e.priority,clientSequence:e.clientSequence,groupAtInfo:e.type!==V||pt(u)?void 0:u,onlineOnlyFlag:n,clientTime:e.clientTime,offlinePushInfo:o?{pushFlag:!0===o.disablePush?1:0,title:o.title||"",desc:o.description||"",ext:o.extension||"",apnsInfo:{badgeMode:!0===o.ignoreIOSBadge?1:0,isVoipPush:this._isVoipPush(o)},androidInfo:{OPPOChannelID:o.androidOPPOChannelID||""}}:void 0,messageControlInfo:0===n?r:void 0,needReadReceipt:!0!==e.needReadReceipt||this.isMessageFromOrToAVChatroom(e.to)?0:1,receiverList:i,isSupportExtension:!0===e.isSupportExtension?1:0,isRelayMessage:!0===e._relayFlag?1:0};return rt(e.to)&&(a.groupID=it(e.to),a.topicID=e.to),{protocolName:Jt,tjgID:this.generateTjgID(e),requestData:a}}_isVoipPush(e){let t=void 0;return Be(e.disableVoipPush)||(t=!1===e.disableVoipPush?1:0),t}revokeMessage(e){const t={groupID:e.to,msgSeqList:[{msgSeq:e.sequence}]};return rt(e.to)&&(t.groupID=it(e.to),t.topicID=e.to),this.request({protocolName:uo,requestData:t})}deleteMessage(e){const{to:t,keyList:o}=e;ke.l(`${this._n}.deleteMessage groupID:${t} count:${o.length}`);const s={groupID:t,deleter:this.getMyUserID(),keyList:o};return rt(t)&&(s.groupID=it(t),s.topicID=t),this.request({protocolName:Io,requestData:s})}modifyRemoteMessage(e){const{to:t,sequence:o,payload:s,type:r,version:i=0,cloudCustomData:n}=e;let u=t,a=void 0;rt(t)&&(u=it(t),a=t);let p=void 0;return function(e){return e===V||e===Y||e===W||e===K}(r)&&(p=[],p.push({type:r,content:s})),this.request({protocolName:yo,requestData:{groupID:u,topicID:a,sequence:o,version:i,elements:p,cloudCustomData:n}})}getRoamingMessage(e){const t=this._n+".getRoamingMessage";let{conversationID:o,groupID:r,sequence:i}=e;const n=new Kt("getGroupRoamingMessages");let u=0,a=void 0;return rt(r)&&(a=r,r=it(a)),this._computeLastSequence({groupID:r,topicID:a,sequence:i}).then(e=>(u=e,ke.l(`${t} groupID:${r} startSequence:${u}`),this.request({protocolName:po,requestData:{groupID:r,count:21,sequence:u,topicID:a}}))).then(e=>{const{messageList:i,complete:p,invisibleSequenceList:l=[]}=e.data;Be(i)?ke.l(`${t} ok. complete:${p} but messageList is undefined!`):ke.l(`${t} ok. complete:${p} count:${i.length}`);let c=this._getMinSequence(l,i)-1;n.setNetworkType(this.getNetworkType()).setMessage(`groupID:${r} topicID:${a} startSequence:${u} complete:${p} nextSequence:${c}`).end();const h=this.getModule(s);let g=[];return pt(i)||(h.updateRoamingMessageSequence(o,c),g=h.onRoamingMessage(i,o),h.updateIsRead(o),h.patchConversationLastMessage(o)),(2===p||c<1)&&(h.setCompleted(o),c=""),ke.l(`${t} nextReqID:${c}, stored message count:${g.length}, invisible sequence count:${l.length}`),{nextReqID:c+"",storedMessageList:g}}).catch(e=>(this.probeNetwork().then(([t,o])=>{n.setError(e,t,o).setMessage(`groupID:${r} topicID:${a} startSequence:${u}`).end()}),ke.w(t+" failed. error:",e),Ut(e)))}_getGroupIDOfMessage(e){return e.conversationID.replace(re,"")}_getMinSequence(e,t){let o=0;pt(t)||(o=t[t.length-1].sequence);let s=0;if(!pt(e)){s=e[e.length-1]}return ke.l(`${this._n}._getMinSequence minVisibleSequence:${o} minInvisibleSequence:${s}`),s>0&&s<o?s:o}getReadReceiptList(e){const t=this._n+".getReadReceiptList",o=this._getGroupIDOfMessage(e[0]),s=this.getMyUserID(),r=e.filter(e=>e.from===s&&!0===e.needReadReceipt).map(e=>({sequence:e.sequence}));if(ke.l(`${t} groupID:${o} sequenceList:${JSON.stringify(r)}`),0===r.length)return qt({messageList:e});const i=new Kt("getReadReceiptList");return i.setMessage("groupID:"+o),this.request({protocolName:lo,requestData:{groupID:o,sequenceList:r}}).then(o=>{i.end(),ke.l(t+" ok");const{readReceiptList:s}=o.data;return je(s)&&s.forEach(t=>{e.forEach(e=>{0===t.code&&t.sequence===e.sequence&&(e.readReceiptInfo.readCount=t.readCount,e.readReceiptInfo.unreadCount=t.unreadCount)})}),lt({messageList:e})}).catch(e=>(this.probeNetwork().then(([t,o])=>{i.setError(e,t,o).end()}),ke.w(t+" failed. error:",e),Ut(e)))}sendReadReceipt(e){const t=this._n+".sendReadReceipt",o=this._getGroupIDOfMessage(e[0]),s=new Kt("sendReadReceipt");s.setMessage("groupID:"+o);const r=this.getMyUserID(),i=e.filter(e=>e.from!==r&&!0===e.needReadReceipt).map(e=>({sequence:e.sequence}));return 0===i.length?Ut({code:gt}):(ke.l(`${t}. sequenceList:${JSON.stringify(i)}`),this.request({protocolName:co,requestData:{groupID:o,sequenceList:i}}).then(e=>(s.end(),ke.l(t+" ok"),lt())).catch(e=>(this.probeNetwork().then(([t,o])=>{s.setError(e,t,o).end()}),ke.w(t+" failed. error:",e),Ut(e))))}getReadReceiptDetail(e){const{message:t,filter:o,cursor:s,count:r}=e,i=this._getGroupIDOfMessage(t),n=t.ID,u=t.sequence,a=this._n+".getReadReceiptDetail",p=this._receiptDetailCompleteMap.get(n)||!1,l=0!==o&&1!==o?0:o,c=Ve(s)?s:"",h=!xe(r)||r<=0||r>=100?100:r,g=`groupID:${i} sequence:${u} cursor:${c} filter:${l} completeFlag:${p}`;ke.l(`${a} ${g}`);const d={cursor:"",isCompleted:!1,messageID:n,unreadUserIDList:[],readUserIDList:[]},m=new Kt("getReadReceiptDetail");return m.setMessage(g),this.request({protocolName:ho,requestData:{groupID:i,sequence:u,flag:l,cursor:c,count:h}}).then(e=>{m.end();const{cursor:t,isCompleted:o,unreadUserIDList:s,readUserIDList:r}=e.data;return d.cursor=t,1===o&&(d.isCompleted=!0,this._receiptDetailCompleteMap.set(n,!0)),0===l?d.readUserIDList=r.map(e=>e.userID):1===l&&(d.unreadUserIDList=s.map(e=>e.userID)),ke.l(a+" ok"),lt(d)}).catch(e=>(this.probeNetwork().then(([t,o])=>{m.setError(e,t,o).end()}),ke.w(a+" failed. error:",e),Ut(e)))}getRoamingMessagesHopping(e){const t=this._n+".getRoamingMessagesHopping",o=new Kt("getGroupRoamingMessagesHopping");let{groupID:r,count:i,sequence:n,direction:u}=e,a=n;1===u&&(a=n+i-1);let p=void 0;rt(r)&&(p=r,r=it(p));const l=`${p?"topicID:"+p:"groupID:"+r} sequence:${n} direction:${u}`;return ke.l(`${t} ${l}`),this.request({protocolName:po,requestData:{groupID:r,topicID:p,count:i,sequence:a}}).then(r=>{const{messageList:i,complete:a}=r.data,p=`complete:${a} count:${i?i.length:0}`;if(ke.l(`${t} ok. ${p}`),o.setNetworkType(this.getNetworkType()).setMessage(`${l} ${p}`).end(),2===a||pt(i)){const e=this._computeResult();return lt(e)}const c=`${re}${e.groupID}`,h=this.getModule(s).onRoamingMessage(i,c,!1),g=this._computeResult({direction:u,sequence:n,remoteMessageList:i,processedMessageList:h});return lt(g)}).catch(e=>(this.probeNetwork().then(([t,s])=>{o.setError(e,t,s).setMessage(`groupID:${r} sequence:${n} count:${i}`).end()}),ke.w(t+" failed. error:",e),Ut(e)))}_computeResult(e){const t={messageList:[],isCompleted:!1,nextMessageSeq:""};if(Be(e))return t.isCompleted=!0,t;const{direction:o,sequence:s,remoteMessageList:r=[],processedMessageList:i=[]}=e,n=r.length;return 1===o?(t.nextMessageSeq=r[0].sequence+1,i.forEach(e=>{e.sequence>=s&&t.messageList.push(e)}),0===t.messageList.length&&r[0].sequence<s&&(t.isCompleted=!0,t.nextMessageSeq=""),t):(t.nextMessageSeq=r[n-1].sequence-1,t.messageList=[...i],0===t.nextMessageSeq&&(t.isCompleted=!0,t.nextMessageSeq=""),t)}setMessageRead({conversationID:e,lastMessageSeq:t}){const r=this._n+".setMessageRead";ke.l(`${r} conversationID:${e} lastMessageSeq:${t}`),xe(t)||this.outputWarning("DoNotModifyLastSeq");const i=new Kt("setGroupMessageRead");i.setMessage(`${e}-${t}`);let n=e.replace(re,""),u=void 0;return rt(n)&&(u=n,n=it(u)),this.request({protocolName:ao,requestData:{groupID:n,topicID:u,messageReadSeq:t}}).then(()=>{i.setNetworkType(this.getNetworkType()).end(),ke.l(r+" ok.");const a=this.getModule(s);a.updateIsReadAfterReadReport({conversationID:e,lastMessageSeq:t});let p=!0;if(!Be(u)){p=!1;const e=this.getModule(o).getLocalTopic(n,u);e&&e.updateSelfInfo({readedSequence:t})}return a.updateUnreadCount(e,p),lt()}).catch(e=>(this.probeNetwork().then(([t,o])=>{i.setError(e,t,o).end()}),ke.l(r+" failed. error:",e),Ut(e)))}_computeLastSequence(e){const{groupID:t,topicID:o,sequence:s}=e;return s>0?Promise.resolve(s):Be(o)||this.hasLocalGroup(t)?Be(o)?this.getGroupLastSequence(t):this.getTopicLastSequence({groupID:t,topicID:o}):Promise.resolve(0)}getGroupLastSequence(e){const t=this._n+".getGroupLastSequence",o=new Kt("getGroupLastSequence");let r=0,i="";if(this.hasLocalGroup(e)){const s=this.getLocalGroupProfile(e),n=s.lastMessage;if(n.lastSequence>0&&!1===n.onlineOnlyFlag)return r=n.lastSequence,i=`got lastSequence:${r} from local group profile[lastMessage.lastSequence]. groupID:${e}`,ke.l(`${t} ${i}`),o.setNetworkType(this.getNetworkType()).setMessage(""+i).end(),Promise.resolve(r);if(s.nextMessageSeq>1)return r=s.nextMessageSeq-1,i=`got lastSequence:${r} from local group profile[nextMessageSeq]. groupID:${e}`,ke.l(`${t} ${i}`),o.setNetworkType(this.getNetworkType()).setMessage(""+i).end(),Promise.resolve(r)}const n="GROUP"+e,u=this.getModule(s).getLocalConversation(n);if(u&&u.lastMessage.lastSequence&&!1===u.lastMessage.onlineOnlyFlag)return r=u.lastMessage.lastSequence,i=`got lastSequence:${r} from local conversation profile[lastMessage.lastSequence]. groupID:${e}`,ke.l(`${t} ${i}`),o.setNetworkType(this.getNetworkType()).setMessage(""+i).end(),Promise.resolve(r);const a={groupIDList:[e],responseFilter:{groupBaseInfoFilter:["NextMsgSeq"]}};return this.getGroupProfileAdvance(a).then(({data:{successGroupList:s}})=>(pt(s)?ke.l(`${t} successGroupList is empty. groupID:${e}`):(r=s[0].nextMessageSeq-1,i=`got lastSequence:${r} from getGroupProfileAdvance. groupID:${e}`,ke.l(`${t} ${i}`)),o.setNetworkType(this.getNetworkType()).setMessage(""+i).end(),r)).catch(s=>(this.probeNetwork().then(([t,r])=>{o.setError(s,t,r).setMessage("get lastSequence failed from getGroupProfileAdvance. groupID:"+e).end()}),ke.w(t+" failed. error:",s),Ut(s)))}getTopicLastSequence({groupID:e,topicID:t}){const s=this._n+".getTopicLastSequence",r=new Kt("getTopicLastSequence");let i=0,n="";const u=this.getModule(o);return u.hasLocalTopic(e,t)?(i=u.getLocalTopic(e,t).nextMessageSeq-1,n=`get lastSequence:${i} from local topic info[nextMessageSeq]. topicID:${t}`,ke.l(`${s} ${n}`),r.setNetworkType(this.getNetworkType()).setMessage(""+n).end(),Promise.resolve(i)):u.getTopicList({groupID:e,topicIDList:[t]}).then(({data:{successTopicList:e}})=>(pt(e)?ke.l(`${s} successTopicList is empty. topicID:${t}`):(i=e[0].nextMessageSeq-1,n=`get lastSequence:${i} from getTopicList. topicID:${t}`,ke.l(`${s} ${n}`)),r.setNetworkType(this.getNetworkType()).setMessage(""+n).end(),i)).catch(e=>(this.probeNetwork().then(([o,s])=>{r.setError(e,o,s).setMessage("get lastSequence failed from getTopicList. topicID:"+t).end()}),ke.w(s+" failed. error:",e),Ut(e)))}isMessageFromOrToAVChatroom(e){return this._AVChatRoomHandler.checkJoinedAVChatRoomByID(e)}hasJoinedAVChatRoom(){return this._AVChatRoomHandler.hasJoinedAVChatRoom()}getJoinedAVChatRoom(){return this._AVChatRoomHandler.getJoinedAVChatRoom()}isOnlineMessage(e,t){return!(!this._canIUseOnlineOnlyFlag(e)||!t||!0!==t.onlineUserOnly)}_canIUseOnlineOnlyFlag(e){const t=this.getJoinedAVChatRoom();return!t||!t.includes(e.to)||e.conversationType!==re}_onAVChatRoomHistoryMessage(e){if(pt(e))return;ke.l(`${this._n}._onAVChatRoomHistoryMessage count:${e.length}`);const t=[];e.forEach(e=>{t.push({...e,isHistoryMessage:1})}),this.onAVChatRoomMessage(t)}onAVChatRoomMessage(e){this._AVChatRoomHandler.onMessage(e)}onAVChatRoomMemberBanned(e){this._AVChatRoomHandler.onAVChatRoomMemberBanned(e)}getGroupSimplifiedInfo(e){const t=new Kt("getGroupSimplifiedInfo"),o={groupIDList:[e],responseFilter:{groupBaseInfoFilter:["Type","Name"]}};return this.getGroupProfileAdvance(o).then(({data:{successGroupList:o}})=>(t.setNetworkType(this.getNetworkType()).setMessage(`groupID:${e} type:${o[0].type}`).end(),o[0])).catch(o=>{this.probeNetwork().then(([s,r])=>{t.setError(o,s,r).setMessage("groupID:"+e).end()})})}setUnjoinedAVChatRoom(e){this._unjoinedAVChatRoomList.set(e,1)}deleteUnjoinedAVChatRoom(e){this._unjoinedAVChatRoomList.has(e)&&this._unjoinedAVChatRoomList.delete(e)}isUnjoinedAVChatRoom(e){return this._unjoinedAVChatRoomList.has(e)}isGroupAttributesUpdatedNotice(e){return this._groupAttributesHandler.isGroupAttributesUpdatedNotice(e)}updateLocalMainSequenceOnReconnected(){this._groupAttributesHandler.updateLocalMainSequenceOnReconnected()}initGroupAttributes(e){return this._groupAttributesHandler.initGroupAttributes(e)}setGroupAttributes(e){return this._groupAttributesHandler.setGroupAttributes(e)}deleteGroupAttributes(e){return this._groupAttributesHandler.deleteGroupAttributes(e)}getGroupAttributes(e){return this._groupAttributesHandler.getGroupAttributes(e)}isMessageFromTopic(e,t){return 2===e&&!pt(t)}isMessageFromCommunityOfTopic(e,t){return 2===e&&pt(t)}getMessageExtensions(e,t){return ke.l(`${this._n}.getMessageExtensions startSequence:${t}`),this.request({protocolName:Ao,requestData:{groupID:e.to,messageSequence:e.sequence,startSequence:t}})}modifyMessageExtensions(e,t,o=1){return ke.l(`${this._n}.modifyMessageExtensions operateType:${o}`),this.request({protocolName:To,requestData:{groupID:e.to,messageSequence:e.sequence,extensionList:t,operateType:o}})}_genNotifyReqList(e){const t=[];for(let o=0,s=e.length;o<s;o++){const s=e[o],r=this.getLocalGroupProfile(s),{type:i}=r,n=this._getGroupLastRevokedTime(s),u=1e3*ve(),a={notifyType:1,limit:20,type:st({type:i,groupID:s})?ce:void 0,groupID:s,beginTime:n,endTime:u};t.push(a)}return t}getGroupNotify(e){const t=this._n+".getGroupNotify",o=e.filter(e=>{const t=this.getLocalGroupProfile(e),{type:o,isSupportTopic:s}=t;return this.hasLocalGroup(e)&&!ot(o)&&!s});let s="filteredGroupIDList.length:"+o.length;o.length<=10&&(s="filteredGroupIDList:"+JSON.stringify(o)),ke.l(`${t} ${s}`),0!==o.length&&this.request({protocolName:So,requestData:{notifyReqList:this._genNotifyReqList(e)}}).then(e=>{const{notifyRspList:o}=e.data,s=[];if(je(o)){const e={dataList:[]};o.forEach(t=>{const{nextRevokedTime:o,groupID:r}=t;e.dataList.push({elements:{revokedInfos:this._genRevokedInfos(t)}}),0!==o?(this._setGroupLastRevokedTime(r,o),s.push(r)):this._setGroupLastRevokedTime(r,1e3*ve())}),this.onGroupMessageRevoked(e)}s.length>0&&this.getGroupNotify(s);let r="nextGroupIDList.length:"+s.length;s.length<=10&&(r="nextGroupIDList:"+JSON.stringify(s)),ke.l(`${t} ${r}`)}).catch(e=>{ke.e(t+" failed. error:",e)})}_genRevokedInfos(e){const{notifyList:t,groupID:o}=e,s=[];return je(t)&&t.forEach(e=>{s.push({groupID:o,sequence:e.sequence,random:e.random,revokerInfo:{...e.revokerInfo}})}),s}_getGroupLastRevokedTime(e){return this.hasLocalGroup(e)?this.getLocalGroupProfile(e)._lastRevokedTime:0}_setGroupLastRevokedTime(e,t){this.hasLocalGroup(e)&&(this.getLocalGroupProfile(e)._lastRevokedTime=t)}isGroupCountersNotice(e){return this._groupCountersHandler.isGroupCountersNotice(e)}setGroupCounters(e){return this._groupCountersHandler.setGroupCounters(e)}increaseGroupCounter(e){return this._groupCountersHandler.increaseGroupCounter(e)}decreaseGroupCounter(e){return this._groupCountersHandler.decreaseGroupCounter(e)}getGroupCounters(e){return this._groupCountersHandler.getGroupCounters(e)}getGroupMemberHandler(){return this._groupMemberHandler}getGroupMemberList(e){return this._groupMemberHandler.getGroupMemberList(e)}getGroupMemberProfile(e){return this._groupMemberHandler.getGroupMemberProfile(e)}addGroupMember(e){return this._groupMemberHandler.addGroupMember(e)}deleteGroupMember(e){return this._groupMemberHandler.deleteGroupMember(e)}setGroupMemberMuteTime(e){return this._groupMemberHandler.setGroupMemberMuteTime(e)}setGroupMemberRole(e){return this._groupMemberHandler.setGroupMemberRole(e)}setGroupMemberNameCard(e){return this._groupMemberHandler.setGroupMemberNameCard(e)}setGroupMemberCustomField(e){return this._groupMemberHandler.setGroupMemberCustomField(e)}markGroupMemberList(e){return this._groupMemberHandler.markGroupMemberList(e)}modifyGroupMemberInfo(e){return this._groupMemberHandler.modifyGroupMemberInfo(e)}restartPolling(){this._AVChatRoomHandler.restartPolling()}getPollingTimerID(e){if(!e)return-1;const t=this.getLocalGroupProfile(e);return t&&ot(t.type)?this._AVChatRoomHandler.getPollingTimerID(e):-1}_canIUseJoinOption(e){return(e=>e===ae)(e)||st({type:e})}reset(){this.groupMap.clear(),this._unjoinedAVChatRoomList.clear(),this._receiptDetailCompleteMap.clear(),this._commonGroupHandler.reset(),this._groupSystemNoticeHandler.reset(),this._groupTipsHandler.reset(),this._groupAttributesHandler.reset(),this._groupCountersHandler.reset(),this._AVChatRoomHandler.reset(),this._groupMemberHandler.reset()}}export{As as default};
