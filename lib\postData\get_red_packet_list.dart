// 红包列表查询参数
class GetRedPacketListData {
  final int pageNum;      // 页码(不能为空),示例值(1)
  final int pageSize;     // 每页显示条数(不能为空),示例值(10)
  final bool searchCount; // 是否查询总条数
  final List<Map<String, dynamic>> sortItemList; // 排序字段集合
  final String? chatType; // 单聊/群聊 private/group，可选
  final int type;         // 0收到1发出
  final String? year;     // 年份，如"2025"，可选

  GetRedPacketListData({
    this.pageNum = 1,
    this.pageSize = 10,
    this.searchCount = true,
    this.sortItemList = const [],
    this.chatType,
    this.type = 0,
    this.year,
  });

  // 从JSON创建对象
  factory GetRedPacketListData.fromJson(Map<String, dynamic> json) {
    return GetRedPacketListData(
      pageNum: json['pageNum'] ?? 1,
      pageSize: json['pageSize'] ?? 10,
      searchCount: json['searchCount'] ?? true,
      sortItemList: json['sortItemList'] != null 
          ? List<Map<String, dynamic>>.from(json['sortItemList']) 
          : [],
      chatType: json['chatType'],
      type: json['type'] ?? 0,
      year: json['year'],
    );
  }

  // 转换为JSON
// 转换为JSON
Map<String, dynamic> toJson() {
  final Map<String, dynamic> data = <String, dynamic>{};
  
  // 必传参数
  data['pageNum'] = pageNum;
  data['pageSize'] = pageSize;
  
  // 非必传参数，只有在需要时才添加
  // 如果searchCount不是默认值true，才添加
  if (searchCount != true) {
    data['searchCount'] = searchCount;
  }
  
  // 如果sortItemList不为空，才添加
  if (sortItemList.isNotEmpty) {
    data['sortItemList'] = sortItemList;
  }
  
  // 其他可选参数
  if (chatType != null) data['chatType'] = chatType;
  data['type'] = type;
  if (year != null) data['year'] = year;
  
  return data;
}

  // 创建默认的排序项
  static Map<String, dynamic> createSortItem({
    required String column,
    bool isAsc = true,
  }) {
    return {
      'isAsc': isAsc,
      'column': column,
    };
  }
}