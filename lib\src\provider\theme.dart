import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_demo/utils/theme.dart';
import 'package:tencent_cloud_chat_uikit/theme/color.dart';
import 'package:tencent_cloud_chat_uikit/theme/tui_theme.dart';
import 'package:flutter/material.dart';

class DefaultThemeData with ChangeNotifier {
  ThemeType _currentThemeType = ThemeType.brisk;
  final CoreServicesImpl _coreInstance = TIMUIKitCore.getInstance();

  TUITheme _theme = const TUITheme(
    weakBackgroundColor: Color(0xFFEDEDED),
    weakDividerColor: Color(0xFFE5E6E9),
    primaryColor: Color(0xFF147AFF),
    secondaryColor: Color(0xFF147AFF),
    infoColor: Color(0xFFFF9C19),
    lightPrimaryColor: Color(0xFFC0E1FF),
    weakTextColor: Color(0xFF999999),
    darkTextColor: Color(0xFF444444),
    cautionColor: Color(0xFFFF584C),
    ownerColor: Colors.orange,
    adminColor: Colors.blue,
    inputFillColor: Color.fromARGB(0, 242, 243, 245),
    textgrey: Color(0xFFAEA4A3),
    appbarBgColor: Color(0xFFFFFFFF),
  );

  // TUITheme _theme = CommonColor.defaultTheme;

  TUITheme get theme {
    return _theme;
  }

  set theme(TUITheme theme) {
    _theme = theme;
    notifyListeners();
  }

  ThemeType get currentThemeType => _currentThemeType;

  set currentThemeType(ThemeType type) {
    _currentThemeType = type;
    Future<SharedPreferences> _prefs = SharedPreferences.getInstance();
    _prefs.then((prefs) {
      prefs.setString("themeType", type.toString());
    });
    _coreInstance.setTheme(theme: _theme);
    notifyListeners();
  }
}
