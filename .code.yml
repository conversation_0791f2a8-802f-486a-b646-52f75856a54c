source:
  # 文件或目录使用绝对路径，绝对路径按代码库根目录计算，以/开头。
  # 提供产品代码库中编写的测试代码存放目录或文件名格式,以便后续代码统计环节进行排除等特殊处理
  # 不要使用.*/等正则表达式屏蔽掉所有代码，会导致部门代码量骤减从而影响部门代码得分，还会导致Coverity扫不到任何代码而失败
  # 请勿使用Tab键，将会导致yaml解析出错
  test_source:
    #用于匹配文件; 匹配方式为正则表达式。
    filepath_regex: [".*/*.java","doc/*",".*/*.h",".*/*.dart",".*/*.js", ".*/*.swift"]
  # 提供产品代码库中工具或框架自动生成的且在代码库中的代码，没有可为空。以便后续代码统计环节进行排除等特殊处理。
  auto_generate_source:
  # 自动生成代码文件的正则表达式，若无统一标识格式，可以指定具体目录，样例可参考test_source举例
    filepath_regex:
  # 提供产品代码库中直接以源码形式存在的第三方代码目录或代码文件名的正则表达。
  # 此处备注的第三方代码在后续统计代码量环节会被排除，若代码库中不存在需要排除的第三方代码，该项配置标识可为空
  third_party_source:
    #第三方代码文件的正则表达式，若无统一标识格式，可以指定具体目录，样例可参考test_source举例
    filepath_regex: