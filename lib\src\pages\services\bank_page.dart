import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';
import 'package:tencent_cloud_chat_demo/config.dart';
import 'package:provider/provider.dart';
import 'package:tencent_cloud_chat_demo/models/backList_response.dart';
import './bank_add_page.dart';
import '../../provider/wallet_provider.dart';
class BankPage extends StatefulWidget {
  const BankPage({Key? key}) : super(key: key);

  @override
  State<BankPage> createState() => _BankPageState();
}

class _BankPageState extends State<BankPage> {
  late WalletProvider walletProvider;
  @override
  void initState() {
    super.initState();
    walletProvider = Provider.of<WalletProvider>(context, listen: false);
    walletProvider.getBankList(1, 10);
  }

  jumpPage(String type) {
    if (type == 'add') {
      // 添加银行卡逻辑
      Navigator.push(context, MaterialPageRoute(builder: (context) => const BankAddPage())).then((result){
        if (result == true) {
          debugPrint('添加银行卡成功');
          // walletProvider.bankVM.getBankList();
        }
      });

    }
  }

  @override
  Widget build(BuildContext context) {


    ScreenUtil.init(
      context,
      designSize: const Size(375, 812),
      minTextAdapt: true,
    );
    return Scaffold(
        backgroundColor: const Color(0xFFF9F9F9),
        appBar: AppBar(
          elevation: 1,
          title: Text(TIM_t("钱包"),style: const TextStyle(fontSize: IMDemoConfig.appBarTitleFontSize),),
          backgroundColor: const Color(0xFFFFFFFF),
          surfaceTintColor: Colors.white,
        ),
        body: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.only(top: 16.h, left: 16.w, right: 16.w),
                child: _bankView(),
              ),
            ),
            // 添加银行卡按钮
            Padding(
              padding: EdgeInsets.only(left: 16.w, right: 16.w, bottom: 32.h),
              child: InkWell(
                  onTap: () {
                    jumpPage('add');
                  },
                  child: Container(
                    width: double.infinity,
                    height: 40.h,
                    decoration: BoxDecoration(
                      color: const Color(0xFF0072FC),
                      borderRadius: BorderRadius.circular(8.w),
                    ),
                    child: Center(
                      child: Text(TIM_t("添加银行卡"),
                          style: TextStyle(
                              fontSize: 14.sp,
                              fontWeight: FontWeight.w500,
                              color: Colors.white)),
                    ),
                  )),
            ),
          ],
        ));
  }

  Widget _bankView() {
    return Consumer<WalletProvider>(builder: (context, vm, child) {
      return ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) {
          return _listView(vm.bankList[index]);
        },
        itemCount: vm.bankList.length,
      );
    });
  }

  Widget _listView(BankCard bank) {
    return Column(children: [
      // 兴业银行卡
      Container(
        height: 118.h,
        width: double.infinity,
        padding: EdgeInsets.only(left: 74.w, top: 16.h),
        margin: EdgeInsets.only(bottom: 12.h),
        decoration: BoxDecoration(
          image: DecorationImage(
              image: Image.asset('assets/serveice/bank_image.png').image),
          borderRadius: BorderRadius.circular(8.w),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(bank.bankName ?? '',
                style: TextStyle(fontSize: 18.sp, color: Colors.white)),
            SizedBox(height: 11.h),
            Text('储蓄卡', style: TextStyle(fontSize: 12.sp, color: Colors.white)),
            SizedBox(height: 8.h),
            Text(bank.cardNo ?? '',
                style: TextStyle(fontSize: 22.sp, color: Colors.white)),
          ],
        ),
      )
    ]);
  }
}
