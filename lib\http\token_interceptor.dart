// 创建一个新文件 token_interceptor.dart
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import '../utils/user_info_local.dart';

class TokenInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    // 从本地存储获取 token
    String? token = await UserInfoLocal.getToken();
    debugPrint("token111111: $token");
    if (token != null && token.isNotEmpty) {
      options.headers['x-access-token'] = token;
    }
    
    handler.next(options);
  }
}