// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCj0H4SyWF3vNT1tnoFIcAxGHDXuCP76WU',
    appId: '1:545427402667:web:9ebd7a1a5c97324cdd6cee',
    messagingSenderId: '545427402667',
    projectId: 'pinim-849e4',
    authDomain: 'pinim-849e4.firebaseapp.com',
    storageBucket: 'pinim-849e4.firebasestorage.app',
    measurementId: 'G-X1Z1XR188T',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDALTJolJGzeax-sa-WXYftoy7lENppsgY',
    appId: '1:545427402667:android:1b0b53bea08253fddd6cee',
    messagingSenderId: '545427402667',
    projectId: 'pinim-849e4',
    storageBucket: 'pinim-849e4.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyAo7f-Ep9CFvT566eUM--AznClvhMNAYiE',
    appId: '1:545427402667:ios:7835434250b949abdd6cee',
    messagingSenderId: '545427402667',
    projectId: 'pinim-849e4',
    storageBucket: 'pinim-849e4.firebasestorage.app',
    iosBundleId: 'com.phichat.phichat',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyAo7f-Ep9CFvT566eUM--AznClvhMNAYiE',
    appId: '1:545427402667:ios:daba49f195cb3a3bdd6cee',
    messagingSenderId: '545427402667',
    projectId: 'pinim-849e4',
    storageBucket: 'pinim-849e4.firebasestorage.app',
    iosBundleId: 'com.tencent.flutter.tuikit',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyCj0H4SyWF3vNT1tnoFIcAxGHDXuCP76WU',
    appId: '1:545427402667:web:cb8d31e2d4a4afc2dd6cee',
    messagingSenderId: '545427402667',
    projectId: 'pinim-849e4',
    authDomain: 'pinim-849e4.firebaseapp.com',
    storageBucket: 'pinim-849e4.firebasestorage.app',
    measurementId: 'G-RD79JS4JQD',
  );
}
