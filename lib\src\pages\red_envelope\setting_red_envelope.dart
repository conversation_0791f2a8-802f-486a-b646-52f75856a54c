import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tencent_cloud_chat_demo/utils/user_info_local.dart';
import '../../../utils/toast.dart';
import '../../../apis/redPacket_api.dart';
import '../../../postData/get_red_packet_cover_list.dart';
import '../../../models/coverList_response.dart';
import '../../../models/language_local.dart';
import '../../provider/local_setting.dart';
import '../../provider/cover_provide.dart';

class SettingRedEnvelopePage extends StatefulWidget {
  const SettingRedEnvelopePage({Key? key}) : super(key: key);

  @override
  State<SettingRedEnvelopePage> createState() => _SettingRedEnvelopePageState();
}

class _SettingRedEnvelopePageState extends State<SettingRedEnvelopePage> {
  // 默认选择的红包封面
  CoverItem selectedRedEnvelopeType = CoverItem();

  // 配置红包封面类型
  List<CoverItem> redEnvelopeType = [];

  late CoverProvider coverProvider;

  // 选择红包封面
  handleSelectRedEnvelopeType(CoverItem type) {
    // 保存红包封面类型
    coverProvider.changeDefaultRedEnvelopeType(type);
    RedEnvelopeLocal.saveRedEnvelopeType(type.toJson());
    Navigator.pop(context, true);
  }

  // 页码
  int pageNum = 1;

  // 每页显示数量
  int pageSize = 10;

  // 获取红包封面列表
  void _readRedEnvelopeType() async {
    await coverProvider.getRedPacketRecord(context);
    debugPrint('红包封面列表: ${coverProvider.redEnvelopeType}');
  }

  @override
  void initState() {
    super.initState();
    // 使用 Provider.of 获取已注册的 CoverProvider 实例
    coverProvider = Provider.of<CoverProvider>(context, listen: false);
    _readRedEnvelopeType();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(
      context,
      designSize: const Size(375, 812),
      minTextAdapt: true,
    );
    return Scaffold(
      backgroundColor: const Color(0xFFF4F7F8),
      appBar: AppBar(
        backgroundColor: const Color(0xFFF4F7F8),
        automaticallyImplyLeading: false,
        surfaceTintColor: Colors.transparent,
        title: Row(
          children: [
            GestureDetector(
              onTap: () => Navigator.pop(context),
              child: Row(
                children: [
                  Image.asset('assets/icon_right_back.png',
                      width: 24, height: 24),
                  const SizedBox(width: 8),
                  const Text('我的红包封面',
                      style: TextStyle(color: Color(0xFF333333), fontSize: 16))
                ],
              ),
            ),
          ],
        ),
      ),
      body: _coverView()
    );
  }

  Widget _coverView() {
    return Consumer<CoverProvider>(builder: (context, coverProvider, child) {
      return Container(
          child: GridView.builder(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2, // 每行显示2个项目
          crossAxisSpacing: 16, // 水平间距
          mainAxisSpacing: 16, // 垂直间距
          childAspectRatio: 0.51, // 宽高比
        ),
        itemCount: coverProvider.redEnvelopeType.length,
        itemBuilder: (context, index) {
          return _coverItemView(coverProvider.redEnvelopeType[index]);
        },
      ));
    });
  }

  Widget _coverItemView(CoverItem coverItem) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFFFFFFFF),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Image.network(coverItem.url!, width: 147.w, height: 232.h),
          Text(coverItem.title!,
              style: const TextStyle(color: Color(0xFF333333), fontSize: 12)),
          const SizedBox(height: 16),
          InkWell(
            onTap: () {
              // 处理选择逻辑
              handleSelectRedEnvelopeType(coverItem);
            },
            child: Container(
              width: 62,
              padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
              child: const Center(
                child: Text('使用',
                    style: TextStyle(color: Color(0xFFFFFFFF), fontSize: 12)),
              ),
              decoration: BoxDecoration(
                color: const Color(0xFFFF5D5E),
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          )
        ],
      ),
    );
  }
}
