import 'package:flutter/foundation.dart';
import 'package:tencent_cloud_chat_demo/models/base_response.dart';
import 'package:tencent_cloud_chat_demo/postData/get_bank_list_data.dart';
import '../../apis/account_api.dart';
import '../../models/backList_response.dart';
import '../../utils/user_info_local.dart';

class WalletProvider with ChangeNotifier {
  // 1. 私有变量（状态）
  double _balance = 0.0;
  String _userId = '';
  int _accountStatus = 0;
  bool _isLoading = false;
  String? _error;
  List<BankCard> _bankList = [];
  BankCard? _selectedBank = BankCard();
  int _pageNum = 1;
  int _pageSize = 10;

  // 2. Getters（只读访问）
  double get balance => _balance;
  String get userId => _userId;
  int get accountStatus => _accountStatus;
  bool get isLoading => _isLoading;
  String? get error => _error;
  List<BankCard> get bankList => _bankList;
  BankCard? get selectedBank => _selectedBank;

  // 3. 构造函数
  WalletProvider() {
    // 初始化操作，例如加载数据
    _initData();
  }

  // 4. 私有初始化方法
  Future<void> _initData() async {
    // 获取用户token
    final token = await UserInfoLocal.getToken();
    if(token != null) {
      await queryAccount();
      await getBankList(_pageNum, _pageSize);
    }


  }

  // 5. 公共方法（业务逻辑）
  Future<void> queryAccount() async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final response = await AccountApi.instance.queryAccount();

      if (response.code == 0 && response.ok == true) {
        if (response.data == null) {
          // 自动开通账户
          await AccountApi.instance.openAccount();
        } else {
          _balance = response.data!.balance!.toDouble();
          _accountStatus = response.data!.status!;
          _userId = response.data!.userId!.toString();
        }
      } else {
        _error = response.msg ?? '查询账户失败';
      }
    } catch (e) {
      _error = '查询账户出错: $e';
      debugPrint('$_error');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // 提现
  Future<void> withdraw(double amount) async {
    try {
      debugPrint('提现 ${_selectedBank!.id!.toString()} ${amount.toString()}');
      final res = await AccountApi.instance.withdraw(
          cardId: _selectedBank!.id!.toString(), amount: amount.toString());
      if (res.code == 0 && res.ok == true) {
        await queryAccount();
      }
      notifyListeners();
    } catch (e) {
      _error = '提现失败: $e';
      debugPrint('$_error');
    }
  }

  Future<BaseResponse> recharge(double amount) async {
    // 实现充值逻辑
    final res = await AccountApi.instance.recharge(
        cardId: _selectedBank!.id!.toString(), amount: amount.toString());
    if (res.code == 0 && res.ok == true) {
      await queryAccount();
    }
    notifyListeners();
    return res;
  }

  // 修改默认选中的银行卡
  Future<void> changeDefaultBank(BankCard bank) async {
    _selectedBank = bank;
    notifyListeners();
  }

  // 分页查询银行卡
  Future<void> getBankList(int pageNum, int pageSize) async {
    final res = await AccountApi.instance.getBankList(
        params: BankListParams(pageNum: pageNum, pageSize: pageSize));
    if (res.code == 0 && res.ok == true) {
      _bankList = res.data?.list ?? [];
      // 设置默认银行卡
      if(_selectedBank != null) {
        _selectedBank = _bankList.first;
      }
    }
    debugPrint('分页查询银行卡 ${res.toJson()}');
    notifyListeners();
  }

  // 添加银行卡
  Future<BaseResponse> addBank(
      String bankName, String cardNo, String cardHolder) async {
    final res = await AccountApi.instance.addBankCard(
        bankName: bankName, cardNo: cardNo, cardHolder: cardHolder);
    if (res.code == 0 && res.ok == true) {
      _pageNum = 1;
      _pageSize = 10;
      await getBankList(_pageNum, _pageSize);
    }
    notifyListeners();
    return res;
  }

  // 删除银行卡
  Future<void> deleteBank(String bankCardId) async {
    final res =
        await AccountApi.instance.deleteBankCard(bankCardId: bankCardId);
    if (res.code == 0 && res.ok == true) {
      _pageNum = 1;
      _pageSize = 10;
      await getBankList(_pageNum, _pageSize);
    }
    notifyListeners();
  }

  // 6. 资源释放（如果需要）
  @override
  void dispose() {
    // 释放资源
    super.dispose();
  }
}
