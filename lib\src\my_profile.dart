// ignore_for_file: unused_import

import 'dart:math';
import 'package:provider/provider.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';
import 'package:tencent_cloud_chat_demo/src/about.dart';
import 'package:tencent_cloud_chat_demo/src/my_profile_detail.dart';
import 'package:tencent_cloud_chat_demo/src/pages/qrcode/qrcode.dart';
import 'package:tencent_cloud_chat_demo/src/pages/skin/skin_page.dart';
import 'package:tencent_cloud_chat_demo/utils/constant.dart';
import 'package:tencent_cloud_chat_sdk/manager/v2_tim_manager.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_conversation.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_friend_info.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_user_full_info.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_value_callback.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_demo/src/pages/login.dart';
import 'package:tencent_cloud_chat_demo/src/provider/local_setting.dart';
import 'package:tencent_cloud_chat_demo/src/provider/login_user_Info.dart';
import 'package:tencent_cloud_chat_demo/src/provider/theme.dart';
import 'package:tencent_cloud_chat_demo/src/routes.dart';
import 'package:tencent_cloud_chat_demo/utils/theme.dart';
import 'package:tencent_cloud_chat_demo/utils/toast.dart';
import 'package:adaptive_action_sheet/adaptive_action_sheet.dart';
import 'package:tencent_cloud_chat_uikit/theme/color.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/screen_utils.dart';
import 'package:tencent_cloud_chat_demo/widgets/avatar.dart';
import 'package:tencent_cloud_chat_demo/src/profile.dart';
import './pages/task_page/task_page.dart';
import './pages/services/services_page.dart';
import '../../apis/account_api.dart';

class MyProfile extends StatefulWidget {
  const MyProfile({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _ProfileState();
}

class _ProfileState extends State<MyProfile> {
  final CoreServicesImpl _coreServices = TIMUIKitCore.getInstance();
  final V2TIMManager sdkInstance = TIMUIKitCore.getSDKInstance();
  final TIMUIKitProfileController _timuiKitProfileController =
      TIMUIKitProfileController();
  String? userID;

  String _getAllowText(int? allowType) {
    if (allowType == 0) {
      return TIM_t("允许任何人");
    }

    if (allowType == 1) {
      return TIM_t("需要验证信息");
    }

    if (allowType == 2) {
      return TIM_t("禁止加我为好友");
    }

    return TIM_t("未指定");
  }

  _handleLogout() async {
    final res = await _coreServices.logout();
    debugPrint('click logout');
    if (res.code == 0) {
      debugPrint('logout success');
      try {
        Future<SharedPreferences> _prefs = SharedPreferences.getInstance();
        SharedPreferences prefs = await _prefs;
        prefs.remove(Const.DEV_LOGIN_USER_ID);
        prefs.remove(Const.DEV_LOGIN_USER_SIG);
        prefs.remove(Const.SMS_LOGIN_TOKEN);
        prefs.remove(Const.SMS_LOGIN_PHONE);
      } catch (err) {
        ToastUtils.log("someError");
        ToastUtils.log(err);
      }
      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (BuildContext context) => const LoginPage()),
        (route) => false, // 清除所有路由历史
      );
    }
  }

  changeFriendVerificationMethod(int allowType) async {
    _timuiKitProfileController.changeFriendVerificationMethod(allowType);
  }

  showApplicationTypeSheet(theme) async {
    const allowAny = 0;
    const neddConfirm = 1;
    const denyAny = 2;

    showAdaptiveActionSheet(
      context: context,
      actions: <BottomSheetAction>[
        BottomSheetAction(
          title: Text(
            TIM_t("允许任何人"),
            style: TextStyle(color: theme.primaryColor, fontSize: 18),
          ),
          onPressed: (_) {
            changeFriendVerificationMethod(allowAny);
            Navigator.of(context, rootNavigator: true).pop();
          },
        ),
        BottomSheetAction(
            title: Text(
              TIM_t("需要验证信息"),
              style: TextStyle(color: theme.primaryColor, fontSize: 18),
            ),
            onPressed: (_) {
              changeFriendVerificationMethod(neddConfirm);
              Navigator.of(context, rootNavigator: true).pop();
            }),
        BottomSheetAction(
          title: Text(
            TIM_t("禁止加我为好友"),
            style: TextStyle(color: theme.primaryColor, fontSize: 18),
          ),
          onPressed: (_) {
            changeFriendVerificationMethod(denyAny);
            Navigator.of(context, rootNavigator: true).pop();
          },
        ),
      ],
      cancelAction: CancelAction(
        title: Text(
          TIM_t("取消"),
          style: const TextStyle(fontSize: 18),
        ),
      ), // onPressed parameter is optional by default will dismiss the ActionSheet
    );
  }

  @override
  void initState() {
    super.initState();
    _queryAccount();
  }


  _queryAccount() {
    AccountApi.instance.queryAccount().then((value) {
      debugPrint('账户查询结果: ${value.toJson()}');
    });
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        const AppLogo(),
        SafeArea(

            child: Column(
          children: [
            _timUIKitProfile(),
          ],
        )),
      ],
    );
  }

  Widget buildUserTop(BuildContext context) {
    final loginUserInfoModel = Provider.of<LoginUserInfo>(context);
    final V2TimUserFullInfo loginUserInfo = loginUserInfoModel.loginUserInfo;
    if (loginUserInfo == null) return const SizedBox();

    return Container(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(right: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                InkWell(
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => Qrcode(userInfo: loginUserInfo),
                      ),
                    );
                  },
                  child: Image.asset('assets/re_code_icon.png', width: 24, height: 24),
                )
              ],
            ),
          ),
          const SizedBox(height: 23),
          Center(
            child: Column(
              children: [
                GestureDetector(
                  onTap: () {
                    final loginUserInfoModel =
                        Provider.of<LoginUserInfo>(context, listen: false);
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => MyProfileDetail(
                            userProfile: loginUserInfoModel.loginUserInfo,
                            controller: _timuiKitProfileController),
                      ),
                    );
                  },
                  child: Avatar(
                    avatarUrl: loginUserInfo.faceUrl,
                    size: 70,
                    radius: 35,
                    showBorder: true,
                    borderColor: Colors.white,
                    borderWidth: 4,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  loginUserInfo.nickName ?? '',
                  style: const TextStyle(
                    color: Color(0xFFFFFFFF),
                    fontSize: 20,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'ID: ${loginUserInfo.userID ?? ''}',
                  style: const TextStyle(
                    color: Color(0x99FFFFFF),
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 32),
          _buildUserCard()
        ],
      ),
    );
  }

  Widget _buildUserCard() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: const Color(0xFFFFFFFF),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Column(
          children: [
            Material(
              color: Colors.white,
              child: InkWell(
                onTap: () {
                  Navigator.push(context, MaterialPageRoute(builder: (context) => const ServicesPage()));
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Image.asset('assets/user_info_card/wallet.png', width: 20, height: 20),
                          const SizedBox(width: 8),
                          Text(TIM_t('钱包与服务')),
                        ],
                      ),
                      Image.asset('assets/icon_right.png', width: 16, height: 16),
                    ],
                  ),
                ),
              ),
            ),
            const Divider(height: 1, color: Color(0xFFEEEEEE)),
            Material(
              color: Colors.white,
              child: InkWell(
                onTap: () {
                  ToastUtils.toast('开发中');
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Image.asset('assets/user_info_card/moments.png', width: 20, height: 20),
                          const SizedBox(width: 8),
                          Text(TIM_t('朋友圈')),
                        ],
                      ),
                      Image.asset('assets/icon_right.png', width: 16, height: 16),
                    ],
                  ),
                ),
              ),
            ),
            const Divider(height: 1, color: Color(0xFFEEEEEE)),
            Material(
              color: Colors.white,
              child: InkWell(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const TaskPage(),
                    ),
                  );
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Image.asset('assets/user_info_card/red_envelope.png', width: 20, height: 20),
                          const SizedBox(width: 8),
                          Text(TIM_t('红包推广')),
                        ],
                      ),
                      Image.asset('assets/icon_right.png', width: 16, height: 16),
                    ],
                  ),
                ),
              ),
            ),
            const Divider(height: 1, color: Color(0xFFEEEEEE)),
            Material(
              color: Colors.white,
              child: InkWell(
                onTap: () {
                  final loginUserInfoModel = Provider.of<LoginUserInfo>(context, listen: false);
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => MyProfileDetail(
                          userProfile: loginUserInfoModel.loginUserInfo,
                          controller: _timuiKitProfileController),
                    ),
                  );
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Image.asset('assets/user_info_card/user_count.png', width: 20, height: 20),
                          const SizedBox(width: 8),
                          Text(TIM_t('个人资料')),
                        ],
                      ),
                      Image.asset('assets/icon_right.png', width: 16, height: 16),
                    ],
                  ),
                ),
              ),
            ),
            const Divider(height: 1, color: Color(0xFFEEEEEE)),
            Material(
              color: Colors.white,
              child: InkWell(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const MyProfileSetting(),
                    ),
                  );
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Image.asset('assets/user_info_card/setting.png', width: 20, height: 20),
                          const SizedBox(width: 8),
                          Text(TIM_t('设置')),
                        ],
                      ),
                      Image.asset('assets/icon_right.png', width: 16, height: 16),
                    ],
                  ),
                ),
              ),
            ),
          ],        ),
      ),
    );
  }

  Widget _timUIKitProfile() {
    final LocalSetting localSetting = Provider.of<LocalSetting>(context);
    final themeType = Provider.of<DefaultThemeData>(context).currentThemeType;
    final theme = Provider.of<DefaultThemeData>(context).theme;
    final bool isWideScreen =
        TUIKitScreenUtils.getFormFactor() == DeviceType.Desktop;
    final loginUserInfoModel = Provider.of<LoginUserInfo>(context);
    final V2TimUserFullInfo loginUserInfo = loginUserInfoModel.loginUserInfo;
    return TIMUIKitProfile(
      isSelf: true,
      userID: loginUserInfo.userID ?? "",
      controller: _timuiKitProfileController,
      builder: (BuildContext context, V2TimFriendInfo userInfo,
          V2TimConversation conversation, int friendType, bool isMute) {
        final userProfile = userInfo.userProfile;
        final int? allowType = userProfile?.allowType;
        final allowText = _getAllowText(allowType);
        return SingleChildScrollView(
            child: Column(
          children: [
            buildUserTop(context),
          ],
        ));
      },
    );
  }
}


class AppLogo extends StatelessWidget {
  const AppLogo({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final double height = MediaQuery.of(context).size.height;
    final theme = Provider.of<DefaultThemeData>(context).theme;
    return Stack(
      alignment: Alignment.topCenter,
      children: [
        Container(
          child: Image.asset(
            'assets/login_bg.png',
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height,
            fit: BoxFit.cover,
          ),
        ),
      ],
    );
  }
}
