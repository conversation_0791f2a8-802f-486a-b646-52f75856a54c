{"name": "im-flutter-uikit", "version": "1.0.0", "description": "[English](./README_EN.md) | 简体中文 # Flutter TUIKit Demo", "main": "get_all_code.js", "directories": {"lib": "lib"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "tox:build@dev": "echo \"Error: no test specified\"", "tox:publish@dev": "tox publish online --silence", "tox:publish@master": "tox publish online --silence", "tox:build@master": "echo \"Error: no test specified\""}, "repository": {"type": "git", "url": "https://git.woa.com/29294-22989-29805-29810/im-flutter-uikit.git"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"fs-extra": "^10.1.0"}}