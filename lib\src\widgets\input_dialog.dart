import 'package:flutter/material.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';

/// 可复用的输入对话框组件
class InputDialog {
  /// 显示输入对话框
  ///
  /// [context] - 上下文
  /// [title] - 对话框标题
  /// [hintText] - 输入框提示文本
  /// [initialValue] - 输入框初始值
  /// [confirmText] - 确认按钮文本
  /// [cancelText] - 取消按钮文本
  /// [maxLines] - 输入框最大行数
  /// [minLines] - 输入框最小行数
  /// [maxLength] - 输入框最大字符数
  /// [removeNewlines] - 确认时是否去除换行符
  ///
  /// 返回用户输入的文本，如果用户取消则返回null
  static Future<String?> show({
    required BuildContext context,
    required String title,
    String hintText = "",
    String initialValue = "",
    String? confirmText,
    String? cancelText,
    int maxLines = 3,
    int minLines = 3,
    int? maxLength,
    bool removeNewlines = false,
  }) async {
    confirmText = confirmText ?? TIM_t("确定");
    cancelText = cancelText ?? TIM_t("取消");
    final TextEditingController controller = TextEditingController(text: initialValue);

    final result = await showDialog<String?>(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          titlePadding: const EdgeInsets.fromLTRB(24.0, 24.0, 24.0, 24.0),
          title: Center(
              child: Text(TIM_t(title),
                  style: const TextStyle(
                      fontSize: 14, fontWeight: FontWeight.w500))),
          contentPadding: const EdgeInsets.fromLTRB(24.0, 0.0, 24.0, 24.0),
          content: SizedBox(
            height: 84,
            child: TextField(
              controller: controller,
              style: const TextStyle(fontSize: 14),
              maxLines: maxLines,
              minLines: minLines,
              maxLength: maxLength,
              decoration: InputDecoration(
                hintText: TIM_t(hintText),
                hintStyle: const TextStyle(
                  fontSize: 14,
                  color: Color(0xFF999999),
                ),
                filled: true,
                fillColor: const Color(0xFFF6F6F6),
                isDense: true,
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(4),
                  borderSide: BorderSide.none,
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(4),
                  borderSide: BorderSide.none,
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(4),
                  borderSide: BorderSide.none,
                ),
                counterText: maxLength != null ? null : "",
              ),
            ),
          ),
          actions: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // ElevatedButton(
                //   style: ElevatedButton.styleFrom(
                //     fixedSize: const Size(64, 32),
                //     padding: const EdgeInsets.all(0),
                //     foregroundColor: const Color(0xFF666666),
                //     backgroundColor: const Color(0xFFE9E9E9),
                //     surfaceTintColor: const Color(0xFFE9E9E9),
                //     shadowColor: const Color(0xFFE9E9E9),
                //     elevation: 0,
                //     shape: RoundedRectangleBorder(
                //       borderRadius: BorderRadius.circular(8),
                //     ),
                //     textStyle: const TextStyle(fontSize: 14),
                //   ),
                //   onPressed: () => Navigator.pop(context),
                //   child: Text(TIM_t(cancelText)),
                // ),
                GestureDetector(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 4),
                      margin: const EdgeInsets.only(right: 16),
                      height: 32,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color: const Color(0xFFE9E9E9),
                      ),
                      child: Center(
                        child: Text(cancelText!,
                            style: const TextStyle(color: Color(0XFF666666))),
                      )),
                ),
                GestureDetector(
                  onTap: () {
                    String result = controller.text.trim();
                    if (removeNewlines) {
                      result = result.replaceAll(RegExp(r'\n|\r\n|\r'), '');
                    }
                    Navigator.pop(context, result);
                  },
                  child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 4),
                      height: 32,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color: const Color(0xFF0072FC),
                      ),
                      child: Center(
                        child: Text(confirmText!,
                            style: const TextStyle(color: Color(0XFFFFFFFF))),
                      )),
                ),
              ],
            ),
          ],
          actionsPadding: const EdgeInsets.only(left: 24, right: 24, bottom: 24),
        );
      },
    );

    return result;
  }
}
