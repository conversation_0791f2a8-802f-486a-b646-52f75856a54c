PODS:
  - audio_session (0.0.1):
    - FlutterMacOS
  - bitsdojo_window_macos (0.0.1):
    - FlutterMacOS
  - desktop_drop (0.0.1):
    - FlutterMacOS
  - desktop_webview_window_for_is (0.0.1):
    - FlutterMacOS
  - device_info_plus (0.0.1):
    - FlutterMacOS
  - fc_native_video_thumbnail (0.0.1):
    - FlutterMacOS
  - file_selector_macos (0.0.1):
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - FMDB (2.7.10):
    - FMDB/standard (= 2.7.10)
  - FMDB/standard (2.7.10)
  - HydraAsync (2.0.6)
  - image_clipboard (0.0.1):
    - FlutterMacOS
  - just_audio (0.0.1):
    - FlutterMacOS
  - location (0.0.1):
    - FlutterMacOS
  - package_info_plus (0.0.1):
    - FlutterMacOS
  - pasteboard (0.0.1):
    - FlutterMacOS
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - photo_manager (2.0.0):
    - Flutter
    - FlutterMacOS
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite (0.0.2):
    - FlutterMacOS
    - FMDB (>= 2.7.5)
  - tencent_cloud_chat_sdk (7.0.0):
    - FlutterMacOS
    - HydraAsync
    - TXIMSDK_Plus_Mac (>= 7.9.5666)
  - TXIMSDK_Plus_Mac (7.9.5666)
  - url_launcher_macos (0.0.1):
    - FlutterMacOS
  - wakelock_plus (0.0.1):
    - FlutterMacOS

DEPENDENCIES:
  - audio_session (from `Flutter/ephemeral/.symlinks/plugins/audio_session/macos`)
  - bitsdojo_window_macos (from `Flutter/ephemeral/.symlinks/plugins/bitsdojo_window_macos/macos`)
  - desktop_drop (from `Flutter/ephemeral/.symlinks/plugins/desktop_drop/macos`)
  - desktop_webview_window_for_is (from `Flutter/ephemeral/.symlinks/plugins/desktop_webview_window_for_is/macos`)
  - device_info_plus (from `Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos`)
  - fc_native_video_thumbnail (from `Flutter/ephemeral/.symlinks/plugins/fc_native_video_thumbnail/macos`)
  - file_selector_macos (from `Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - image_clipboard (from `Flutter/ephemeral/.symlinks/plugins/image_clipboard/macos`)
  - just_audio (from `Flutter/ephemeral/.symlinks/plugins/just_audio/macos`)
  - location (from `Flutter/ephemeral/.symlinks/plugins/location/macos`)
  - package_info_plus (from `Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos`)
  - pasteboard (from `Flutter/ephemeral/.symlinks/plugins/pasteboard/macos`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - photo_manager (from `Flutter/ephemeral/.symlinks/plugins/photo_manager/macos`)
  - shared_preferences_foundation (from `Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite (from `Flutter/ephemeral/.symlinks/plugins/sqflite/macos`)
  - tencent_cloud_chat_sdk (from `Flutter/ephemeral/.symlinks/plugins/tencent_cloud_chat_sdk/macos`)
  - url_launcher_macos (from `Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos`)
  - wakelock_plus (from `Flutter/ephemeral/.symlinks/plugins/wakelock_plus/macos`)

SPEC REPOS:
  trunk:
    - FMDB
    - HydraAsync
    - TXIMSDK_Plus_Mac

EXTERNAL SOURCES:
  audio_session:
    :path: Flutter/ephemeral/.symlinks/plugins/audio_session/macos
  bitsdojo_window_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/bitsdojo_window_macos/macos
  desktop_drop:
    :path: Flutter/ephemeral/.symlinks/plugins/desktop_drop/macos
  desktop_webview_window_for_is:
    :path: Flutter/ephemeral/.symlinks/plugins/desktop_webview_window_for_is/macos
  device_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos
  fc_native_video_thumbnail:
    :path: Flutter/ephemeral/.symlinks/plugins/fc_native_video_thumbnail/macos
  file_selector_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  image_clipboard:
    :path: Flutter/ephemeral/.symlinks/plugins/image_clipboard/macos
  just_audio:
    :path: Flutter/ephemeral/.symlinks/plugins/just_audio/macos
  location:
    :path: Flutter/ephemeral/.symlinks/plugins/location/macos
  package_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos
  pasteboard:
    :path: Flutter/ephemeral/.symlinks/plugins/pasteboard/macos
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  photo_manager:
    :path: Flutter/ephemeral/.symlinks/plugins/photo_manager/macos
  shared_preferences_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin
  sqflite:
    :path: Flutter/ephemeral/.symlinks/plugins/sqflite/macos
  tencent_cloud_chat_sdk:
    :path: Flutter/ephemeral/.symlinks/plugins/tencent_cloud_chat_sdk/macos
  url_launcher_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos
  wakelock_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/wakelock_plus/macos

SPEC CHECKSUMS:
  audio_session: dea1f41890dbf1718f04a56f1d6150fd50039b72
  bitsdojo_window_macos: 44e3b8fe3dd463820e0321f6256c5b1c16bb6a00
  desktop_drop: 69eeff437544aa619c8db7f4481b3a65f7696898
  desktop_webview_window_for_is: 173eb6a1bcdf3e54b54a16649e23fe83cbef8f13
  device_info_plus: 5401765fde0b8d062a2f8eb65510fb17e77cf07f
  fc_native_video_thumbnail: 828487c6e1509b0ba74f6741b2788ac47aa0285b
  file_selector_macos: 468fb6b81fac7c0e88d71317f3eec34c3b008ff9
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  FMDB: eae540775bf7d0c87a5af926ae37af69effe5a19
  HydraAsync: 8d589bd725b0224f899afafc9a396327405f8063
  image_clipboard: 09a15b2a0ee0b0a6d3d7f2e033f41b2bade1c495
  just_audio: 9b67ca7b97c61cfc9784ea23cd8cc55eb226d489
  location: 7cdb0665bd6577d382b0a343acdadbcb7f964775
  package_info_plus: 02d7a575e80f194102bef286361c6c326e4c29ce
  pasteboard: 9b69dba6fedbb04866be632205d532fe2f6b1d99
  path_provider_foundation: 3784922295ac71e43754bd15e0653ccfd36a147c
  photo_manager: 4f6810b7dfc4feb03b461ac1a70dacf91fba7604
  shared_preferences_foundation: 5b919d13b803cadd15ed2dc053125c68730e5126
  sqflite: a5789cceda41d54d23f31d6de539d65bb14100ea
  tencent_cloud_chat_sdk: 9b98eb3dc7bee6d63a95a155a6ed69ce5c29d8ff
  TXIMSDK_Plus_Mac: 7a82e7602e894c706bb9b61bd78f0b7b2cf69281
  url_launcher_macos: 5335912b679c073563f29d89d33d10d459f95451
  wakelock_plus: 4783562c9a43d209c458cb9b30692134af456269

PODFILE CHECKSUM: dac0ddf03d136db544afc27b87cc6c08492e67b9

COCOAPODS: 1.15.2
