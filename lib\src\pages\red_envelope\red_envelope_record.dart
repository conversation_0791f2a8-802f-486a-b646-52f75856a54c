import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';
import 'package:tencent_cloud_chat_demo/src/provider/local_setting.dart';
import 'package:tencent_cloud_chat_demo/src/provider/login_user_Info.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_user_full_info.dart';
import '../../../widgets/avatar.dart';
import '../../widgets/empty.dart';
import '../../../apis/redPacket_api.dart';
import '../../../postData/get_red_packet_list.dart';
import '../../../models/getPacketRecord_response.dart';
import '../../../utils/toast.dart';

class RedEnvelopeRecordPage extends StatefulWidget {
  const RedEnvelopeRecordPage({super.key});
  @override
  State<StatefulWidget> createState() => _RedEnvelopeRecordPageState();
}

class _RedEnvelopeRecordPageState extends State<RedEnvelopeRecordPage> {
  // 当前选中
  String _currentTab = 'my_received';

  // 切换tab
  void _switchTab(String tab) {
    setState(() {
      _currentTab = tab;
      _getRedPacketRecord();
      _getRedPacketTotal();    
    });
  }

  // 模拟红包列表数据
  List<RecordItem> _redEnvelopeList = [];

  String getLanguage() {
    final localSetting = Provider.of<LocalSetting>(context, listen: false);
    switch (localSetting.language) {
      case "zh-Hans":
        return "zh-Hans";
      case "zh-Hant":
        return "zh-Hant";
      case "en":
        return "en";
      case "ja":
        return "ja";
      case "ko":
        return "ko";
      default:
        return "zh-Hans";
    }
  }

  String _selectedYear = '2025'; // 添加选中年份的状态变量
  int _currentPage = 1; // 当前页码
  bool _isLoading = false; // 是否正在加载
  bool _hasMoreData = true; // 是否还有更多数据
  double totalAmount = 0;
  int totalCount=0;
  final ScrollController _scrollController = ScrollController(); // 滚动控制器

  // 获取红包记录
  Future<void> _getRedPacketRecord({String? year}) async {
    setState(() {
      _isLoading = true;
      _currentPage = 1; // 重置页码
      _redEnvelopeList.clear();
      _hasMoreData = true; // 重置更多数据标志
    });
    Future.microtask(() => ToastUtils.showLoading());
    Api.instance.getRedPacketRecord(GetRedPacketListData(
      pageNum: _currentPage,
      pageSize: 10,
      type: _currentTab == 'my_received' ? 0 : 1,
      year: year ?? _selectedYear
    )).then((response) {
      if(response.code == 0 && response.ok!){
        setState(() {
          _redEnvelopeList = response.data?.list ?? [];
          if (year != null) {
            _selectedYear = year;
          }
          
          // 更新分页状态
          _isLoading = false;
          Future.microtask(() => ToastUtils.hideLoading());
          _hasMoreData = (response.data?.pages ?? 0) > _currentPage;
        });
      } else {
        setState(() {
          _isLoading = false;
        });
      }
    }).catchError((error) {
      debugPrint("error:$error");
      setState(() {
        _isLoading = false;
      });
      Future.microtask(() => ToastUtils.hideLoading());
    });
  }
  // 查询总数
  Future<void> _getRedPacketTotal() async {
    final response = await Api.instance.getRedPacketAmount(GetRedPacketListData(
      pageNum: _currentPage,
      pageSize: 10,
      type: _currentTab == 'my_received' ? 0 : 1,
      year: _selectedYear
    ));
    if(response.code == 0 && response.ok!){
      debugPrint("获取红包记录成功: ${response.data}");
      if(response.data != null){
        setState(() {
          totalAmount = double.tryParse(response.data!.totalAmount ?? '0') ?? 0;
          totalCount = response.data!.totalCount ?? 0;
        });
      }
    } else {
      debugPrint("获取红包记录失败: ${response.msg}");
    }
  }
  
  // 加载更多数据
  Future<void> _loadMoreData() async {
    if (_isLoading) return;
    
    setState(() {
      _isLoading = true;
    });
    
    _currentPage++;
    
    final response = await Api.instance.getRedPacketRecord(GetRedPacketListData(
      pageNum: _currentPage,
      pageSize: 10,
      type: _currentTab == 'my_received' ? 0 : 1,
      year: _selectedYear
    ));
    
    if(response.code == 0 && response.ok!){
      setState(() {
        // 将新数据添加到列表末尾
        if (response.data?.list != null && response.data!.list!.isNotEmpty) {
          _redEnvelopeList.addAll(response.data!.list!);
        }
        
        // 更新分页状态
        _isLoading = false;
        _hasMoreData = (response.data?.pages ?? 0) > _currentPage;
      });
    } else {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  void initState() {
    super.initState();
    debugPrint('获取红包记录');
    _getRedPacketRecord();
    _getRedPacketTotal();    
    // 添加滚动监听器
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 50) {
        // 滚动到底部，加载更多数据
        if (!_isLoading && _hasMoreData) {
          _loadMoreData();
        }
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose(); // 释放滚动控制器
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final loginUserInfoModel = Provider.of<LoginUserInfo>(context);
    final V2TimUserFullInfo loginUserInfo = loginUserInfoModel.loginUserInfo;

    ScreenUtil.init(
      context,
      designSize: const Size(375, 812),
      minTextAdapt: true,
    );
    return Scaffold(
      backgroundColor: const Color(0xFFF4F7F8),
      appBar: AppBar(
        backgroundColor: const Color(0xFFFF5D5E),
        automaticallyImplyLeading: false,
        title: Row(
          children: [
            GestureDetector(
              onTap: () => Navigator.pop(context),
              child: Row(
                children: [
                  Image.asset(
                    'assets/icon_right_back.png',
                    width: 24,
                    height: 24,
                    color: Colors.white,
                  ),
                  const SizedBox(width: 8),
                  Text(TIM_t('红包记录'),
                      style: const TextStyle(color: Colors.white, fontSize: 16))
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              _showTimeFilter();
            },
            child: Row(
              children: [
                Text(_selectedYear, style: TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.w400)),
                const SizedBox(width: 4),
                Image.asset('assets/red_envelope/ic_down.png', width: 12, height: 12, color: Colors.white,)
              ],
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          _topStatistics(loginUserInfo),
          Expanded(
            child: _redEnvelopeRecordList(),
          ),
        ],
      ),
    );
  }

  // 顶部统计区域
  Widget _topStatistics(V2TimUserFullInfo loginUserInfo) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 17),
      decoration: BoxDecoration(
        color: const Color(0xFFFFFFFF),
        borderRadius: BorderRadius.circular(5),
      ),
      child: Column(
        children: [
          // tab栏
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              InkWell(
                  onTap: () {
                    _switchTab('my_received');
                  },
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(vertical: 0, horizontal: 0),
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: _currentTab == 'my_received'
                            ? const BorderSide(
                                color: Color(0xFF333333),
                                width: 1,
                              )
                            : BorderSide.none,
                      ),
                    ),
                    child: Text( TIM_t('我收到的'),
                        style: TextStyle(
                            color: _currentTab == 'my_received'
                                ? const Color(0xFF333333)
                                : const Color(0xFFBBBBBB),
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w400)),
                  )),
              SizedBox(
                width: 57.w,
              ),
              InkWell(
                  onTap: () {
                    _switchTab('my_seed');
                  },
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(vertical: 0, horizontal: 0),
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: _currentTab == 'my_seed'
                            ? const BorderSide(
                                color: Color(0xFF333333),
                                width: 1,
                              )
                            : BorderSide.none,
                      ),
                    ),
                    child: Text(TIM_t('我发出的'),
                        style: TextStyle(
                            color: _currentTab == 'my_seed'
                                ? const Color(0xFF333333)
                                : const Color(0xFFBBBBBB),
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w400)),
                  ))
            ],
          ),
          // 用户信息和红包金额明细
          _userInfoAndRedEnvelopeDetail(loginUserInfo),
        ],
      ),
    );
  }

  // 用户信息和红包金额明细
  Widget _userInfoAndRedEnvelopeDetail(V2TimUserFullInfo loginUserInfo) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFFFFFFFF),
        borderRadius: BorderRadius.circular(5),
      ),
      child: Column(
        children: [
          // 用户信息
          Container(
            margin: const EdgeInsets.only(top: 20),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  spreadRadius: 1,
                  blurRadius: 5,
                  offset: const Offset(4, 6), // 阴影偏移，可以调整
                ),
              ],
            ),
            child: Avatar(
              avatarUrl: loginUserInfo.faceUrl,
              size: 68,
              radius: 34,
              showBorder: true,
              borderWidth: 4,
              borderColor: const Color(0xFFFFFFFF),
            ),
          ),
          const SizedBox(height: 12),
          Text.rich(
            TextSpan(
              children: [
                TextSpan(
                  text: loginUserInfo.nickName ?? '',
                  style: TextStyle(
                      color: const Color(0xFF333333),
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600),
                ),
                TextSpan(
                  text: _currentTab == 'my_received' ? TIM_t('共收到') : TIM_t('共发出'),
                  style: TextStyle(
                      color: const Color(0xFF333333),
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600),
                ),
              ],
            ),
          ),
          const SizedBox(height: 4),
          Text.rich(
            TextSpan(
              children: [
                TextSpan(
                  text: '₱',
                  style: TextStyle(
                      color: const Color(0xFF333333),
                      fontSize: 24.sp,
                      fontWeight: FontWeight.w500),
                ),
                TextSpan(
                  text: totalAmount.toStringAsFixed(2),
                  style: TextStyle(
                      color: const Color(0xFF333333),
                      fontSize: 36.sp,
                      fontWeight: FontWeight.w500),
                ),
              ],
            ),
          ),
          const SizedBox(height: 4),
          _redEnvelopeTotal(),
        ],
      ),
    );
  }

  Widget _redEnvelopeTotal() {
    final language = getLanguage();
    if (language == 'zh-Hans') {
      return Text.rich(
        TextSpan(
          children: [
            TextSpan(
              text: _currentTab == 'my_received' ? '收到的红包总数' : '发出的红包总数', // 正确使用 TIM_t_para
              style: TextStyle(
                  color: const Color(0xFF999999),
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w400),
            ),
            TextSpan(
              text: totalCount.toString(), // 正确使用 TIM_t_para
              style: TextStyle(
                  color: const Color(0xFF0072FC),
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w400),
            ),
            TextSpan(
              text: '个', // 正确使用 TIM_t_para
              style: TextStyle(
                  color: const Color(0xFF999999),
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w400),
            ),
          ],
        ),
      );
    } else {
      return Text.rich(
        TextSpan(
          children: [
            TextSpan(
              text:
                  _currentTab == 'my_received' ? 'Total number of red packets received $totalCount' : 'Total number of red packets issued $totalCount', // 正确使用 TIM_t_para
              style: TextStyle(
                  color: const Color(0xFF999999),
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w400),
            ),
          ],
        ),
      );
    }
  }
  

  // 红包记录列表
  Widget _redEnvelopeRecordList() {
    return Container(
      margin: const EdgeInsets.only(top: 20),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: const Color(0xFFFFFFFF),
        borderRadius: BorderRadius.circular(5),
      ),
      child: _redEnvelopeList.isEmpty
          ? const EmptyStateWidget(type: EmptyStateType.redEnvelopeRecord)
          : Column(
              children: [
                Expanded(
                  child: ListView.builder(
                    controller: _scrollController,
                    itemCount: _redEnvelopeList.length + (_hasMoreData ? 1 : 0),
                    itemBuilder: (context, index) {
                      if (index == _redEnvelopeList.length) {
                        // 显示加载更多指示器
                        return _buildLoadingIndicator();
                      } else {
                        return _redEnvelopeItem(_redEnvelopeList[index], index);
                      }
                    },
                  ),
                ),
              ],
            ),
    );
  }

  // 加载更多指示器
  Widget _buildLoadingIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16),
      alignment: Alignment.center,
      child: const CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFE84B32)),
        strokeWidth: 2,
      ),
    );
  }

  // 红包记录列表项
  Widget _redEnvelopeItem(RecordItem item, int index) {
    return Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
            color: const Color(0xFFFFFFFF),
            borderRadius: BorderRadius.circular(5),
            border: const Border(
              bottom: BorderSide(
                color: Color(0xFFE9E9E9),
                width: 0.5,
              ),
            )),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.nick!,
                  style: TextStyle(
                      color: const Color(0xFF333333),
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w500),
                ),
                Text(
                  item.createTime ?? '',
                  style: TextStyle(
                      color: const Color(0xFF999999),
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w400),
                )
              ],
            ),
            Text(
              '₱${double.tryParse(item.totalAmount ?? '0')?.toStringAsFixed(2) ?? '0.00'}',
              style: TextStyle(
                  color: const Color(0xFF333333),
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500),
            ),
          ],
        ));
  }

  // 显示时间筛选框
  void _showTimeFilter() {
    String selectedYear = _selectedYear; // 使用当前选中的年份
    // 开始年份为当前的年份，结束年份为2025年
    int startYear = DateTime.now().year;
    int endYear = 2025;
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFFFFFFFF),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          contentPadding: const EdgeInsets.fromLTRB(0, 16, 0, 16), // 内容内边距
          title: Center(child: Text(TIM_t('选择年份'), style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500))),
          content: SingleChildScrollView(
            child: Container(
              color: const Color(0xFFFFFFFF),
              width: double.maxFinite,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  for (int year = startYear; year >= endYear; year--)
                    ListTile(
                      title: Text('$year', style: const TextStyle(fontSize: 16)),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 16),
                      trailing: Radio<String>(
                        value: year.toString(),
                        groupValue: selectedYear,
                        activeColor: const Color(0xffFF5D5E),
                        onChanged: (String? value) {
                          Navigator.of(context).pop(value);
                        },
                      ),
                      onTap: () {
                        Navigator.of(context).pop(year.toString());
                      },
                    ),
                ],
              ),
            ),
          ),
        );
      },
    ).then((value) {
      if (value != null) {
        setState(() {
          _selectedYear = value; // 更新选中的年份
          _getRedPacketRecord(year: value);
        });
      }
    });
  }
}
