import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';
import 'package:tencent_cloud_chat_demo/config.dart';
import 'package:provider/provider.dart';
import 'package:tencent_cloud_chat_demo/models/backList_response.dart';
import '../../provider/wallet_provider.dart';
import '../../../utils/toast.dart';
import '../../widgets/bottom_sheet_picker_bank.dart';

class WithdrawPage extends StatefulWidget {
  final String type;
  const WithdrawPage({Key? key, required this.type}) : super(key: key);

  @override
  State<WithdrawPage> createState() => _WithdrawPageState();
}

class _WithdrawPageState extends State<WithdrawPage> {
  late WalletProvider walletProvider;
  final TextEditingController amountController = TextEditingController();


  @override
  void initState() {
    super.initState();
    walletProvider = Provider.of<WalletProvider>(context, listen: false);
  }

  showBankChoseSheet(BuildContext context, WalletProvider walletProvider) {
    // 默认选择的银行卡
    BankCard? defaultBank;
    if(walletProvider.selectedBank != null) {
      defaultBank = walletProvider.selectedBank;
    }else {
      defaultBank = walletProvider.bankList.first;
    }
    debugPrint('defaultBank ${defaultBank?.id.toString()}');
    // 使用组件
      BottomSheetPicker.show(
      context: context,
      title: TIM_t("提现至"),
      options: walletProvider.bankList.map((bank) => BottomSheetOption(
          title: bank.cardNo.toString(),
          isSelected: bank.id == defaultBank?.id,
          onTap: () async {
            debugPrint(bank.cardNo);
            walletProvider.changeDefaultBank(bank);
          },
        ),
      ).toList(),
      cancelText: TIM_t("取消"),
    );
  }

  // 全部提现
  void _allWithdraw() {
    // 获取当前余额并填入输入框
    amountController.text = walletProvider.balance.toStringAsFixed(2);
  }

  void handleWithdraw(String amount) {
    if (amount.isEmpty) {
      ToastUtils.toast(TIM_t('请输入提现金额'));
      return;
    }

    double? withdrawAmount = double.tryParse(amount);
    if (withdrawAmount == null) {
      ToastUtils.toast(TIM_t('请输入有效的金额'));
      return;
    }

    if (withdrawAmount <= 0) {
      ToastUtils.toast(TIM_t('请输入有效的金额'));
      return;
    }

    if (withdrawAmount > walletProvider.balance) {
      ToastUtils.toast(TIM_t('余额不足'));
      return;
    }

    // 执行提现操作，使用 walletProvider.withdraw 而不是 bankVM.withdraw
    walletProvider.withdraw(withdrawAmount);
    
    // 清空输入框
    amountController.clear();
    
    // 显示提现成功提示
    ToastUtils.toast(TIM_t('提交成功, 请等待'));
    Navigator.pop(context);
  }

  void handleRecharge(String amount) {
    if (amount.isEmpty) {
      ToastUtils.toast(TIM_t('请输入充值金额'));
      return;
    }

    double? rechargeAmount = double.tryParse(amount);
    if (rechargeAmount == null) {
      ToastUtils.toast(TIM_t('请输入有效的金额'));
      return;
    }

    if (rechargeAmount <= 0) {
      ToastUtils.toast(TIM_t('请输入有效的金额'));
      return;
    }

    walletProvider.recharge(rechargeAmount).then((value) {
      debugPrint('walletProviderwalletProvider ${value.toJson()}');
      if(value.code == 0 && value.ok == true) {
        ToastUtils.toast(TIM_t('提交成功, 请等待'));
        Navigator.pop(context);
      }else {
        ToastUtils.toast(value.msg ?? '');
      }
    });
    
    // 清空输入框
    amountController.clear();
  }
  
  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(
      context,
      designSize: const Size(375, 812),
      minTextAdapt: true,
    );
    return  Scaffold(
        backgroundColor: const Color(0xFFF4F7F8),
        appBar: AppBar(
          elevation: 1,
          title: Text(widget.type == 'withdraw' ? TIM_t("提现") : TIM_t("充值"),style: const TextStyle(fontSize: IMDemoConfig.appBarTitleFontSize),),
          backgroundColor: const Color(0xFFFFFFFF),
          surfaceTintColor: Colors.white,
        ),
        body: Column(
          children: [
            Container(
              padding: EdgeInsets.only(top: 16.h, left: 24.w, right: 24.w, bottom: 16.h),
              child: _bankView(),
            ),
            Expanded(
              child: Container(
                padding: EdgeInsets.only(top: 24.h, left: 24.w, right: 24.w),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(topLeft: Radius.circular(24.w), topRight: Radius.circular(24.w)),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(widget.type == 'withdraw' ? TIM_t('提现金额') : TIM_t('充值金额'), style: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w400, color: const Color(0xFF333333))),
                  SizedBox(height: 18.h,),
                  Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                     border: Border(
                      bottom: BorderSide(
                        color: const Color(0xFFE9E9E9),
                        width: 1.w,
                      )
                     )
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text('₱', style: TextStyle(fontSize: 24.sp, fontWeight: FontWeight.w500, color: const Color(0xFF333333))),
                        SizedBox(width: 8.w,),
                        SizedBox(
                          width: 200.w,
                          child: TextField(
                            controller: amountController,
                            onSubmitted: (value) {
                              if(widget.type == 'withdraw') {
                                handleWithdraw(value);
                              }else {
                                handleRecharge(value);
                              }
                            },
                            style: TextStyle(fontSize: 36.sp, fontWeight: FontWeight.w500, color: const Color(0xFF333333)),
                            cursorHeight: 36.h,
                            cursorWidth: 2.w,
                            cursorColor: const Color(0xFF333333),
                            keyboardType: TextInputType.number,
                            decoration: InputDecoration(
                              border: InputBorder.none,
                              focusedBorder: InputBorder.none,
                              enabledBorder: InputBorder.none,
                              errorBorder: InputBorder.none,
                              disabledBorder: InputBorder.none,
                              contentPadding: EdgeInsets.symmetric(vertical: 0.h, horizontal: 8.w),
                              hintText: '0.00',
                              hintStyle: TextStyle(fontSize: 36.sp, fontWeight: FontWeight.w500, color: const Color(0xFF999999)),
                            
                            ),
                          ),
                        )
                      ],
                    ),
                  ),
                  SizedBox(height: 16.h,),
                  if(widget.type == 'withdraw')
                  _allWithdrawButton(),
                  SizedBox(height: 16.h,),
                  _withdrawButton(),
                ],
              ),
              )
            )
          ],
        ));

  }

  Widget _allWithdrawButton() {
    return Consumer<WalletProvider>(
      builder: (context, walletProvider, child) {
        return Row(
          children: [
            Text(TIM_t('当前零钱余额'), style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.w400, color: const Color(0xFF999999))),
            Text('₱', style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.w400, color: const Color(0xFF999999))),
            Text(walletProvider.balance.toStringAsFixed(2), style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.w500, color: const Color(0xFF999999))),
            Text(',  ', style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.w400, color: const Color(0xFF999999))),
            InkWell(
              onTap: () {
                _allWithdraw();
              },
              child: Text(TIM_t('全部提现'), style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.w400, color: const Color(0xFF0072FC))),
            )
          ],
        );
      }
    );
  }

  Widget _withdrawButton() {
    return InkWell(
      onTap: () {
        if(widget.type == 'withdraw') {
          handleWithdraw(amountController.text);
        }else {
          handleRecharge(amountController.text);
        }
      },
      child: Container(
        width: double.infinity,
        height: 48.h,
        decoration: BoxDecoration(
          color: const Color(0xFF0072FC),
          borderRadius: BorderRadius.circular(24.w),
        ),
        child: Center(
          child: Text(widget.type == 'withdraw' ? TIM_t('提现') : TIM_t('充值'), style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w500, color: Colors.white)),
        ),
      ),
    );
  }

  Widget _bankView() {
    return Consumer<WalletProvider>(
      builder: (context, walletProvider, child) {
          // 默认选择的银行卡
          BankCard? defaultBank;
          if(walletProvider.selectedBank != null) {
            defaultBank = walletProvider.selectedBank;
          }else {
            defaultBank = walletProvider.bankList.first;
          }

        return InkWell(
                onTap: () {
                  showBankChoseSheet(context, walletProvider);
                },
                child: Row(
                  children: [
                    Text(widget.type == 'withdraw' ? TIM_t('到账银行卡') : TIM_t('充值方式'), style: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w400, color: const Color(0xFF333333))),
                    SizedBox(width: 12.w,),
                    Image.asset('assets/serveice/bank_icon.png', width: 16.w, height: 16.h,),
                    SizedBox(width: 4.w,),
                    Text(defaultBank!.cardNo!, style: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w500, color: const Color(0xFF333333))),
                    const Spacer(),
                    Image.asset('assets/serveice/right_icon.png', width: 16.w, height: 16.h,),
                  ],
                ),
              );
      }
    );
  }
}

