import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';
import 'package:tencent_cloud_chat_demo/config.dart';
import 'package:tencent_cloud_chat_demo/utils/toast.dart';
import './bank_page.dart';
import './money_page.dart';
import './bill_page.dart';
import '../../provider/wallet_provider.dart';
import 'package:provider/provider.dart';


class WalletPage extends StatefulWidget {
  const WalletPage({Key? key}) : super(key: key);
  @override
  State<StatefulWidget> createState() => _WalletPageState();
}
class _WalletPageState extends State<WalletPage> {
  late WalletProvider walletProvider;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    walletProvider = Provider.of<WalletProvider>(context, listen: false);
  }

  jumpPage(String type) {
    if (type == 'money') {
      Navigator.push(context, MaterialPageRoute(builder: (context) => const MoneyPage()));
    }
    if (type == 'bank') {
      Navigator.push(context, MaterialPageRoute(builder: (context) => const BankPage()));
    }
    if (type == 'bill') {
      // Navigator.push(context, MaterialPageRoute(builder: (context) => const BillPage()));
      ToastUtils.toast(TIM_t('开发中'));
    }
  }

  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(
      context,
      designSize: const Size(375, 812),
      minTextAdapt: true,
    );
    return Scaffold(
      backgroundColor: const Color(0xFFF9F9F9),
      appBar: AppBar(
        elevation: 1,
        title: Text(
          TIM_t("钱包"),
          style: const TextStyle(fontSize: IMDemoConfig.appBarTitleFontSize),
        ),
        backgroundColor: const Color(0xFFFFFFFF),
        surfaceTintColor: Colors.white,
      ),
      body: Container(
        padding: EdgeInsets.only(top: 16.h),
        child: Column(
          children: [
           InkWell(
            onTap: () {
              jumpPage('money');
            },
            child: Container(
              padding: EdgeInsets.only(top: 12.h, left: 16.w, right: 16.w, bottom: 12.h),
              color: Colors.white,
              child: Row(
                children: [
                Image.asset('assets/serveice/money_icon.png', width: 24.w, height: 24.h,),
                SizedBox(width: 12.w,),
                Text(TIM_t("零钱"), style: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w500, color: const Color(0xFF333333))),
                const Spacer(),
                moneyView(),
                SizedBox(width: 8.w,),
                Image.asset('assets/serveice/right_icon.png', width: 16.w, height: 16.h,),
              ],
             ),
            )
           ),
           SizedBox(height: 12.h,),
           InkWell(
            onTap: () {
              jumpPage('bank');
            },
              child: Container(
                padding: EdgeInsets.only(top: 12.h, left: 16.w, right: 16.w, bottom: 12.h),
                color: Colors.white,
                child: Row(
                  children: [
                  Image.asset('assets/serveice/bank_card_icon.png', width: 24.w, height: 24.h,),
                  SizedBox(width: 12.w,),
                  Text(TIM_t("银行卡"), style: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w500, color: const Color(0xFF333333))),
                  const Spacer(),
                  Image.asset('assets/serveice/right_icon.png', width: 16.w, height: 16.h,),
                ],
              ),
            ),
           ),
           SizedBox(height: 12.h,),
           InkWell(
            onTap: () {
              jumpPage('bill');
            },
            child: Container(
              padding: EdgeInsets.only(top: 12.h, left: 16.w, right: 16.w, bottom: 12.h),
              color: Colors.white,
              child: Row(
                children: [
                Image.asset('assets/serveice/bill_icon.png', width: 24.w, height: 24.h,),
                SizedBox(width: 12.w,),
                Text(TIM_t("账单"), style: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w500, color: const Color(0xFF333333))),
                const Spacer(),
                Image.asset('assets/serveice/right_icon.png', width: 16.w, height: 16.h,),
              ],
             ),
           ))
          ],
        ),
      )
    );
  }
  Widget moneyView() {
    return Consumer<WalletProvider>(
      builder: (context, walletProvider, child){
        return Text('₱${walletProvider.balance.toStringAsFixed(2)}', style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.w500, color: const Color(0xFF333333)));
      }
    );
  }
}
  