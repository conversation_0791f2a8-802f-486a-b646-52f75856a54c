import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tencent_cloud_chat_demo/models/coverList_response.dart';
import 'package:tencent_cloud_chat_demo/src/provider/local_setting.dart';
import '../../apis/redPacket_api.dart';
import '../../models/getPacketRecord_response.dart';
import '../../models/language_local.dart';
import '../../postData/get_red_packet_cover_list.dart';

class CoverProvider with ChangeNotifier {
  List<CoverItem> _redEnvelopeType = [];

    // 默认选择的红包封面
  CoverItem _selectedRedEnvelopeType = CoverItem();


  List<CoverItem> get redEnvelopeType => _redEnvelopeType;

  CoverItem get selectedRedEnvelopeType => _selectedRedEnvelopeType;

  // 3. 构造函数
  CoverProvider() {
    // 初始化操作，例如加载数据
    _initData();
  }

    // 4. 私有初始化方法
  Future<void> _initData() async {
    // await getRedPacketRecord(context);
  }

  // 分页获取红包列表
  Future<void> getRedPacketRecord(BuildContext context) async {
    final localSetting = Provider.of<LocalSetting>(context, listen: false);
    final res = await Api.instance.getRedPacketCoverList(GetRedPacketCoverListData(
      pageNum: 1,
      pageSize: 10,
      language: localSetting.language == LanguageEnum.zhHans.value ? RedPacketLanguage.zh : RedPacketLanguage.en,
    ));

      if(res.code == 0 && res.ok!){
        // 本地调试将list中的item的url的前缀http://192.168.2.1:1024替换成http://101.34.90.82:18082/
        // res.data?.list?.forEach((element) {
        //   element.url = element.url?.replaceFirst('http://192.168.2.1:1024', 'http://101.34.90.82:18082');
        // });
        _redEnvelopeType = res.data?.list ?? [];
        if(_selectedRedEnvelopeType.id == null){
          _selectedRedEnvelopeType = _redEnvelopeType.first;
        }
      }
      notifyListeners();
  }

  // 切换默认选择的红包封面
  void changeDefaultRedEnvelopeType(CoverItem coverItem) {
    _selectedRedEnvelopeType = coverItem;
    notifyListeners();
  }

  // 清除默认选择的红包封面和红包封面列表
  void clearRedEnvelopeType() {
    _selectedRedEnvelopeType = CoverItem();
    _redEnvelopeType = [];
    notifyListeners();
  }

}