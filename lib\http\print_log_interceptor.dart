import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

class PrintLogInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    debugPrint('\nrequest---------->');
    options.headers.forEach((key, value) {
      debugPrint('请求头信息：key=$key value=${value.toString()}');
    });
    debugPrint('path:${options.path}');
    debugPrint('method:${options.method}');
    debugPrint('data:${options.data}');
    if (options.queryParameters.isNotEmpty) {
      debugPrint('请求参数：${options.queryParameters}');
    }
    debugPrint('\nrequest---------->');
    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    debugPrint('\nresponse---------->');
    debugPrint('path:${response.realUri}');
    debugPrint('Headers: ${response.headers.toString()}');
    debugPrint('statusCode:${response.statusCode}');
    debugPrint('requestId:${response.statusMessage}');
    debugPrint('Data222: ${response.data}');
    debugPrint('\nresponse---------->');
    super.onResponse(response, handler);
  }

  @override
  void onError(DioError err, ErrorInterceptorHandler handler) {
    debugPrint('\nresponse---------->');
    debugPrint('Error: ${err.toString()}');
    debugPrint('\nresponse---------->');
    super.onError(err, handler);
  }
}
