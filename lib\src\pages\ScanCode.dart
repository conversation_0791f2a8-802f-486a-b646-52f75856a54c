import 'package:flutter/material.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'dart:ui' as ui;
import 'dart:async';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';
import 'package:tencent_cloud_chat_demo/src/pages/qrcode/qrcode.dart';
import 'package:image_picker/image_picker.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_user_full_info.dart';

import '../provider/login_user_Info.dart';

class ScanCode extends StatefulWidget {
  final void Function(String result) onScanCompleted;
  const ScanCode({super.key, required this.onScanCompleted});

  @override
  State<ScanCode> createState() => _ScanCode();
}

class _ScanCode extends State<ScanCode> with WidgetsBindingObserver {
  Barcode? _barcode;
  final MobileScannerController controller = MobileScannerController();
  bool isFlashOn = false;
  bool showFlashButton = false;
  bool scanSuccess = false;
  Timer? _brightnessTimer;

  void _handleBarcode(BarcodeCapture barcodes) async {
    if (mounted && !scanSuccess) {
      final barcode = barcodes.barcodes.firstOrNull;
      if (barcode != null) {
        // 触发震动
        HapticFeedback.mediumImpact();

        // 触发扫描成功状态
        setState(() {
          _barcode = barcode;
          scanSuccess = true;
        });

        // 等待短暂动画后返回
        await Future.delayed(const Duration(milliseconds: 300));

        widget.onScanCompleted(barcode.displayValue ?? '');

        await controller.stop();
        // ignore: use_build_context_synchronously
        if (mounted) Navigator.pop(context);
      }
    }
  }

  // 从相册选择图片并识别二维码
  void _pickImageFromGallery() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
      );

      if (image != null && mounted) {
        // 显示加载提示
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => Center(
            child: Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.8),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF1AAD19)),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    TIM_t('正在识别二维码')+'...',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );

        try {
          // 使用 mobile_scanner 分析图片中的二维码
          final BarcodeCapture? result = await controller.analyzeImage(image.path);

          // 关闭加载对话框
          if (mounted) Navigator.pop(context);

          if (result != null && result.barcodes.isNotEmpty) {
            final barcode = result.barcodes.first;
            if (barcode.displayValue != null && barcode.displayValue!.isNotEmpty) {
              // 触发震动
              HapticFeedback.mediumImpact();

              // 返回结果
              widget.onScanCompleted(barcode.displayValue!);
              await controller.stop();
              // ignore: use_build_context_synchronously
              if (mounted) Navigator.pop(context);
              return;
            }
          }

          // 如果没有识别到二维码，显示提示
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: [
                    Icon(Icons.error_outline, color: Colors.white),
                    const SizedBox(width: 8),
                    const Text('未在图片中发现二维码'),
                  ],
                ),
                backgroundColor: Colors.red.withOpacity(0.9),
                behavior: SnackBarBehavior.floating,
                margin: const EdgeInsets.all(16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                duration: const Duration(seconds: 3),
              ),
            );
          }
        } catch (e) {
          // 关闭加载对话框
          if (mounted) Navigator.pop(context);

          // 处理识别错误
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: [
                    Icon(Icons.error_outline, color: Colors.white),
                    const SizedBox(width: 8),
                     Text(TIM_t('图片识别失败请重试')),
                  ],
                ),
                backgroundColor: Colors.red.withOpacity(0.9),
                behavior: SnackBarBehavior.floating,
                margin: const EdgeInsets.all(16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                duration: const Duration(seconds: 3),
              ),
            );
          }
        }
      }
    } catch (e) {
      // 处理选择图片错误
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.error_outline, color: Colors.white),
                const SizedBox(width: 8),
                 Text(TIM_t('选择图片失败请重试')),
              ],
            ),
            backgroundColor: Colors.red.withOpacity(0.9),
            behavior: SnackBarBehavior.floating,
            margin: const EdgeInsets.all(16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  void _toggleFlash() async {
    await controller.toggleTorch();
    setState(() {
      isFlashOn = !isFlashOn;
    });
  }

  @override
  void didChangePlatformBrightness() {
    super.didChangePlatformBrightness();
    _checkCameraBrightness();
  }

  void _checkCameraBrightness() async {
    if (!mounted || scanSuccess) return;

    try {
      final mediaQuery = MediaQuery.of(context);
      final platformBrightness = mediaQuery.platformBrightness;

      bool shouldShow = false;

      if (platformBrightness == Brightness.dark) {
        shouldShow = true;
      }

      final themeBrightness = Theme.of(context).brightness;
      if (themeBrightness == Brightness.dark) {
        shouldShow = true;
      }

      if (showFlashButton != shouldShow && mounted && !scanSuccess) {
        setState(() {
          showFlashButton = shouldShow;
        });
      }
    } catch (e) {
      final shouldShow = MediaQuery.of(context).platformBrightness == Brightness.dark;
      if (showFlashButton != shouldShow && mounted && !scanSuccess) {
        setState(() {
          showFlashButton = shouldShow;
        });
      }
    }
  }

  void _startBrightnessMonitoring() {
    _brightnessTimer = Timer.periodic(const Duration(seconds: 2), (timer) {
      if (!mounted || scanSuccess) {
        timer.cancel();
        return;
      }
      _checkCameraBrightness();
    });
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          _checkCameraBrightness();
          _startBrightnessMonitoring();
        }
      });
    });
  }

  @override
  void dispose() {
    _brightnessTimer?.cancel();
    WidgetsBinding.instance.removeObserver(this);
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // 顶部状态栏和关闭按钮
          // 相机预览 - 添加亮度调整
          ColorFiltered(
            colorFilter: ColorFilter.matrix([
              1.2, 0, 0, 0, 0,    // 红色增强
              0, 1.2, 0, 0, 0,    // 绿色增强
              0, 0, 1.2, 0, 0,    // 蓝色增强
              0, 0, 0, 1, 0,      // Alpha不变
            ]),
            child: MobileScanner(
              controller: controller,
              onDetect: _handleBarcode,
            ),
          ),



          // 扫描框和动画
          WeChatQrCodeScanAnimation(scanSuccess: scanSuccess),

          // 底部工具栏
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: SafeArea(
              child: Container(
                padding: const EdgeInsets.only(bottom: 10),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      Colors.black.withOpacity(0.8),
                    ],
                  ),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const SizedBox(height: 20),

                    // 轻触照亮
                    AnimatedOpacity(
                      opacity: showFlashButton ? 1.0 : 0.0,
                      duration: scanSuccess ? Duration.zero : const Duration(milliseconds: 300),
                      child: AnimatedContainer(
                        duration: scanSuccess ? Duration.zero : const Duration(milliseconds: 300),
                        height: showFlashButton ? null : 0,
                        child: showFlashButton ? GestureDetector(
                          onTap: scanSuccess ? null : _toggleFlash,
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Container(
                                width: 60,
                                height: 60,
                                decoration: BoxDecoration(
                                  color: isFlashOn ? Colors.yellow.withOpacity(0.8) : Colors.white.withOpacity(0.1),
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: Colors.white.withOpacity(0.3),
                                    width: 1,
                                  ),
                                ),
                                child: Icon(
                                  Icons.flash_on,
                                  color: isFlashOn ? Colors.black : Colors.white,
                                  size: 28,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                TIM_t('轻触照亮'),
                                style: TextStyle(
                                  color: Colors.white.withOpacity(0.8),
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ) : const SizedBox.shrink(),
                      ),
                    ),

                    const SizedBox(height: 25),

                    // 底部功能按钮
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 40),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          // 我的二维码
                          GestureDetector(
                            onTap: () {
                              final loginUserInfoModel = Provider.of<LoginUserInfo>(context, listen: false);
                              final V2TimUserFullInfo loginUserInfo = loginUserInfoModel.loginUserInfo;
                              Navigator.push(
                                context,
                                MaterialPageRoute(builder: (context) => Qrcode(userInfo: loginUserInfo,)),
                              );
                            },
                            child: Opacity(
                              opacity: scanSuccess ? 0.5 : 1.0,
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Container(
                                    width: 50,
                                    height: 50,
                                    decoration: BoxDecoration(
                                      color: Colors.white.withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: const Icon(
                                      Icons.qr_code,
                                      color: Colors.white,
                                      size: 24,
                                    ),
                                  ),
                                  const SizedBox(height: 6),
                                  Text(
                                    TIM_t('我的二维码'),
                                    style: TextStyle(
                                      color: Colors.white.withOpacity(0.8),
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),

                          // 相册
                          GestureDetector(
                            onTap: scanSuccess ? null : _pickImageFromGallery,
                            child: Opacity(
                              opacity: scanSuccess ? 0.5 : 1.0,
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Container(
                                    width: 50,
                                    height: 50,
                                    decoration: BoxDecoration(
                                      color: Colors.white.withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: const Icon(
                                      Icons.photo_library,
                                      color: Colors.white,
                                      size: 24,
                                    ),
                                  ),
                                  const SizedBox(height: 6),
                                  Text(
                                    TIM_t('相册'),
                                    style: TextStyle(
                                      color: Colors.white.withOpacity(0.8),
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 10),
                  ],
                ),
              ),
            ),
          ),
          SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // 关闭按钮
                  GestureDetector(
                    onTap: (){
                      print("object");
                      Navigator.pop(context);
                    },
                    child: Container(
                      width: 44,
                      height: 44,
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.5),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.close,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                  ),

                  // 更多按钮
                  // GestureDetector(
                  //   onTap: () {
                  //     // 更多功能
                  //   },
                  //   child: Container(
                  //     width: 44,
                  //     height: 44,
                  //     decoration: BoxDecoration(
                  //       color: Colors.black.withOpacity(0.5),
                  //       shape: BoxShape.circle,
                  //     ),
                  //     child: const Icon(
                  //       Icons.more_horiz,
                  //       color: Colors.white,
                  //       size: 24,
                  //     ),
                  //   ),
                  // ),
                ],
              ),
            ),
          ),

        ],
      ),
    );
  }
}

class WeChatQrCodeScanAnimation extends StatefulWidget {
  final bool scanSuccess;
  const WeChatQrCodeScanAnimation({super.key, required this.scanSuccess});

  @override
  State<WeChatQrCodeScanAnimation> createState() => _WeChatQrCodeScanAnimationState();
}

class _WeChatQrCodeScanAnimationState extends State<WeChatQrCodeScanAnimation>
    with TickerProviderStateMixin {
  AnimationController? _scanController;
  AnimationController? _successController;
  Animation<double>? _successAnimation;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  void _initializeControllers() {
    _scanController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _successController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _successAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _successController!,
      curve: Curves.elasticOut,
    ));

    if (!widget.scanSuccess) {
      _scanController!.repeat();
    }
  }

  @override
  void didUpdateWidget(WeChatQrCodeScanAnimation oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.scanSuccess && !oldWidget.scanSuccess && _scanController != null && _successController != null) {
      _scanController!.stop();
      _successController!.forward();
    }
  }

  @override
  void dispose() {
    _scanController?.dispose();
    _successController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final scanAreaSize = screenSize.width * 0.7;

    if (_scanController == null || _successController == null || _successAnimation == null) {
      return const SizedBox.shrink();
    }

    return Center(
      child: Stack(
        children: [
          // 半透明遮罩
          Container(
            width: screenSize.width,
            height: screenSize.height,
            color: Colors.black.withOpacity(0.5),
          ),

          // 扫描区域透明部分
          Center(
            child: Container(
              width: scanAreaSize,
              height: scanAreaSize,
              decoration: BoxDecoration(
                color: Colors.transparent,
                border: Border.all(
                  color: Colors.transparent,
                ),
              ),
              child: ClipRRect(
                child: BackdropFilter(
                  filter: ui.ImageFilter.blur(sigmaX: 0, sigmaY: 0),
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.transparent,
                    ),
                  ),
                ),
              ),
            ),
          ),

          // 四个角的边框和扫描线
          Center(
            child: CustomPaint(
              painter: WeChatScannerPainter(
                _scanController!,
                _successAnimation!,
                widget.scanSuccess,
              ),
              size: Size(scanAreaSize, scanAreaSize),
            ),
          ),

          // 提示文字
          Positioned(
            bottom: screenSize.height * 0.25,
            left: 0,
            right: 0,
            child: Text(
              TIM_t('将二维码放入框内,即可自动扫描'),
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.white.withOpacity(0.8),
                fontSize: 16,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class WeChatScannerPainter extends CustomPainter {
  final Animation<double> scanAnimation;
  final Animation<double> successAnimation;
  final bool scanSuccess;

  WeChatScannerPainter(
      this.scanAnimation,
      this.successAnimation,
      this.scanSuccess,
      ) : super(repaint: Listenable.merge([scanAnimation, successAnimation]));

  @override
  void paint(Canvas canvas, Size size) {
    final width = size.width;
    final height = size.height;

    // 四角边框始终保持微信绿色
    final borderPaint = Paint()
      ..color = const Color(0xFF1AAD19)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 4;

    // 四个角的长度
    final cornerLength = 20.0;

    // 绘制四个角
    // 左上角
    canvas.drawLine(
      const Offset(0, 0),
      Offset(cornerLength, 0),
      borderPaint,
    );
    canvas.drawLine(
      const Offset(0, 0),
      Offset(0, cornerLength),
      borderPaint,
    );

    // 右上角
    canvas.drawLine(
      Offset(width, 0),
      Offset(width - cornerLength, 0),
      borderPaint,
    );
    canvas.drawLine(
      Offset(width, 0),
      Offset(width, cornerLength),
      borderPaint,
    );

    // 左下角
    canvas.drawLine(
      Offset(0, height),
      Offset(cornerLength, height),
      borderPaint,
    );
    canvas.drawLine(
      Offset(0, height),
      Offset(0, height - cornerLength),
      borderPaint,
    );

    // 右下角
    canvas.drawLine(
      Offset(width, height),
      Offset(width - cornerLength, height),
      borderPaint,
    );
    canvas.drawLine(
      Offset(width, height),
      Offset(width, height - cornerLength),
      borderPaint,
    );

    // 扫描线（成功时隐藏）
    if (!scanSuccess) {
      final linePaint = Paint()
        ..shader = LinearGradient(
          colors: [
            const Color(0xFF1AAD19).withOpacity(0.1),
            const Color(0xFF1AAD19),
            const Color(0xFF1AAD19).withOpacity(0.1),
          ],
          stops: const [0.0, 0.5, 1.0],
        ).createShader(Rect.fromLTWH(0, 0, width, 3))
        ..strokeWidth = 3;

      // 计算扫描线位置
      final progress = scanAnimation.value;
      late double lineY;

      if (progress <= 0.5) {
        lineY = height * (progress * 2);
      } else {
        lineY = height * (2 - progress * 2);
      }

      canvas.drawLine(
        Offset(0, lineY),
        Offset(width, lineY),
        linePaint,
      );

      // 绘制扫描线上的小光点效果
      final glowPaint = Paint()
        ..color = const Color(0xFF1AAD19)
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3);

      canvas.drawCircle(
        Offset(width / 2, lineY),
        2,
        glowPaint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}