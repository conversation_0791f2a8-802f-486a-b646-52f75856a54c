import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../src/provider/local_setting.dart';

class LanguageInterceptor extends Interceptor {
  final BuildContext? context;

  LanguageInterceptor({this.context});

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    // 使用 Provider.of<LocalSetting>(context, listen: false).language 获取语言设置
    if (context != null) {
      try {
        final localSetting = Provider.of<LocalSetting>(context!, listen: false);
        String? language = localSetting.language;

        // 添加 accept-language 请求头
        if (language != null && language.isNotEmpty) {
          options.headers['accept-language'] = language;
        }
      } catch (e) {
        // 如果获取失败，使用默认值
        options.headers['accept-language'] = 'en';
      }
    } else {
      // 如果没有 context，使用默认值
      options.headers['accept-language'] = 'en';
    }

    handler.next(options);
  }
}
