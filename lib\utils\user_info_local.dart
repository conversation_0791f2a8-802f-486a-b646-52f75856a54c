import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/login_response.dart';

class UserInfoLocal {
  static const String _keyToken = 'token';
  static const String _keyUserId = 'userId';
  static const String _keyAppId = 'appId';
  static const String _keyUserInfo = 'userInfo';
  static const String _keyGenUserSig = 'genUserSig';

  /// 保存登录响应数据
  static Future<void> saveLoginResponse(LoginData loginData) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_keyToken, loginData.token);
    debugPrint("loginData: ${loginData.toJson()}");
    debugPrint("设置token: ${loginData.token}");
    debugPrint("设置token: ${loginData.token.toString()}");
    await prefs.setString(_keyUserId, loginData.phone.toString());
    await prefs.setInt(_keyAppId, loginData.appId ?? 0);
    await prefs.setString(_keyGenUserSig, loginData.genUserSig);
    
    // 保存完整的用户信息
    await prefs.setString(_keyUserInfo, jsonEncode(loginData.toJson()));
  }

  /// 获取Token
  static Future<String?> getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_keyToken);
  }

  /// 获取用户ID
  static Future<String?> getUserId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_keyUserId);
  }

  /// 获取AppID
  static Future<int?> getAppId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_keyAppId);
  }

  /// 获取用户签名
  static Future<String?> getGenUserSig() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_keyGenUserSig);
  }

  /// 获取完整的用户信息
  static Future<LoginData?> getUserInfo() async {
    final prefs = await SharedPreferences.getInstance();
    final userInfoJson = prefs.getString(_keyUserInfo);
    if (userInfoJson != null) {
      return LoginData.fromJson(jsonDecode(userInfoJson));
    }
    return null;
  }

  /// 检查是否已登录
  static Future<bool> isLoggedIn() async {
    final token = await getToken();
    return token != null && token.isNotEmpty;
  }

  /// 清除登录信息
  static Future<void> clearLoginInfo() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_keyToken);
    await prefs.remove(_keyUserId);
    await prefs.remove(_keyAppId);
    await prefs.remove(_keyUserInfo);
    await prefs.remove(_keyGenUserSig);
  }

  /// 清除所有数据
  static Future<void> clearAll() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.clear();
  }
}

// 红包本地缓存
class RedEnvelopeLocal {
  static const String _keyRedEnvelopeType = 'redEnvelopeType';

  /// 保存红包封面类型
  static Future<void> saveRedEnvelopeType(Map<String, dynamic> type) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_keyRedEnvelopeType, jsonEncode(type));
  }

  /// 获取红包封面类型
  static Future<Map<String, dynamic>?> getRedEnvelopeType() async {
    final prefs = await SharedPreferences.getInstance();
    final redEnvelopeTypeJson = prefs.getString(_keyRedEnvelopeType);
    if (redEnvelopeTypeJson != null) {
      return jsonDecode(redEnvelopeTypeJson);
    }
    return null;
  }

  // 清除红包封面类型
  static Future<void> clearRedEnvelopeType() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_keyRedEnvelopeType);
  }
}

// 多语言本地缓存
class LanguageLocal {
  static const String _keyLanguage = 'language';
  /// 保存语言
  static Future<void> saveLanguage(String language) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_keyLanguage, language);
  }

  /// 获取语言
  static Future<String?> getLanguage() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_keyLanguage);
  }
}    